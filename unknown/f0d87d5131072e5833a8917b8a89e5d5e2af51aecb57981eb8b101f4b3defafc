---
description: 
globs: 
alwaysApply: true
---
### 5. 数据结构 Schema 设计要点与注意事项

#### 总体目标

* 数据结构需满足当前需求，并考虑未来可能扩展。
* 命名、索引、数据类型选择必须明确规范。
* 数据结构和索引定义，可能极大影响性能，可扩展性，易错性，鲁棒性。使用AI反问，完善这些考量。

#### 基本要求

* **主用数据库**：MongoDB。
* **字段命名**：MongoDB 字段名使用简写（如 `nm` 表示 `name`），但保持可理解性，遵循简写对应表：@Link @TODO @Maggie/@LuoXiaowei
* **字段要求**：所有字段需定义类型、是否必填、默认值。
* **索引策略**：
  * 查询字段必须建立索引
  * 频繁排序字段优先建立复合索引
  * 索引字段顺序应与查询一致
* **字段结构建议**：

```json
{
  "_id": ObjectId,
  "nm": String,
  "m": String, # description
  "ts": ISODate, # created at timestamp
  "tags": [String],
  "active": Boolean,
  "mix": {Object|String|Int|Float}
}
```

#### 性能与扩展性

* 注意字段增长趋势，避免嵌套过深。
* 大对象建议拆分子集合或 GridFS。
* 若预估写入量大，应明确写入频率和数据保留策略。

#### 安全性与一致性

* 敏感字段必须加密或脱敏存储（如手机号、email）。
* 所有写操作应使用事务（如涉及多集合）。
* 查询操作应显式判断为空，避免误用空数组或 null。

#### Review 检查点

* 是否存在字段重复或含义不清？
* 是否遗漏索引、复合索引顺序错误？
* 是否存在数据安全隐患？