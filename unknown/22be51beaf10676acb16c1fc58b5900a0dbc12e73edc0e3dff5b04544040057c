---
description: 
globs: 
alwaysApply: true
---
### 2. 需求文档要点（供项目成员参考）

#### 基本原则

* 明确目标，不讨论技术实现。
* 用简洁语言描述业务场景，避免含糊、冗长。
* 如果需求存在多个解决方案，需明确列出优缺点供设计参考。
* 所有需求应有编号，便于引用和追踪。一般使用repo的branch_name，保持一致。
* 若对用户意图有疑问，必须主动向用户确认。
* 要与***最初提出人***确认需求。
* UI设计认为是需求文档的一部分。需要与提出人确认。

#### 格式建议（.md 文件）

```md
// Requirement ID: Branch_ID
// Author: Alex
// Created: 2025-05-18
// Reviewed by: (空，待 Review 完成填写。一般应该是***最初提出人***)
// Review Date: 2025-05-20

## 标题：用户提交订单

### 背景说明
用户可以通过平台提交商品订单。此功能需处理用户选择的商品、配送信息和支付偏好。

### 功能需求
1. 用户可以查看购物车并确认商品信息。
2. 用户需填写收货地址（默认使用账户中保存的地址）。
3. 用户选择支付方式（微信/支付宝/余额）。
4. 提交后系统生成订单编号并保存。

### 非功能性需求
- 页面响应时间 ≤ 1 秒。
- 每次提交前端需校验字段完整性。

### 可选扩展（设计时考虑）
- 自动保存用户最近一次使用的支付方式。
- 提交后跳转到订单详情页。
```

#### Review 提示

* 是否漏掉重要流程/角色？
* 是否误把技术实现写入需求？
* 文字是否足够清晰、没有歧义？
