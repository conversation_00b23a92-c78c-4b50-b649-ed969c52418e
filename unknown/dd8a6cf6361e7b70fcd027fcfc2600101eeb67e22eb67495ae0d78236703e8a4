---
description: 
globs: 
alwaysApply: true
---
### 4. API 设计要点与注意事项

#### 总体目标

* 提供清晰、稳定、安全的接口，包括外部网络 API 和系统内部模块间调用。
* 所有接口必须在文档中描述请求参数、响应结构、错误码或异常定义。
* 接口定义禁忌泛化，不应包括不必要的功能，要专注于需求的满足。
* 如果需要多次调用才能完成的功能，比如返回回调函数等情况，需要考察是否有更简单的方式实现整体功能。
* 使用AI反问，完善接口定义的功能范围，使用的便利性，安全性，和无副作用。

#### 基本规范

* **统一返回结构（适用于 REST 接口）**：

```json
{
  "ok": 1,
  "msg": "success",
  "err": null,
  "data": { ... }
}
```

* **权限校验**：每个接口需标注权限要求（登录/特定角色）。
* **字段转换**：对展示字段进行中英文转换或格式映射（如日期、金额等）。
* **安全性要求**：
  * 参数校验必须（类型、长度、格式）
  * 防止 SQL 注入、XSS、CSRF 等常见攻击

#### 外部接口设计（RESTful 建议结构）

* GET    `/api/items` 获取列表
* POST   `/api/items` 新建项
* GET    `/api/items/:id` 获取详情
* PUT    `/api/items/:id` 更新项
* DELETE `/api/items/:id` 删除项

#### 内部模块接口设计

* 可采用函数调用、事件总线、gRPC、消息队列等方式。
* 接口应明确说明：调用方、参数结构、异常处理、同步/异步行为。
* 内部接口尽量保持无副作用，便于测试和复用。

#### 文档与维护建议

* 所有接口说明统一写入 API 设计文档中，使用 Markdown 或 OpenAPI 规范。
* 每次接口变更或新增，及时更新文档并标记版本变更记录。
* 推荐维护 `api.openapi.yaml` 或 `api.md` 等统一文件。

#### Review 检查点

* 是否遗漏参数校验、权限限制？
* 响应字段是否稳定并具有扩展性？
* 是否存在数据暴露、敏感字段未脱敏等风险？
* 内部接口是否易读、明确、可被复用？