---
description: 
globs: 
alwaysApply: true
---
### 3. 架构设计要点与注意事项

#### 总体原则

* 架构设计应服务于清晰的业务目标。
* 优先考虑系统的可维护性、可部署性与可测试性。
* 在设计文档中描述系统各组件的职责边界、数据流向、部署模式等核心点。

#### 核心规范

* **服务器环境**：Host: Rocky Linux 9，/bin/bash。Containered: alpine with /bin/sh only.
* **禁止使用**：所有公共云服务，CDN等外部依赖。如有例外必须有CTO同意，并记入文档。
* **设计思路**：遵循 Make it work → Make it right → Make it fast。
* **可扩展性**：如非用户指定，不预设高复杂度分布式方案。
* **组件选择**：
  * 使用组件需检查是否活跃维护；若无更新，列出备选方案。
  * 引入外部服务模块需备注依赖和版本，明确替代或替换策略。
* **日志与安全**：
  * 日志不得记录 token、密码、PII 等敏感信息。
  * 注意服务边界及错误隔离策略，避免单点失效传染。

#### 设计文档应包含：

* 系统简图（推荐 UML 或 Mermaid）
* 各组件职责说明（例如：User Service、Auth Service、Report Engine）
* 核心通信协议（REST、gRPC、消息队列）
* 部署架构（单机、多节点、容灾、负载均衡方案）
* 初步的资源规划（每服务预估 CPU/Mem/Disk）

#### Review 检查点

* 是否有过度设计或偏离实际需求的部分？
* 是否每个模块职责清晰，不存在交叉或模糊？
* 是否考虑后续变更或新模块加入时的影响？