---
description: 
globs: 
alwaysApply: true
---
### 7. 导引文件要点与注意事项（生成代码的前置说明）

#### 总体定位

* 本文档是连接“逻辑要点”与“代码生成”的桥梁。
* 用于投喂至自动生成器（如 Cursor、CodeWhisperer、Copilot 等），指导其生成稳定、准确、可复用的结构化代码。
* 减少生成代码的逻辑漏洞、字段误解、风格偏差与重复返工。

#### 内容结构建议（.mdc 格式）

```md
// Guide for: OrderService
// Author: Fred
// Created: 2025-05-18
// Reviewed by: Maggie at 2025-05-19

## 模块目标
统一订单模块的创建、支付、取消等核心流程。确保逻辑与 schema 一致，避免状态错乱。

## 技术栈指令
- 使用 Node.js + Express
- MongoDB + Mongoose
- 统一中间件处理错误与响应格式

## API 说明
- 创建订单接口 → POST /api/orders
- 支付回调接口 → POST /api/orders/:id/pay
- 取消订单接口 → POST /api/orders/:id/cancel

## 数据约定
- 所有订单状态字段必须使用枚举
- 所有时间戳字段统一为 `ISODate` 格式
- ID 字段统一命名为 `_id`，引用字段以 `xxxId` 结尾

## 逻辑要点
- 创建订单前检查库存 → 减库存 → 创建状态日志
- 支付成功 → 更新状态 → 写入支付日志
- 取消订单必须校验当前状态为待支付

## 风格与约束
- 所有 API 返回统一格式 `{ ok, msg, err, data }`
- 严格使用 async/await，禁用回调
- 禁止硬编码常量，应引用统一常量表
- 所有写操作必须带 `createdBy/updatedBy`

## 常见风险提示（AI 生成器需注意）
- 不要跳过异常处理（try/catch）
- 不要省略字段校验逻辑（空/类型/格式）
- 禁止暴露内部调试日志到响应中
- 默认分页 limit ≤ 100，避免一次性加载全部数据
```

#### Review 检查点

* 是否包含生成器所需的全部上下文信息？
* 是否足够明确哪些流程必须实现，哪些是可选？
* 是否屏蔽了自动生成器常见的误解点与陷阱？
