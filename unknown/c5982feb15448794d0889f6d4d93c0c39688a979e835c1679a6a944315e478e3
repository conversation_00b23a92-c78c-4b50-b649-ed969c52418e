---
description: 
globs: 
alwaysApply: true
---
### 6. 逻辑要点（设计与编码之间的桥梁文档）

#### 总体目标

* 本文档用于记录系统中关键逻辑、性能优化、边界处理和通用约定。
* 为导引文件、代码实现、AI生成器提供准确的逻辑参考依据。
* 本文档为可选项，简单需求可以不要。内容各项也都是可选。
* AI反问，帮助完善细节。

#### 内容组成

##### 核心逻辑说明

* 明确功能模块中的“主流程”与“边界流程”。
* 举例说明触发条件、流程顺序、依赖状态：

```text
订单支付流程：
1. 检查订单状态是否为待支付 → 校验库存 → 冻结库存 → 发起支付 → 支付成功回调 → 更新订单状态 → 解锁库存
```

##### 性能优化点

* 数据频繁变动模块使用缓存（如 Redis），设置合理 TTL
* 减少 DB 多次查询，可使用 aggregation 或预写入缓存字段
* 对高并发路径使用异步消息队列代替同步阻塞逻辑

##### 常见错误与处理建议：只规定统一规则以外，该模块特别处理的部分

* 网络超时、依赖服务失败 → 使用重试机制（最多3次，间隔指数退避）
* 用户输入异常 → 给出用户可操作的提示，而非抛堆栈信息
* 权限不足 → 返回标准错误结构 { ok: 0, err: 'NO\_PERMISSION', msg: '权限不足' }

##### 编码风格与约定：只规定统一规则以外，该模块特别处理的部分

* 所有判断请避免使用隐式 coercion，例如 `if (value)` 应改为 `if (value !== undefined && value !== null)`
* 所有字段转换（例如前端时间字符串）请统一在服务中处理，避免业务逻辑分散


#### Review 检查点

* 是否列出了所有核心流程与异常场景？
* 是否包含性能或边界处理建议？
* 是否便于导引文档和代码生成器引用？