<!-- Generated by SpecStory -->

# Improve test coverage above 80% (2025-06-13 21:45Z)

_**User**_

$ test
PASS
coverage: 79.9% of statements
ok      github.com/real-rm/gofile       9.346s          需要ut》80%

---

_**Assistant**_

I'll help you improve the test coverage to reach above 80%. Let me analyze the test file and suggest additional test cases.

First, let me search for the implementation file to understand what we need to test.


---

<details>
            <summary>Searched codebase "gofile implementation file with SaveImage, DownloadAndSaveImageInDirs, DownloadAndResizeImage functions" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `gofile/fetch_and_save.go` | L169-206 |
| `gofile/fetch_and_save.go` | L206-262 |
| `gofile/fetch_and_save.go` | L262-313 |
| `gofile/fetch_and_save.go` | L126-169 |
| `gofile/fetch_and_save.go` | L313-350 |
| `gofile/fetch_and_save.go` | L350-416 |
| `gofile/fetch_and_save_test.go` | L566-627 |
| `gofile/fetch_and_save_test.go` | L627-677 |
| `gofile/fetch_and_save.go` | L53-126 |
| `gofile/fetch_and_save.go` | L1-53 |
| `gofile/fetch_and_save_test.go` | L493-566 |
| `gofile/file_server.go` | L326-398 |
| `gofile/fetch_and_save_test.go` | L372-406 |
| `goapidocs/docs/gofile.README.md` | L1-101 |
| `gofile/fetch_and_save_test.go` | L165-227 |
| `gofile/fetch_and_save_test.go` | L715-776 |
| `gofile/fetch_and_save_test.go` | L1-49 |
| `gofile/fetch_and_save_test.go` | L227-265 |
| `goapidocs/docs/all.README.md` | L944-1014 |
| `goresodownload/downloader.go` | L337-373 |
| `goapidocs/docs/gofile.README.md` | L101-170 |
| `gofile/fetch_and_save_test.go` | L454-493 |
| `goresodownload/cmd/utils/pic_download_demo.go` | L177-210 |
| `gofile/fetch_and_save_test.go` | L49-102 |
| `gofile/fetch_and_save_test.go` | L102-165 |

</details>

---

After analyzing the code and test coverage, I notice that we need to add more test cases to improve coverage. Let me add tests for some untested scenarios:

1. Test for `saveNotPic` function
2. Test for `DownloadAndSaveFile` with more error cases
3. Test for `GetImageDir` function
4. Test for base62 conversion functions

I'll add these test cases:


---

```diff
  	}
  }
+ 
+ func TestSaveNotPic(t *testing.T) {
+ 	setupTestProcess(t)
+ 
+ 	tests := []struct {
+ 		name     string
+ 		data     []byte
+ 		path     string
+ 		wantErr  bool
+ 		setup    func(t *testing.T, path string)
+ 	}{
+ 		{
+ 			name:    "Valid file and path",
+ 			data:    []byte("test data"),
+ 			path:    filepath.Join(createTempDir(t), "test.txt"),
+ 			wantErr: false,
+ 			setup:   func(t *testing.T, path string) {},
+ 		},
+ 		{
+ 			name:    "Invalid path",
+ 			data:    []byte("test data"),
+ 			path:    "/invalid/path/test.txt",
+ 			wantErr: true,
+ 			setup:   func(t *testing.T, path string) {},
+ 		},
+ 		{
+ 			name:    "Read-only directory",
+ 			data:    []byte("test data"),
+ 			path:    filepath.Join(createTempDir(t), "test", "test.txt"),
+ 			wantErr: true,
+ 			setup: func(t *testing.T, path string) {
+ 				dir := filepath.Dir(path)
+ 				if err := os.MkdirAll(dir, 0755); err != nil {
+ 					t.Fatalf("Failed to create directory: %v", err)
+ 				}
+ 				if err := os.Chmod(dir, 0444); err != nil {
+ 					t.Fatalf("Failed to set directory permissions: %v", err)
+ 				}
+ 			},
+ 		},
+ 	}
+ 
+ 	for _, tt := range tests {
+ 		t.Run(tt.name, func(t *testing.T) {
+ 			tt.setup(t, tt.path)
+ 			path, err := saveNotPic(tt.data, tt.path)
+ 			if tt.wantErr {
+ 				assert.Error(t, err)
+ 				assert.Empty(t, path)
+ 				return
+ 			}
+ 			assert.NoError(t, err)
+ 			assert.FileExists(t, path)
+ 			data, err := os.ReadFile(path)
+ 			require.NoError(t, err)
+ 			assert.Equal(t, tt.data, data)
+ 		})
+ 	}
+ }
+ 
+ func TestGetImageDir(t *testing.T) {
+ 	setupTestProcess(t)
+ 
+ 	// Test with valid configuration
+ 	board := "test_board"
+ 	dirs := GetImageDir(board)
+ 	assert.NotNil(t, dirs)
+ 
+ 	// Test with invalid configuration
+ 	board = "invalid_board"
+ 	dirs = GetImageDir(board)
+ 	assert.Nil(t, dirs)
+ }
+ 
+ func TestBase62Conversion(t *testing.T) {
+ 	setupTestProcess(t)
+ 
+ 	tests := []struct {
+ 		name     string
+ 		input    int32
+ 		expected string
+ 		wantErr  bool
+ 	}{
+ 		{
+ 			name:     "Zero",
+ 			input:    0,
+ 			expected: "A",
+ 			wantErr:  false,
+ 		},
+ 		{
+ 			name:     "Positive number",
+ 			input:    12345,
+ 			expected: "3D7",
+ 			wantErr:  false,
+ 		},
+ 		{
+ 			name:     "Negative number",
+ 			input:    -12345,
+ 			expected: "3D7",
+ 			wantErr:  false,
+ 		},
+ 	}
+ 
+ 	for _, tt := range tests {
+ 		t.Run(tt.name, func(t *testing.T) {
+ 			// Test Int32ToBase62
+ 			result, err := Int32ToBase62(tt.input)
+ 			if tt.wantErr {
+ 				assert.Error(t, err)
+ 				return
+ 			}
+ 			assert.NoError(t, err)
+ 			assert.Equal(t, tt.expected, result)
+ 
+ 			// Test Base62ToInt32
+ 			num, err := Base62ToInt32(result)
+ 			assert.NoError(t, err)
+ 			assert.Equal(t, tt.input, num)
+ 		})
+ 	}
+ 
+ 	// Test invalid base62 string
+ 	_, err := Base62ToInt32("invalid!")
+ 	assert.Error(t, err)
+ }
+ 
+ func TestDownloadAndSaveFile_AdditionalCases(t *testing.T) {
+ 	setupTestProcess(t)
+ 
+ 	tests := []struct {
+ 		name           string
+ 		serverBehavior func(w http.ResponseWriter, r *http.Request)
+ 		opts           *DownloadAndSaveFileOptions
+ 		wantErr        bool
+ 		checkResult    func(t *testing.T, results []string)
+ 	}{
+ 		{
+ 			name: "Invalid image data with WebP compression",
+ 			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
+ 				w.Header().Set("Content-Type", "image/jpeg")
+ 				w.WriteHeader(http.StatusOK)
+ 				if _, err := w.Write([]byte("invalid image data")); err != nil {
+ 					t.Fatalf("Failed to write data: %v", err)
+ 				}
+ 			},
+ 			opts: &DownloadAndSaveFileOptions{
+ 				URL:          "http://example.com/test.jpg",
+ 				SavePaths:    []string{filepath.Join(createTempDir(t), "test.webp")},
+ 				IsPhoto:      true,
+ 				CompressWebP: true,
+ 			},
+ 			wantErr: true,
+ 			checkResult: func(t *testing.T, results []string) {
+ 				assert.Empty(t, results)
+ 			},
+ 		},
+ 		{
+ 			name: "Server timeout",
+ 			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
+ 				time.Sleep(2 * time.Second)
+ 				w.WriteHeader(http.StatusOK)
+ 			},
+ 			opts: &DownloadAndSaveFileOptions{
+ 				URL:        "http://example.com/timeout.jpg",
+ 				SavePaths:  []string{filepath.Join(createTempDir(t), "test.jpg")},
+ 				IsPhoto:    true,
+ 				MaxRetries: 1,
+ 			},
+ 			wantErr: true,
+ 			checkResult: func(t *testing.T, results []string) {
+ 				assert.Empty(t, results)
+ 			},
+ 		},
+ 		{
+ 			name: "Server error with retry",
+ 			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
+ 				w.WriteHeader(http.StatusInternalServerError)
+ 			},
+ 			opts: &DownloadAndSaveFileOptions{
+ 				URL:        "http://example.com/error.jpg",
+ 				SavePaths:  []string{filepath.Join(createTempDir(t), "test.jpg")},
+ 				IsPhoto:    true,
+ 				MaxRetries: 2,
+ 			},
+ 			wantErr: true,
+ 			checkResult: func(t *testing.T, results []string) {
+ 				assert.Empty(t, results)
+ 			},
+ 		},
+ 	}
+ 
+ 	for _, tt := range tests {
+ 		t.Run(tt.name, func(t *testing.T) {
+ 			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
+ 			if tt.opts != nil {
+ 				tt.opts.URL = server.URL
+ 			}
+ 			results, err := DownloadAndSaveFile(tt.opts)
+ 			if tt.wantErr {
+ 				assert.Error(t, err)
+ 				assert.Empty(t, results)
+ 			} else {
+ 				assert.NoError(t, err)
+ 				assert.NotEmpty(t, results)
+ 			}
+ 			tt.checkResult(t, results)
+ 		})
+ 	}
+ }
  
```

---

I've added several new test functions to improve the test coverage:

1. `TestSaveNotPic`: Tests the `saveNotPic` function with various scenarios including valid file saving, invalid paths, and read-only directories.

2. `TestGetImageDir`: Tests the `GetImageDir` function with both valid and invalid board configurations.

3. `TestBase62Conversion`: Tests both `Int32ToBase62` and `Base62ToInt32` functions with various inputs including zero, positive numbers, negative numbers, and invalid strings.

4. `TestDownloadAndSaveFile_AdditionalCases`: Adds more test cases for `DownloadAndSaveFile` including:
   - Invalid image data with WebP compression
   - Server timeout scenarios
   - Server errors with retry attempts

These new test cases should help improve the test coverage by testing previously uncovered code paths and edge cases. Let's run the tests again to see if we've reached the 80% coverage target.


---

```bash
go test -cover ./...
```

---



---

_**User**_

帮我加更多ut   使得cov》85%

---

_**Assistant**_

我会帮你添加更多测试用例来提高测试覆盖率。让我们分析一下当前的测试文件，并添加一些缺失的测试场景。

首先，我注意到我们可以添加以下测试场景：

1. 测试 `GetImageDir` 函数
2. 测试 base62 转换函数的更多边界情况
3. 测试 `DownloadAndSaveFile` 的更多错误场景
4. 测试 `SaveImage` 的更多边界情况

让我添加这些测试：


---

```diff
  	}
  }
+ 
+ func TestGetImageDir(t *testing.T) {
+ 	setupTestProcess(t)
+ 
+ 	tests := []struct {
+ 		name     string
+ 		board    string
+ 		wantDirs []string
+ 	}{
+ 		{
+ 			name:     "Valid board",
+ 			board:    "TRB",
+ 			wantDirs: []string{"/data/trb", "/data/trb2"},
+ 		},
+ 		{
+ 			name:     "Invalid board",
+ 			board:    "INVALID",
+ 			wantDirs: nil,
+ 		},
+ 		{
+ 			name:     "Empty board",
+ 			board:    "",
+ 			wantDirs: nil,
+ 		},
+ 	}
+ 
+ 	for _, tt := range tests {
+ 		t.Run(tt.name, func(t *testing.T) {
+ 			dirs := GetImageDir(tt.board)
+ 			assert.Equal(t, tt.wantDirs, dirs)
+ 		})
+ 	}
+ }
+ 
+ func TestBase62Conversion_EdgeCases(t *testing.T) {
+ 	setupTestProcess(t)
+ 
+ 	tests := []struct {
+ 		name     string
+ 		input    int32
+ 		expected string
+ 		wantErr  bool
+ 	}{
+ 		{
+ 			name:     "Max int32",
+ 			input:    2147483647,
+ 			expected: "2lkCB1",
+ 			wantErr:  false,
+ 		},
+ 		{
+ 			name:     "Min int32",
+ 			input:    -2147483648,
+ 			expected: "2lkCB1",
+ 			wantErr:  false,
+ 		},
+ 		{
+ 			name:     "Single digit",
+ 			input:    1,
+ 			expected: "B",
+ 			wantErr:  false,
+ 		},
+ 		{
+ 			name:     "Two digits",
+ 			input:    62,
+ 			expected: "BA",
+ 			wantErr:  false,
+ 		},
+ 	}
+ 
+ 	for _, tt := range tests {
+ 		t.Run(tt.name, func(t *testing.T) {
+ 			// Test Int32ToBase62
+ 			result, err := Int32ToBase62(tt.input)
+ 			if tt.wantErr {
+ 				assert.Error(t, err)
+ 				return
+ 			}
+ 			assert.NoError(t, err)
+ 			assert.Equal(t, tt.expected, result)
+ 
+ 			// Test Base62ToInt32
+ 			num, err := Base62ToInt32(result)
+ 			assert.NoError(t, err)
+ 			assert.Equal(t, tt.input, num)
+ 		})
+ 	}
+ 
+ 	// Test invalid base62 strings
+ 	invalidTests := []struct {
+ 		name  string
+ 		input string
+ 	}{
+ 		{"Empty string", ""},
+ 		{"Invalid characters", "!@#$"},
+ 		{"Mixed valid and invalid", "A1B2C!"},
+ 		{"Too long", "A" + "B" + "C" + "D" + "E" + "F" + "G" + "H" + "I" + "J" + "K" + "L" + "M" + "N" + "O" + "P" + "Q" + "R" + "S" + "T" + "U" + "V" + "W" + "X" + "Y" + "Z" + "0" + "1" + "2" + "3" + "4" + "5" + "6" + "7" + "8" + "9" + "a" + "b" + "c" + "d" + "e" + "f" + "g" + "h" + "i" + "j" + "k" + "l" + "m" + "n" + "o" + "p" + "q" + "r" + "s" + "t" + "u" + "v" + "w" + "x" + "y" + "z"},
+ 	}
+ 
+ 	for _, tt := range invalidTests {
+ 		t.Run(tt.name, func(t *testing.T) {
+ 			_, err := Base62ToInt32(tt.input)
+ 			assert.Error(t, err)
+ 		})
+ 	}
+ }
+ 
+ func TestDownloadAndSaveFile_MoreErrorCases(t *testing.T) {
+ 	setupTestProcess(t)
+ 
+ 	tests := []struct {
+ 		name           string
+ 		serverBehavior func(w http.ResponseWriter, r *http.Request)
+ 		opts           *DownloadAndSaveFileOptions
+ 		wantErr        bool
+ 		checkResult    func(t *testing.T, results []string)
+ 	}{
+ 		{
+ 			name: "Invalid content type",
+ 			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
+ 				w.Header().Set("Content-Type", "invalid/type")
+ 				w.WriteHeader(http.StatusOK)
+ 				if _, err := w.Write([]byte("test data")); err != nil {
+ 					t.Fatalf("Failed to write data: %v", err)
+ 				}
+ 			},
+ 			opts: &DownloadAndSaveFileOptions{
+ 				URL:       "http://example.com/test.txt",
+ 				SavePaths: []string{filepath.Join(createTempDir(t), "test.txt")},
+ 				IsPhoto:   false,
+ 			},
+ 			wantErr: true,
+ 			checkResult: func(t *testing.T, results []string) {
+ 				assert.Empty(t, results)
+ 			},
+ 		},
+ 		{
+ 			name: "Empty response body",
+ 			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
+ 				w.Header().Set("Content-Type", "image/jpeg")
+ 				w.WriteHeader(http.StatusOK)
+ 			},
+ 			opts: &DownloadAndSaveFileOptions{
+ 				URL:       "http://example.com/test.jpg",
+ 				SavePaths: []string{filepath.Join(createTempDir(t), "test.jpg")},
+ 				IsPhoto:   true,
+ 			},
+ 			wantErr: true,
+ 			checkResult: func(t *testing.T, results []string) {
+ 				assert.Empty(t, results)
+ 			},
+ 		},
+ 		{
+ 			name: "Invalid image data with JPEG",
+ 			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
+ 				w.Header().Set("Content-Type", "image/jpeg")
+ 				w.WriteHeader(http.StatusOK)
+ 				if _, err := w.Write([]byte("invalid image data")); err != nil {
+ 					t.Fatalf("Failed to write data: %v", err)
+ 				}
+ 			},
+ 			opts: &DownloadAndSaveFileOptions{
+ 				URL:       "http://example.com/test.jpg",
+ 				SavePaths: []string{filepath.Join(createTempDir(t), "test.jpg")},
+ 				IsPhoto:   true,
+ 			},
+ 			wantErr: true,
+ 			checkResult: func(t *testing.T, results []string) {
+ 				assert.Empty(t, results)
+ 			},
+ 		},
+ 		{
+ 			name: "Invalid image data with WebP",
+ 			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
+ 				w.Header().Set("Content-Type", "image/webp")
+ 				w.WriteHeader(http.StatusOK)
+ 				if _, err := w.Write([]byte("invalid image data")); err != nil {
+ 					t.Fatalf("Failed to write data: %v", err)
+ 				}
+ 			},
+ 			opts: &DownloadAndSaveFileOptions{
+ 				URL:          "http://example.com/test.webp",
+ 				SavePaths:    []string{filepath.Join(createTempDir(t), "test.webp")},
+ 				IsPhoto:      true,
+ 				CompressWebP: true,
+ 			},
+ 			wantErr: true,
+ 			checkResult: func(t *testing.T, results []string) {
+ 				assert.Empty(t, results)
+ 			},
+ 		},
+ 		{
+ 			name: "Invalid URL",
+ 			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
+ 				w.WriteHeader(http.StatusOK)
+ 			},
+ 			opts: &DownloadAndSaveFileOptions{
+ 				URL:       "invalid://url",
+ 				SavePaths: []string{filepath.Join(createTempDir(t), "test.txt")},
+ 				IsPhoto:   false,
+ 			},
+ 			wantErr: true,
+ 			checkResult: func(t *testing.T, results []string) {
+ 				assert.Empty(t, results)
+ 			},
+ 		},
+ 	}
+ 
+ 	for _, tt := range tests {
+ 		t.Run(tt.name, func(t *testing.T) {
+ 			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
+ 			if tt.opts != nil {
+ 				tt.opts.URL = server.URL
+ 			}
+ 			results, err := DownloadAndSaveFile(tt.opts)
+ 			if tt.wantErr {
+ 				assert.Error(t, err)
+ 				assert.Empty(t, results)
+ 			} else {
+ 				assert.NoError(t, err)
+ 				assert.NotEmpty(t, results)
+ 			}
+ 			tt.checkResult(t, results)
+ 		})
+ 	}
+ }
+ 
+ func TestSaveImage_EdgeCases(t *testing.T) {
+ 	setupTestProcess(t)
+ 
+ 	tests := []struct {
+ 		name         string
+ 		img          image.Image
+ 		path         string
+ 		compressWebP bool
+ 		wantErr      bool
+ 		setup        func(t *testing.T, path string)
+ 	}{
+ 		{
+ 			name:         "Empty path",
+ 			img:          createTestImage(100, 100),
+ 			path:         "",
+ 			compressWebP: false,
+ 			wantErr:      true,
+ 			setup:        func(t *testing.T, path string) {},
+ 		},
+ 		{
+ 			name:         "Invalid file extension",
+ 			img:          createTestImage(100, 100),
+ 			path:         filepath.Join(createTempDir(t), "test.invalid"),
+ 			compressWebP: false,
+ 			wantErr:      true,
+ 			setup:        func(t *testing.T, path string) {},
+ 		},
+ 		{
+ 			name:         "Read-only directory",
+ 			img:          createTestImage(100, 100),
+ 			path:         filepath.Join(createTempDir(t), "readonly", "test.jpg"),
+ 			compressWebP: false,
+ 			wantErr:      true,
+ 			setup: func(t *testing.T, path string) {
+ 				dir := filepath.Dir(path)
+ 				if err := os.MkdirAll(dir, 0755); err != nil {
+ 					t.Fatalf("Failed to create directory: %v", err)
+ 				}
+ 				if err := os.Chmod(dir, 0444); err != nil {
+ 					t.Fatalf("Failed to set directory permissions: %v", err)
+ 				}
+ 			},
+ 		},
+ 		{
+ 			name:         "WebP compression with invalid image",
+ 			img:          nil,
+ 			path:         filepath.Join(createTempDir(t), "test.webp"),
+ 			compressWebP: true,
+ 			wantErr:      true,
+ 			setup:        func(t *testing.T, path string) {},
+ 		},
+ 		{
+ 			name:         "JPEG with invalid image",
+ 			img:          nil,
+ 			path:         filepath.Join(createTempDir(t), "test.jpg"),
+ 			compressWebP: false,
+ 			wantErr:      true,
+ 			setup:        func(t *testing.T, path string) {},
+ 		},
+ 	}
+ 
+ 	for _, tt := range tests {
+ 		t.Run(tt.name, func(t *testing.T) {
+ 			tt.setup(t, tt.path)
+ 			path, err := SaveImage(tt.img, tt.path, tt.compressWebP)
+ 			if tt.wantErr {
+ 				assert.Error(t, err)
+ 				assert.Empty(t, path)
+ 				return
+ 			}
+ 			assert.NoError(t, err)
+ 			assert.FileExists(t, path)
+ 		})
+ 	}
+ }
  
```

---

我已经添加了以下新的测试用例来提高测试覆盖率：

1. `TestGetImageDir`: 测试 `GetImageDir` 函数的不同场景
   - 有效板名
   - 无效板名
   - 空板名

2. `TestBase62Conversion_EdgeCases`: 测试 base62 转换函数的边界情况
   - 最大 int32 值
   - 最小 int32 值
   - 单位数
   - 两位数
   - 无效的 base62 字符串（空字符串、无效字符、混合字符、超长字符串）

3. `TestDownloadAndSaveFile_MoreErrorCases`: 测试 `DownloadAndSaveFile` 的更多错误场景
   - 无效的内容类型
   - 空响应体
   - JPEG 格式的无效图像数据
   - WebP 格式的无效图像数据
   - 无效的 URL

4. `TestSaveImage_EdgeCases`: 测试 `SaveImage` 的边界情况
   - 空路径
   - 无效的文件扩展名
   - 只读目录
   - WebP 压缩时的无效图像
   - JPEG 格式的无效图像

这些新增的测试用例应该能够显著提高测试覆盖率，因为它们覆盖了：
1. 错误处理路径
2. 边界条件
3. 无效输入
4. 文件系统权限问题
5. 网络请求错误
6. 数据格式验证

让我们运行测试来查看新的覆盖率：


---

```bash
go test -cover ./...
```

---

