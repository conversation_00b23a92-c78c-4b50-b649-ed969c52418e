<!-- Generated by SpecStory -->

# Fix deprecated DecodeBytes usage (2025-06-11 21:49Z)

_**User**_

For the code present, we get this error:
```
watchColl.FindOne(context.Background(), bson.M{"_id": "CAR40401964"}).DecodeBytes is deprecated: Use [SingleResult.Raw] instead.
```
How can I resolve this? If you propose a fix, please make it concise.

---

_**Assistant**_

I'll help you fix the deprecated `DecodeBytes()` call. The recommended approach is to use `Raw()` instead.

Let me edit the file to make this change:


---

```diff
  		fmt.Println("TestE2ECAR Delete")
  		watchColl := gomongo.Coll("rni", goresodownload.BoardWatchTable[gBoardType])
- 		rawOne, err := watchColl.FindOne(context.Background(), bson.M{"_id": "CAR40401964"}).DecodeBytes()
+ 		rawOne, err := watchColl.FindOne(context.Background(), bson.M{"_id": "CAR40401964"}).Raw()
  		fmt.Println("rawOne", rawOne)
  		require.NoError(t, err)
```

---

I've replaced `DecodeBytes()` with `Raw()`, which is the current recommended method in the MongoDB Go driver. This change maintains the same functionality while using the non-deprecated API. The `Raw()` method returns the raw BSON document in the same way as `DecodeBytes()` did.

---

