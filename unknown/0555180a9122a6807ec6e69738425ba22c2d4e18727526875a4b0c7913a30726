---
description: 
globs: 
alwaysApply: true
---
### 1. 文档要求总纲（适用于所有设计与开发文档）

#### 总体原则

* 文档和代码同等重要，甚至优先于代码。
* 所有类型文档应简洁、清晰，避免过度设计，强调可操作性。
* 鼓励用AI工具辅助生成初稿、对比方案和发现遗漏点。特别是使用AI反问，完善文档补充漏洞。

#### 流程顺序

```
需求文档* 
→ 架构设计* 
→ API 设计* 
→ 数据 Schema 设计* 
→ 逻辑要点* 
→ 导引文档 
→ 代码生成 ↔ UT 测试 
→ 整体测试 
→ 提交 Review*
```

* 带 `*` 的文档必须 Review，并在文件顶部记录：
  `// Reviewed by <姓名> at <YYYY-MM-DD>` 或者根据模版
* Review 记录置于文档顶部，类似代码文件注释。
* 每个阶段的设计产出都可能引起前一步的回退修改。
* 每一步建议由 AI 协助草拟或审查，再由人 Review。
* 视需求复杂度决定是否完整执行，但多数需求仍建议按此流程走一遍，哪怕内容简单。
* UI设计认为是需求文档的一部分。需要与提出人确认。

#### Pair 与个人开发

* Pair 开发：关键阶段需双人协作并 Review。
* 个人开发：每阶段产出后需提交他人 Review。
* Review 优先级高于编码。
* Review 人如不在同一时区，可继续开发，但在生成导引文档之前必须完成 Review。