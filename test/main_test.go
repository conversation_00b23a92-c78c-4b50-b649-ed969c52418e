package main

import (
	"context"
	"testing"
	"time"

	gobase "github.com/real-rm/gobase"
	gofile "github.com/real-rm/gofile"
	gomongo "github.com/real-rm/gomongo"
	goresodownload "github.com/real-rm/goresodownload"
	"go.mongodb.org/mongo-driver/bson"
)

func TestProcessInsert(t *testing.T) {
	// Initialize test environment
	if err := gobase.InitBase(); err != nil {
		t.Fatalf("Failed to initialize base: %v", err)
	}
	if err := gomongo.InitMongoDB(); err != nil {
		t.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Set test board type
	gBoardType = "CAR"

	// Create test components
	if err := createComponents(); err != nil {
		t.Fatalf("Failed to create components: %v", err)
	}

	// Create test data
	testSid := "test123"
	testId := "test456"
	testTs := time.Now()

	// Create test media data
	testMedia := []bson.M{
		{
			"ClassName":         "Property",
			"MediaCategory":     "Photo",
			"MediaKey":          "test-key-1",
			"MediaObjectID":     "test-obj-1",
			"MediaURL":          "https://example.com/test1.jpg",
			"MimeType":          "image/jpeg",
			"Order":             1,
			"ResourceName":      "Property",
			"ResourceRecordKey": "test123",
		},
		{
			"ClassName":         "Property",
			"MediaCategory":     "Photo",
			"MediaKey":          "test-key-2",
			"MediaObjectID":     "test-obj-2",
			"MediaURL":          "https://example.com/test2.jpg",
			"MimeType":          "image/jpeg",
			"Order":             2,
			"ResourceName":      "Property",
			"ResourceRecordKey": "test123",
		},
	}

	// Create test change document
	changeDoc := bson.M{
		"fullDocument": bson.M{
			"_id":       testId,
			"ListingId": testSid,
			"Media":     testMedia,
			"ts":        testTs,
		},
	}

	// Create test property document
	prop := bson.M{
		"_id": testId,
	}

	// Insert test data into logs table
	logsCol := gomongo.Coll("rni", goresodownload.BoardLogsTable[gBoardType])
	oldLog := bson.M{
		"sid": testSid,
		"_mt": time.Now(),
		"data": bson.M{
			"media": []bson.M{
				{
					"MediaKey": "test-key-1",
					"MediaURL": "https://example.com/old1.jpg",
				},
			},
		},
	}
	_, err := logsCol.InsertOne(context.Background(), oldLog)
	if err != nil {
		t.Fatalf("Failed to insert test log: %v", err)
	}

	// Test ProcessInsert
	err = ProcessInsert(changeDoc, prop)
	if err != nil {
		t.Fatalf("ProcessInsert failed: %v", err)
	}

	// Verify results
	// 1. Check if files were downloaded
	filePath, err := gofile.GetFullFilePath(testTs, gBoardType, testSid)
	if err != nil {
		t.Fatalf("Failed to get file path: %v", err)
	}

	// 2. Check if merged table was updated
	mergedCol := gomongo.Coll("rni", goresodownload.BoardMergedTable[gBoardType])
	var mergedDoc bson.M
	err = mergedCol.FindOne(context.Background(), bson.M{"_id": testId}).Decode(&mergedDoc)
	if err != nil {
		t.Fatalf("Failed to find merged document: %v", err)
	}

	// Verify phoLH and docLH fields
	phoLH, ok := mergedDoc["phoLH"].([]int)
	if !ok {
		t.Error("phoLH field not found or invalid type")
	}
	if len(phoLH) != 2 {
		t.Errorf("Expected 2 photos in phoLH, got %d", len(phoLH))
	}

	// Clean up test data
	logsCol.DeleteOne(context.Background(), bson.M{"sid": testSid})
	mergedCol.DeleteOne(context.Background(), bson.M{"_id": testId})
}

func TestProcessInsertWithNoMedia(t *testing.T) {
	// Initialize test environment
	if err := gobase.InitBase(); err != nil {
		t.Fatalf("Failed to initialize base: %v", err)
	}
	if err := gomongo.InitMongoDB(); err != nil {
		t.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Set test board type
	gBoardType = "CAR"

	// Create test components
	if err := createComponents(); err != nil {
		t.Fatalf("Failed to create components: %v", err)
	}

	// Create test data with no media
	changeDoc := bson.M{
		"fullDocument": bson.M{
			"_id":       "test-no-media",
			"ListingId": "test-no-media-sid",
			"ts":        time.Now(),
		},
	}

	prop := bson.M{
		"_id": "test-no-media",
	}

	// Test ProcessInsert with no media
	err := ProcessInsert(changeDoc, prop)
	if err != nil {
		t.Fatalf("ProcessInsert failed with no media: %v", err)
	}
}

func TestProcessInsertWithInvalidData(t *testing.T) {
	// Initialize test environment
	if err := gobase.InitBase(); err != nil {
		t.Fatalf("Failed to initialize base: %v", err)
	}
	if err := gomongo.InitMongoDB(); err != nil {
		t.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Set test board type
	gBoardType = "CAR"

	// Create test components
	if err := createComponents(); err != nil {
		t.Fatalf("Failed to create components: %v", err)
	}

	// Test cases with invalid data
	testCases := []struct {
		name      string
		changeDoc bson.M
		prop      bson.M
		wantErr   bool
	}{
		{
			name: "Missing ListingId",
			changeDoc: bson.M{
				"fullDocument": bson.M{
					"_id": "test-invalid-1",
					"ts":  time.Now(),
				},
			},
			prop: bson.M{
				"_id": "test-invalid-1",
			},
			wantErr: true,
		},
		{
			name: "Missing _id",
			changeDoc: bson.M{
				"fullDocument": bson.M{
					"ListingId": "test-invalid-2",
					"ts":        time.Now(),
				},
			},
			prop: bson.M{
				"_id": "test-invalid-2",
			},
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := ProcessInsert(tc.changeDoc, tc.prop)
			if (err != nil) != tc.wantErr {
				t.Errorf("ProcessInsert() error = %v, wantErr %v", err, tc.wantErr)
			}
		})
	}
}
