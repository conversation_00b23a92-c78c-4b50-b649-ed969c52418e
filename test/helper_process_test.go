package gohelper

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

// setupTest sets up the test environment
func setupTestProcess(t *testing.T) (*gomongo.MongoCollection, func(*gomongo.MongoCollection)) {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	SetRmbaseFileCfg(configPath)
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}
	// Initialize test environment
	if err := SetupTestEnv(TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}
	coll := gomongo.Coll("tmp", "processStatus")

	// Return collection and cleanup function
	return coll, func(coll *gomongo.MongoCollection) {
		if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
			t.Errorf("Failed to cleanup collection: %v", err)
		}
	}
}

func TestProcessMonitor(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	// Create a new process monitor with 30 second update interval
	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		ID:               "test-id",
		FileName:         "test-file",
		UpdateInterval:   time.Second * 2,
		Multiplier:       2.0,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Test multiplier setting
	if monitor.multiplier != 2.0 {
		t.Errorf("Expected multiplier 2.0, got %v", monitor.multiplier)
	}

	// Test operation recording
	operation := "test-operation"
	monitor.RecordOperation(operation)
	time.Sleep(2 * time.Second)
	monitor.RecordOperation(operation)
	time.Sleep(time.Second)
	monitor.RecordOperation(operation)

	// Check if max interval is calculated correctly
	monitor.mu.Lock()
	times := monitor.operationTimes[operation]
	if len(times) != 3 {
		t.Errorf("Expected 3 recorded times, got %d", len(times))
	}

	maxInterval := monitor.maxIntervals[operation]
	if maxInterval == 0 {
		t.Error("Expected non-zero max interval")
	}
	monitor.mu.Unlock()

	// Test operation checking with multiplier
	time.Sleep(3 * time.Second)
	monitor.checkOperations()

	result := coll.FindOne(context.Background(), bson.M{"_id": "test-id_" + os.Getenv("HOSTNAME")})
	if result == nil {
		t.Error("Failed to find process status document")
		return
	}
	var doc map[string]interface{}
	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["status"] != RunningNormal {
		t.Errorf("Expected status %s, got %s", RunningNormal, doc["status"])
	}

	// Wait for monitorLoop to run
	// 5 seconds will cause the status to be RunningError
	time.Sleep(5 * time.Second)

	// Verify that the status was updated to RunningError
	result = coll.FindOne(context.Background(), bson.M{"_id": "test-id_" + os.Getenv("HOSTNAME")})
	if result == nil {
		t.Error("Failed to find process status document")
		return
	}

	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["status"] != RunningWithWarning {
		t.Errorf("Expected status %s, got %s", RunningWithWarning, doc["status"])
	}

	// Test stopping the monitor
	monitor.StopMonitoring()
}

func TestProcessMonitorMultiplier(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		ID:               "test-id",
		FileName:         "test-file",
		UpdateInterval:   time.Second,
		Multiplier:       1.0,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Test different multiplier values
	testCases := []struct {
		multiplier float64
		expected   float64
	}{
		{1.0, 1.0},
		{2.0, 2.0},
		{0.5, 0.5},
		{1.5, 1.5},
	}

	for _, tc := range testCases {
		monitor.SetMultiplier(tc.multiplier)
		if monitor.multiplier != tc.expected {
			t.Errorf("Expected multiplier %v, got %v", tc.expected, monitor.multiplier)
		}
	}
}

func TestProcessMonitorOperationTracking(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		ID:               "test-id",
		FileName:         "test-file",
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Record multiple operations
	operation := "test-operation"
	for i := 0; i < 15; i++ { // More than 10 to test the limit
		monitor.RecordOperation(operation)
		time.Sleep(10 * time.Millisecond)
	}

	monitor.mu.Lock()
	times := monitor.operationTimes[operation]
	if len(times) != 10 {
		t.Errorf("Expected 10 recorded times (max limit), got %d", len(times))
	}
	monitor.mu.Unlock()
}

func TestProcessMonitorErrorStatus(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		ID:               "test-id",
		FileName:         "test-file",
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Record an operation
	operation := "test-operation"
	monitor.RecordOperation(operation)

	// Wait longer than the tolerance
	time.Sleep(2 * time.Second)

	// Check operations - should set RunningError status
	monitor.checkOperations()

	// Verify that the status was updated to RunningError
	result := gomongo.Coll("tmp", "processStatus").FindOne(context.Background(), bson.M{"_id": "test-id_" + os.Getenv("HOSTNAME")})
	if result == nil {
		t.Error("Failed to find process status document")
		return
	}

	var doc map[string]interface{}
	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["status"] != RunningWithWarning {
		t.Errorf("Expected status %s, got %s", RunningWithWarning, doc["status"])
	}

	// Verify that an error message was set
	if doc["notify"] == nil {
		t.Error("Expected error message to be set")
	}
}

func TestProcessMonitorExitHandlers(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		ID:               "test-id",
		FileName:         "test-file",
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Test basic exit handler
	exitHandler := monitor.getHandleExitFnBasic(func(errorMsg string) {
		if errorMsg != "test error" {
			t.Errorf("Expected error message 'test error', got '%s'", errorMsg)
		}
	})
	exitHandler(fmt.Errorf("test error"))

	// Test exit handler with system data
	sysDataHandler := monitor.getHandleExitFnWithUpdateSysdata(func(data UpdateFields) error {
		if data.Status != "complete" {
			t.Errorf("Expected status 'complete', got '%v'", data.Status)
		}
		return nil
	})

	sysDataHandler(map[string]interface{}{
		"error":          "test error",
		"token":          "test-token",
		"tokenClusterTs": time.Now(),
		"tokenChangeTs":  time.Now(),
		"exitFn":         func(errorMsg string) {},
	})
}

func TestProcessMonitorStatusUpdate(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		ID:               "test-id",
		FileName:         "test-file",
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Test status update with all parameters
	now := time.Now()
	errorMsg := "test error"
	stats := map[string]interface{}{
		"processed": 100,
		"errors":    0,
	}

	opts := UpdateProcessStatusOptions{
		Status:   RunningNormal,
		StartTs:  &now,
		ErrorMsg: &errorMsg,
		Stats:    stats,
	}
	err = monitor.UpdateProcessStatus(opts)
	if err != nil {
		t.Errorf("Error updating status: %v", err)
	}

	// Test status update with minimal parameters
	opts = UpdateProcessStatusOptions{
		Status: RunningNormal,
	}
	err = monitor.UpdateProcessStatus(opts)
	if err != nil {
		t.Errorf("Error updating status: %v", err)
	}
}

func TestProcessMonitorUpdateStatusWhenAllFinish(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		ID:               "test-id",
		FileName:         "test-file",
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Test with error message
	errorMsg := "test error"
	monitor.updateStatusWhenAllFinish(errorMsg, func(msg string) {
		if msg != errorMsg {
			t.Errorf("Expected error message '%s', got '%s'", errorMsg, msg)
		}
	})

	// Verify status in MongoDB
	result := coll.FindOne(context.Background(), bson.M{"_id": "test-id_" + os.Getenv("HOSTNAME")})
	if result == nil {
		t.Error("Failed to find process status document")
		return
	}

	var doc map[string]interface{}
	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["status"] != FinishWithError {
		t.Errorf("Expected status %s, got %s", FinishWithError, doc["status"])
	}

	// Test without error message
	monitor.updateStatusWhenAllFinish("", func(msg string) {
		if msg != "" {
			t.Errorf("Expected empty error message, got '%s'", msg)
		}
	})

	// Verify status in MongoDB
	result = coll.FindOne(context.Background(), bson.M{"_id": "test-id_" + os.Getenv("HOSTNAME")})
	if result == nil {
		t.Error("Failed to find process status document")
		return
	}

	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["status"] != FinishWithoutError {
		t.Errorf("Expected status %s, got %s", FinishWithoutError, doc["status"])
	}
}

func TestProcessMonitorGetHandleExitFn(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		ID:               "test-id",
		FileName:         "test-file",
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Test with updateSysData
	updateSysData := func(data UpdateFields) error {
		if data.Status != "complete" {
			t.Errorf("Expected status 'complete', got '%v'", data.Status)
		}
		return nil
	}

	exitHandler := monitor.GetHandleExitFn(func(errorMsg string) {
		if errorMsg != "test error" {
			t.Errorf("Expected error message 'test error', got '%s'", errorMsg)
		}
	}, updateSysData)

	// Test with string error
	if fn, ok := exitHandler.(func(map[string]interface{})); ok {
		fn(map[string]interface{}{
			"error": "test error",
		})
	} else {
		t.Error("Unexpected exit handler type")
	}

	// Test with map error
	if fn, ok := exitHandler.(func(map[string]interface{})); ok {
		fn(map[string]interface{}{
			"error": "test error",
		})
	} else {
		t.Error("Unexpected exit handler type")
	}

	// Test without updateSysData
	exitHandler = monitor.GetHandleExitFn(func(errorMsg string) {
		if errorMsg != "test error" {
			t.Errorf("Expected error message 'test error', got '%s'", errorMsg)
		}
	}, nil)

	if fn, ok := exitHandler.(func(interface{})); ok {
		fn("test error")
	} else {
		t.Error("Unexpected exit handler type")
	}
}

func TestProcessMonitorGetCheckRunningProcessFn(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		ID:               "test-id",
		FileName:         "test-file",
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// First update status to running
	opts := UpdateProcessStatusOptions{
		Status: RunningNormal,
	}
	err = monitor.UpdateProcessStatus(opts)
	if err != nil {
		t.Errorf("Error updating status: %v", err)
	}

	// Wait for MongoDB update to complete
	time.Sleep(100 * time.Millisecond)

	// Get check function
	isRunning, err := monitor.CheckRunningProcess()
	if err != nil {
		t.Errorf("Error checking running process: %v", err)
	}
	if !isRunning {
		t.Error("Expected process to be running")
	}

	// Update status to finished
	opts = UpdateProcessStatusOptions{
		Status: FinishWithoutError,
	}
	err = monitor.UpdateProcessStatus(opts)
	if err != nil {
		t.Errorf("Error updating status: %v", err)
	}

	// Wait for MongoDB update to complete
	time.Sleep(100 * time.Millisecond)

	// Test when process is not running
	isRunning, err = monitor.CheckRunningProcess()
	if err != nil {
		t.Errorf("Error checking running process: %v", err)
	}
	if isRunning {
		t.Error("Expected process to not be running")
	}
}

func TestProcessMonitorGetHandleExitFnWithUpdateSysdata(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		ID:               "test-id",
		FileName:         "test-file",
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Test with watched object
	updateSysData := func(data UpdateFields) error {
		if data.Status != "complete" {
			t.Errorf("Expected status 'complete', got '%v'", data.Status)
		}
		if data.Token == nil {
			t.Errorf("Expected token, got nil")
		}
		return nil
	}

	handler := monitor.getHandleExitFnWithUpdateSysdata(updateSysData)

	// Test with processing counter > 0
	handler(map[string]interface{}{
		"watchedObject": map[string]interface{}{
			"processingCounter": 1,
			"currentToken":      "test-token",
			"ns":                "test.ns",
		},
		"exitFn": func(errorMsg string) {},
	})

	// Test with processing counter = 0
	handler(map[string]interface{}{
		"watchedObject": map[string]interface{}{
			"processingCounter": 0,
			"currentToken":      "test-token",
			"ns":                "test.ns",
		},
		"exitFn": func(errorMsg string) {},
	})

	// Test without watched object
	handler(map[string]interface{}{
		"error":  "test error",
		"exitFn": func(errorMsg string) {},
	})
}

func TestProcessMonitorGetHandleExitFnBasic(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		ID:               "test-id",
		FileName:         "test-file",
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Test with string error
	exitHandler := monitor.getHandleExitFnBasic(func(errorMsg string) {
		if errorMsg != "test error" {
			t.Errorf("Expected error message 'test error', got '%s'", errorMsg)
		}
	})
	exitHandler("test error")

	// Test with map error
	exitHandler = monitor.getHandleExitFnBasic(func(errorMsg string) {
		if errorMsg != "test error" {
			t.Errorf("Expected error message 'test error', got '%s'", errorMsg)
		}
	})
	exitHandler(map[string]interface{}{
		"error": "test error",
	})
}
