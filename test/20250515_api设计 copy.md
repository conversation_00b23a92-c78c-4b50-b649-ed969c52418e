# 图片存储系统 API 设计文档

## 目录
1. [概述](#概述)
2. [系统架构](#系统架构)
3. [API 设计](#api-设计)
4. [数据结构](#数据结构)
5. [错误处理](#错误处理)
6. [性能考虑](#性能考虑)

## 概述

本文档描述了图片存储系统的 API 设计，该系统主要负责处理媒体文件的下载、存储和管理。系统通过监听数据库变更，自动处理媒体文件的更新，并维护文件的一致性。

### 主要功能
- 媒体字段变更监听
- 文件下载和存储
- 路径管理和缓存
- 状态同步和错误处理
- 统计信息收集

## 系统架构

系统分为五个主要模块：
1. ChangeStream Watcher
2. Download System
3. State Management
4. Database Layer
5. Error Handling

## API 设计

### 1. ChangeStream Watcher API

#### 1.1 ChangeStreamWatcher 接口
```go
type ChangeStreamWatcher interface {
    // StartWatching 开始监听指定表的变更
    StartWatching(ctx context.Context, board string, collection string) error
    
    // StopWatching 停止监听
    StopWatching() error
    
    // GetWatchStatus 获取监听状态
    GetWatchStatus() WatchStatus
}
```

#### 1.2 MediaMonitor 接口
```go
type MediaMonitor interface {
    // HandleMediaChange 处理Media字段变更
    HandleMediaChange(ctx context.Context, change *MediaChange) error
    
    // GetMediaDiff 获取新旧Media差异
    GetMediaDiff(oldMedia, newMedia []Media) (*MediaDiff, error)
}
```

#### 1.3 TREBHandler 接口
```go
type TREBHandler interface {
    // ProcessTREBMedia 处理TREB特有的媒体处理逻辑
    ProcessTREBMedia(ctx context.Context, mediaData *TREBMediaData) error
    
    // GetMediaByPriority 按优先级获取媒体
    GetMediaByPriority(mediaList []Media) ([]Media, error)
}
```

### 2. Download System API

#### 2.1 DownloadManager 接口
```go
type DownloadManager interface {
    // DownloadMedia 下载媒体文件
    DownloadMedia(ctx context.Context, task *MediaTask) error
    
    // BatchDownload 批量下载
    BatchDownload(ctx context.Context, tasks []MediaTask) error
    
    // GetDownloadStatus 获取下载状态
    GetDownloadStatus(taskID string) (*DownloadStatus, error)
}
```

#### 2.2 FileStorageManager 接口
```go
type FileStorageManager interface {
    // StoreFile 存储文件
    StoreFile(ctx context.Context, file *FileData) error
    
    // GetFileInfo 获取文件信息
    GetFileInfo(filePath string) (*FileInfo, error)
    
    // DeleteFile 删除文件
    DeleteFile(ctx context.Context, filePath string) error
}
```

#### 2.3 HashValidator 接口
```go
type HashValidator interface {
    // ValidateHash 验证文件哈希
    ValidateHash(filePath string, expectedHash string) (bool, error)
    
    // GenerateHash 生成文件哈希
    GenerateHash(filePath string) (string, error)
}
```

### 3. State Management API

#### 3.1 PathMapper 接口
```go
type PathMapper interface {
    // GetPath 获取文件路径
    GetPath(sid string) (string, error)
    
    // UpdatePath 更新路径映射
    UpdatePath(sid string, path string) error
    
    // ClearCache 清理缓存
    ClearCache() error
}
```

#### 3.2 DirKeyStore 接口
```go
type DirKeyStore interface {
    // AddDirStats 添加目录统计
    AddDirStats(l1 string, l2 string, entityCount int, fileCount int) error
    
    // GetDirStats 获取目录统计
    GetDirStats(l1 string, l2 string) (*DirStats, error)
    
    // BatchUpdateStats 批量更新统计
    BatchUpdateStats(stats []DirStats) error
}
```

### 4. Database Layer API

#### 4.1 StatusWriter 接口
```go
type StatusWriter interface {
    // UpdateMergedStatus 更新merged表状态
    UpdateMergedStatus(ctx context.Context, status *MergedStatus) error
    
    // GetMergedStatus 获取merged表状态
    GetMergedStatus(sid string) (*MergedStatus, error)
}
```

#### 4.2 SourceTableReader 接口
```go
type SourceTableReader interface {
    // GetMediaData 获取媒体数据
    GetMediaData(ctx context.Context, sid string) (*MediaData, error)
    
    // GetMediaHistory 获取媒体历史
    GetMediaHistory(ctx context.Context, sid string) ([]MediaHistory, error)
}
```

### 5. Error Handling API

#### 5.1 ErrorLogger 接口
```go
type ErrorLogger interface {
    // LogError 记录错误
    LogError(ctx context.Context, err *ProcessError) error
    
    // GetErrorHistory 获取错误历史
    GetErrorHistory(ctx context.Context, filter ErrorFilter) ([]ProcessError, error)
}
```

#### 5.2 RetryScheduler 接口
```go
type RetryScheduler interface {
    // ScheduleRetry 调度重试
    ScheduleRetry(ctx context.Context, task *RetryTask) error
    
    // GetRetryStatus 获取重试状态
    GetRetryStatus(taskID string) (*RetryStatus, error)
    
    // CancelRetry 取消重试
    CancelRetry(taskID string) error
}
```

## 数据结构

### 1. 核心数据结构

```go
// MediaTask 媒体任务结构
type MediaTask struct {
    TaskID    string
    Sid       string
    Order     int
    MediaKey  string
    URL       string
    Type      string // photo, pdf, zip
    DestPath  string
}

// MediaDiff 媒体差异结构
type MediaDiff struct {
    Added     []Media
    Removed   []Media
    Modified  []Media
    Unchanged []Media
}

// MergedStatus 合并状态结构
type MergedStatus struct {
    Sid       string
    PhoLH     []string // 图片类hash列表
    DocLH     []string // 文档类hash列表
    Status    string
    UpdatedAt time.Time
}

// ProcessError 处理错误结构
type ProcessError struct {
    TaskID     string
    ErrorType  string
    Message    string
    Timestamp  time.Time
    RetryCount int
}
```

### 2. 配置结构

```go
// SystemConfig 系统配置
type SystemConfig struct {
    MaxConcurrentDownloads int
    RetryAttempts         int
    RetryInterval         time.Duration
    CacheSize            int
    UpdateInterval       time.Duration
}

// WatchConfig 监听配置
type WatchConfig struct {
    Board       string
    Collection  string
    BatchSize   int
    MaxAwaitTime time.Duration
}
```

## 错误处理

### 1. 错误类型

```go
const (
    ErrDownloadFailed    = "DOWNLOAD_FAILED"
    ErrHashMismatch     = "HASH_MISMATCH"
    ErrStorageFailed    = "STORAGE_FAILED"
    ErrInvalidPath      = "INVALID_PATH"
    ErrWatchFailed      = "WATCH_FAILED"
    ErrStatusUpdateFailed = "STATUS_UPDATE_FAILED"
)
```

### 2. 重试策略

- 下载失败：最多重试3次，间隔递增（1s, 2s, 4s）
- 存储失败：最多重试2次，间隔1s
- 状态更新失败：最多重试2次，间隔500ms

## 性能考虑

### 1. 并发控制
- 每个属性最多3-5个并发下载
- 使用带缓冲的任务通道
- 内存缓存限制在5000条记录

### 2. 资源管理
- 定期清理过期缓存
- 批量更新统计信息
- 异步处理错误日志

### 3. 监控指标
- 下载成功率
- 处理延迟
- 缓存命中率
- 错误率统计

## 实现注意事项

1. 所有接口方法都应该支持上下文控制
2. 关键操作需要记录详细日志
3. 文件操作需要做好并发控制
4. 定期清理临时文件和缓存
5. 实现优雅的关闭机制
6. 提供健康检查接口
