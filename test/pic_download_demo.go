package main

import (
	"context"
	"path/filepath"
	"time"

	goconfig "github.com/real-rm/goconfig"
	gofile "github.com/real-rm/gofile"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
)

const (
	BOARD = "BRE"
)

// MediaInfo represents a single media item
type MediaInfo struct {
	Order             int
	MediaKey          string
	MediaURL          string
	ResourceRecordKey string
	ResourceName      string
	ClassName         string
	MediaCategory     string
	MimeType          string
	MediaObjectID     string
	ShortDescription  string
}

// PropertyInfo represents a property with its media
type PropertyInfo struct {
	ts    time.Time
	SID   string
	Media []MediaInfo
}

func init() {
	// Load configuration first
	if err := goconfig.LoadConfig(); err != nil {
		golog.Error("Failed to load config", "error", err)
	}

	// Initialize logging last
	if err := golog.InitLog(); err != nil {
		golog.Error("Failed to initialize logging", "error", err)
	}

	// Initialize MongoDB second (before any package that needs it)
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Error("Failed to initialize MongoDB", "error", err)
	}
}

var demoProps = []PropertyInfo{
	{
		ts:  time.Now(),
		SID: "R2936743",
		Media: []MediaInfo{
			{
				Order:             1,
				MediaKey:          "357ebbe9b2583396229d8816f7cb7c71-m1",
				MediaURL:          "https://dvvjkgh94f2v6.cloudfront.net/8ab08887/262958370/83dcefb7.jpeg",
				ResourceRecordKey: "357ebbe9b2583396229d8816f7cb7c71",
				ResourceName:      "Property",
				ClassName:         "Residential",
				MediaCategory:     "Photo",
				MimeType:          "image/jpeg",
				MediaObjectID:     "262958370_1",
				ShortDescription:  "",
			},
			{
				Order:             2,
				MediaKey:          "357ebbe9b2583396229d8816f7cb7c71-m2",
				MediaURL:          "https://dvvjkgh94f2v6.cloudfront.net/8ab08887/262958370/1ad5be0d.jpeg",
				ResourceRecordKey: "357ebbe9b2583396229d8816f7cb7c71",
				ResourceName:      "Property",
				ClassName:         "Residential",
				MediaCategory:     "Photo",
				MimeType:          "image/jpeg",
				MediaObjectID:     "262958370_2",
				ShortDescription:  "",
			},
			{
				Order:             3,
				MediaKey:          "357ebbe9b2583396229d8816f7cb7c71-m3",
				MediaURL:          "https://dvvjkgh94f2v6.cloudfront.net/8ab08887/262958370/6dd28e9b.jpeg",
				ResourceRecordKey: "357ebbe9b2583396229d8816f7cb7c71",
				ResourceName:      "Property",
				ClassName:         "Residential",
				MediaCategory:     "Photo",
				MimeType:          "image/jpeg",
				MediaObjectID:     "262958370_3",
				ShortDescription:  "",
			},
		},
	},
	{
		ts:  time.Now(),
		SID: "R2936744",
		Media: []MediaInfo{
			{
				Order:             1,
				MediaKey:          "f70c8280c229267e05e950ccb16d956b-m1",
				MediaURL:          "https://dvvjkgh94f2v6.cloudfront.net/8ab08887/262958365/83dcefb7.jpeg",
				ResourceRecordKey: "f70c8280c229267e05e950ccb16d956b",
				ResourceName:      "Property",
				ClassName:         "Residential",
				MediaCategory:     "Photo",
				MimeType:          "image/jpeg",
				MediaObjectID:     "262958365_1",
				ShortDescription:  "",
			},
			{
				Order:             2,
				MediaKey:          "f70c8280c229267e05e950ccb16d956b-m2",
				MediaURL:          "https://dvvjkgh94f2v6.cloudfront.net/8ab08887/262958365/1ad5be0d.jpeg",
				ResourceRecordKey: "f70c8280c229267e05e950ccb16d956b",
				ResourceName:      "Property",
				ClassName:         "Residential",
				MediaCategory:     "Photo",
				MimeType:          "image/jpeg",
				MediaObjectID:     "262958365_2",
				ShortDescription:  "",
			},
		},
	},
}

func main() {
	ctx := context.Background()

	// 从配置获取图片存储目录
	imageDir := goconfig.Config("imageStore.bre_image_dir")
	if imageDir == "" {
		golog.Fatal("Image directory not configured in imageStore.bre_image_dir")
	}

	// 创建 dirKeyStore 实例
	collection := gomongo.Coll("vow", "file_server")
	dirStore := gofile.NewDirKeyStore(BOARD, collection)
	if dirStore == nil {
		golog.Fatal("Failed to create dirKeyStore")
	}

	// 遍历每个属性
	for _, prop := range demoProps {
		// 获取存储路径（对每个属性只需计算一次）
		fullPath, err := gofile.GetFullFilePath(prop.ts, BOARD, prop.SID)
		if err != nil {
			golog.Error("Failed to get full file path",
				"ts", prop.ts,
				"sid", prop.SID,
				"error", err)
			continue
		}

		// 获取 L2 目录列表（对每个属性只需获取一次）
		tsD := int(prop.ts.Unix())
		dirArray, err := dirStore.FetchDirArray(ctx, BOARD, tsD)
		if err != nil {
			golog.Error("Failed to fetch dir array",
				"ts", tsD,
				"error", err)
			continue
		}

		// 构建基础保存路径
		savePaths := []string{}
		for _, dir := range imageDir.([]string) {
			baseSavePath := filepath.Join(dir, fullPath)
			savePaths = append(savePaths, baseSavePath)
		}

		// 处理该属性的所有媒体文件
		mediaAmount := 0
		for _, media := range prop.Media {
			// 下载并保存图片
			paths, err := gofile.DownloadAndSaveImageInDirs(media.MediaURL, savePaths, media.MediaKey, false)
			if err != nil {
				golog.Error("Failed to download and save image",
					"url", media.MediaURL,
					"paths", paths,
					"error", err)
				continue
			}
			mediaAmount += 1

			// 更新目录统计
			l1 := gofile.CalculateL1(tsD, BOARD)
			l2, err := gofile.CalculateL2(prop.SID, dirArray)
			if err != nil {
				golog.Error("Failed to calculate l2",
					"sid", prop.SID,
					"dirArray", dirArray,
					"error", err)
			}
			dirStore.AddDirStats(l1, l2, 1, mediaAmount)

			golog.Info("Successfully downloaded and saved image",
				"ts", prop.ts,
				"sid", prop.SID,
				"url", media.MediaURL,
				"paths", paths,
				"l1", l1,
				"l2", l2)
		}
	}
}
