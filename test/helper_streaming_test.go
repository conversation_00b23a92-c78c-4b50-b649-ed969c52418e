package gohelper

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"testing"
	"time"
)

// MockStream implements a mock stream for testing
type MockStream struct {
	data     []interface{}
	current  int
	closed   bool
	hasError error
	mu       sync.Mutex
}

func NewMockStream(data []interface{}) *MockStream {
	return &MockStream{
		data:    data,
		current: 0,
	}
}

func (m *MockStream) Next(ctx context.Context) bool {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.closed || m.current >= len(m.data) {
		return false
	}
	m.current++
	return true
}

func (m *MockStream) Decode(v interface{}) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.closed {
		return fmt.Errorf("stream closed")
	}
	if m.current == 0 || m.current > len(m.data) {
		return fmt.Errorf("no data")
	}
	*(v.(*interface{})) = m.data[m.current-1]
	return nil
}

func (m *MockStream) Err() error {
	return m.hasError
}

// setupTest setup test environment
func setupTestStreaming(t *testing.T) func() {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}
	if _, err := os.Stat(configPath); err != nil {
		t.Fatalf("Config file not found at %s: %v", configPath, err)
	}
	SetRmbaseFileCfg(configPath)
	// Initialize test environment
	if err := SetupTestEnv(TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}

	return CleanupTestEnv
}

func TestStreaming(t *testing.T) {
	cleanup := setupTestStreaming(t)
	defer cleanup()

	tests := []struct {
		name      string
		numItems  int
		batchSize int
	}{
		{
			name:      "normal processing",
			numItems:  100,
			batchSize: 10,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test data
			testData := make([]interface{}, tt.numItems)
			for i := 0; i < tt.numItems; i++ {
				testData[i] = i
			}

			// Create mock stream
			stream := NewMockStream(testData)

			// Track processed items
			var processedItems []interface{}
			var mu sync.Mutex
			var wg sync.WaitGroup
			wg.Add(1)

			// Create streaming options
			opts := &StreamingOptions{
				Stream: stream,
				Process: func(item interface{}) error {
					mu.Lock()
					processedItems = append(processedItems, item)
					mu.Unlock()

					// Simulate processing time
					time.Sleep(time.Millisecond)
					return nil
				},
				End: func(err error) {
					defer wg.Done()
					if err != nil {
						t.Errorf("unexpected error: %v", err)
					}
				},
				Error: func(err error) {
					t.Errorf("unexpected error: %v", err)
				},
				High:          tt.batchSize * 2,
				SpeedInterval: 100,
				Verbose:       1,
			}

			// Start streaming
			err := Streaming(context.Background(), opts)
			if err != nil {
				t.Errorf("unexpected error: %v", err)
			}

			// Wait for completion with timeout
			done := make(chan struct{})
			go func() {
				wg.Wait()
				close(done)
			}()

			select {
			case <-done:
				// Check results
				mu.Lock()
				if len(processedItems) != tt.numItems {
					t.Errorf("processed %d items, want %d", len(processedItems), tt.numItems)
				}
				mu.Unlock()
			case <-time.After(5 * time.Second):
				t.Error("test timed out")
			}
		})
	}
}

func TestStreamingEdgeCases(t *testing.T) {
	cleanup := setupTestStreaming(t)
	defer cleanup()

	t.Run("nil stream", func(t *testing.T) {
		opts := &StreamingOptions{
			Stream: nil,
			Process: func(item interface{}) error {
				return nil
			},
		}
		err := Streaming(context.Background(), opts)
		if err == nil {
			t.Errorf("expected error, got nil")
		}
		if err.Error() != "stream not provided" {
			t.Errorf("expected error message 'stream not provided', got '%s'", err.Error())
		}
	})

	t.Run("empty stream", func(t *testing.T) {
		stream := NewMockStream([]interface{}{})
		done := make(chan struct{})
		opts := &StreamingOptions{
			Stream: stream,
			Process: func(item interface{}) error {
				return nil
			},
			End: func(err error) {
				close(done)
			},
		}
		err := Streaming(context.Background(), opts)
		if err != nil {
			t.Errorf("expected no error, got %v", err)
		}

		select {
		case <-done:
			// Test passed
		case <-time.After(time.Second):
			t.Error("timeout waiting for empty stream to complete")
		}
	})
}
