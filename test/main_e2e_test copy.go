package e2e

import (
	"context"
	"fmt"
	"image"
	"image/color"
	"image/jpeg"
	"net/http"
	"net/http/httptest"
	"os"
	"os/exec"
	"path/filepath"
	"syscall"
	"testing"
	"time"

	gobase "github.com/real-rm/gobase"
	"github.com/real-rm/gofile"
	gohelper "github.com/real-rm/gohelper"
	gomongo "github.com/real-rm/gomongo"
	goresodownload "github.com/real-rm/goresodownload"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	gBoardType string
	dryRun     bool
	force      bool
	downloader struct {
		StoragePaths []string
	}
)
var gLoadedFixtures = false

// func init() {
// 	// Initialize flags for testing
// 	testFlags.StringVar(&gBoardType, "board", "TRB", "Board type (CAR/DDF/BRE/EDM/TRB)")
// 	testFlags.BoolVar(&dryRun, "dryrun", true, "Run in dry run mode")
// 	testFlags.BoolVar(&force, "force", true, "Force start a new process")

// 	// Parse test flags
// 	testFlags.Parse(os.Args[1:])
// }

// setupE2ETest sets up the test environment for end-to-end tests
func setupE2ETest(t *testing.T) (*httptest.Server, func()) {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	projectRoot := filepath.Dir(filepath.Dir(currentDir))
	configPath, err := filepath.Abs(filepath.Join(projectRoot, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}
	gohelper.SetRmbaseFileCfg(configPath)
	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}

	// Set up global variables for testing
	gBoardType = "TRB" // Default to TRB for testing
	dryRun = true      // Always run in dry run mode for tests
	force = true       // Force start for tests

	sysdataColl := gomongo.Coll("rni", "sysdata")
	if _, err := sysdataColl.DeleteMany(context.Background(), bson.M{}); err != nil {
		t.Fatalf("Failed to delete sysdata collection: %v", err)
	}

	if gLoadedFixtures == false {
		loadFixturesInit(t)
		gLoadedFixtures = true
	}

	// Create test image
	img := image.NewRGBA(image.Rect(0, 0, 100, 100))
	for y := 0; y < 100; y++ {
		for x := 0; x < 100; x++ {
			img.Set(x, y, color.RGBA{uint8(x), uint8(y), 128, 255})
		}
	}

	// Create mock image server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Simulate different responses based on URL
		switch r.URL.Path {
		case "/success.jpg":
			w.Header().Set("Content-Type", "image/jpeg")
			jpeg.Encode(w, img, nil)
		case "/success2.jpg":
			w.Header().Set("Content-Type", "image/jpeg")
			jpeg.Encode(w, img, nil)
		case "/error.jpg":
			w.WriteHeader(http.StatusInternalServerError)
		case "/timeout.jpg":
			time.Sleep(2 * time.Second)
			w.Header().Set("Content-Type", "image/jpeg")
			jpeg.Encode(w, img, nil)
		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))

	// Initialize test environment
	if err := gobase.InitBase(); err != nil {
		t.Fatalf("Failed to initialize base: %v", err)
	}
	if err := gomongo.InitMongoDB(); err != nil {
		t.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Create test directories
	testDir := filepath.Join(os.TempDir(), "goresodownload_test")
	if err := os.MkdirAll(testDir, 0755); err != nil {
		t.Fatalf("Failed to create test directory: %v", err)
	}

	// Return cleanup function
	return server, func() {
		server.Close()
		if err := os.RemoveAll(testDir); err != nil {
			t.Logf("Failed to remove test directory: %v", err)
		}
	}
}

func loadFixturesInit(t *testing.T) {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	cfg := &FixtureConfig{
		Folder: currentDir,
	}
	if err := LoadFixtures(cfg); err != nil {
		t.Fatalf("Failed to load fixtures: %v", err)
	}
}

// runMainProgram starts the main program for testing
func runMainProgram(t *testing.T, boardType string) (*exec.Cmd, context.CancelFunc) {
	ctx, cancel := context.WithCancel(context.Background())
	wd, _ := os.Getwd()
	projectRoot := filepath.Dir(filepath.Dir(wd))
	mainPath := filepath.Join(projectRoot, "cmd", "goresodownload", "main.go")

	fmt.Println("Starting main program from:", mainPath)
	cmd := exec.CommandContext(ctx, "go", "run", mainPath, "-board", boardType, "-dryrun", "-force")

	// Capture stdout and stderr
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	// Start the process
	if err := cmd.Start(); err != nil {
		cancel()
		t.Fatalf("Failed to start main program: %v", err)
	}

	// Wait a bit to ensure program is running
	time.Sleep(1 * time.Second)

	// Check if process is still running
	if cmd.Process == nil {
		cancel()
		t.Fatalf("Main program failed to start")
	}
	fmt.Println("Main program started with PID:", cmd.Process.Pid)

	return cmd, cancel
}

func cleanupMainProcess(t *testing.T, cmd *exec.Cmd, cancel context.CancelFunc) {
	cancel()

	if cmd.Process != nil {
		_ = cmd.Process.Signal(syscall.SIGINT)
	}

	done := make(chan error, 1)
	go func() {
		done <- cmd.Wait()
	}()

	select {
	case err := <-done:
		if err != nil {
			t.Logf("Process exited with error: %v", err)
		}
	case <-time.After(10 * time.Second):
		t.Logf("Timeout waiting for process to exit. Killing it.")
		_ = cmd.Process.Kill()
		<-done
	}
}

func TestE2ECAR(t *testing.T) {
	server, cleanup := setupE2ETest(t)
	defer func() {
		// Give some time for I/O operations to complete
		// time.Sleep(10 * time.Second)
		cleanup()
	}()

	// sysdataColl := gomongo.Coll("rni", "sysdata")
	// if _, err := sysdataColl.DeleteMany(context.Background(), bson.M{}); err != nil {
	// 	t.Fatalf("Failed to delete sysdata collection: %v", err)
	// }

	// Set board type
	gBoardType = "CAR"
	storagePaths := gofile.GetImageDir(gBoardType)
	// delete the storage paths
	for _, path := range storagePaths {
		if err := os.RemoveAll(path); err != nil {
			t.Fatalf("Failed to delete storage path: %v", err)
		}
	}

	// Start the main program
	cmd, cancel := runMainProgram(t, "CAR")
	defer cleanupMainProcess(t, cmd, cancel)

	// get the raw one
	watchColl := gomongo.Coll("rni", goresodownload.BoardWatchTable[gBoardType])
	var rawOne bson.M
	if err := watchColl.FindOne(context.Background(), bson.M{"_id": "CAR40401964"}).Decode(&rawOne); err != nil {
		t.Fatalf("Failed to find document: %v", err)
	}
	fmt.Println("Found raw document:", rawOne["_id"])

	// delete the raw one
	if _, err := watchColl.DeleteOne(context.Background(), bson.M{"_id": "CAR40401964"}); err != nil {
		t.Fatalf("Failed to delete document: %v", err)
	}
	fmt.Println("Deleted existing document")

	// merged collection
	mergedColl := gomongo.Coll("rni", goresodownload.BoardMergedTable[gBoardType])
	var mergedOne bson.M
	// if err := mergedColl.FindOne(context.Background(), bson.M{"_id": "CAR40401964"}).Decode(&mergedOne); err != nil {
	// 	t.Fatalf("Failed to find document: %v", err)
	// }
	// fmt.Println("mergedOne", mergedOne)

	// Test Insert
	t.Run("Insert", func(t *testing.T) {
		// fmt.Println("rawOne", rawOne["Media"])
		phoUrls := rawOne["Media"].(primitive.A)
		// fmt.Println("Media URLs:", phoUrls)
		phoUrls[0].(primitive.M)["MediaURL"] = server.URL + "/success.jpg"
		phoUrls[1].(primitive.M)["MediaURL"] = server.URL + "/success2.jpg"

		// Start the main program if not already running
		if cmd == nil {
			cmd, _ = runMainProgram(t, "CAR")
			// Give the program more time to initialize
			time.Sleep(2 * time.Second)
		}

		// Insert document to trigger processing
		fmt.Println("Inserting document with ID:", rawOne["_id"])
		if _, err := watchColl.InsertOne(context.Background(), rawOne); err != nil {
			t.Fatalf("Failed to insert document: %v", err)
		}
		fmt.Println("Document inserted successfully")

		// Wait longer for processing
		time.Sleep(5 * time.Second)
		var path string
		fmt.Println("rawOne", rawOne["ts"])
		tsDate := rawOne["ts"].(primitive.DateTime).Time()
		// Convert +0000 to +00:00 format
		sid := rawOne["ListingId"].(string)
		path, err := gofile.GetFullFilePath(tsDate, gBoardType, sid)
		require.NoError(t, err)
		fmt.Println("---path", path) // /1273/82aec

		// Get image directory from config

		require.NotEmpty(t, storagePaths, "Image directory should be configured")
		imgDir := filepath.Join(storagePaths[0], path)
		fmt.Println("--imgDir", imgDir)

		media1_int32 := gofile.MurmurToInt32(phoUrls[0].(primitive.M)["MediaKey"].(string))           // 723827482
		media2_int32 := gofile.MurmurToInt32(phoUrls[1].(primitive.M)["MediaKey"].(string))           // 1070454342
		media1_tn_int32 := gofile.MurmurToInt32(phoUrls[0].(primitive.M)["MediaKey"].(string) + "-t") // 723827482
		media1_name, err := gofile.Int32ToBase62(media1_int32)
		require.NoError(t, err)
		media2_name, err := gofile.Int32ToBase62(media2_int32)
		require.NoError(t, err)
		media1_tn_name, err := gofile.Int32ToBase62(media1_tn_int32)
		require.NoError(t, err)

		// Verify files are downloaded to correct path
		expectedPaths := []string{
			filepath.Join(imgDir, sid+"_"+media1_name+".jpg"),    // /1273/82aec/40401964_w9Gky.jpg
			filepath.Join(imgDir, sid+"_"+media2_name+".jpg"),    // /1273/82aec/40401964_BKbgEm.jpg
			filepath.Join(imgDir, sid+"_"+media1_tn_name+".jpg"), // /1273/82aec/40401964_ElC0qG.jpg
		}

		for _, path := range expectedPaths {
			assert.FileExists(t, path, "File should be downloaded to: %s", path)
		}

		if err := mergedColl.FindOne(context.Background(), bson.M{"_id": "CAR40401964"}).Decode(&mergedOne); err != nil {
			t.Fatalf("Failed to find document: %v", err)
		}
		fmt.Println("mergedOne", mergedOne["phoLH"])
		assert.Equal(t, 2, len(mergedOne["phoLH"].(primitive.A)))
		assert.Equal(t, int32(723827482), mergedOne["phoLH"].(primitive.A)[0].(int32))
		assert.Equal(t, int32(1070454342), mergedOne["phoLH"].(primitive.A)[1].(int32))
		fmt.Println("mergedOne", mergedOne["tnLH"])
		assert.Equal(t, int32(-83032382), mergedOne["tnLH"].(int32))

	})

	// Test Update
	// t.Run("Update", func(t *testing.T) {
	// 	rawOne["phoUrls"] = []bson.M{{"MediaKey": "test1", "MediaURL": "/success.jpg", "MimeType": "image/jpeg", "Order": 1}}
	// 	if _, err := watchColl.UpdateOne(context.Background(), bson.M{"_id": "CAR40401964"}, bson.M{"$set": bson.M{"phoUrls": []bson.M{{"MediaKey": "test1", "MediaURL": "/success.jpg", "MimeType": "image/jpeg", "Order": 1}}}}); err != nil {
	// 		t.Fatalf("Failed to update document: %v", err)
	// 	}

	// 	// Wait for processing
	// 	time.Sleep(2 * time.Second)
	// 	verifyMediaFiles(t, "CAR", true)
	// })

	// // Test Replace
	// t.Run("Replace", func(t *testing.T) {
	// 	rawOne["phoUrls"] = []bson.M{{"MediaKey": "test1", "MediaURL": "/success.jpg", "MimeType": "image/jpeg", "Order": 1}}
	// 	if _, err := watchColl.ReplaceOne(context.Background(), bson.M{"_id": "CAR40401964"}, rawOne); err != nil {
	// 		t.Fatalf("Failed to replace document: %v", err)
	// 	}

	// 	// Wait for processing
	// 	time.Sleep(2 * time.Second)
	// 	verifyMediaFiles(t, "CAR", true)
	// })

	// // Test Delete
	// t.Run("Delete", func(t *testing.T) {
	// 	if _, err := watchColl.DeleteOne(context.Background(), bson.M{"_id": "CAR40401964"}); err != nil {
	// 		t.Fatalf("Failed to delete document: %v", err)
	// 	}

	// 	// Wait for processing
	// 	time.Sleep(2 * time.Second)
	// 	verifyMediaFiles(t, "CAR", false)
	// })
}

// func TestE2EDDF(t *testing.T) {
// 	_, cleanup := setupE2ETest(t)
// 	defer cleanup()

// 	// Set board type
// 	gBoardType = "DDF"

// 	// Start the main program
// 	cmd, _ := runMainProgram(t, "DDF")
// 	defer func() {
// 		if err := cmd.Process.Kill(); err != nil {
// 			t.Logf("Failed to kill process: %v", err)
// 		}
// 	}()

// 	// Test Insert
// 	t.Run("Insert", func(t *testing.T) {
// 		// Wait for processing
// 		time.Sleep(2 * time.Second)
// 		verifyMediaFiles(t, "DDF", true)
// 	})

// 	// Test Update
// 	t.Run("Update", func(t *testing.T) {
// 		// Wait for processing
// 		time.Sleep(2 * time.Second)
// 		verifyMediaFiles(t, "DDF", true)
// 	})

// 	// Test Replace
// 	t.Run("Replace", func(t *testing.T) {
// 		// Wait for processing
// 		time.Sleep(2 * time.Second)
// 		verifyMediaFiles(t, "DDF", true)
// 	})

// 	// Test Delete
// 	t.Run("Delete", func(t *testing.T) {
// 		// Wait for processing
// 		time.Sleep(2 * time.Second)
// 		verifyMediaFiles(t, "DDF", false)
// 	})
// }

// func TestE2EBRE(t *testing.T) {
// 	_, cleanup := setupE2ETest(t)
// 	defer cleanup()

// 	// Set board type
// 	gBoardType = "BRE"

// 	// Start the main program
// 	cmd, _ := runMainProgram(t, "BRE")
// 	defer func() {
// 		if err := cmd.Process.Kill(); err != nil {
// 			t.Logf("Failed to kill process: %v", err)
// 		}
// 	}()

// 	// Test Insert
// 	t.Run("Insert", func(t *testing.T) {
// 		// Wait for processing
// 		time.Sleep(2 * time.Second)
// 		verifyMediaFiles(t, "BRE", true)
// 	})

// 	// Test Update
// 	t.Run("Update", func(t *testing.T) {
// 		// Wait for processing
// 		time.Sleep(2 * time.Second)
// 		verifyMediaFiles(t, "BRE", true)
// 	})

// 	// Test Replace
// 	t.Run("Replace", func(t *testing.T) {
// 		// Wait for processing
// 		time.Sleep(2 * time.Second)
// 		verifyMediaFiles(t, "BRE", true)
// 	})

// 	// Test Delete
// 	t.Run("Delete", func(t *testing.T) {
// 		// Wait for processing
// 		time.Sleep(2 * time.Second)
// 		verifyMediaFiles(t, "BRE", false)
// 	})
// }

// func TestE2EEDM(t *testing.T) {
// 	_, cleanup := setupE2ETest(t)
// 	defer cleanup()

// 	// Set board type
// 	gBoardType = "EDM"

// 	// Start the main program
// 	cmd, _ := runMainProgram(t, "EDM")
// 	defer func() {
// 		if err := cmd.Process.Kill(); err != nil {
// 			t.Logf("Failed to kill process: %v", err)
// 		}
// 	}()

// 	// Test Insert
// 	t.Run("Insert", func(t *testing.T) {
// 		// Wait for processing
// 		time.Sleep(2 * time.Second)
// 		verifyMediaFiles(t, "EDM", true)
// 	})

// 	// Test Update
// 	t.Run("Update", func(t *testing.T) {
// 		// Wait for processing
// 		time.Sleep(2 * time.Second)
// 		verifyMediaFiles(t, "EDM", true)
// 	})

// 	// Test Replace
// 	t.Run("Replace", func(t *testing.T) {
// 		// Wait for processing
// 		time.Sleep(2 * time.Second)
// 		verifyMediaFiles(t, "EDM", true)
// 	})

// 	// Test Delete
// 	t.Run("Delete", func(t *testing.T) {
// 		// Wait for processing
// 		time.Sleep(2 * time.Second)
// 		verifyMediaFiles(t, "EDM", false)
// 	})
// }

// func TestE2ETRB(t *testing.T) {
// 	_, cleanup := setupE2ETest(t)
// 	defer cleanup()

// 	// Set board type
// 	gBoardType = "TRB"

// 	// Start the main program
// 	cmd, _ := runMainProgram(t, "TRB")
// 	defer func() {
// 		if err := cmd.Process.Kill(); err != nil {
// 			t.Logf("Failed to kill process: %v", err)
// 		}
// 	}()

// 	// Test Insert
// 	t.Run("Insert", func(t *testing.T) {
// 		// Wait for processing
// 		time.Sleep(2 * time.Second)
// 		verifyMediaFiles(t, "TRB", true)
// 	})

// 	// Test Update
// 	t.Run("Update", func(t *testing.T) {
// 		// Wait for processing
// 		time.Sleep(2 * time.Second)
// 		verifyMediaFiles(t, "TRB", true)
// 	})

// 	// Test Replace
// 	t.Run("Replace", func(t *testing.T) {
// 		// Wait for processing
// 		time.Sleep(2 * time.Second)
// 		verifyMediaFiles(t, "TRB", true)
// 	})

// 	// Test Delete
// 	t.Run("Delete", func(t *testing.T) {
// 		// Wait for processing
// 		time.Sleep(2 * time.Second)
// 		verifyMediaFiles(t, "TRB", false)
// 	})
// }
