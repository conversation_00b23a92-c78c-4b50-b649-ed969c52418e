package gohelper

import (
	"context"
	"fmt"
	"time"

	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	// Import the package containing StreamingOptions and Streaming
)

const (
	// Error codes for MongoDB change stream operations
	ChangeStreamHistoryLost = 286 // Error when change stream history is lost
	InvalidToken            = 280 // Error when token is invalid
	CappedPositionLost      = 136 // Error when capped collection position is lost

	// Default configuration values
	DefaultTokenDelayMs      = 5 * 60 * 1000 // 5 minutes delay for token updates
	DefaultLowWaterMark      = 5             // Minimum number of documents to process before resuming
	DefaultHighWaterMark     = 10            // Maximum number of documents to process before pausing
	DefaultWaterMarkerTimerS = 600           // 10 minutes timer for water mark checks
	DefaultUpdateTokenTimerS = 60            // 1 minute timer for token updates
)

// WatchOptions defines options for watching MongoDB collections
type WatchOptions struct {
	ImportWatchedRecordsCol *gomongo.MongoCollection                     // Collection to import watched records
	WatchedColl             *gomongo.MongoCollection                     // Collection to watch for changes
	OnChange                func(bson.M, *gomongo.MongoCollection) error // Callback for document changes
	OnError                 func(error)                                  // Callback for error handling
	OnTokenUpdate           func(TokenUpdateOptions) error               // Callback for token updates
	QueryWhenInvalidToken   bson.M                                       // Query to use when token is invalid
	WatchPipeline           []bson.M                                     // MongoDB aggregation pipeline for change stream
	TokenDelayMs            int64                                        // Delay between token updates
	LowWaterMark            int                                          // Minimum documents to process before resuming
	HighWaterMark           int                                          // Maximum documents to process before pausing
	WaterMarkerTimerS       int                                          // Timer for water mark checks
	UpdateTokenTimerS       int                                          // Timer for token updates
	SavedToken              *bson.M                                      // Token to resume from
	UpdateProcessStatusFn   func() error                                 // Function to update process status
	OnClose                 func()                                       // Callback when watch is closed
}

// TokenUpdateOptions contains token update information
type TokenUpdateOptions struct {
	Token          *bson.M     // The new token
	TokenClusterTs time.Time   // Timestamp when token was created
	TokenChangeTs  time.Time   // Timestamp when token was updated
	End            func(error) // Function to end the watch operation
	ResumeMt       time.Time   // Timestamp to resume from
}

// WatchObject represents the watch operation state
type WatchObject struct {
	ChangeStream          *mongo.ChangeStream
	CurrentToken          *bson.M
	SavedToken            *bson.M
	SavedTokenClusterTs   time.Time
	End                   func(error)
	ProcessingCounter     int
	ProcessedCounter      int
	NS                    string
	IsPaused              bool
	IsWatchEnd            bool // Flag to indicate if watch has ended
	ToWarnHighWaterLevel  bool
	ResumeMt              time.Time
	CurrentTokenClusterTs time.Time
	CurrentTokenChangeTs  time.Time
	waterMarkerTimer      *Interval
	updateTokenTimer      *Interval
	watchChangeTimer      *Interval
}

// WatchResult contains the result of starting a watch operation
type WatchResult struct {
	WatchObj *WatchObject
	Err      error
}

// WatchCallback is the callback function type for WatchTarget
type WatchCallback func(error, *WatchObject)

// saveChangedObj saves the changed object to the import collection
func saveChangedObj(col *gomongo.MongoCollection, changedObj bson.M) error {
	if col == nil {
		return nil
	}
	insertObj := bson.M{
		"id":                changedObj["documentKey"],
		"operationType":     changedObj["operationType"],
		"ns":                changedObj["ns"],
		"updateDescription": changedObj["updateDescription"],
		"clusterTime":       changedObj["clusterTime"],
		"token":             changedObj["_id"],
	}
	_, err := col.InsertOne(context.Background(), insertObj)
	if err != nil {
		golog.Error("when insert into importWatchedRecordsCol, error:", err, "changedObj", changedObj)
	}
	return err
}

type UpdateFields struct {
	Token          any
	TokenClusterTs time.Time
	TokenChangeTs  time.Time
	Status         string
	ResumeMt       time.Time
}

type UpdateSysDataFunc func(UpdateFields) error

// GetUpdateSysdataFunction returns a function to update system data
func GetUpdateSysdataFunction(
	SysData *gomongo.MongoCollection,
	SysDataId interface{},
) UpdateSysDataFunc {
	if SysData == nil || SysDataId == nil {
		golog.Error("SysData and sysDataId required")
		return nil
	}

	return func(fields UpdateFields) error {
		// Set default status
		status := fields.Status
		if status == "" {
			status = "running"
		}

		// Prepare update set
		set := bson.M{
			"_id":    SysDataId,
			"status": status,
		}

		// Add optional fields
		if !fields.TokenClusterTs.IsZero() {
			set["tokenClusterTs"] = fields.TokenClusterTs
		}
		if !fields.TokenChangeTs.IsZero() {
			set["tokenChangeTs"] = fields.TokenChangeTs
		}
		if fields.Token != nil {
			// Convert token to JSON string
			tokenBytes, err := bson.MarshalExtJSON(fields.Token, false, false)
			if err != nil {
				golog.Error("Error marshaling token to JSON:", err)
				return err
			}
			set["token"] = string(tokenBytes)
		}
		if !fields.ResumeMt.IsZero() {
			set["resumeMt"] = fields.ResumeMt
		}

		// Prepare update operation
		update := bson.M{"$set": set}
		golog.Debug("updateMongoToken", update)

		// Execute update
		opts := options.Update().SetUpsert(true)
		_, err := SysData.UpdateOne(context.Background(), bson.M{"_id": SysDataId}, update, opts)
		if err != nil {
			golog.Error("Error updating system data:", err)
		}

		return err
	}
}

// WatchTarget starts watching a MongoDB collection for changes
func WatchTarget(opt WatchOptions) (*WatchObject, error) {
	// Validate required options
	if opt.WatchedColl == nil {
		return nil, fmt.Errorf("watchedColl Required")
	}
	if opt.OnError == nil {
		return nil, fmt.Errorf("onError function Required")
	}
	if opt.OnChange == nil && opt.OnTokenUpdate == nil {
		return nil, fmt.Errorf("process function Required")
	}

	// Set default values
	if opt.TokenDelayMs == 0 {
		opt.TokenDelayMs = DefaultTokenDelayMs
	}
	if opt.LowWaterMark == 0 {
		opt.LowWaterMark = DefaultLowWaterMark
	}
	if opt.HighWaterMark == 0 {
		opt.HighWaterMark = DefaultHighWaterMark
	}
	if opt.WaterMarkerTimerS == 0 {
		opt.WaterMarkerTimerS = DefaultWaterMarkerTimerS
	}
	if opt.UpdateTokenTimerS == 0 {
		opt.UpdateTokenTimerS = DefaultUpdateTokenTimerS
	}
	if opt.WatchPipeline == nil {
		opt.WatchPipeline = []bson.M{}
	}

	// Create watch object
	watchObj := &WatchObject{
		NS:         opt.WatchedColl.Name(),
		IsWatchEnd: false,
		IsPaused:   false, // Start in running state
	}

	// Create end function
	watchObj.End = func(err error) {
		golog.Debug("End function called")
		watchObj.IsWatchEnd = true
		watchObj.IsPaused = true // Set IsPaused to true to stop the watch loop
		golog.Debug("Stopping timers")
		watchObj.stopTimers()

		if opt.OnClose != nil {
			golog.Debug("Calling OnClose callback")
			opt.OnClose()
		}

		if watchObj.ChangeStream != nil {
			golog.Debug("Closing change stream")
			defer func() {
				if watchObj != nil && watchObj.ChangeStream != nil {
					if err := watchObj.ChangeStream.Close(context.Background()); err != nil {
						golog.Error("error closing change stream:", err)
					}
				}
			}()
			watchObj.ChangeStream = nil // Set ChangeStream to nil after closing
		}

		if err != nil {
			golog.Debug("Calling OnError callback with error:", err)
			opt.OnError(err)
		}
		golog.Debug("End function completed")
	}

	// Configure change stream options
	opts := options.ChangeStream().SetFullDocument(options.UpdateLookup)
	opts.SetMaxAwaitTime(3 * time.Hour)
	if opt.SavedToken != nil {
		opts.SetStartAfter(opt.SavedToken)
	}

	// Create change stream
	changeStream, err := opt.WatchedColl.Watch(context.Background(), opt.WatchPipeline, opts)
	if err != nil {
		golog.Error("watchHelper watchedColl watch error:", err)
		return nil, err
	}

	if changeStream == nil {
		err = fmt.Errorf("no change stream when watch")
		golog.Error("no change stream when watch:", err)
		return nil, err
	}

	golog.Info("start watch ", "collection", watchObj.NS, " with pipeline:", opt.WatchPipeline)
	startToken := opt.SavedToken
	if startToken == nil {
		startToken = &bson.M{}
	}

	watchObj.ProcessingCounter = 0
	watchObj.ProcessedCounter = 0
	// Update watch object state
	watchObj.ChangeStream = changeStream
	watchObj.CurrentToken = startToken
	watchObj.SavedToken = startToken

	// Start timers
	watchObj.startTimers(opt)

	// Start watching in a goroutine
	go watchObj.myOnWatch(opt)

	return watchObj, nil
}

// checkWaterMark checks the processing status and updates water mark state
func (watchObj *WatchObject) checkWaterMark(opt WatchOptions) {
	if watchObj.IsPaused {
		return
	}
	if watchObj.ToWarnHighWaterLevel {
		// second time warn
		golog.Warn("high water mark", opt.HighWaterMark, watchObj.ProcessingCounter)
	} else {
		// first time set warn
		watchObj.ToWarnHighWaterLevel = true
	}
}

// startTimers initializes and starts all required timers
func (watchObj *WatchObject) startTimers(opt WatchOptions) {
	// Water marker timer - checks processing status
	watchObj.waterMarkerTimer = StartInterval(opt.WaterMarkerTimerS, func() {
		if !watchObj.IsWatchEnd {
			watchObj.checkWaterMark(opt)
		}
	})

	// Update token timer - updates token periodically
	watchObj.updateTokenTimer = StartInterval(opt.UpdateTokenTimerS, func() {
		if !watchObj.IsWatchEnd {
			watchObj.updateToken(opt)
		}
	})

	// Watch change timer - checks for watch changes every minute
	watchObj.watchChangeTimer = StartInterval(60, func() {
		if !watchObj.IsWatchEnd {
			watchObj.myOnWatch(opt)
		}
	})
}

// updateToken updates the current token
func (watchObj *WatchObject) updateToken(opt WatchOptions) {
	golog.Debug("_updateTokenTimer", (*watchObj.CurrentToken)["_data"])

	// Update process status if function exists
	if opt.UpdateProcessStatusFn != nil {
		if err := opt.UpdateProcessStatusFn(); err != nil {
			golog.Error("updateProcessStatus", err)
		}
	}

	// Handle resumeMt update if exists
	if !watchObj.ResumeMt.IsZero() && opt.OnTokenUpdate != nil {
		err := opt.OnTokenUpdate(TokenUpdateOptions{
			ResumeMt:       watchObj.ResumeMt,
			TokenClusterTs: watchObj.CurrentTokenClusterTs,
		})
		if err != nil {
			golog.Error("onTokenUpdate", err)
		}
		watchObj.ResumeMt = time.Time{} // Clear resumeMt
	}

	// Check if token needs update
	if watchObj.CurrentToken == nil || (*watchObj.CurrentToken)["_data"] == nil {
		golog.Debug("no retObject.resumeToken?._data, do not need update", watchObj.NS)
		return
	}

	if watchObj.SavedToken != nil && (*watchObj.SavedToken)["_data"] == (*watchObj.CurrentToken)["_data"] {
		golog.Debug("no tokenChange, do not need update", watchObj.NS)
		return
	}

	golog.Debug("[updateToken in timer] token Changed", watchObj.NS,
		"tokenClusterTs", watchObj.CurrentTokenClusterTs,
		"tokenChangeTs", watchObj.CurrentTokenChangeTs)

	// Update token if callback exists
	if opt.OnTokenUpdate != nil {
		tokenOpt := TokenUpdateOptions{
			Token:          watchObj.CurrentToken,
			TokenClusterTs: watchObj.CurrentTokenClusterTs,
			TokenChangeTs:  watchObj.CurrentTokenChangeTs,
			End:            watchObj.End,
		}

		err := opt.OnTokenUpdate(tokenOpt)
		if err != nil {
			golog.Error("onTokenUpdate", err)
			opt.OnError(err)
			return
		}

		golog.Debug("[updateToken in timer] token Changed,", watchObj.NS,
			"retObject.savedToken", watchObj.SavedToken,
			"currentToken", watchObj.CurrentToken)

		watchObj.SavedToken = watchObj.CurrentToken
		watchObj.SavedTokenClusterTs = watchObj.CurrentTokenClusterTs
	}
}

// myOnWatch checks for any changes in the watch status
func (watchObj *WatchObject) myOnWatch(opt WatchOptions) {
	unlock := func() {
		cursorInUseLock = false
	}

	if cursorInUseLock {
		golog.Debug("cursorInUseLock, return")
		return
	}

	// Check if changeStream is nil or watch has ended
	if watchObj.ChangeStream == nil || watchObj.IsWatchEnd {
		golog.Debug("changeStream is nil or watch has ended, return")
		unlock()
		return
	}

	for {
		// Check if watch has ended
		if watchObj.IsWatchEnd {
			golog.Debug("watch has ended, return")
			unlock()
			return
		}

		// Check if watch is paused
		if watchObj.IsPaused {
			golog.Debug("watch is paused, return")
			unlock()
			return
		}

		// Check if changeStream is closed or nil
		if watchObj.ChangeStream == nil {
			golog.Debug("changeStream is nil, return")
			unlock()
			return
		}

		// Check if changeStream is already closed
		if watchObj.ChangeStream.ID() == 0 {
			golog.Debug("Change stream already closed")
			unlock()
			return
		}

		// Check if changeStream is in a valid state
		if watchObj.ChangeStream.Err() != nil {
			golog.Error("Change stream in error state:", watchObj.ChangeStream.Err())
			unlock()
			return
		}

		cursorInUseLock = true

		// Check for next change
		hasNext := watchObj.ChangeStream.Next(context.Background())
		if !hasNext {
			// Check for errors if hasNext is false
			if err := watchObj.ChangeStream.Err(); err != nil {
				golog.Error("Error during changeStream.Next:", err)
				if !watchObj.IsWatchEnd {
					watchObj.myOnError(opt, err)
				}
			} else {
				golog.Info("Change stream closed normally or context cancelled.")
			}
			unlock()
			return
		}

		// Get the current document and convert it to bson.M
		var changedObj bson.M
		if watchObj.ChangeStream == nil {
			golog.Error("ChangeStream is nil, cannot decode")
			unlock()
			return
		}
		if err := watchObj.ChangeStream.Decode(&changedObj); err != nil {
			golog.Error("Error decoding change stream document:", err)
			if !watchObj.IsWatchEnd {
				watchObj.myOnError(opt, err)
			}
			unlock()
			return
		}

		unlock()
		watchObj.myOnChange(opt, changedObj)
	}
}

// stopTimers stops all running timers
func (watchObj *WatchObject) stopTimers() {
	if watchObj.waterMarkerTimer != nil {
		watchObj.waterMarkerTimer.Stop()
	}
	if watchObj.updateTokenTimer != nil {
		watchObj.updateTokenTimer.Stop()
	}
	if watchObj.watchChangeTimer != nil {
		watchObj.watchChangeTimer.Stop()
	}
}

// myOnChange handles document changes
func (watchObj *WatchObject) myOnChange(opt WatchOptions, changedObj bson.M) {
	// Check if changeStream is closed
	if watchObj.ChangeStream == nil {
		return
	}

	if changedObj == nil {
		golog.Debug("no changedObj in onchange event")
		return
	}

	// Save to import collection if specified
	if opt.ImportWatchedRecordsCol != nil {
		if err := saveChangedObj(opt.ImportWatchedRecordsCol, changedObj); err != nil {
			golog.Error("saveChangedObj", err)
			// Ignore this error, just log it
		}
	}

	golog.Debug("stream change event:", changedObj["operationType"],
		changedObj["documentKey"], "isPaused", watchObj.IsPaused,
		"watchObj.ProcessingCounter", watchObj.ProcessingCounter)

	watchObj.ProcessingCounter++
	golog.Debug("highWaterMark", opt.HighWaterMark)
	golog.Debug("processingCounter", watchObj.ProcessingCounter)

	// Check high water mark
	if (opt.HighWaterMark > 0) && (watchObj.ProcessingCounter > opt.HighWaterMark) {
		if !watchObj.IsPaused {
			golog.Info("pause stream, processingCounter:", watchObj.ProcessingCounter,
				"highWaterMark:", opt.HighWaterMark)
			watchObj.IsPaused = true // This will block next change in myOnWatch
		}
	}

	golog.Debug("onChange, processingCounter:", watchObj.ProcessingCounter,
		opt.HighWaterMark, watchObj.IsPaused)

	// Process the change
	if err := opt.OnChange(changedObj, opt.WatchedColl); err != nil {
		golog.Error("error in onChange event:", err)
		if err := watchObj.ChangeStream.Close(context.Background()); err != nil {
			golog.Error("error closing change stream:", err)
		}
		opt.OnError(err)
		return
	}

	watchObj.ProcessingCounter--
	watchObj.ProcessedCounter++
	watchObj.ResumeMt = time.Now()

	// Check if we should resume
	if watchObj.IsPaused {
		if opt.LowWaterMark == 0 || watchObj.ProcessingCounter < opt.LowWaterMark {
			golog.Info("resume stream", watchObj.ProcessingCounter, opt.LowWaterMark)
			watchObj.IsPaused = false
			watchObj.ToWarnHighWaterLevel = false // Clear flag
			watchObj.myOnWatch(opt)               // Resume watching in a new goroutine
		}
	}

	if changedObj != nil {
		golog.Debug("[onChange Event]changed token:", changedObj["_id"],
			"clusterTime", changedObj["clusterTime"],
			changedObj["ns"], changedObj["operationType"])
	}
}

// myOnError handles watch errors
func (watchObj *WatchObject) myOnError(opt WatchOptions, err error) {
	golog.Error("watch on Error:", err)
	if mongoErr, ok := err.(mongo.CommandError); ok {
		switch mongoErr.Code {
		case InvalidToken, ChangeStreamHistoryLost:
			watchObj.myOnClose(opt)
			watchObj.myOnInvalidToken(opt)
		case CappedPositionLost:
			watchObj.myOnClose(opt)
			watchObj.myOnCappedPositionLost(opt)
		default:
			watchObj.myOnClose(opt)
			if err := watchObj.ChangeStream.Close(context.Background()); err != nil {
				golog.Error("error closing change stream:", err)
			}
			opt.OnError(err)
		}
	} else {
		golog.Error("mongoErr code parse error:", err)
		watchObj.myOnClose(opt)
		if err := watchObj.ChangeStream.Close(context.Background()); err != nil {
			golog.Error("error closing change stream:", err)
		}
		opt.OnError(err)
	}
}

var cursorInUseLock = false

func (watchObj *WatchObject) myOnClose(opt WatchOptions) {
	golog.Verbose("on close")
	watchObj.stopTimers()
	if opt.OnClose != nil {
		opt.OnClose()
	}
}

func (watchObj *WatchObject) myOnInvalidToken(opt WatchOptions) {
	golog.Info("on invalid token: queryWhenInvalidToken:", opt.QueryWhenInvalidToken)
	watchObj.getTokenAndProcessQuery(opt)
}

// myOnCappedPositionLost handles capped position lost errors
func (watchObj *WatchObject) myOnCappedPositionLost(opt WatchOptions) {
	golog.Warn("queryWhenInvalidToken:", opt.QueryWhenInvalidToken)
	golog.Warn("on CollectionScan died Error process")
	watchObj.getTokenAndProcessQuery(opt)
}

// getTokenAndProcessQuery gets a new token and processes remaining documents
func (watchObj *WatchObject) getTokenAndProcessQuery(opt WatchOptions) {
	var query bson.M
	if !watchObj.SavedTokenClusterTs.IsZero() {
		query = bson.M{"_mt": bson.M{"$gte": watchObj.SavedTokenClusterTs}}
	} else if opt.QueryWhenInvalidToken != nil {
		query = opt.QueryWhenInvalidToken
	} else {
		opt.OnError(fmt.Errorf("no invalid query"))
		return
	}

	golog.Warn("_getTokenAndProcessQuery, do Query:", query)
	processOneFn := func(item bson.M) error {
		return opt.OnChange(bson.M{
			"operationType": "update",
			"fullDocument":  item,
			"documentKey":   bson.M{"_id": item["_id"]},
		}, opt.WatchedColl)
	}

	token, tokenClusterTs, err := GetNewTokenAndProcessRemainingDocs(GetNewTokenAndProcessRemainingDocsOptions{
		FromCol:       opt.WatchedColl,
		Query:         query,
		ProcessOneFn:  processOneFn,
		HighWaterMark: 1,
	})

	if err != nil {
		golog.Error("error in getTokenAndProcessQuery:", err)
		opt.OnError(err)
		return
	}

	if opt.OnTokenUpdate != nil {
		err = opt.OnTokenUpdate(TokenUpdateOptions{
			Token:          token,
			TokenClusterTs: tokenClusterTs,
			TokenChangeTs:  time.Now(),
			End:            watchObj.End,
		})
		if err != nil {
			golog.Error("error in onTokenUpdate:", err)
			opt.OnError(err)
			return
		}
	}

	golog.Info("start watch after _getTokenAndProcessQuery")
	// Create new change stream with the token
	opts := options.ChangeStream().SetFullDocument(options.UpdateLookup)
	if token != nil {
		opts.SetStartAfter(token)
	}

	changeStream, err := opt.WatchedColl.Watch(context.Background(), []bson.M{}, opts)
	if err != nil {
		golog.Error("Error creating new change stream:", err)
		opt.OnError(err)
		return
	}

	watchObj.ChangeStream = changeStream
	watchObj.CurrentToken = token
	watchObj.SavedToken = token
	watchObj.CurrentTokenClusterTs = tokenClusterTs
}

// GetToken gets a new token from a MongoDB collection,
// which can be used to resume a change stream from a specific point in time.
// The token contains information about the last processed operation and is essential for implementing resilient change stream processing with recovery capabilities.
func GetToken(col *gomongo.MongoCollection) (*bson.M, time.Time, error) {
	golog.Info("start get new token from db")

	// Check if collection is nil
	if col == nil {
		return nil, time.Time{}, fmt.Errorf("collection is nil")
	}

	opts := options.ChangeStream().
		SetFullDocument(options.UpdateLookup).
		SetMaxAwaitTime(3 * time.Hour)

	changeStream, err := col.Watch(context.Background(), mongo.Pipeline{}, opts)
	if err != nil {
		golog.Error("error when getToken (watch):", err)
		return nil, time.Time{}, err
	}
	defer func() {
		if changeStream != nil {
			if err := changeStream.Close(context.Background()); err != nil {
				golog.Error("error closing change stream:", err)
			}
		}
	}()

	golog.Debug("Change stream created for GetToken")

	// use timeout context to limit the blocking time
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()

	if changeStream.Next(ctx) {
		// there is an event
		resumeTokenRaw := changeStream.ResumeToken()
		if resumeTokenRaw == nil {
			err := fmt.Errorf("getToken error: received event but resume token is nil")
			golog.Error("getToken error:", err)
			return nil, time.Time{}, err
		}
		var tokenDoc bson.M
		if err := bson.Unmarshal(resumeTokenRaw, &tokenDoc); err != nil {
			return nil, time.Time{}, fmt.Errorf("failed to unmarshal resume token: %w", err)
		}
		token := &bson.M{
			"_data": tokenDoc["_data"],
		}
		tokenClusterTs := time.Now()
		golog.Debug("Got resume token after receiving change", "token", token)
		return token, tokenClusterTs, nil
	}

	// try to get resume token even if there is no event
	resumeTokenRaw := changeStream.ResumeToken()
	if resumeTokenRaw != nil {
		var tokenDoc bson.M
		if err := bson.Unmarshal(resumeTokenRaw, &tokenDoc); err != nil {
			return nil, time.Time{}, fmt.Errorf("failed to unmarshal resume token: %w", err)
		}
		token := &bson.M{
			"_data": tokenDoc["_data"],
		}
		tokenClusterTs := time.Now()
		golog.Debug("Got resume token without change", "token", token)
		return token, tokenClusterTs, nil
	}

	// check if there is an error
	if err := changeStream.Err(); err != nil {
		golog.Error("changeStream.Next error:", err)
		return nil, time.Time{}, err
	}
	err = fmt.Errorf("getToken error: no resume token received after short wait")
	golog.Error("getToken error:", err)
	return nil, time.Time{}, err
}

// ProcessChangedObjectOptions contains options for processing changed objects
type ProcessChangedObjectOptions struct {
	ChangedObj            bson.M
	WatchedColl           *gomongo.MongoCollection
	DeleteOneFn           func(interface{}) error
	IgnoreDelete          bool
	UpdateOneFn           func(bson.M) error
	UpdateOneFnWithFields func(bson.M, bson.M) error
	ReplaceOneFn          func(bson.M) error
	InsertOneFn           func(bson.M) error
	ChangeStatusOnlyFn    func(bson.M) error
	SkipChangeMtOnly      bool
	WatchedStream         *WatchObject
}

// ProcessChangedObject processes a changed object from the MongoDB change stream
// by identifying the operation type (insert, update, replace, delete) and invoking the appropriate callback function.
// This enables applications to react to specific database changes with custom logic while handling common patterns like skipping duplicate events.
func ProcessChangedObject(opt ProcessChangedObjectOptions) error {
	golog.Debug("processChangedObject", opt.ChangedObj["operationType"], opt.ChangedObj)

	// Handle invalidate event
	if opt.ChangedObj["operationType"] == "invalidate" {
		golog.Error("invalid event")
		return fmt.Errorf("invalid event, please do init again")
	}

	// Check if operation type is valid
	validTypes := map[string]bool{
		"insert":  true,
		"update":  true,
		"delete":  true,
		"replace": true,
	}

	if !validTypes[opt.ChangedObj["operationType"].(string)] {
		golog.Verbose("ignore:", opt.ChangedObj["operationType"], opt.ChangedObj)
		return nil
	}

	// Get document ID
	docKey, ok := opt.ChangedObj["documentKey"].(bson.M)
	if !ok {
		golog.Error("no documentKey", opt.ChangedObj)
		return nil
	}

	id, ok := docKey["_id"]
	if !ok {
		golog.Error("no id", opt.ChangedObj)
		return nil
	}

	// Handle delete operation
	doDelete := !opt.IgnoreDelete && (opt.ChangedObj["operationType"] == "delete" ||
		(opt.ChangedObj["fullDocument"] != nil && opt.ChangedObj["fullDocument"].(bson.M)["del"] == 1))

	if doDelete {
		if opt.DeleteOneFn == nil {
			return fmt.Errorf("need deleteOne function")
		}
		if err := opt.DeleteOneFn(id); err != nil {
			return err
		}
		golog.Verbose("delete success:", opt.ChangedObj["documentKey"])
		return nil
	}

	// Handle update operation
	if opt.ChangedObj["operationType"] == "delete" {
		golog.Info("delete", "id", id, "in target, no update")
		return nil
	}

	// Check for _mt only updates
	updateDesc, ok := opt.ChangedObj["updateDescription"].(bson.M)
	if ok {
		updatedFields, _ := updateDesc["updatedFields"].(bson.M)
		removedFields, _ := updateDesc["removedFields"].(bson.M)

		changedFieldsNumber := 0
		if updatedFields != nil {
			changedFieldsNumber = len(updatedFields)
		}

		removedFieldsNumber := 0
		if removedFields != nil {
			removedFieldsNumber = len(removedFields)
		}

		if changedFieldsNumber > 0 && removedFieldsNumber == 0 {
			// Check for _mt only update
			if changedFieldsNumber == 1 && updatedFields["_mt"] != nil {
				golog.Debug("change _mt only, no update")
				return nil
			}

			// Check for mt only update
			if changedFieldsNumber == 1 && updatedFields["mt"] != nil && opt.SkipChangeMtOnly {
				golog.Debug("change mt only, no update")
				return nil
			}

			// Check for status only update
			changeStatusOnly := updatedFields["status"] != nil &&
				(changedFieldsNumber == 1 ||
					(changedFieldsNumber == 2 && updatedFields["mt"] != nil))

			if opt.ChangeStatusOnlyFn != nil && changeStatusOnly {
				return opt.ChangeStatusOnlyFn(bson.M{
					"id":              id,
					"prop":            updatedFields,
					"watchedCollName": opt.WatchedColl.Name(),
				})
			}
		}
	}

	// Process update with full document
	if opt.ChangedObj["fullDocument"] != nil {
		golog.Debug("change fullDocument", opt.ChangedObj["fullDocument"])
		fullDoc, ok := opt.ChangedObj["fullDocument"].(bson.M)
		if !ok {
			golog.Error("invalid fullDocument format in change stream")
			return nil
		}

		var updatedFields bson.M
		if updateDesc["updatedFields"] != nil {
			updatedFields, ok = updateDesc["updatedFields"].(bson.M)
			if !ok {
				golog.Error("invalid updatedFields format in change stream")
				return nil
			}
		}

		return processUpdate(ProcessUpdateOptions{
			ReplaceOneFn:          opt.ReplaceOneFn,
			UpdateOneFn:           opt.UpdateOneFn,
			UpdateOneFnWithFields: opt.UpdateOneFnWithFields,
			InsertOneFn:           opt.InsertOneFn,
			OperationType:         opt.ChangedObj["operationType"].(string),
			FullDocument:          fullDoc,
			UpdatedFields:         updatedFields,
		})
	}

	// Find document in watched collection
	var prop bson.M
	err := opt.WatchedColl.FindOne(context.Background(), bson.M{"_id": id}).Decode(&prop)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			golog.Error("no fullDocument, can not find", id, "in watchedColl")
			return nil
		}
		return err
	}

	golog.Info("no fullDocument, find", id, "in watchedColl")
	return processUpdate(ProcessUpdateOptions{
		ReplaceOneFn:          opt.ReplaceOneFn,
		UpdateOneFn:           opt.UpdateOneFn,
		UpdateOneFnWithFields: opt.UpdateOneFnWithFields,
		InsertOneFn:           opt.InsertOneFn,
		OperationType:         opt.ChangedObj["operationType"].(string),
		FullDocument:          prop,
		UpdatedFields:         updateDesc["updatedFields"].(bson.M),
	})
}

// ProcessUpdateOptions contains options for processing updates
type ProcessUpdateOptions struct {
	UpdateOneFn           func(bson.M) error
	UpdateOneFnWithFields func(bson.M, bson.M) error
	InsertOneFn           func(bson.M) error
	ReplaceOneFn          func(bson.M) error
	OperationType         string
	FullDocument          bson.M
	UpdatedFields         bson.M
}

var gImmediateUpdatedId = struct {
	ID   interface{}
	Type string
	TS   time.Time
}{}

// processUpdate processes an update operation
func processUpdate(opt ProcessUpdateOptions) error {
	// Check for duplicate updates
	if gImmediateUpdatedId.ID == opt.FullDocument["_id"] &&
		time.Since(gImmediateUpdatedId.TS) < 2*time.Second {
		golog.Error("Error: watch update with same record_id:", opt.FullDocument["_id"])
		return nil
	}

	// Update global tracking
	gImmediateUpdatedId = struct {
		ID   interface{}
		Type string
		TS   time.Time
	}{
		ID:   opt.FullDocument["_id"],
		Type: opt.OperationType,
		TS:   time.Now(),
	}

	// Handle replace operation
	if opt.OperationType == "replace" {
		if opt.ReplaceOneFn == nil {
			return fmt.Errorf("need replaceOne function")
		}
		return opt.ReplaceOneFn(opt.FullDocument)
	}

	// Handle insert operation with insertOneFn
	if opt.OperationType == "insert" && opt.InsertOneFn != nil {
		return opt.InsertOneFn(opt.FullDocument)
	}

	// Handle insert or update operations
	if opt.OperationType == "insert" || opt.OperationType == "update" {
		golog.Debug("processUpdate")
		if opt.UpdateOneFnWithFields != nil {
			if err := opt.UpdateOneFnWithFields(opt.FullDocument, opt.UpdatedFields); err != nil {
				golog.Error("update/insert error:", err)
				return err
			}
			golog.Debug("update/insert success:", opt.FullDocument, opt.UpdatedFields)
			return nil
		}
		if opt.UpdateOneFn != nil {
			if err := opt.UpdateOneFn(opt.FullDocument); err != nil {
				golog.Error("update error:", err)
				return err
			}
			golog.Debug("update success:", opt.FullDocument)
			return nil
		}
		return fmt.Errorf("need updateOne function")
	}

	golog.Error("Not supported operationType", opt.OperationType)
	return nil
}

// GetNewTokenAndProcessRemainingDocsOptions contains options for getting new token and processing remaining docs
type GetNewTokenAndProcessRemainingDocsOptions struct {
	FromCol       *gomongo.MongoCollection // Collection to process
	Query         bson.M                   // Query to find remaining documents
	ProcessOneFn  func(bson.M) error       // Function to process each document
	HighWaterMark int                      // Maximum number of documents to process at once
}

// GetNewTokenAndProcessRemainingDocs gets a new token and processes remaining documents
func GetNewTokenAndProcessRemainingDocs(opt GetNewTokenAndProcessRemainingDocsOptions) (*bson.M, time.Time, error) {
	if opt.HighWaterMark == 0 {
		opt.HighWaterMark = 1
	}

	token, tokenClusterTs, err := GetToken(opt.FromCol)
	if err != nil {
		return nil, time.Time{}, err
	}

	golog.Info("getNewTokenAndProcessRemainingDocs, query:", opt.Query, "new token:", token)

	// Create cursor with maxTimeMS
	opts := options.Find().SetMaxTime(3 * time.Hour)
	cursor, err := opt.FromCol.Find(context.Background(), opt.Query, opts)
	if err != nil {
		return nil, time.Time{}, err
	}
	defer func() {
		if cursor != nil {
			if err := cursor.Close(context.Background()); err != nil {
				golog.Error("error closing cursor:", err)
			}
		}
	}()

	oldRecordCnt := 0
	var processErr error
	var resultToken *bson.M
	var resultTokenClusterTs time.Time

	// Create streaming options
	streamOpts := StreamingOptions{
		Stream: cursor,
		Process: func(item interface{}) error {
			doc, ok := item.(bson.M)
			if !ok {
				return fmt.Errorf("invalid document type: %T", item)
			}
			oldRecordCnt++
			return opt.ProcessOneFn(doc)
		},
		End: func(err error) {
			processErr = err
			resultToken = token
			resultTokenClusterTs = tokenClusterTs
			golog.Info("finish getNewTokenAndProcessRemainingDocs", oldRecordCnt, "records, new token:", token)
		},
		Error: func(err error) {
			golog.Error("Processing error:", err)
		},
		High:    opt.HighWaterMark,
		Verbose: 3,
	}

	// Start streaming
	if err := Streaming(context.Background(), &streamOpts); err != nil {
		return nil, time.Time{}, err
	}

	if processErr != nil {
		return nil, time.Time{}, processErr
	}

	return resultToken, resultTokenClusterTs, nil
}
