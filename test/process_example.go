package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	goconfig "github.com/real-rm/goconfig"
	gohelper "github.com/real-rm/gohelper"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

// ExampleProcess demonstrates how to use ProcessMonitor with MongoDB streaming
type ExampleProcess struct {
	monitor *gohelper.ProcessMonitor
	ctx     context.Context
	cancel  context.CancelFunc
}

// NewExampleProcess creates a new example process
func NewExampleProcess() (*ExampleProcess, error) {
	// Load configuration
	if err := goconfig.LoadConfig(); err != nil {
		return nil, fmt.Errorf("failed to load config: %v", err)
	}

	// Initialize logging
	if err := golog.InitLog(); err != nil {
		return nil, fmt.Errorf("failed to initialize logging: %v", err)
	}

	// Initialize MongoDB
	if err := gomongo.InitMongoDB(); err != nil {
		return nil, fmt.Errorf("failed to initialize MongoDB: %v", err)
	}

	// Get process status collection
	processStatusCol := gomongo.Coll("system", "process_status")

	// Create a new process monitor with default 5-minute interval
	monitor := gohelper.NewProcessMonitor(gohelper.ProcessMonitorOptions{
		ProcessStatusCol: processStatusCol,
		ID:               "example_process",
		FileName:         "example_process.go",
		UpdateInterval:   5 * time.Minute,
		Multiplier:       1.0,
	})

	if monitor == nil {
		return nil, fmt.Errorf("failed to create process monitor")
	}

	// Create a cancellable context
	ctx, cancel := context.WithCancel(context.Background())

	return &ExampleProcess{
		monitor: monitor,
		ctx:     ctx,
		cancel:  cancel,
	}, nil
}

// Run starts the example process
func (ep *ExampleProcess) Run() error {
	// Start monitoring
	ep.monitor.StartMonitoring()

	// Set up exit handler
	exitHandler := ep.monitor.GetHandleExitFnBasic(func(errorMsg string) {
		if errorMsg != "" {
			golog.Error("Process exited with error", "error", errorMsg)
		}
		// Cancel the context to stop all goroutines
		ep.cancel()
	})

	// Handle exit signals
	gohelper.HandleExitSignal("example_process", func(msg string) {
		golog.Info("Received exit signal", "message", msg)
		exitHandler(msg)
	})

	// Update initial status
	err := ep.monitor.UpdateProcessStatus(gohelper.RunningNormal, nil, nil, nil)
	if err != nil {
		return fmt.Errorf("failed to update initial status: %v", err)
	}

	// Example MongoDB streaming operations
	go func() {
		// Get collection
		coll := gomongo.Coll("vow", "properties")

		// Get query parameters
		query := bson.M{
			"status": "A",
		}

		options := gomongo.QueryOptions{
			Projection: bson.D{
				{Key: "status", Value: 1},
				{Key: "lp", Value: 1},
			},
			Sort: bson.D{{Key: "lp", Value: 1}},
		}

		// Get cursor
		cursor, err := coll.Find(ep.ctx, query, options)
		if err != nil {
			golog.Error("Failed to execute query", "error", err)
			exitHandler(fmt.Sprintf("Failed to execute query: %v", err))
			return
		}

		// Set up streaming options
		opts := gohelper.StreamingOptions{
			Stream: cursor,
			Process: func(item interface{}) error {
				// Record operation
				ep.monitor.RecordOperation("process_item")

				// Process item
				golog.Info("Processing item", "data", item)
				return nil
			},
			End: func(err error) {
				if err != nil {
					golog.Error("Stream ended with error", "error", err)
					exitHandler(fmt.Sprintf("Stream ended with error: %v", err))
				} else {
					golog.Info("Stream completed successfully")
					exitHandler("")
				}
			},
			Error: func(err error) {
				golog.Error("Processing error", "error", err)
				exitHandler(fmt.Sprintf("Processing error: %v", err))
			},
			High:          10,
			Low:           0,
			SpeedInterval: 100,
			Verbose:       3,
			Event:         "mongoData",
		}

		// Start streaming
		err = gohelper.Streaming(ep.ctx, &opts)
		if err != nil {
			golog.Error("Failed to stream data", "error", err)
			exitHandler(fmt.Sprintf("Failed to stream data: %v", err))
		}
	}()

	return nil
}

// Stop gracefully stops the process
func (ep *ExampleProcess) Stop() {
	golog.Info("Stopping process...")
	ep.cancel()
	ep.monitor.StopMonitoring()
}

func main() {
	// Create a channel to receive OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	process, err := NewExampleProcess()
	if err != nil {
		golog.Fatal("Failed to create process", "error", err)
	}

	err = process.Run()
	if err != nil {
		golog.Fatal("Process failed", "error", err)
	}

	// Wait for a signal
	sig := <-sigChan
	golog.Info("Received signal", "signal", sig)

	// Stop the process gracefully
	process.Stop()

	// Give some time for cleanup
	time.Sleep(1 * time.Second)
	golog.Info("Process stopped")
}
