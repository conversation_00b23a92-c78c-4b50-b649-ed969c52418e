package gowatch

import (
	"context"
	"fmt"
	"sync"
	"time"

	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// ProcessChangedObjectOptions contains options for processing changed objects
type ProcessChangedObjectOptions struct {
	ChangedObj            bson.M
	WatchedColl           *gomongo.MongoCollection
	DeleteOneFn           func(interface{}) error
	IgnoreDelete          bool
	UpdateOneFn           func(bson.M) error
	UpdateOneFnWithFields func(bson.M, bson.M) error
	ReplaceOneFn          func(bson.M) error
	InsertOneFn           func(bson.M) error
	ChangeStatusOnlyFn    func(bson.M) error
	SkipChangeMtOnly      bool
	WatchedStream         *WatchObject
}

// ProcessChangedObject processes a changed object from the change stream
func ProcessChangedObject(opt ProcessChangedObjectOptions) error {
	golog.Debug("processChangedObject", opt.ChangedObj["operationType"], opt.ChangedObj)

	// Handle invalidate event
	if opt.ChangedObj["operationType"] == "invalidate" {
		golog.Error("invalid event")
		return fmt.Errorf("invalid event, please do init again")
	}

	// Check if operation type is valid
	validTypes := map[string]bool{
		"insert":  true,
		"update":  true,
		"delete":  true,
		"replace": true,
	}

	opRaw, ok := opt.ChangedObj["operationType"]
	if !ok {
		golog.Warn("operationType missing", "operationType", opt.ChangedObj)
		return nil
	}
	opType, ok := opRaw.(string)
	if !ok {
		golog.Error("operationType is not string", opRaw)
		return nil
	}

	if !validTypes[opType] {
		golog.Verbose("ignore:", "operationType", opt.ChangedObj["operationType"], "opt", opt.ChangedObj)
		return nil
	}

	// Get document ID
	docKey, ok := opt.ChangedObj["documentKey"].(bson.M)
	if !ok {
		golog.Error("no documentKey", opt.ChangedObj)
		return nil
	}

	id, ok := docKey["_id"]
	if !ok {
		golog.Error("no id", opt.ChangedObj)
		return nil
	}

	var err error

	doDelete := (func() bool {
		if opt.IgnoreDelete {
			return false
		}
		if opt.ChangedObj["operationType"] == "delete" {
			return true
		}
		fullDoc, ok := opt.ChangedObj["fullDocument"].(bson.M)
		if !ok {
			return false
		}
		return fullDoc["del"] == 1
	})()

	if doDelete {
		if opt.DeleteOneFn == nil {
			return fmt.Errorf("need deleteOne function")
		}
		golog.Info("delete", "id", id, "in target, no update")
		err = opt.DeleteOneFn(id)
		if err != nil {
			golog.Error("delete error:", err)
			return err
		}
		golog.Verbose("delete success:", "documentKey", opt.ChangedObj["documentKey"])
	} else if opt.ChangedObj["operationType"] == "delete" {
		golog.Info("delete", "id", id, "in target, no update")
	} else {
		// Check for _mt only updates
		updateDescAny := opt.ChangedObj["updateDescription"]
		updateDesc, _ := updateDescAny.(bson.M)
		if updateDesc != nil {
			updatedFields, _ := updateDesc["updatedFields"].(bson.M)
			changedFieldsNumber := 0
			if updatedFields != nil {
				changedFieldsNumber = len(updatedFields)
			}
			removedFieldsAny := updateDesc["removedFields"]
			var removedFieldsNumber int
			if arr, ok := removedFieldsAny.(bson.A); ok {
				removedFieldsNumber = len(arr)
			}

			if changedFieldsNumber > 0 && removedFieldsNumber == 0 {
				// Check for _mt only update
				if changedFieldsNumber == 1 && updatedFields["_mt"] != nil {
					golog.Debug("change _mt only, no update")
					return nil
				}

				// Check for mt only update
				if changedFieldsNumber == 1 && updatedFields["mt"] != nil && opt.SkipChangeMtOnly {
					golog.Debug("change mt only, no update")
					return nil
				}

				// Check for status only update
				changeStatusOnly := updatedFields["status"] != nil &&
					(changedFieldsNumber == 1 ||
						(changedFieldsNumber == 2 && updatedFields["mt"] != nil))

				if opt.ChangeStatusOnlyFn != nil && changeStatusOnly {
					err = opt.ChangeStatusOnlyFn(bson.M{
						"id":              id,
						"prop":            updatedFields,
						"watchedCollName": opt.WatchedColl.Name(),
					})
				}
			}
		}

		// Process update with full document
		if err == nil && opt.ChangedObj["fullDocument"] != nil {
			golog.Debug("change fullDocument", opt.ChangedObj["fullDocument"])
			fullDoc, ok := opt.ChangedObj["fullDocument"].(bson.M)
			if !ok {
				golog.Error("invalid fullDocument format in change stream")
				return nil
			}

			var updatedFields bson.M
			if updateDesc["updatedFields"] != nil {
				updatedFields, ok = updateDesc["updatedFields"].(bson.M)
				if !ok {
					golog.Error("invalid updatedFields format in change stream")
					updatedFields = bson.M{}
				}
			}

			err = processUpdate(ProcessUpdateOptions{
				ReplaceOneFn:          opt.ReplaceOneFn,
				UpdateOneFn:           opt.UpdateOneFn,
				UpdateOneFnWithFields: opt.UpdateOneFnWithFields,
				InsertOneFn:           opt.InsertOneFn,
				OperationType:         opt.ChangedObj["operationType"].(string),
				FullDocument:          fullDoc,
				UpdatedFields:         updatedFields,
			})
		} else if err == nil {
			// Find document in watched collection
			var prop bson.M
			err = opt.WatchedColl.FindOne(context.Background(), bson.M{"_id": id}).Decode(&prop)
			if err != nil {
				if err == mongo.ErrNoDocuments {
					golog.Error("no fullDocument, can not find", id, "in watchedColl")
					return nil
				}
				return err
			}

			golog.Info("no fullDocument, find", id, "in watchedColl")
			updateFieldsRaw := updateDesc["updatedFields"]
			updateFields, ok := updateFieldsRaw.(bson.M)
			if !ok {
				golog.Error("invalid updatedFields format in change stream")
				return nil
			}

			err = processUpdate(ProcessUpdateOptions{
				ReplaceOneFn:          opt.ReplaceOneFn,
				UpdateOneFn:           opt.UpdateOneFn,
				UpdateOneFnWithFields: opt.UpdateOneFnWithFields,
				InsertOneFn:           opt.InsertOneFn,
				OperationType:         opt.ChangedObj["operationType"].(string),
				FullDocument:          prop,
				UpdatedFields:         updateFields,
			})
		}
	}

	// Update token after processing if no error
	if err == nil && opt.WatchedStream != nil && opt.ChangedObj["_id"] != nil && opt.ChangedObj["clusterTime"] != nil {
		opt.WatchedStream.tokenMutex.Lock()
		if id, ok := opt.ChangedObj["_id"].(bson.M); ok {
			opt.WatchedStream.currentToken = &id
		}
		if clusterTime, ok := opt.ChangedObj["clusterTime"].(primitive.Timestamp); ok {
			opt.WatchedStream.currentTokenClusterTs = time.Unix(int64(clusterTime.T), 0)
		}
		opt.WatchedStream.tokenMutex.Unlock()
		opt.WatchedStream.currentTokenChangeTs = time.Now()

		golog.Debug("update currentToken after finished process changed event",
			"currentToken", opt.WatchedStream.currentToken,
			"currentTokenClusterTs", opt.WatchedStream.currentTokenClusterTs,
			"savedToken", opt.WatchedStream.savedToken)
	}

	return err
}

// ProcessUpdateOptions contains options for processing updates
type ProcessUpdateOptions struct {
	UpdateOneFn           func(bson.M) error
	UpdateOneFnWithFields func(bson.M, bson.M) error
	InsertOneFn           func(bson.M) error
	ReplaceOneFn          func(bson.M) error
	OperationType         string
	FullDocument          bson.M
	UpdatedFields         bson.M
}

var (
	gImmediateUpdatedId = struct {
		ID   interface{}
		Type string
		TS   time.Time
	}{}
	gImmediateUpdatedIdMutex sync.Mutex
)

// processUpdate processes an update operation
func processUpdate(opt ProcessUpdateOptions) error {
	// Check for duplicate updates
	gImmediateUpdatedIdMutex.Lock()
	if gImmediateUpdatedId.ID == opt.FullDocument["_id"] &&
		time.Since(gImmediateUpdatedId.TS) < 2*time.Second {
		gImmediateUpdatedIdMutex.Unlock()
		golog.Error("Error: watch update with same record_id:", opt.FullDocument["_id"])
		return nil
	}

	// Update global tracking
	gImmediateUpdatedId = struct {
		ID   interface{}
		Type string
		TS   time.Time
	}{
		ID:   opt.FullDocument["_id"],
		Type: opt.OperationType,
		TS:   time.Now(),
	}
	gImmediateUpdatedIdMutex.Unlock()

	// Handle replace operation
	if opt.OperationType == "replace" {
		if opt.ReplaceOneFn == nil {
			return fmt.Errorf("need replaceOne function")
		}
		return opt.ReplaceOneFn(opt.FullDocument)
	}

	// Handle insert operation with insertOneFn
	if opt.OperationType == "insert" && opt.InsertOneFn != nil {
		return opt.InsertOneFn(opt.FullDocument)
	}

	// Handle insert or update operations
	if opt.OperationType == "insert" || opt.OperationType == "update" {
		golog.Debug("processUpdate")
		if opt.UpdateOneFnWithFields != nil {
			if err := opt.UpdateOneFnWithFields(opt.FullDocument, opt.UpdatedFields); err != nil {
				golog.Error("update/insert error:", err)
				return err
			}
			golog.Debug("update/insert success:", opt.FullDocument, opt.UpdatedFields)
			return nil
		}
		if opt.UpdateOneFn != nil {
			if err := opt.UpdateOneFn(opt.FullDocument); err != nil {
				golog.Error("update error:", err)
				return err
			}
			golog.Debug("update success:", opt.FullDocument)
			return nil
		}
		return fmt.Errorf("need updateOne function")
	}

	golog.Error("Not supported operationType", opt.OperationType)
	return nil
}

// saveChangedObj saves the changed object to the import collection
func saveChangedObj(col *gomongo.MongoCollection, changedObj bson.M) error {
	if col == nil {
		return nil
	}
	insertObj := bson.M{
		"id":                changedObj["documentKey"],
		"operationType":     changedObj["operationType"],
		"ns":                changedObj["ns"],
		"updateDescription": changedObj["updateDescription"],
		"clusterTime":       changedObj["clusterTime"],
		"token":             changedObj["_id"],
	}
	_, err := col.InsertOne(context.Background(), insertObj)
	if err != nil {
		golog.Error("when insert into importWatchedRecordsCol, error:", err, "changedObj", changedObj)
	}
	return err
}
