package gohelper

import (
	"fmt"
	"log"
	"os"
)

// 假设这是你的配置结构体
type Config struct {
	ServerBase struct {
		DeveloperMode bool `json:"developer_mode"`
	}
	Contact struct {
		AlertSMS string `json:"alertSMS"`
	}
}

// 假设这些变量是你程序中预先加载的全局配置和当前任务
var cfg Config
var batchFile = "batch.go"

// sendSMS 模拟短信服务（你应该替换为真实实现）
func sendSMS(to, from, body string) error {
	// 模拟发送短信
	fmt.Printf("[SMS] To: %s | From: %s | Body: %s\n", to, from, body)
	return nil
}

// errorNotify 负责日志 + 发送错误短信（非开发模式）
func ErrorNotify(err error) {
	if err == nil {
		return
	}

	errMsg := err.Error()
	log.Printf("[ERROR] %s", errMsg)

	// 开发模式不发送通知
	if cfg.ServerBase.DeveloperMode {
		return
	}

	to := cfg.Contact.AlertSMS
	if to == "" {
		to = "6472223733" // fallback
	}
	body := fmt.Sprintf("%s at %s in %s", errMsg, hostname(), batchFile)
	from := "+19056142609"

	if err := sendSMS(to, from, body); err != nil {
		log.Printf("[ERROR] failed to send SMS: %v", err)
	}
}

// 获取主机名
func hostname() string {
	name, err := os.Hostname()
	if err != nil {
		return "unknown-host"
	}
	return name
}
