package goresodownload

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"testing"
	"time"

	gofile "github.com/real-rm/gofile"
	gohelper "github.com/real-rm/gohelper"
	gomongo "github.com/real-rm/gomongo"
	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// setupTest sets up the test environment
func setupTest(t *testing.T) (*gomongo.MongoCollection, func(*gomongo.MongoCollection)) {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}
	gohelper.SetRmbaseFileCfg(configPath)

	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}

	// Initialize MongoDB
	if err := gomongo.InitMongoDB(); err != nil {
		t.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Create test collections
	testColl := gomongo.Coll("tmp", "mediaDiff")
	if testColl == nil {
		t.Fatalf("Failed to get test collection")
	}

	// Create logs collections for each board
	for _, table := range BoardLogsTable {
		// Create collection in test database instead of rni
		coll := gomongo.Coll("tmp", table)
		if coll == nil {
			t.Fatalf("Failed to get logs collection: %s", table)
		}
		// Clean up collection before test
		if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
			t.Errorf("Failed to cleanup collection %s: %v", table, err)
		}
	}

	// Clean up test collection before test
	if _, err := testColl.DeleteMany(context.Background(), bson.M{}); err != nil {
		t.Errorf("Failed to cleanup test collection: %v", err)
	}

	// Create test directories for treb board
	testDir := filepath.Join(currentDir, "test_data")
	if err := os.MkdirAll(testDir, 0755); err != nil {
		t.Fatalf("Failed to create test directory: %v", err)
	}

	// Mock gofile.GetImageDir for treb board
	originalGetImageDir := gofile.GetImageDir
	gofile.GetImageDir = func(board string) []string {
		if board == "TRB" {
			return []string{testDir}
		}
		return originalGetImageDir(board)
	}

	// Return collection and cleanup function
	return testColl, func(coll *gomongo.MongoCollection) {
		// Clean up test collection after test
		if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}
		// Clean up logs collections
		for _, table := range BoardLogsTable {
			if logsColl := gomongo.Coll("tmp", table); logsColl != nil {
				if _, err := logsColl.DeleteMany(context.Background(), bson.M{}); err != nil {
					t.Errorf("Failed to cleanup logs collection %s: %v", table, err)
				}
			}
		}
		// Restore original GetImageDir function
		gofile.GetImageDir = originalGetImageDir
		// Clean up test directory
		if err := os.RemoveAll(testDir); err != nil {
			t.Errorf("Failed to cleanup test directory: %v", err)
		}
	}
}

// TestMain runs before all tests
func TestMain(m *testing.M) {
	// Run tests
	code := m.Run()

	// Clean up after all tests
	coll := gomongo.Coll("tmp", "mediaDiff")
	if coll != nil {
		if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
			fmt.Printf("Failed to cleanup collection after all tests: %v\n", err)
		}
	}

	os.Exit(code)
}

func TestGetOldMediaFromLogs(t *testing.T) {
	// Setup test environment
	coll, cleanup := setupTest(t)
	defer cleanup(coll)

	// Test cases
	testCases := []struct {
		name          string
		board         string
		sid           string
		insertDoc     bson.M
		expectedMedia []bson.M
		expectedError bool
		errorContains string
	}{
		{
			name:  "empty_media_array",
			board: "CAR",
			sid:   "test-sid",
			insertDoc: bson.M{
				"sid": "test-sid",
				"data": bson.M{
					"media": []interface{}{},
				},
			},
			expectedMedia: []bson.M{},
			expectedError: false,
		},
		{
			name:  "valid_media_array",
			board: "CAR",
			sid:   "test-sid-2",
			insertDoc: bson.M{
				"sid": "test-sid-2",
				"data": bson.M{
					"media": []interface{}{
						bson.M{
							"MediaKey":      "key1",
							"MediaURL":      "http://example.com/1.jpg",
							"MimeType":      "image/jpeg",
							"MediaCategory": "Photo",
						},
						bson.M{
							"MediaKey":      "key2",
							"MediaURL":      "http://example.com/2.pdf",
							"MimeType":      "application/pdf",
							"MediaCategory": "Document",
						},
					},
				},
			},
			expectedMedia: []bson.M{
				{
					"MediaKey":      "key1",
					"MediaURL":      "http://example.com/1.jpg",
					"MimeType":      "image/jpeg",
					"MediaCategory": "Photo",
				},
				{
					"MediaKey":      "key2",
					"MediaURL":      "http://example.com/2.pdf",
					"MimeType":      "application/pdf",
					"MediaCategory": "Document",
				},
			},
			expectedError: false,
		},
		{
			name:  "invalid_board",
			board: "invalid-board",
			sid:   "test-sid-3",
			insertDoc: bson.M{
				"sid": "test-sid-3",
				"data": bson.M{
					"media": []interface{}{},
				},
			},
			expectedMedia: nil,
			expectedError: true,
			errorContains: "unsupported board type",
		},
		{
			name:  "invalid_data_field",
			board: "CAR",
			sid:   "test-sid-4",
			insertDoc: bson.M{
				"sid":  "test-sid-4",
				"data": "invalid-data",
			},
			expectedMedia: nil,
			expectedError: true,
			errorContains: "invalid data field format",
		},
		{
			name:  "missing_media_field",
			board: "CAR",
			sid:   "test-sid-5",
			insertDoc: bson.M{
				"sid":  "test-sid-5",
				"data": bson.M{},
			},
			expectedMedia: []bson.M{},
			expectedError: false,
		},
		{
			name:  "nil_media_field",
			board: "CAR",
			sid:   "test-sid-6",
			insertDoc: bson.M{
				"sid": "test-sid-6",
				"data": bson.M{
					"media": nil,
				},
			},
			expectedMedia: []bson.M{},
			expectedError: false,
		},
		{
			name:  "invalid_media_type",
			board: "CAR",
			sid:   "test-sid-7",
			insertDoc: bson.M{
				"sid": "test-sid-7",
				"data": bson.M{
					"media": "invalid-media",
				},
			},
			expectedMedia: nil,
			expectedError: true,
			errorContains: "invalid media field format",
		},
		{
			name:  "primitive_a_media",
			board: "CAR",
			sid:   "test-sid-8",
			insertDoc: bson.M{
				"sid": "test-sid-8",
				"data": bson.M{
					"media": primitive.A{
						bson.M{
							"MediaKey":      "key1",
							"MediaURL":      "http://example.com/1.jpg",
							"MimeType":      "image/jpeg",
							"MediaCategory": "Photo",
						},
					},
				},
			},
			expectedMedia: []bson.M{
				{
					"MediaKey":      "key1",
					"MediaURL":      "http://example.com/1.jpg",
					"MimeType":      "image/jpeg",
					"MediaCategory": "Photo",
				},
			},
			expectedError: false,
		},
		{
			name:  "empty_primitive_a_media",
			board: "CAR",
			sid:   "test-sid-9",
			insertDoc: bson.M{
				"sid": "test-sid-9",
				"data": bson.M{
					"media": primitive.A{},
				},
			},
			expectedMedia: []bson.M{},
			expectedError: false,
		},
	}

	// Run test cases
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Get logs collection for the board
			logsTable, exists := BoardLogsTable[tc.board]
			if !exists {
				// For invalid board test case, we don't need to insert data
				if tc.expectedError {
					analyzer := NewMediaDiffAnalyzer()
					_, err := analyzer.getOldMediaFromLogs(tc.sid, tc.board)
					if err == nil {
						t.Error("Expected error but got nil")
					} else if !strings.Contains(err.Error(), tc.errorContains) {
						t.Errorf("Expected error to contain '%s', got '%s'", tc.errorContains, err.Error())
					}
					return
				}
				t.Fatalf("Invalid board type: %s", tc.board)
			}

			// Get logs collection
			logsColl := gomongo.Coll("tmp", logsTable)
			if logsColl == nil {
				t.Fatalf("Failed to get logs collection: %s", logsTable)
			}

			// Clean up existing data
			_, err := logsColl.DeleteMany(context.Background(), bson.M{"sid": tc.sid})
			if err != nil {
				t.Fatalf("Failed to cleanup existing data: %v", err)
			}

			// Insert test document
			_, err = logsColl.InsertOne(context.Background(), tc.insertDoc)
			if err != nil {
				t.Fatalf("Failed to insert test document: %v", err)
			}

			// Create analyzer
			analyzer := NewMediaDiffAnalyzer()

			// Get old media
			media, err := analyzer.getOldMediaFromLogs(tc.sid, tc.board)

			// Check error
			if tc.expectedError {
				if err == nil {
					t.Error("Expected error but got nil")
				} else if tc.errorContains != "" && !strings.Contains(err.Error(), tc.errorContains) {
					t.Errorf("Expected error to contain '%s', got '%s'", tc.errorContains, err.Error())
				}
			} else if err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			// Check media array
			if !reflect.DeepEqual(media, tc.expectedMedia) {
				t.Errorf("Expected media %v, got %v", tc.expectedMedia, media)
			}
		})
	}
}

func TestBoardLogsTable(t *testing.T) {
	tests := []struct {
		name     string
		board    string
		expected string
		exists   bool
	}{
		{"car board", "CAR", "mls_car_logs", true},
		{"crea board", "DDF", "reso_crea_logs", true},
		{"bcre board", "BRE", "bridge_bcre_logs", true},
		{"rae board", "EDM", "mls_rae_logs", true},
		{"invalid board", "invalid", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			table, exists := BoardLogsTable[tt.board]
			assert.Equal(t, tt.exists, exists)
			if exists {
				assert.Equal(t, tt.expected, table)
			}
		})
	}
}

func TestGenerateFileName(t *testing.T) {
	tests := []struct {
		name    string
		media   bson.M
		want    string
		wantErr bool
	}{
		{
			name: "valid media",
			media: bson.M{
				"key": "test",
				"url": "https://example.com/test.jpg",
			},
			want:    "testsid_DZpM91.jpg",
			wantErr: false,
		},
		{
			name:    "empty media",
			media:   bson.M{},
			want:    "",
			wantErr: true,
		},
		{
			name: "missing key",
			media: bson.M{
				"url": "https://example.com/test.jpg",
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "missing url",
			media: bson.M{
				"key": "test",
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "nil key",
			media: bson.M{
				"key": nil,
				"url": "https://example.com/test.jpg",
			},
			want:    "",
			wantErr: true,
		},
	}

	analyzer := NewMediaDiffAnalyzer()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := analyzer.generateFileName("testsid", tt.media)
			if tt.wantErr {
				if err == nil {
					t.Error("Expected error, got nil")
				}
				return
			}
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}
			if got != tt.want {
				t.Errorf("Expected %q, got %q", tt.want, got)
			}
		})
	}
}

func TestCheckFileExists(t *testing.T) {
	tests := []struct {
		name       string
		fileName   string
		localPaths []string
		localFiles map[string]bool
		want       bool
	}{
		{
			name:       "file exists",
			fileName:   "test.jpg",
			localPaths: []string{"/tmp/test"},
			localFiles: map[string]bool{
				"/tmp/test/test.jpg": true,
			},
			want: true,
		},
		{
			name:       "file does not exist",
			fileName:   "test.jpg",
			localPaths: []string{"/tmp/test"},
			localFiles: map[string]bool{
				"/tmp/test/other.jpg": true,
			},
			want: false,
		},
		{
			name:       "empty paths",
			fileName:   "test.jpg",
			localPaths: []string{},
			localFiles: map[string]bool{},
			want:       false,
		},
		{
			name:       "empty files",
			fileName:   "test.jpg",
			localPaths: []string{"/tmp/test"},
			localFiles: map[string]bool{},
			want:       false,
		},
	}

	analyzer := NewMediaDiffAnalyzer()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := analyzer.checkFileExists(tt.fileName, tt.localPaths, tt.localFiles)
			if got != tt.want {
				t.Errorf("Expected %v, got %v", tt.want, got)
			}
		})
	}
}

func TestExtractMediaInfo(t *testing.T) {
	tests := []struct {
		name      string
		media     bson.M
		isTreb    bool
		want      *MediaItemInfo
		wantError bool
	}{
		{
			name: "valid treb media",
			media: bson.M{
				"key": "abc",
				"url": "https://example.com/image.jpg",
				"tp":  "image/jpeg",
				"cat": "Photo",
			},
			isTreb: true,
			want: &MediaItemInfo{
				MediaKey: "abc",
				URL:      "https://example.com/image.jpg",
				Type:     "image/jpeg",
				Category: "Photo",
				IsPhoto:  true,
			},
			wantError: false,
		},
		{
			name: "valid regular media",
			media: bson.M{
				"MediaKey":      "abc",
				"URL":           "https://example.com/image.jpg",
				"MediaCategory": "Photo",
			},
			isTreb: false,
			want: &MediaItemInfo{
				MediaKey: "abc",
				URL:      "https://example.com/image.jpg",
				Type:     "image/jpeg",
				Category: "Photo",
				IsPhoto:  true,
			},
			wantError: false,
		},
		{
			name:      "nil media",
			media:     nil,
			isTreb:    false,
			want:      nil,
			wantError: true,
		},
		{
			name: "missing key",
			media: bson.M{
				"URL":           "https://example.com/image.jpg",
				"MediaCategory": "Photo",
			},
			isTreb:    false,
			want:      nil,
			wantError: true,
		},
		{
			name: "missing URL",
			media: bson.M{
				"MediaKey":      "abc",
				"MediaCategory": "Photo",
			},
			isTreb:    false,
			want:      nil,
			wantError: true,
		},
		{
			name: "missing category",
			media: bson.M{
				"MediaKey": "abc",
				"URL":      "https://example.com/image.jpg",
			},
			isTreb: false,
			want: &MediaItemInfo{
				MediaKey: "abc",
				URL:      "https://example.com/image.jpg",
				Type:     "image/jpeg",
				Category: "Photo",
				IsPhoto:  true,
			},
			wantError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			analyzer := NewMediaDiffAnalyzer()
			got, err := analyzer.extractMediaInfo(tt.media, tt.isTreb)

			if tt.wantError {
				if err == nil {
					t.Error("Expected error but got nil")
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if got == nil {
				t.Error("Expected non-nil result")
				return
			}

			if got.MediaKey != tt.want.MediaKey {
				t.Errorf("MediaKey = %v, want %v", got.MediaKey, tt.want.MediaKey)
			}
			if got.URL != tt.want.URL {
				t.Errorf("URL = %v, want %v", got.URL, tt.want.URL)
			}
			if got.Type != tt.want.Type {
				t.Errorf("Type = %v, want %v", got.Type, tt.want.Type)
			}
			if got.IsPhoto != tt.want.IsPhoto {
				t.Errorf("IsPhoto = %v, want %v", got.IsPhoto, tt.want.IsPhoto)
			}
		})
	}
}

func TestFilterMedias(t *testing.T) {
	tests := []struct {
		name       string
		medias     []bson.M
		wantPhotos int
		wantDocs   int
	}{
		{
			name: "mixed media types",
			medias: []bson.M{
				{
					"id":       "1",
					"url":      "https://example.com/photo1.jpg",
					"tp":       "image/jpeg",
					"cat":      "Photo",
					"status":   "Active",
					"sizeDesc": "LargestNoWatermark",
				},
				{
					"id":       "1",
					"url":      "https://example.com/photo1_thumb.jpg",
					"tp":       "image/jpeg",
					"cat":      "Photo",
					"status":   "Active",
					"sizeDesc": "Thumbnail",
				},
				{
					"id":     "2",
					"url":    "https://example.com/doc1.pdf",
					"tp":     "application/pdf",
					"cat":    "Document",
					"status": "Active",
				},
			},
			wantPhotos: 1, // Only the largest photo should be selected
			wantDocs:   1,
		},
		{
			name: "inactive media",
			medias: []bson.M{
				{
					"id":     "1",
					"url":    "https://example.com/photo1.jpg",
					"type":   "image/jpeg",
					"cat":    "Photo",
					"status": "Inactive",
				},
			},
			wantPhotos: 0,
			wantDocs:   0,
		},
		{
			name:       "empty media list",
			medias:     []bson.M{},
			wantPhotos: 0,
			wantDocs:   0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			photos, docs := FilterMedias(tt.medias)
			assert.Equal(t, tt.wantPhotos, len(photos))
			assert.Equal(t, tt.wantDocs, len(docs))
		})
	}
}

func TestGenerateHashList(t *testing.T) {
	tests := []struct {
		name    string
		medias  []bson.M
		want    []int
		wantErr bool
	}{
		{
			name: "valid media list",
			medias: []bson.M{
				{
					"key": "key1",
					"cat": "Photo",
				},
				{
					"key": "key2",
					"cat": "Photo",
				},
			},
			want:    []int{int(gofile.MurmurToInt32("key1")), int(gofile.MurmurToInt32("key2"))},
			wantErr: false,
		},
		{
			name:    "empty media list",
			medias:  []bson.M{},
			want:    []int{},
			wantErr: false,
		},
		{
			name: "missing key",
			medias: []bson.M{
				{
					"other": "value",
				},
			},
			want:    []int{},
			wantErr: true,
		},
		{
			name: "nil key",
			medias: []bson.M{
				{
					"key": nil,
				},
			},
			want:    []int{},
			wantErr: true,
		},
	}

	analyzer := NewMediaDiffAnalyzer()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := analyzer.generatePhotoHashList(tt.medias)
			if tt.wantErr {
				if len(got) != 0 {
					t.Errorf("Expected empty list, got %v", got)
				}
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Expected %v hashes, got %v", tt.want, got)
			}
		})
	}
}

func TestGenerateDeleteTasks(t *testing.T) {
	tests := []struct {
		name          string
		deletedMedias []bson.M
		sid           string
		filePath      string
		wantLen       int
	}{
		{
			name: "single deleted media",
			deletedMedias: []bson.M{
				{"MK": "abc"},
			},
			sid:      "123",
			filePath: "/path/to/files",
			wantLen:  1,
		},
		{
			name: "multiple deleted media",
			deletedMedias: []bson.M{
				{"MK": "abc"},
				{"MK": "def"},
			},
			sid:      "123",
			filePath: "/path/to/files",
			wantLen:  2,
		},
		{
			name:          "no deleted media",
			deletedMedias: []bson.M{},
			sid:           "123",
			filePath:      "/path/to/files",
			wantLen:       0,
		},
	}

	analyzer := NewMediaDiffAnalyzer()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := analyzer.generateTrebDeleteTasks(tt.deletedMedias, tt.sid, tt.filePath)
			assert.Equal(t, tt.wantLen, len(got))
			for i, task := range got {
				assert.Equal(t, tt.sid, task.Sid)
				assert.Equal(t, tt.deletedMedias[i]["MK"], task.MediaKey)
				assert.Equal(t, tt.filePath, task.Path)
			}
		})
	}
}

func TestGenerateDeleteTasks_Int32ToBase62Error(t *testing.T) {
	// Save original function
	originalInt32ToBase62 := gofile.Int32ToBase62
	// Restore original function after test
	defer func() {
		gofile.Int32ToBase62 = originalInt32ToBase62
	}()

	// Mock Int32ToBase62 to return error
	gofile.Int32ToBase62 = func(n int32) (string, error) {
		return "", fmt.Errorf("mock error")
	}

	analyzer := NewMediaDiffAnalyzer()
	toDelete := []struct {
		hash    int32
		isPhoto bool
		ext     string
	}{
		{
			hash:    123,
			isPhoto: true,
			ext:     ".jpg",
		},
	}

	result := analyzer.generateDeleteTasks(toDelete, "test123", "/path/to/files")
	assert.Empty(t, result, "Expected no delete tasks when Int32ToBase62 returns error")
}

func TestMediaDiffAnalyzerRegularBoard(t *testing.T) {
	coll, cleanup := setupTest(t)
	defer cleanup(coll)

	// Create test data
	now := time.Now()
	changeDoc := bson.M{
		"fullDocument": bson.M{
			"_id":       "test123ID",
			"ListingId": "test123",
			"Media": []interface{}{
				bson.M{
					"MediaKey":      "key1",
					"MediaURL":      "http://example.com/1.jpg",
					"MimeType":      "image/jpeg",
					"MediaCategory": "Photo",
					"Order":         1,
				},
				bson.M{
					"MediaKey":      "key2",
					"MediaURL":      "http://example.com/2.pdf",
					"MimeType":      "application/pdf",
					"MediaCategory": "Document",
					"Order":         2,
				},
			},
			"ts": now,
		},
	}

	// Create old media in logs table
	oldMedia := []bson.M{
		{
			"MediaKey":      "key1",
			"MediaURL":      "http://example.com/1.jpg",
			"MimeType":      "image/jpeg",
			"MediaCategory": "Photo",
			"Order":         1,
		},
		{
			"MediaKey":      "key3",
			"MediaURL":      "http://example.com/3.jpg",
			"MimeType":      "image/jpeg",
			"MediaCategory": "Photo",
			"Order":         3,
		},
	}

	// Insert old media into logs table
	logsColl := gomongo.Coll("rni", "mls_car_logs")
	if logsColl == nil {
		t.Fatalf("Failed to get logs collection")
	}

	oldLog := bson.M{
		"_id":  "test123ID",
		"sid":  "test123",
		"_mt":  now.Add(-time.Hour),
		"data": bson.M{"media": oldMedia},
	}
	if _, err := logsColl.InsertOne(context.Background(), oldLog); err != nil {
		t.Fatalf("Failed to insert old log: %v", err)
	}

	// Create analyzer and analyze
	analyzer := NewMediaDiffAnalyzer()
	result, err := analyzer.Analyze(changeDoc, "CAR")
	if err != nil {
		t.Fatalf("Failed to analyze: %v", err)
	}
	// Verify results
	if len(result.DownloadTasks) != 1 {
		t.Errorf("Expected 1 download task, got %d", len(result.DownloadTasks))
	}
	if len(result.DeleteTasks) != 1 {
		t.Errorf("Expected 1 delete task, got %d", len(result.DeleteTasks))
	}
	if len(result.PhoLH) != 1 {
		t.Errorf("Expected 1 photo hash, got %d", len(result.PhoLH))
	}
	if len(result.DocLH) != 1 {
		t.Errorf("Expected 1 document hash, got %d", len(result.DocLH))
	}

	// Verify download task
	downloadTask := result.DownloadTasks[0]
	if downloadTask.Sid != "test123" {
		t.Errorf("Expected sid test123, got %s", downloadTask.Sid)
	}
	if downloadTask.MediaKey != "key2" {
		t.Errorf("Expected mediaKey key2, got %s", downloadTask.MediaKey)
	}
	if downloadTask.URL != "http://example.com/2.pdf" {
		t.Errorf("Expected URL http://example.com/2.pdf, got %s", downloadTask.URL)
	}
	if downloadTask.Type != "application/pdf" {
		t.Errorf("Expected type application/pdf, got %s", downloadTask.Type)
	}
	if downloadTask.IsPhoto != false {
		t.Error("Expected IsPhoto to be false")
	}

	// Verify delete task
	deleteTask := result.DeleteTasks[0]
	if deleteTask.Sid != "test123" {
		t.Errorf("Expected sid test123, got %s", deleteTask.Sid)
	}
	if deleteTask.MediaKey != "key3" {
		t.Errorf("Expected mediaKey key3, got %s", deleteTask.MediaKey)
	}
}

func TestMediaDiffAnalyzerTrebBoard(t *testing.T) {
	coll, cleanup := setupTest(t)
	defer cleanup(coll)

	// Create test data
	now := time.Now()
	changeDoc := bson.M{
		"fullDocument": bson.M{
			"_id":       "test123ID",
			"ListingId": "test123",
			"media": []interface{}{
				bson.M{
					"key":    "key1",
					"url":    "http://example.com/1.jpg",
					"tp":     "image/jpeg",
					"cat":    "Photo",
					"id":     "1",
					"status": "Active",
				},
				bson.M{
					"key":    "key2",
					"url":    "http://example.com/2.pdf",
					"tp":     "application/pdf",
					"cat":    "Document",
					"id":     "2",
					"status": "Active",
				},
			},
			"ts": now,
		},
	}

	// Create test directory
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	testDir := filepath.Join(currentDir, "test_data")
	if err := os.MkdirAll(testDir, 0755); err != nil {
		t.Fatalf("Failed to create test directory: %v", err)
	}
	defer func() {
		if err := os.RemoveAll(testDir); err != nil {
			t.Fatalf("Failed to remove test directory: %v", err)
		}
	}()

	// Create analyzer and analyze
	analyzer := NewMediaDiffAnalyzer()
	result, err := analyzer.Analyze(changeDoc, "TRB")
	if err != nil {
		t.Fatalf("Failed to analyze: %v", err)
	}

	// Verify results
	if len(result.DownloadTasks) != 2 {
		t.Errorf("Expected 2 download tasks, got %d", len(result.DownloadTasks))
	}
	if len(result.DeleteTasks) != 0 {
		t.Errorf("Expected 0 delete tasks, got %d", len(result.DeleteTasks))
	}
	if len(result.PhoLH) != 1 {
		t.Errorf("Expected 1 photo hash, got %d", len(result.PhoLH))
	}
	if len(result.DocLH) != 1 {
		t.Errorf("Expected 1 document hash, got %d", len(result.DocLH))
	}

	// Verify download tasks
	for _, task := range result.DownloadTasks {
		if task.Sid != "test123" {
			t.Errorf("Expected sid test123, got %s", task.Sid)
		}
		switch task.MediaKey {
		case "key1":
			if task.URL != "http://example.com/1.jpg" {
				t.Errorf("Expected URL http://example.com/1.jpg, got %s", task.URL)
			}
			if task.Type != "image/jpeg" {
				t.Errorf("Expected type image/jpeg, got %s", task.Type)
			}
			if !task.IsPhoto {
				t.Error("Expected IsPhoto to be true")
			}
		case "key2":
			if task.URL != "http://example.com/2.pdf" {
				t.Errorf("Expected URL http://example.com/2.pdf, got %s", task.URL)
			}
			if task.Type != "application/pdf" {
				t.Errorf("Expected type application/pdf, got %s", task.Type)
			}
			if task.IsPhoto {
				t.Error("Expected IsPhoto to be false")
			}
		default:
			t.Errorf("Unexpected mediaKey: %s", task.MediaKey)
		}
	}
}

func TestMediaDiffAnalyzerInvalidBoard(t *testing.T) {
	coll, cleanup := setupTest(t)
	defer cleanup(coll)

	// Create test data
	changeDoc := bson.M{
		"fullDocument": bson.M{
			"ListingId": "test123",
			"Media":     []interface{}{},
			"ts":        time.Now(),
		},
	}

	// Create analyzer and analyze
	analyzer := NewMediaDiffAnalyzer()
	_, err := analyzer.Analyze(changeDoc, "INVALID")
	if err == nil {
		t.Error("Expected error for invalid board")
	}
}

func TestMediaDiffAnalyzerInvalidChangeDoc(t *testing.T) {
	coll, cleanup := setupTest(t)
	defer cleanup(coll)

	// Create test data with invalid format
	changeDoc := bson.M{
		"fullDocument": bson.M{
			"Media": "invalid",
			"ts":    time.Now(),
		},
	}

	// Create analyzer and analyze
	analyzer := NewMediaDiffAnalyzer()
	_, err := analyzer.Analyze(changeDoc, "CAR")
	if err == nil {
		t.Error("Expected error for invalid change document")
	}
}

func TestGetTrebMediaFromDB(t *testing.T) {
	coll, cleanup := setupTest(t)
	defer cleanup(coll)

	// Create test data
	medias := []bson.M{
		{
			"RRK":  "test123",
			"MURL": "http://example.com/1.jpg",
			"tp":   "image/jpeg",
			"cat":  "Photo",
			"O":    "0",
			"MS":   "Deleted",
			"MK":   "5ba66c60-9ee8-4cee-b294-61674f388ab2-nw",
		},
		{
			"RRK":  "test123",
			"MURL": "http://example.com/2.jpg",
			"tp":   "image/jpeg",
			"cat":  "Photo",
			"O":    "1",
			"MS":   "Active",
			"MK":   "5ba66c60-9ee8-4cee-b294-61674f388ab2",
		},
	}
	// Convert []bson.M to []interface{}
	interfaceSlice := make([]interface{}, len(medias))
	for i, v := range medias {
		interfaceSlice[i] = v
	}
	mediaColl := gomongo.Coll("rni", "reso_treb_evow_media")
	if mediaColl == nil {
		t.Fatalf("Failed to get logs collection")
	}
	if _, err := mediaColl.InsertMany(context.Background(), interfaceSlice); err != nil {
		t.Fatalf("Failed to insert test data: %v", err)
	}

	// Create analyzer and analyze
	tests := []struct {
		name    string
		sid     string
		wantLen int
	}{
		{
			name:    "valid sid",
			sid:     "test123",
			wantLen: 1,
		},
		{
			name:    "empty sid",
			sid:     "",
			wantLen: 0,
		},
	}

	analyzer := NewMediaDiffAnalyzer()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := analyzer.getTrebMedia(tt.sid)
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
			}
			if len(got) != tt.wantLen {
				t.Errorf("Expected %d medias, got %d", tt.wantLen, len(got))
			}
		})
	}
	if _, err := mediaColl.DeleteMany(context.Background(), bson.M{}); err != nil {
		t.Fatalf("Failed to delete test data: %v", err)
	}
}

func TestSelectBestPhoto(t *testing.T) {
	tests := []struct {
		name          string
		photos        []bson.M
		sizePriority  []string
		expectedPhoto *bson.M
	}{
		{
			name: "highest_priority_size",
			photos: []bson.M{
				{
					"sizeDesc": "Thumbnail",
					"url":      "http://example.com/thumb.jpg",
				},
				{
					"sizeDesc": "LargestNoWatermark",
					"url":      "http://example.com/large.jpg",
				},
			},
			sizePriority: []string{
				"LargestNoWatermark",
				"Largest",
				"Large",
				"media",
				"Thumbnail",
			},
			expectedPhoto: &bson.M{
				"sizeDesc": "LargestNoWatermark",
				"url":      "http://example.com/large.jpg",
			},
		},
		{
			name: "no_size_description",
			photos: []bson.M{
				{
					"url": "http://example.com/photo.jpg",
				},
			},
			sizePriority: []string{
				"LargestNoWatermark",
				"Largest",
				"Large",
				"media",
				"Thumbnail",
			},
			expectedPhoto: &bson.M{
				"url": "http://example.com/photo.jpg",
			},
		},
		{
			name:          "empty_photos",
			photos:        []bson.M{},
			sizePriority:  []string{},
			expectedPhoto: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := selectBestPhoto(tt.photos, tt.sizePriority)
			if tt.expectedPhoto == nil {
				if got != nil {
					t.Errorf("Expected nil photo, got %v", got)
				}
			} else if !reflect.DeepEqual(got, tt.expectedPhoto) {
				t.Errorf("Expected photo %v, got %v", tt.expectedPhoto, got)
			}
		})
	}
}

func TestGetRegularMediaInfo(t *testing.T) {
	tests := []struct {
		name        string
		changeDoc   bson.M
		wantSid     string
		wantMedia   []bson.M
		wantTs      time.Time
		wantErr     bool
		errorString string
	}{
		{
			name: "valid change document",
			changeDoc: bson.M{
				"fullDocument": bson.M{
					"_id":       "test123ID",
					"ListingId": "test123",
					"Media": []interface{}{
						bson.M{
							"MediaKey":      "key1",
							"MediaURL":      "http://example.com/1.jpg",
							"MimeType":      "image/jpeg",
							"MediaCategory": "Photo",
						},
					},
					"ts": time.Now(),
				},
			},
			wantSid: "test123",
			wantMedia: []bson.M{
				{
					"MediaKey":      "key1",
					"MediaURL":      "http://example.com/1.jpg",
					"MimeType":      "image/jpeg",
					"MediaCategory": "Photo",
				},
			},
			wantTs:  time.Now(),
			wantErr: false,
		},
		{
			name: "missing ListingId",
			changeDoc: bson.M{
				"fullDocument": bson.M{
					"_id":   "test123ID",
					"Media": []interface{}{},
					"ts":    time.Now(),
				},
			},
			wantErr:     true,
			errorString: "invalid listingId or _id field format in change document",
		},
		{
			name: "invalid Media field",
			changeDoc: bson.M{
				"fullDocument": bson.M{
					"_id":       "test123ID",
					"ListingId": "test123",
					"Media":     "invalid",
					"ts":        time.Now(),
				},
			},
			wantErr:     true,
			errorString: "invalid media field format in change document",
		},
		{
			name: "missing ts field",
			changeDoc: bson.M{
				"fullDocument": bson.M{
					"_id":       "test123ID",
					"ListingId": "test123",
					"Media":     []interface{}{},
				},
			},
			wantErr:     true,
			errorString: "invalid ts field type: <nil>",
		},
	}

	analyzer := NewMediaDiffAnalyzer()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := analyzer.getRegularMediaInfo(tt.changeDoc)
			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got nil")
				} else if !strings.Contains(err.Error(), tt.errorString) {
					t.Errorf("Expected error to contain '%s', got '%s'", tt.errorString, err.Error())
				}
				return
			}
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}
			if got.Sid != tt.wantSid {
				t.Errorf("Expected sid %s, got %s", tt.wantSid, got.Sid)
			}
			if !reflect.DeepEqual(got.NewMediaList, tt.wantMedia) {
				t.Errorf("Expected media %v, got %v", tt.wantMedia, got.NewMediaList)
			}
		})
	}
}

func TestGetNewTrebMediaInfo(t *testing.T) {
	tests := []struct {
		name        string
		changeDoc   bson.M
		wantSid     string
		wantID      string
		wantMedia   []interface{}
		wantTs      time.Time
		wantErr     bool
		errorString string
	}{
		{
			name: "valid change document",
			changeDoc: bson.M{
				"fullDocument": bson.M{
					"_id":       "123",
					"ListingId": "test123",
					"media": []interface{}{
						bson.M{
							"key":  "key1",
							"url":  "http://example.com/1.jpg",
							"type": "image/jpeg",
							"cat":  "Photo",
						},
					},
					"ts": time.Now(),
				},
			},
			wantSid: "test123",
			wantID:  "123",
			wantMedia: []interface{}{
				bson.M{
					"key":  "key1",
					"url":  "http://example.com/1.jpg",
					"type": "image/jpeg",
					"cat":  "Photo",
				},
			},
			wantTs:  time.Now(),
			wantErr: false,
		},
		{
			name: "missing ListingId",
			changeDoc: bson.M{
				"fullDocument": bson.M{
					"_id":   "test123ID",
					"media": []interface{}{},
					"ts":    time.Now(),
				},
			},
			wantErr:     true,
			errorString: "invalid listingId or _id field format in change document",
		},
		{
			name: "missing ts field",
			changeDoc: bson.M{
				"fullDocument": bson.M{
					"_id":       "test123ID",
					"ListingId": "test123",
					"media":     []interface{}{},
				},
			},
			wantErr:     true,
			errorString: "invalid ts field type: <nil>",
		},
	}

	analyzer := NewMediaDiffAnalyzer()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotSid, gotID, gotMedia, gotTs, err := analyzer.getNewTrebMediaInfo(tt.changeDoc)
			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got nil")
				} else if !strings.Contains(err.Error(), tt.errorString) {
					t.Errorf("Expected error to contain '%s', got '%s'", tt.errorString, err.Error())
				}
				return
			}
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}
			if gotID != tt.wantID {
				t.Errorf("Expected id %s, got %s", tt.wantID, gotID)
			}
			if gotSid != tt.wantSid {
				t.Errorf("Expected sid %s, got %s", tt.wantSid, gotSid)
			}
			if !reflect.DeepEqual(gotMedia, tt.wantMedia) {
				t.Errorf("Expected media %v, got %v", tt.wantMedia, gotMedia)
			}
			if !gotTs.Equal(tt.wantTs) {
				// Compare only main time components
				gotMain := time.Date(gotTs.Year(), gotTs.Month(), gotTs.Day(), gotTs.Hour(), gotTs.Minute(), 0, 0, gotTs.Location())
				wantMain := time.Date(tt.wantTs.Year(), tt.wantTs.Month(), tt.wantTs.Day(), tt.wantTs.Hour(), tt.wantTs.Minute(), 0, 0, tt.wantTs.Location())
				if !gotMain.Equal(wantMain) {
					t.Errorf("Expected ts %v, got %v", wantMain, gotMain)
				}
			}
		})
	}
}

func TestPrepareRegularMediaTasks(t *testing.T) {
	tests := []struct {
		name          string
		taskInfo      *MediaTaskInfo
		wantDownloads int
		wantDeletes   int
		wantErr       bool
		errorString   string
	}{
		{
			name: "valid task info",
			taskInfo: &MediaTaskInfo{
				Sid: "test123",
				NewMedia: []bson.M{
					{
						"MediaKey":      "key1",
						"MediaURL":      "http://example.com/1.jpg",
						"MimeType":      "image/jpeg",
						"MediaCategory": "Photo",
					},
				},
				OldMedia: []bson.M{
					{
						"MediaKey":      "key2",
						"MediaURL":      "http://example.com/2.jpg",
						"MimeType":      "image/jpeg",
						"MediaCategory": "Photo",
					},
				},
				Timestamp: time.Now(),
				Board:     "CAR",
			},
			wantDownloads: 1,
			wantDeletes:   1,
			wantErr:       false,
		},
		{
			name: "invalid file path",
			taskInfo: &MediaTaskInfo{
				Sid:       "test123",
				NewMedia:  []bson.M{},
				OldMedia:  []bson.M{},
				Timestamp: time.Now(),
				Board:     "INVALID",
			},
			wantErr:     true,
			errorString: "invalid board: INVALID",
		},
	}

	analyzer := NewMediaDiffAnalyzer()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotDownloads, gotDeletes, err := analyzer.prepareRegularMediaTasks(tt.taskInfo)
			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got nil")
				} else if !strings.Contains(err.Error(), tt.errorString) {
					t.Errorf("Expected error to contain '%s', got '%s'", tt.errorString, err.Error())
				}
				return
			}
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}
			if len(gotDownloads) != tt.wantDownloads {
				t.Errorf("Expected %d download tasks, got %d", tt.wantDownloads, len(gotDownloads))
			}
			if len(gotDeletes) != tt.wantDeletes {
				t.Errorf("Expected %d delete tasks, got %d", tt.wantDeletes, len(gotDeletes))
			}
		})
	}
}

func TestPrepareTrebMediaTasks(t *testing.T) {
	coll, cleanup := setupTest(t)
	defer cleanup(coll)
	tests := []struct {
		name          string
		taskInfo      *TrebMediaTaskInfo
		wantDownloads int
		wantDeletes   int
		wantErr       bool
		errorString   string
	}{
		{
			name: "valid task info",
			taskInfo: &TrebMediaTaskInfo{
				Sid: "test123",
				NewMedias: []interface{}{
					bson.M{
						"key":    "key1",
						"url":    "http://example.com/1.jpg",
						"cat":    "Photo",
						"status": "Active",
						"tp":     "image/jpeg",
						"id":     "1",
					},
				},
				Timestamp:  time.Now(),
				LocalPaths: []string{"/tmp/test"},
			},
			wantDownloads: 1,
			wantDeletes:   0,
			wantErr:       false,
		},
		{
			name: "invalid file path",
			taskInfo: &TrebMediaTaskInfo{
				Sid:        "test123",
				NewMedias:  []interface{}{},
				Timestamp:  time.Now(),
				LocalPaths: []string{},
			},
			wantErr: false,
		},
	}

	analyzer := NewMediaDiffAnalyzer()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotDownloads, gotDeletes, _, _, err := analyzer.prepareTrebMediaTasks(tt.taskInfo)
			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got nil")
				} else if !strings.Contains(err.Error(), tt.errorString) {
					t.Errorf("Expected error to contain '%s', got '%s'", tt.errorString, err.Error())
				}
				return
			}
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}
			if len(gotDownloads) != tt.wantDownloads {
				t.Errorf("Expected %d download tasks, got %d", tt.wantDownloads, len(gotDownloads))
			}
			if len(gotDeletes) != tt.wantDeletes {
				t.Errorf("Expected %d delete tasks, got %d", tt.wantDeletes, len(gotDeletes))
			}
		})
	}
}

func TestGenerateDocHashList(t *testing.T) {
	tests := []struct {
		name    string
		medias  []bson.M
		want    []int
		wantErr bool
	}{
		{
			name: "valid document list",
			medias: []bson.M{
				{
					"key": "key1",
					"cat": "Document",
				},
				{
					"key": "key2",
					"cat": "Document",
				},
			},
			want:    []int{int(gofile.MurmurToInt32("key1")), int(gofile.MurmurToInt32("key2"))},
			wantErr: false,
		},
		{
			name: "mixed media types",
			medias: []bson.M{
				{
					"key": "key1",
					"cat": "Document",
				},
				{
					"key": "key2",
					"cat": "Photo",
				},
			},
			want:    []int{int(gofile.MurmurToInt32("key1"))},
			wantErr: false,
		},
		{
			name:    "empty media list",
			medias:  []bson.M{},
			want:    make([]int, 0),
			wantErr: false,
		},
		{
			name: "missing key",
			medias: []bson.M{
				{
					"other": "value",
					"cat":   "Document",
				},
			},
			want:    make([]int, 0),
			wantErr: false,
		},
		{
			name: "nil key",
			medias: []bson.M{
				{
					"key": nil,
					"cat": "Document",
				},
			},
			want:    make([]int, 0),
			wantErr: false,
		},
	}

	analyzer := NewMediaDiffAnalyzer()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := analyzer.generateDocHashList(tt.medias)
			if len(got) != len(tt.want) {
				t.Errorf("Expected %d hashes, got %d", len(tt.want), len(got))
				return
			}
			if len(got) > 0 && !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Expected %v hashes, got %v", tt.want, got)
			}
		})
	}
}

func TestAnalyzeMediaDiff(t *testing.T) {
	tests := []struct {
		name         string
		oldMedias    []bson.M
		newMedias    []bson.M
		wantDownload []bson.M
		wantDelete   []bson.M
		wantErr      bool
	}{
		{
			name: "new media added",
			oldMedias: []bson.M{
				{
					"key": "key1",
					"url": "http://example.com/1.jpg",
				},
			},
			newMedias: []bson.M{
				{
					"key": "key1",
					"url": "http://example.com/1.jpg",
				},
				{
					"key": "key2",
					"url": "http://example.com/2.jpg",
				},
			},
			wantDownload: []bson.M{
				{
					"key": "key2",
					"url": "http://example.com/2.jpg",
				},
			},
			wantDelete: make([]bson.M, 0),
			wantErr:    false,
		},
		{
			name: "media deleted",
			oldMedias: []bson.M{
				{
					"key": "key1",
					"url": "http://example.com/1.jpg",
				},
				{
					"key": "key2",
					"url": "http://example.com/2.jpg",
				},
			},
			newMedias: []bson.M{
				{
					"key": "key1",
					"url": "http://example.com/1.jpg",
				},
			},
			wantDownload: make([]bson.M, 0),
			wantDelete: []bson.M{
				{
					"key": "key2",
					"url": "http://example.com/2.jpg",
				},
			},
			wantErr: false,
		},
		{
			name: "no changes",
			oldMedias: []bson.M{
				{
					"key": "key1",
					"url": "http://example.com/1.jpg",
				},
			},
			newMedias: []bson.M{
				{
					"key": "key1",
					"url": "http://example.com/1.jpg",
				},
			},
			wantDownload: make([]bson.M, 0),
			wantDelete:   make([]bson.M, 0),
			wantErr:      false,
		},
		{
			name: "invalid media key",
			oldMedias: []bson.M{
				{
					"url": "http://example.com/1.jpg",
				},
			},
			newMedias: []bson.M{
				{
					"key": "key1",
					"url": "http://example.com/1.jpg",
				},
			},
			wantDownload: []bson.M{
				{
					"key": "key1",
					"url": "http://example.com/1.jpg",
				},
			},
			wantDelete: make([]bson.M, 0),
			wantErr:    false,
		},
	}

	analyzer := NewMediaDiffAnalyzer()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotDownload, gotDelete, err := analyzer.AnalyzeMediaDiff(tt.oldMedias, tt.newMedias)
			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got nil")
				}
				return
			}
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			// Compare download lists
			if len(gotDownload) != len(tt.wantDownload) {
				t.Errorf("Expected %d download items, got %d", len(tt.wantDownload), len(gotDownload))
			} else if len(gotDownload) > 0 && !reflect.DeepEqual(gotDownload, tt.wantDownload) {
				t.Errorf("Expected download %v, got %v", tt.wantDownload, gotDownload)
			}

			// Compare delete lists
			if len(gotDelete) != len(tt.wantDelete) {
				t.Errorf("Expected %d delete items, got %d", len(tt.wantDelete), len(gotDelete))
			} else if len(gotDelete) > 0 && !reflect.DeepEqual(gotDelete, tt.wantDelete) {
				t.Errorf("Expected delete %v, got %v", tt.wantDelete, gotDelete)
			}
		})
	}
}

func TestGenerateTrebMediaTasks(t *testing.T) {
	tests := []struct {
		name       string
		medias     []bson.M
		sid        string
		filePath   string
		localPaths []string
		localFiles map[string]bool
		wantTasks  int
		wantErr    bool
	}{
		{
			name: "valid media list",
			medias: []bson.M{
				{
					"key":    "key1",
					"url":    "http://example.com/1.jpg",
					"tp":     "image/jpeg",
					"cat":    "Photo",
					"status": "Active",
				},
			},
			sid:        "test123",
			filePath:   "/tmp/test",
			localPaths: []string{"/tmp/test"},
			localFiles: map[string]bool{},
			wantTasks:  1,
			wantErr:    false,
		},
		{
			name: "file already exists",
			medias: []bson.M{
				{
					"key":    "key1",
					"url":    "http://example.com/1.jpg",
					"tp":     "image/jpeg",
					"cat":    "Photo",
					"status": "Active",
				},
			},
			sid:        "test123",
			filePath:   "/tmp/test",
			localPaths: []string{"/tmp/test"},
			localFiles: map[string]bool{
				"/tmp/test/test123_C0ozGl.jpg": true,
			},
			wantTasks: 0,
			wantErr:   false,
		},
		{
			name: "invalid media info",
			medias: []bson.M{
				{
					"url":    "http://example.com/1.jpg",
					"tp":     "image/jpeg",
					"cat":    "Photo",
					"status": "Active",
				},
			},
			sid:        "test123",
			filePath:   "/tmp/test",
			localPaths: []string{"/tmp/test"},
			localFiles: map[string]bool{},
			wantTasks:  0,
			wantErr:    false,
		},
		{
			name:       "empty media list",
			medias:     []bson.M{},
			sid:        "test123",
			filePath:   "/tmp/test",
			localPaths: []string{"/tmp/test"},
			localFiles: map[string]bool{},
			wantTasks:  0,
			wantErr:    false,
		},
	}

	analyzer := NewMediaDiffAnalyzer()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := analyzer.generateTrebMediaTasks(tt.medias, tt.sid, tt.filePath, tt.localPaths, tt.localFiles)
			if len(got) != tt.wantTasks {
				t.Errorf("Expected %d tasks, got %d", tt.wantTasks, len(got))
			}
			if tt.wantTasks > 0 {
				task := got[0]
				if task.Sid != tt.sid {
					t.Errorf("Expected sid %s, got %s", tt.sid, task.Sid)
				}
				// Check that the DestPath starts with the expected filePath
				if !strings.HasPrefix(task.DestPath, tt.filePath) {
					t.Errorf("Expected DestPath to start with %s, got %s", tt.filePath, task.DestPath)
				}
			}
		})
	}
}
