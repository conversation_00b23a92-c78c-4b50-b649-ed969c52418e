package main

import (
	"context"
	"flag"
	"image"
	"image/color"
	"image/jpeg"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"testing"
	"time"

	gobase "github.com/real-rm/gobase"
	gomongo "github.com/real-rm/gomongo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	gBoardType string
	dryRun     bool
	force      bool
	downloader struct {
		StoragePaths []string
	}
)

func init() {
	// Initialize flags for testing
	flag.StringVar(&gBoardType, "board", "TRB", "Board type (CAR/DDF/BRE/EDM/TRB)")
	flag.BoolVar(&dryRun, "dryrun", true, "Run in dry run mode")
	flag.BoolVar(&force, "force", true, "Force start a new process")
	flag.String("config", "", "path to config file")
	flag.Parse()
}

func createComponents() error {
	// Initialize downloader paths
	downloader.StoragePaths = []string{filepath.Join(os.TempDir(), "goresodownload_test")}
	return nil
}

func ProcessInsert(changeDoc bson.M, filter bson.M) error {
	// Implementation for insert processing
	return nil
}

func ProcessUpdate(changeDoc bson.M, filter bson.M) error {
	// Implementation for update processing
	return nil
}

func ProcessDelete(changeDoc bson.M, id interface{}) error {
	// Implementation for delete processing
	return nil
}

// setupE2ETest sets up the test environment for end-to-end tests
func setupE2ETest(t *testing.T) (*httptest.Server, func()) {
	// Set up global variables for testing
	gBoardType = "TRB" // Default to TRB for testing
	dryRun = true      // Always run in dry run mode for tests
	force = true       // Force start for tests

	// Create test image
	img := image.NewRGBA(image.Rect(0, 0, 100, 100))
	for y := 0; y < 100; y++ {
		for x := 0; x < 100; x++ {
			img.Set(x, y, color.RGBA{uint8(x), uint8(y), 128, 255})
		}
	}

	// Create mock image server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Simulate different responses based on URL
		switch r.URL.Path {
		case "/success.jpg":
			w.Header().Set("Content-Type", "image/jpeg")
			jpeg.Encode(w, img, nil)
		case "/error.jpg":
			w.WriteHeader(http.StatusInternalServerError)
		case "/timeout.jpg":
			time.Sleep(2 * time.Second)
			w.Header().Set("Content-Type", "image/jpeg")
			jpeg.Encode(w, img, nil)
		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))

	// Initialize test environment
	if err := gobase.InitBase(); err != nil {
		t.Fatalf("Failed to initialize base: %v", err)
	}
	if err := gomongo.InitMongoDB(); err != nil {
		t.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Create test directories
	testDir := filepath.Join(os.TempDir(), "goresodownload_test")
	if err := os.MkdirAll(testDir, 0755); err != nil {
		t.Fatalf("Failed to create test directory: %v", err)
	}

	// Load fixtures
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}

	cfg := &FixtureConfig{
		Folder: currentDir,
	}
	if err := LoadFixtures(cfg); err != nil {
		t.Fatalf("Failed to load fixtures: %v", err)
	}

	// Return cleanup function
	return server, func() {
		server.Close()
		if err := os.RemoveAll(testDir); err != nil {
			t.Logf("Failed to remove test directory: %v", err)
		}
	}
}

// createTestData creates test data for different board types
func createTestData(t *testing.T, boardType string) ([]bson.M, bson.M) {
	var mediaList []bson.M
	var changeDoc bson.M

	// Create test data based on board type
	switch boardType {
	case "TRB":
		mediaList = []bson.M{
			{
				"key":           "test1",
				"MediaCategory": "Photo",
				"url":           "/success.jpg",
				"MediaKey":      "test1",
				"MediaURL":      "/success.jpg",
				"MimeType":      "image/jpeg",
				"Order":         1,
			},
			{
				"key":           "test2",
				"MediaCategory": "Photo",
				"url":           "/error.jpg",
				"MediaKey":      "test2",
				"MediaURL":      "/error.jpg",
				"MimeType":      "image/jpeg",
				"Order":         2,
			},
		}
		changeDoc = bson.M{
			"updateDescription": bson.M{
				"updatedFields": bson.M{
					"phoUrls": mediaList,
				},
			},
			"fullDocument": bson.M{
				"_id":        primitive.NewObjectID(),
				"ListingKey": "test123",
				"phoUrls":    mediaList,
				"ts":         time.Now(),
			},
		}
	default:
		mediaList = []bson.M{
			{
				"MediaKey":      "test1",
				"MediaCategory": "Photo",
				"MediaURL":      "/success.jpg",
				"MimeType":      "image/jpeg",
				"Order":         1,
			},
		}
		changeDoc = bson.M{
			"updateDescription": bson.M{
				"updatedFields": bson.M{
					"Media": mediaList,
				},
			},
			"fullDocument": bson.M{
				"_id":       primitive.NewObjectID(),
				"ListingId": "test123",
				"Media":     mediaList,
				"ts":        time.Now(),
			},
		}
	}

	return mediaList, changeDoc
}

func TestE2EProcessInsert(t *testing.T) {
	_, cleanup := setupE2ETest(t)
	defer cleanup()

	// Test each board type
	boardTypes := []string{"TRB", "DDF", "BRE", "EDM"}
	for _, boardType := range boardTypes {
		t.Run(boardType, func(t *testing.T) {
			// Set board type
			gBoardType = boardType

			// Create components
			err := createComponents()
			require.NoError(t, err)

			// Create test data
			mediaList, changeDoc := createTestData(t, boardType)

			// Process insert
			err = ProcessInsert(changeDoc, bson.M{"_id": changeDoc["fullDocument"].(bson.M)["_id"]})
			require.NoError(t, err)

			// Verify results
			for _, media := range mediaList {
				if media["MediaURL"] == "/success.jpg" {
					// Check if file exists
					filePath := filepath.Join(downloader.StoragePaths[0], media["MediaKey"].(string)+".jpg")
					assert.FileExists(t, filePath)

					// Check MongoDB update
					var result bson.M
					err := gomongo.Coll(boardType, "media").FindOne(
						context.Background(),
						bson.M{"_id": media["MediaKey"]},
					).Decode(&result)
					assert.NoError(t, err)
					assert.Equal(t, "success", result["status"])
				}
			}
		})
	}
}

func TestE2EProcessUpdate(t *testing.T) {
	_, cleanup := setupE2ETest(t)
	defer cleanup()

	// Test each board type
	boardTypes := []string{"TRB", "DDF", "BRE", "EDM"}
	for _, boardType := range boardTypes {
		t.Run(boardType, func(t *testing.T) {
			// Set board type
			gBoardType = boardType

			// Create components
			err := createComponents()
			require.NoError(t, err)

			// Create test data
			mediaList, changeDoc := createTestData(t, boardType)

			// Process update
			err = ProcessUpdate(changeDoc, bson.M{"_id": changeDoc["fullDocument"].(bson.M)["_id"]})
			require.NoError(t, err)

			// Verify results
			for _, media := range mediaList {
				if media["MediaURL"] == "/success.jpg" {
					// Check if file exists
					filePath := filepath.Join(downloader.StoragePaths[0], media["MediaKey"].(string)+".jpg")
					assert.FileExists(t, filePath)

					// Check MongoDB update
					var result bson.M
					err := gomongo.Coll(boardType, "media").FindOne(
						context.Background(),
						bson.M{"_id": media["MediaKey"]},
					).Decode(&result)
					assert.NoError(t, err)
					assert.Equal(t, "success", result["status"])
				}
			}
		})
	}
}

func TestE2EProcessDelete(t *testing.T) {
	_, cleanup := setupE2ETest(t)
	defer cleanup()

	// Test each board type
	boardTypes := []string{"TRB", "DDF", "BRE", "EDM"}
	for _, boardType := range boardTypes {
		t.Run(boardType, func(t *testing.T) {
			// Set board type
			gBoardType = boardType

			// Create components
			err := createComponents()
			require.NoError(t, err)

			// Create test data
			mediaList, changeDoc := createTestData(t, boardType)

			// Process delete
			err = ProcessDelete(changeDoc, changeDoc["fullDocument"].(bson.M)["_id"])
			require.NoError(t, err)

			// Verify results
			for _, media := range mediaList {
				// Check if file is deleted
				filePath := filepath.Join(downloader.StoragePaths[0], media["MediaKey"].(string)+".jpg")
				assert.NoFileExists(t, filePath)

				// Check MongoDB update
				var result bson.M
				err := gomongo.Coll(boardType, "media").FindOne(
					context.Background(),
					bson.M{"_id": media["MediaKey"]},
				).Decode(&result)
				assert.Error(t, err) // Should not find the document
			}
		})
	}
}
