package goresodownload

import (
	"context"
	"fmt"
	"path/filepath"
	"sort"
	"strconv"
	"time"

	gofile "github.com/real-rm/gofile"
	golog "github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	// Media types
	MediaTypePhoto = "Photo"
	MediaTypePDF   = "pdf"
	MediaTypeZip   = "zip"

	// Media categories
	MediaCategoryPhoto = "Photo"
	MediaCategoryDoc   = "Document"

	// Default file extension
	DefaultImageExt = ".jpg"
)

// BoardLogsTable maps board type to its corresponding logs table
var BoardLogsTable = map[string]string{
	"CAR": "mls_car_logs",
	"DDF": "reso_crea_logs",
	"BRE": "bridge_bcre_logs",
	"EDM": "mls_rae_logs",
}

// MediaInfo represents a single media item
type MediaInfo struct {
	MediaKey string
	Order    int
	URL      string
	Type     string // photo/pdf/zip
	Category string // Photo/Document
}

// MediaChangeEvent represents a media field change event
type MediaChangeEvent struct {
	Sid      string
	NewMedia []bson.M // From updatedFields.Media
	PropTs   time.Time
}

// MediaTask represents a download task
type MediaTask struct {
	// TaskID   string
	Sid      string
	Order    int
	MediaKey string
	URL      string
	Type     string // photo/pdf/zip
	DestPath string
	PropTs   time.Time
	IsPhoto  bool
}

// DeleteTask represents a file deletion task
type DeleteTask struct {
	Sid      string
	MediaKey string
	Path     string
}

// AnalysisResult represents the result of media analysis
type AnalysisResult struct {
	DownloadTasks []MediaTask
	DeleteTasks   []DeleteTask
	PhoLH         []int // photo hash list
	DocLH         []int // document hash list
}

// MediaDiffAnalyzer analyzes differences between old and new media lists
type MediaDiffAnalyzer struct {
}

// NewMediaDiffAnalyzer creates a new MediaDiffAnalyzer instance
func NewMediaDiffAnalyzer() *MediaDiffAnalyzer {
	return &MediaDiffAnalyzer{}
}

// Analyze compares old and new media lists and generates download and delete tasks
func (a *MediaDiffAnalyzer) Analyze(changeDoc bson.M, board string) (AnalysisResult, error) {
	// For treb board, use special handling
	if board == "TRB" {
		localPaths := gofile.GetImageDir(board)
		if len(localPaths) == 0 {
			return AnalysisResult{}, fmt.Errorf("no local paths provided")
		}
		return a.analyzeTreb(changeDoc, localPaths)
	}

	// For regular boards, validate board type
	if _, exists := BoardLogsTable[board]; !exists {
		return AnalysisResult{}, fmt.Errorf("invalid board: %s", board)
	}

	// Extract media information
	sid, newMediaList, ts, err := a.getRegularMediaInfo(changeDoc)
	if err != nil {
		return AnalysisResult{}, err
	}

	// Get old media from logs table
	oldMedia, err := a.getOldMediaFromLogs(sid, board)
	if err != nil {
		golog.Error("Failed to get old media from logs",
			"sid", sid,
			"error", err)
		return AnalysisResult{}, err
	}

	// Prepare tasks
	downloadTasks, deleteTasks, err := a.prepareRegularMediaTasks(sid, newMediaList, oldMedia, ts, board)
	if err != nil {
		return AnalysisResult{}, err
	}

	// Generate hash lists
	phoLH := a.generateHashList(newMediaList)
	docLH := a.generateHashList(newMediaList)

	golog.Info("Regular board media analysis complete",
		"sid", sid,
		"downloadTasks", len(downloadTasks),
		"deleteTasks", len(deleteTasks),
		"phoLHCount", len(phoLH),
		"docLHCount", len(docLH))

	return AnalysisResult{
		DownloadTasks: downloadTasks,
		DeleteTasks:   deleteTasks,
		PhoLH:         phoLH,
		DocLH:         docLH,
	}, nil
}

// getRegularMediaInfo extracts media information from change document
func (a *MediaDiffAnalyzer) getRegularMediaInfo(changeDoc bson.M) (string, []bson.M, time.Time, error) {
	sid, ok := changeDoc["fullDocument"].(bson.M)["ListingId"].(string)
	if !ok {
		return "", nil, time.Time{}, fmt.Errorf("invalid listingId field format in change document")
	}

	// Get new media from updatedFields
	newMedia, ok := changeDoc["fullDocument"].(bson.M)["Media"].([]interface{})
	if !ok {
		return "", nil, time.Time{}, fmt.Errorf("invalid media field format in change document")
	}

	// Convert []interface{} to []bson.M
	var newMediaList []bson.M
	for _, m := range newMedia {
		if mediaMap, ok := m.(bson.M); ok {
			newMediaList = append(newMediaList, mediaMap)
		}
	}

	ts, ok := changeDoc["fullDocument"].(bson.M)["ts"].(time.Time)
	if !ok {
		return "", nil, time.Time{}, fmt.Errorf("invalid ts field format in change document")
	}

	return sid, newMediaList, ts, nil
}

// prepareRegularMediaTasks prepares media tasks for regular boards
func (a *MediaDiffAnalyzer) prepareRegularMediaTasks(sid string, newMediaList []bson.M, oldMedia []bson.M, ts time.Time, board string) ([]MediaTask, []DeleteTask, error) {
	// build map for comparison
	oldMap := make(map[string]bson.M)
	for _, m := range oldMedia {
		oldMap[m["MediaKey"].(string)] = m
	}

	newMap := make(map[string]bson.M)
	for _, m := range newMediaList {
		newMap[m["MediaKey"].(string)] = m
	}

	var (
		keepList   []bson.M
		toDownload []bson.M
		toDelete   []bson.M
	)

	// find media to keep, download and delete
	for _, m := range newMediaList {
		mediaKey := m["MediaKey"].(string)
		if _, exists := oldMap[mediaKey]; exists {
			keepList = append(keepList, m) // keep
		} else {
			toDownload = append(toDownload, m) // new
		}
	}

	for _, m := range oldMedia {
		mediaKey := m["MediaKey"].(string)
		if _, exists := newMap[mediaKey]; !exists {
			toDelete = append(toDelete, m) // deleted
		}
	}

	// Get full file path
	filePath, err := gofile.GetFullFilePath(ts, board, sid)
	if err != nil {
		golog.Error("Failed to get full file path",
			"sid", sid,
			"error", err)
		return nil, nil, err
	}

	// Generate download tasks
	downloadTasks := a.generateRegularDownloadTasks(toDownload, sid, filePath)

	// Generate delete tasks
	deleteTasks := a.generateRegularDeleteTasks(toDelete, sid, filePath)

	return downloadTasks, deleteTasks, nil
}

// generateRegularDownloadTasks generates download tasks for regular boards
func (a *MediaDiffAnalyzer) generateRegularDownloadTasks(toDownload []bson.M, sid string, filePath string) []MediaTask {
	var downloadTasks []MediaTask
	for _, m := range toDownload {
		mediaKey, url, mediaType, isPhoto, err := a.extractMediaInfo(m, false)
		if err != nil {
			golog.Error("Failed to extract media info",
				"sid", sid,
				"error", err)
			continue
		}

		fileName := a.generateFileName(sid, mediaKey, url)
		if fileName == "" {
			continue
		}

		task := MediaTask{
			Sid:      sid,
			MediaKey: mediaKey,
			URL:      url,
			Type:     mediaType,
			IsPhoto:  isPhoto,
			DestPath: filePath,
		}
		downloadTasks = append(downloadTasks, task)
		golog.Debug("New media found for download",
			"sid", sid,
			"mediaKey", mediaKey,
			"order", m["Order"],
			"type", mediaType)
	}
	return downloadTasks
}

// generateRegularDeleteTasks generates delete tasks for regular boards
func (a *MediaDiffAnalyzer) generateRegularDeleteTasks(toDelete []bson.M, sid string, filePath string) []DeleteTask {
	var deleteTasks []DeleteTask
	for _, m := range toDelete {
		task := DeleteTask{
			Sid:      sid,
			MediaKey: m["MediaKey"].(string),
			Path:     filePath,
		}
		deleteTasks = append(deleteTasks, task)
		golog.Debug("Old media found for deletion",
			"sid", sid,
			"mediaKey", m["MediaKey"],
			"type", m["MimeType"])
	}
	return deleteTasks
}

// analyzeRegularBoard handles analysis for car/crea/bcre/rae boards
func (a *MediaDiffAnalyzer) analyzeRegularBoard(changeDoc bson.M, board string) (AnalysisResult, error) {
	// Extract media information
	sid, newMediaList, ts, err := a.getRegularMediaInfo(changeDoc)
	if err != nil {
		return AnalysisResult{}, err
	}

	// Get old media from logs table
	oldMedia, err := a.getOldMediaFromLogs(sid, board)
	if err != nil {
		golog.Error("Failed to get old media from logs",
			"sid", sid,
			"error", err)
		return AnalysisResult{}, err
	}

	// Prepare tasks
	downloadTasks, deleteTasks, err := a.prepareRegularMediaTasks(sid, newMediaList, oldMedia, ts, board)
	if err != nil {
		return AnalysisResult{}, err
	}

	// Generate hash lists
	phoLH := a.generateHashList(newMediaList)
	docLH := a.generateHashList(newMediaList)

	golog.Info("Regular board media analysis complete",
		"sid", sid,
		"downloadTasks", len(downloadTasks),
		"deleteTasks", len(deleteTasks),
		"phoLHCount", len(phoLH),
		"docLHCount", len(docLH))

	return AnalysisResult{
		DownloadTasks: downloadTasks,
		DeleteTasks:   deleteTasks,
		PhoLH:         phoLH,
		DocLH:         docLH,
	}, nil
}

// generateFileName generates a file name for a media item
func (a *MediaDiffAnalyzer) generateFileName(sid string, mediaKey string, url string) string {
	hash := gofile.MurmurToInt32(mediaKey)
	hashStr, err := gofile.Int32ToBase62(hash)
	if err != nil {
		golog.Error("Failed to convert hash to base62",
			"sid", sid,
			"mediaKey", mediaKey,
			"error", err)
		return ""
	}

	// Get file extension from URL
	ext := filepath.Ext(url)
	if ext == "" {
		ext = DefaultImageExt
	}

	return fmt.Sprintf("%s_%s%s", sid, hashStr, ext)
}

// checkFileExists checks if a file exists in any of the local paths
func (a *MediaDiffAnalyzer) checkFileExists(fileName string, localPaths []string, localFiles map[string]bool) bool {
	for _, path := range localPaths {
		if localFiles[filepath.Join(path, fileName)] {
			return true
		}
	}
	return false
}

// extractMediaInfo extracts common media information from a media item
func (a *MediaDiffAnalyzer) extractMediaInfo(m bson.M, isTreb bool) (string, string, string, bool, error) {
	if m == nil {
		return "", "", "", false, fmt.Errorf("media info is nil")
	}

	var mediaKey, url, mediaType string
	var isPhoto bool

	if isTreb {
		key, ok := m["key"]
		if !ok || key == nil {
			return "", "", "", false, fmt.Errorf("invalid media info: missing key")
		}
		mediaKey, ok = key.(string)
		if !ok {
			return "", "", "", false, fmt.Errorf("invalid media info: key is not string")
		}

		urlVal, ok := m["url"]
		if !ok || urlVal == nil {
			return "", "", "", false, fmt.Errorf("invalid media info: missing url")
		}
		url, ok = urlVal.(string)
		if !ok {
			return "", "", "", false, fmt.Errorf("invalid media info: url is not string")
		}

		typeVal, ok := m["type"]
		if !ok || typeVal == nil {
			return "", "", "", false, fmt.Errorf("invalid media info: missing type")
		}
		mediaType, ok = typeVal.(string)
		if !ok {
			return "", "", "", false, fmt.Errorf("invalid media info: type is not string")
		}

		catVal, ok := m["cat"]
		if !ok || catVal == nil {
			return "", "", "", false, fmt.Errorf("invalid media info: missing cat")
		}
		cat, ok := catVal.(string)
		if !ok {
			return "", "", "", false, fmt.Errorf("invalid media info: cat is not string")
		}
		isPhoto = cat == MediaCategoryPhoto
	} else {
		key, ok := m["MediaKey"]
		if !ok || key == nil {
			return "", "", "", false, fmt.Errorf("invalid media info: missing MediaKey")
		}
		mediaKey, ok = key.(string)
		if !ok {
			return "", "", "", false, fmt.Errorf("invalid media info: MediaKey is not string")
		}

		urlVal, ok := m["MediaURL"]
		if !ok || urlVal == nil {
			return "", "", "", false, fmt.Errorf("invalid media info: missing MediaURL")
		}
		url, ok = urlVal.(string)
		if !ok {
			return "", "", "", false, fmt.Errorf("invalid media info: MediaURL is not string")
		}

		typeVal, ok := m["MimeType"]
		if !ok || typeVal == nil {
			return "", "", "", false, fmt.Errorf("invalid media info: missing MimeType")
		}
		mediaType, ok = typeVal.(string)
		if !ok {
			return "", "", "", false, fmt.Errorf("invalid media info: MimeType is not string")
		}

		catVal, ok := m["MediaCategory"]
		if !ok || catVal == nil {
			return "", "", "", false, fmt.Errorf("invalid media info: missing MediaCategory")
		}
		cat, ok := catVal.(string)
		if !ok {
			return "", "", "", false, fmt.Errorf("invalid media info: MediaCategory is not string")
		}
		isPhoto = cat == MediaCategoryPhoto
	}

	if mediaKey == "" || url == "" {
		return "", "", "", false, fmt.Errorf("invalid media info: empty key or url")
	}

	return mediaKey, url, mediaType, isPhoto, nil
}

// generateMediaTasks generates download tasks for media items
func (a *MediaDiffAnalyzer) generateMediaTasks(medias []bson.M, sid string, filePath string, localPaths []string, localFiles map[string]bool) []MediaTask {
	var tasks []MediaTask

	for _, m := range medias {
		mediaKey, url, mediaType, isPhoto, err := a.extractMediaInfo(m, true)
		if err != nil {
			golog.Error("Failed to extract media info",
				"sid", sid,
				"error", err)
			continue
		}

		fileName := a.generateFileName(sid, mediaKey, url)
		if fileName == "" {
			continue
		}

		// Check if file exists in any of the local paths
		if a.checkFileExists(fileName, localPaths, localFiles) {
			continue
		}

		task := MediaTask{
			Sid:      sid,
			MediaKey: mediaKey,
			URL:      url,
			Type:     mediaType,
			IsPhoto:  isPhoto,
			DestPath: filePath,
		}
		tasks = append(tasks, task)
		golog.Debug("New media found for download in TREB",
			"sid", sid,
			"mediaKey", mediaKey,
			"type", mediaType)
	}

	return tasks
}

// generateHashList generates a list of hashes from media items
func (a *MediaDiffAnalyzer) generateHashList(medias []bson.M) []int {
	var hashList []int
	for _, m := range medias {
		hash := gofile.MurmurToInt32(m["key"].(string))
		hashList = append(hashList, int(hash))
	}
	return hashList
}

// getTrebMediaInfo extracts media information from change document
func (a *MediaDiffAnalyzer) getTrebMediaInfo(changeDoc bson.M) (string, []interface{}, time.Time, error) {
	sid, ok := changeDoc["fullDocument"].(bson.M)["ListingKey"].(string)
	if !ok {
		return "", nil, time.Time{}, fmt.Errorf("invalid listingId field format in change document")
	}

	// Get new media from fullDocument
	var newMedias []interface{}
	if fullDoc, ok := changeDoc["fullDocument"].(bson.M); ok {
		if media, ok := fullDoc["media"].([]interface{}); ok {
			newMedias = media
		}
	}

	// Get timestamp for file path
	ts, ok := changeDoc["fullDocument"].(bson.M)["ts"].(time.Time)
	if !ok {
		return "", nil, time.Time{}, fmt.Errorf("invalid ts field format in change document")
	}

	return sid, newMedias, ts, nil
}

// prepareTrebMediaTasks prepares media tasks for TREB board
func (a *MediaDiffAnalyzer) prepareTrebMediaTasks(sid string, newMedias []interface{}, ts time.Time, localPaths []string) ([]MediaTask, []DeleteTask, []bson.M, []bson.M, error) {
	// Get deleted media from reso_treb_evow_media table
	deletedMedias, err := a.getTrebMedia(sid)
	if err != nil {
		golog.Error("Failed to get TREB media",
			"sid", sid,
			"error", err)
		return nil, nil, nil, nil, err
	}

	// Get full file path
	filePath, err := gofile.GetFullFilePath(ts, "treb", sid)
	if err != nil {
		golog.Error("Failed to get full file path",
			"sid", sid,
			"error", err)
		return nil, nil, nil, nil, err
	}

	// Get existing local files
	localFiles := make(map[string]bool)
	files, err := gofile.ListFiles(localPaths[0], filePath, sid)
	if err != nil {
		golog.Error("Failed to list local files",
			"path", localPaths[0],
			"filePath", filePath,
			"sid", sid,
			"error", err)
	}
	for _, file := range files {
		localFiles[file] = true
	}

	// Filter media items
	var filteredMedias []bson.M
	var filteredDocs []bson.M
	if len(newMedias) > 0 {
		var mediaList []bson.M
		for _, m := range newMedias {
			if mediaMap, ok := m.(bson.M); ok {
				mediaList = append(mediaList, mediaMap)
			}
		}
		filteredMedias, filteredDocs = FilterMedias(mediaList)
	}

	// Generate tasks
	downloadTasks := a.generateMediaTasks(filteredMedias, sid, filePath, localPaths, localFiles)
	downloadTasks = append(downloadTasks, a.generateMediaTasks(filteredDocs, sid, filePath, localPaths, localFiles)...)

	deleteTasks := a.generateDeleteTasks(deletedMedias, sid, filePath)

	return downloadTasks, deleteTasks, filteredMedias, filteredDocs, nil
}

// generateDeleteTasks generates delete tasks from deleted media
func (a *MediaDiffAnalyzer) generateDeleteTasks(deletedMedias []bson.M, sid string, filePath string) []DeleteTask {
	var deleteTasks []DeleteTask
	for _, m := range deletedMedias {
		task := DeleteTask{
			Sid:      sid,
			MediaKey: m["MK"].(string),
			Path:     filePath,
		}
		deleteTasks = append(deleteTasks, task)
		golog.Debug("Deleted media found in TREB",
			"sid", sid,
			"mediaKey", m["MediaKey"])
	}
	return deleteTasks
}

// analyzeTreb handles analysis for TREB board
func (a *MediaDiffAnalyzer) analyzeTreb(changeDoc bson.M, localPaths []string) (AnalysisResult, error) {
	if len(localPaths) == 0 {
		return AnalysisResult{}, fmt.Errorf("no local paths provided")
	}

	// Extract media information
	sid, newMedias, ts, err := a.getTrebMediaInfo(changeDoc)
	if err != nil {
		return AnalysisResult{}, err
	}

	// Prepare tasks
	downloadTasks, deleteTasks, filteredMedias, filteredDocs, err := a.prepareTrebMediaTasks(sid, newMedias, ts, localPaths)
	if err != nil {
		return AnalysisResult{}, err
	}

	// Generate hash lists
	phoLH := a.generateHashList(filteredMedias)
	docLH := a.generateHashList(filteredDocs)

	golog.Info("TREB media analysis complete",
		"sid", sid,
		"downloadTasks", len(downloadTasks),
		"deleteTasks", len(deleteTasks),
		"phoLHCount", len(phoLH),
		"docLHCount", len(docLH))

	return AnalysisResult{
		DownloadTasks: downloadTasks,
		DeleteTasks:   deleteTasks,
		PhoLH:         phoLH,
		DocLH:         docLH,
	}, nil
}

// getOldMediaFromLogs gets the old media list from logs table
func (a *MediaDiffAnalyzer) getOldMediaFromLogs(sid string, board string) ([]bson.M, error) {
	// get logs table name from board type
	logsTable, exists := BoardLogsTable[board]
	if !exists {
		return nil, fmt.Errorf("unsupported board type: %s", board)
	}

	// get logs collection
	logsCol := gomongo.Coll("rni", logsTable)
	if logsCol == nil {
		return nil, fmt.Errorf("failed to get logs collection: %s", logsTable)
	}

	query := bson.M{"sid": sid}
	sort := bson.D{{Key: "_mt", Value: -1}}
	skip := 1
	ctx := context.Background()

	var oldLog bson.M
	err := logsCol.FindOne(ctx, query, sort, skip).Decode(&oldLog)
	if err != nil {
		if err.Error() == "mongo: no documents in result" {
			golog.Info("No old media found in logs table",
				"board", board,
				"logsTable", logsTable,
				"sid", sid)
			return []bson.M{}, nil
		}
		return nil, fmt.Errorf("failed to get old media from logs table: %w", err)
	}

	golog.Debug("Found old log",
		"sid", sid,
		"board", board,
		"log", oldLog)

	// parse media field
	data, ok := oldLog["data"].(bson.M)
	if !ok {
		golog.Error("Invalid data field format",
			"sid", sid,
			"data", oldLog["data"])
		return nil, fmt.Errorf("invalid data field format in log for sid: %s", sid)
	}

	golog.Debug("Found data field",
		"sid", sid,
		"data", data)

	mediaField, ok := data["media"]
	if !ok {
		golog.Info("No media field found",
			"sid", sid)
		return []bson.M{}, nil
	}

	golog.Debug("Found media field",
		"sid", sid,
		"media", mediaField,
		"type", fmt.Sprintf("%T", mediaField))

	// Convert media field to []bson.M
	var mediaArray []bson.M
	switch v := mediaField.(type) {
	case []bson.M:
		mediaArray = v
	case []interface{}:
		if len(v) == 0 {
			mediaArray = []bson.M{}
		} else {
			for _, m := range v {
				if mediaMap, ok := m.(bson.M); ok {
					mediaArray = append(mediaArray, mediaMap)
				}
			}
		}
	case primitive.A:
		if len(v) == 0 {
			mediaArray = []bson.M{}
		} else {
			for _, m := range v {
				if mediaMap, ok := m.(bson.M); ok {
					mediaArray = append(mediaArray, mediaMap)
				}
			}
		}
	case nil:
		mediaArray = []bson.M{}
	default:
		golog.Error("Invalid media field type",
			"sid", sid,
			"type", fmt.Sprintf("%T", mediaField))
		return nil, fmt.Errorf("invalid media field format in log for sid: %s", sid)
	}

	golog.Debug("Converted media array",
		"sid", sid,
		"mediaArray", mediaArray)

	return mediaArray, nil
}

// getTrebMedia gets media from reso_treb_evow_media table
func (a *MediaDiffAnalyzer) getTrebMedia(sid string) ([]bson.M, error) {
	mediaCol := gomongo.Coll("rni", "reso_treb_evow_media")
	query := bson.M{"RRK": sid, "MS": "Deleted"}
	deletedMedias, err := mediaCol.FindToArray(context.Background(), query)
	if err != nil {
		return nil, fmt.Errorf("failed to get deleted media from reso_treb_evow_media table for sid %s: %w", sid, err)
	}

	golog.Debug("Got deleted media from reso_treb_evow_media",
		"sid", sid,
		"count", len(deletedMedias))

	return deletedMedias, nil
}

// FilterMedias filters medias by status, permission, and statement
func FilterMedias(medias []bson.M) ([]bson.M, []bson.M) {
	if len(medias) == 0 {
		return []bson.M{}, []bson.M{}
	}

	// Define size priority for photo selection
	sizePriority := []string{
		"LargestNoWatermark",
		"Largest",
		"Large",
		"media",
		"Thumbnail",
	}

	// Initialize result slices
	photoResult := make([]bson.M, 0)
	docResult := make([]bson.M, 0)

	// Group photos by ID
	groupedPhotos := make(map[string][]bson.M)
	for _, media := range medias {
		// Skip invalid media
		if media["url"] == "" || (media["status"] != "" && media["status"] != "Active") {
			continue
		}

		// Handle non-photo media
		if media["type"] != "image/jpeg" || media["cat"] != MediaCategoryPhoto {
			docResult = append(docResult, media)
			continue
		}

		// Group photos by ID
		id := media["id"].(string)
		groupedPhotos[id] = append(groupedPhotos[id], media)
	}

	// Sort photo IDs numerically
	sortedIDs := make([]string, 0, len(groupedPhotos))
	for id := range groupedPhotos {
		sortedIDs = append(sortedIDs, id)
	}
	sort.Slice(sortedIDs, func(i, j int) bool {
		ai, _ := strconv.Atoi(sortedIDs[i])
		bi, _ := strconv.Atoi(sortedIDs[j])
		return ai < bi
	})

	// Select best photo for each ID
	for _, id := range sortedIDs {
		photos := groupedPhotos[id]
		selectedPhoto := selectBestPhoto(photos, sizePriority)
		if selectedPhoto != nil {
			photoResult = append(photoResult, *selectedPhoto)
		}
	}

	return photoResult, docResult
}

// selectBestPhoto selects the best photo based on size priority
func selectBestPhoto(photos []bson.M, sizePriority []string) *bson.M {
	if len(photos) == 0 {
		return nil
	}

	// If no size description, return the first photo
	if photos[0]["sizeDesc"] == nil || photos[0]["sizeDesc"] == "" {
		return &photos[0]
	}

	// Try to find the highest priority size
	for _, size := range sizePriority {
		for _, photo := range photos {
			if photo["sizeDesc"] == size {
				return &photo
			}
		}
	}

	// If no matching size found, return the first photo
	return &photos[0]
}
