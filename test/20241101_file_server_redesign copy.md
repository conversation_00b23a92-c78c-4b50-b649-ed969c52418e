# 需求 [file_server_redesign]

## 反馈

1. 需要重新设计图片服的图片保存方式，并迁移原来图片数据，需要满足如下要求：
   1. 性能（performance）：系统需要确保在图片访问和保存时保持高性能，特别是在大量图片上传和访问的场景下。可以通过将最新、访问频率较高的图片存储在高速存储设备上来提升访问速度，同时在低访问频率时切换至成本更低的存储。
   2. 防止路径自动计算（Prevent Path Auto-Calculation）：路径在图片保存时应当固定，不允许每次生成动态路径，以避免路径自动生成带来的计算开销和不一致性。每张图片上传时直接使用预定义的路径结构，这样可以节省计算时间并保证路径唯一。且根据固定生成的路径，可以确认图片的新旧。
   3. 访问跟踪（Track Access）：图片的路径没有规律，不会被直接猜到，保障数据安全。
   4. 视频就绪（Video Ready）：系统不仅支持图片，还要能够无缝处理视频文件。对于视频，路径结构和存储分层应与图片保持一致。

## 需求提出人:   Fred, Rain

## 修改人：      Maggie

## 提出日期:     2024-08-27

## 原因

1. 原来图片保存盘要满了，而且不满足要求

## 解决办法

0. libapp/properties.coffee listingPicUrls
   http://img.realmaster.com  房源图片
   http://f.realmaster.com   上传图片

1. 图片url和硬盘保存路径：
   url: /[TRB|DDF|BRE|CLG|OTW|EDM|CAR]/[timestamp].[uid-uuid].[width-id]/[number4(*L1)]/[uuid-5(*L2)]/[mls-id]_[index].jpg
   保存文件硬盘路径:[number4(*L1)]/[uuid-5(*L2)]/[mls-id]_[Hash(MediaKey)].jpg 只保存没有水印图片。   
   # NOTE: trebReso deleted的图的index会被新的图占用，如果只保留index，无法保证删除掉正确的图片 
   [TRB|DDF|BRE|CLG|OTW|EDM|CAR]: BoardNameShort 【使用src字段，后面BRE也改成下载到本地】
   时间戳（timestamp）: 1730471806  【使用现在的_getSortTimeStamp，能做到图片修改不一致】
   用户 UUID（uid-uuid）: U241055301【user.uuid】   [如果没有login 使用b4luuid, 用cookie里uuid] 如果都没有【如google/bing】give fix nanoId12位 
   宽度 ID（width-id）: 414（代表某个宽度）     【3个值，414，828，全】

   L1 目录（number4(*L1)）: 修改为一年50个文件夹，每周一个， 0和49后面的合并到一个folder0，2015年作为750，2024-》1200，2025-》1250。【用户上传，默认按照每年一个文件夹，年份修改作为L1】
   L2 目录（uuid-5(*L2)）: abc12 【提前生成好2048个uuid5，保存到文件中，每次运行的时候，先根据L1获得以L1/1024的mod为index开始的list，读取保存到内存中，同时保存到数据库中。
   一次生成，随机的，保证同一个L1中没有重复。L2 使用crc32(sid，seed)=index, 根据用户id生成对应数字，然后将这个数字作为index，找到对应的uuid-5】 保存文件
   MLS ID（mls-id）: W4054894 【sid】
   Hash(MediaKey): md5(MediaKey)后取4位。 mongo中存对应的10进制数。

   url例子显示：/TRB/1730471806.U241055301.414/003/abc12/W4054894_01.jpg
   *L1: 记录从20150101开始的周数，按时间排序，20年最终1000个。每年50个folder，每周一个。通过把最新200个folder放到高速存储上，达到既提高性能又节省成本。根据prop的onD计算对应的L1，
   *L2: uuid 5 chars。具体个数见下面【】内
   2023: db.properties.countDocuments({ onD: { $gte: 20230101, $lte: 20231231 }, src: "DDF" })
   TRB: 469496/52 = 9028 props/week 使用【512】个L2，每个L2中约18个props，约18*30=540张图
   DDF: 858449/52 = 16509 props/week 使用【1024】个L2，每个L2中约16个props，约16*30=480张图
   BRE: 87078/52 = 1675 props/week 使用【64】个L2，每个L2中约26个props，约26*30=780张图
   CLG: 76906/52 = 1479 props/week 使用【64】个L2，每个L2中约23个props，约23*30=690张图
   OTW: 41850/52 = 805 props/week 使用【64】个L2，每个L2中约13个props，约13*30=390张图
   EDM: 4027(*************)/4 = 1007 props/week 使用【64】个L2，每个L2中约43个props，约43*30=1290张图
   CAR: 141911/52 = 2730 props/week 使用【256】个L2，每个L2中约10个props，约10*30=300张图
   hash比较：time33均一性最差，crc32和sha256结果基本一致，crc32更平均一些，速度更快295ms（sha256 1345ms）。后续选取crc32，根据id, seed计算。


bridge reso:
"MediaKey" : "a6cb2050a0589ce39c7e40706aeba6e5-m1",

treb reso:
  "MediaKey" : "_timmins-T9292800-2-nw",
  MediaKey": "c4f0cb89-6f45-421e-a164-90e54662cb83-nw",    mediaKey 做hash，保留小于1million概率



1. class 和 db设计：
 - class DirKeyStore {
    constructor: (prefix, DBClient/collName, updateTimeMin=10) -> 【prefix：src， 所有用户用一个prefix:USER】
      创建collection referrence；createIndex；
      @_tmpDirMap: {002:[,,],003:[,,,,]}  将最近使用的l1:[l2,l2]保存到内存
      @_dirMap: {[l1-uuid5]:{l: Entity amount, f: file amount}}    记录新增数目【每次重启，丢失10min数据】
      @_lastUpdateTs: new Date() 最后更新的时间
      @_lastSavedTs: 最后保存时间
      setInterval: _saveChangedCurrent(), 10 min,每个instance独立
    _genUUIDMap: (arrayLength=512, keyLength=5)-> ['uuid-5','uuid-5'...]
            产生key map(arrayLength), uuid(key length)  []
    _saveCurrent: ()->
            await save/upsert current info (L1, map, file and Entity numbers in each folder, total files, total Entitis)
            set @_lastSavedTs = new Date()
    _saveChangedCurrent: ()->  如果 lastUpdateTs > lastSaveTs，_saveCurrent() 自动10min调用
 }
   

   保存文件硬盘路径:多个boardPath/[number4(*L1)]/[uuid-5(*L2)]/[mls-id]_[index].jpg
  - DB structure: collection name (dir_key_map) 【每个board的每个L1一条记录】
      - _id: {board: str, l1: int}    【l1: int 如750】   【USER】
      - dirArray: [str] 【uuid5，uuid5，uuid5】    [一次性生成，L2 随机均匀，映射，linux inode]
      - dirMap: {[uuid5]:{l: Entity amount, f: file amount}}
      - totalFiles: int
      - totalEntitys: int
      - ts: timestamp
      - _mt: system modified date

3. 数据来源：
  1. board download： 
     1. treb 多个来源，idx，evow，不做手动。每个来源一个instance
     2. prop[处理新进来的和旧的]，根据每个onD计算L1，根据sid计算L2， 在prop中添加rmMedia:[{rmPath,rmName}],rmPhoMt
     3. 支持多个房源并行/同一个房源的多个图片并行-》每个房源独立获取dir，互相不影响 
     4. 图片更改，删除，添加。目前图片修改是全下载覆盖。-》根据onD获得L1，使用hash_dir(id,onD,seed)计算获得num(对应到文件夹L2index)。始终保持一致。
  2. 用户上传，VIP Console，支持多个经济同时上传：
     1. DirKeyStore只一个instance。prefix使用USER
     2. 现有用户的部分不变。新用户，使用上面相同架构：
      /P2/[L1]/[L2]/uuid5_uuid5.[jpg/pdf/png/...]
      L1：每年一个 2015年-》000 【2015年以前的都放到000里，使用user mt计算获得】
      L2：总共512个，使用用户"_id" : ObjectId("66b28c65229506ccb40a299e"), hash计算获取index
      用户uuid5_文件uuid5 用户fuuid【nanoId9位，db.user_file.fuuid，上传的时候 check 新建】——文件uuid【nanoId6位】
   3. 旧数据 batch，按照onD 从新到旧，【生成一周文件夹，处理一周，反复】

4. 修改部分
   1. batch/gen_file_server_dirs.go:
  （1）添加默认的src/constants/L2_FOLDER_LIST或使用已经存在的该文件，
  （2）建立对应的文件夹L1，L2及写数据库。可以一次生成之前的和未来10年的。也支持指定某一周。旧数据支持输入后一次性生成 batch/gen_file_server_dirs.go
   2. class DirKeyStore:entities/dirKeyStore.go
   3. 新图片保存，prop关联：只把数据保留在media表中：更新简单，但展示时候需要查找，做一个对应的 listing.media
   4. picurl要refactor：传给前端的url，后端用的 lib/property [再确认需求]
   5. 写一个高速盘定期删除文件夹batch。

## 确认日期:    2024-11

## online-step
1. 运行 batch/gen_file_server.go 生成对应的文件夹及记录到数据库
   ```
   go run ./batch/gen_file_server_dirs.go -start 20150101 -end 20161231
   ```
2. 先把图片下载重启
3. 把旧文件转-》图片+prop phoKey

phoKey 判断用新的还是旧的
<!-- # mongo 自己锁 -->
   <!-- oreb,  for dir in params.dirs -->
   <!-- 【fuuid db.user_file.fuuid  上传的时候 check 新建】 -->
   <!-- 1. photo download treb continuslyExecuteJobs->executeDownloadJob->photoWriteJobs
                 -》downloadPhotosForTrebListingWithPriority  -->



- lib/file_server.go: calculateL1, calculateL2, getL2Array, generateUUID5Array


