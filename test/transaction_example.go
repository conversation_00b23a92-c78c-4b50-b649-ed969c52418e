package gomongo

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/readconcern"
	"go.mongodb.org/mongo-driver/mongo/writeconcern"
)

// ExampleTransaction demonstrates basic transaction usage
func ExampleTransaction() error {
	// Start a session
	session, err := StartSession("your_db_name")
	if err != nil {
		return fmt.Errorf("failed to start session: %v", err)
	}
	defer session.EndSession()

	// Start a transaction
	txn, err := session.StartTransaction()
	if err != nil {
		return fmt.Errorf("failed to start transaction: %v", err)
	}

	// Get session context for operations
	sessCtx := txn.GetSessionContext()

	// Perform operations within transaction
	usersColl := Coll("your_db_name", "users")
	accountsColl := Coll("your_db_name", "accounts")

	// Insert a user
	userResult, err := usersColl.InsertOne(sessCtx, bson.M{
		"name":  "<PERSON>",
		"email": "<EMAIL>",
	})
	if err != nil {
		// Abort transaction on error
		if abortErr := txn.AbortTransaction(); abortErr != nil {
			return fmt.Errorf("failed to abort transaction: %v", abortErr)
		}
		return fmt.Errorf("failed to insert user: %v", err)
	}

	// Insert an account for the user
	accountResult, err := accountsColl.InsertOne(sessCtx, bson.M{
		"userId":  userResult.InsertedID,
		"balance": 1000,
		"type":    "savings",
	})
	if err != nil {
		// Abort transaction on error
		if abortErr := txn.AbortTransaction(); abortErr != nil {
			return fmt.Errorf("failed to abort transaction: %v", abortErr)
		}
		return fmt.Errorf("failed to insert account: %v", err)
	}

	// Commit the transaction
	err = txn.CommitTransaction()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	fmt.Printf("Transaction completed successfully. User ID: %v, Account ID: %v\n",
		userResult.InsertedID, accountResult.InsertedID)
	return nil
}

// ExampleWithTransaction demonstrates using WithTransaction helper
func ExampleWithTransaction() error {
	// Start a session
	session, err := StartSession("your_db_name")
	if err != nil {
		return fmt.Errorf("failed to start session: %v", err)
	}
	defer session.EndSession()

	// Define transaction options
	txnOpts := &TransactionOptions{
		ReadConcern:  readconcern.Snapshot(),
		WriteConcern: writeconcern.Majority(),
	}

	// Execute transaction using WithTransaction helper
	err = session.WithTransaction(func(sessCtx mongo.SessionContext) error {
		usersColl := Coll("your_db_name", "users")
		accountsColl := Coll("your_db_name", "accounts")

		// Insert a user
		userResult, err := usersColl.InsertOne(sessCtx, bson.M{
			"name":  "Jane Doe",
			"email": "<EMAIL>",
		})
		if err != nil {
			return fmt.Errorf("failed to insert user: %v", err)
		}

		// Insert an account for the user
		_, err = accountsColl.InsertOne(sessCtx, bson.M{
			"userId":  userResult.InsertedID,
			"balance": 2000,
			"type":    "checking",
		})
		if err != nil {
			return fmt.Errorf("failed to insert account: %v", err)
		}

		return nil
	}, txnOpts)

	if err != nil {
		return fmt.Errorf("transaction failed: %v", err)
	}

	fmt.Println("Transaction completed successfully using WithTransaction helper")
	return nil
}

// ExampleTransferMoney demonstrates a money transfer transaction
func ExampleTransferMoney(fromUserId, toUserId interface{}, amount float64) error {
	session, err := StartSession("your_db_name")
	if err != nil {
		return fmt.Errorf("failed to start session: %v", err)
	}
	defer session.EndSession()

	err = session.WithTransaction(func(sessCtx mongo.SessionContext) error {
		accountsColl := Coll("your_db_name", "accounts")

		// Deduct from source account
		result, err := accountsColl.UpdateOne(
			sessCtx,
			bson.M{"userId": fromUserId},
			bson.M{"$inc": bson.M{"balance": -amount}},
		)
		if err != nil {
			return fmt.Errorf("failed to deduct from source account: %v", err)
		}
		if result.ModifiedCount == 0 {
			return fmt.Errorf("source account not found or insufficient balance")
		}

		// Add to destination account
		result, err = accountsColl.UpdateOne(
			sessCtx,
			bson.M{"userId": toUserId},
			bson.M{"$inc": bson.M{"balance": amount}},
		)
		if err != nil {
			return fmt.Errorf("failed to add to destination account: %v", err)
		}
		if result.ModifiedCount == 0 {
			return fmt.Errorf("destination account not found")
		}

		// Log the transfer
		transfersColl := Coll("your_db_name", "transfers")
		_, err = transfersColl.InsertOne(sessCtx, bson.M{
			"fromUserId": fromUserId,
			"toUserId":   toUserId,
			"amount":     amount,
			"timestamp":  time.Now(),
		})
		if err != nil {
			return fmt.Errorf("failed to log transfer: %v", err)
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("money transfer failed: %v", err)
	}

	fmt.Printf("Money transfer completed successfully: %.2f from %v to %v\n",
		amount, fromUserId, toUserId)
	return nil
}

// ExampleContextWithTimeout demonstrates using context with timeout
func ExampleContextWithTimeout() error {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Start session with context
	session, err := StartSessionWithContext(ctx, "your_db_name")
	if err != nil {
		return fmt.Errorf("failed to start session: %v", err)
	}
	defer session.EndSession()

	// Execute transaction with timeout
	err = session.WithTransaction(func(sessCtx mongo.SessionContext) error {
		// Your transaction operations here
		usersColl := Coll("your_db_name", "users")
		_, err := usersColl.InsertOne(sessCtx, bson.M{
			"name":  "Timeout Test User",
			"email": "<EMAIL>",
		})
		return err
	})

	if err != nil {
		return fmt.Errorf("transaction with timeout failed: %v", err)
	}

	fmt.Println("Transaction with timeout completed successfully")
	return nil
}
