package gohelper

import (
	"context"
	"fmt"
	"time"

	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// TokenUpdateOptions contains token update information
type TokenUpdateOptions struct {
	Token          *bson.M     // The new token
	TokenClusterTs time.Time   // Timestamp when token was created
	TokenChangeTs  time.Time   // Timestamp when token was updated
	End            func(error) // Function to end the watch operation
	ResumeMt       time.Time   // Timestamp to resume from
}

// GetToken gets a new token from a collection
func GetToken(col *gomongo.MongoCollection) (*bson.M, time.Time, error) {
	golog.Info("Start getting new token from DB")

	if col == nil {
		return nil, time.Time{}, fmt.Errorf("collection is nil")
	}

	// Create a context with longer timeout
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// First try to insert a dummy document to ensure connection is ready
	_, err := col.InsertOne(ctx, bson.M{"_dummy": time.Now()})
	if err != nil {
		golog.Error("GetToken insert dummy error:", err)
		return nil, time.Time{}, fmt.Errorf("failed to insert dummy document: %w", err)
	}

	opts := options.ChangeStream().
		SetFullDocument(options.UpdateLookup).
		SetMaxAwaitTime(3 * time.Hour)

	// Create change stream with proper context
	changeStream, err := col.Watch(ctx, mongo.Pipeline{}, opts)
	if err != nil {
		golog.Error("GetToken watch error:", err)
		return nil, time.Time{}, err
	}
	defer func() {
		if cerr := changeStream.Close(context.Background()); cerr != nil {
			golog.Error("Error closing change stream:", cerr)
		}
	}()

	golog.Debug("Change stream created for GetToken")

	// Try to get an event with shorter timeout
	eventCtx, eventCancel := context.WithTimeout(ctx, 2*time.Second)
	defer eventCancel()

	// Try to get an event
	ok := changeStream.Next(eventCtx)
	if !ok {
		// Check for error
		if err := changeStream.Err(); err != nil {
			golog.Error("GetToken changeStream error:", err)
			return nil, time.Time{}, err
		}
		// No error and no next, this is expected timeout
		golog.Debug("GetToken timeout, this is expected")
	}

	// Attempt to get the resume token
	resumeTokenRaw := changeStream.ResumeToken()
	if resumeTokenRaw != nil {
		var tokenDoc bson.M
		if err := bson.Unmarshal(resumeTokenRaw, &tokenDoc); err != nil {
			err = fmt.Errorf("failed to unmarshal resume token: %w", err)
			golog.Error("GetToken unmarshal error:", err)
			return nil, time.Time{}, err
		}
		token := &bson.M{"_data": tokenDoc["_data"]}
		tokenClusterTs := time.Now()
		golog.Debug("Got resume token", "token", token)
		return token, tokenClusterTs, nil
	}

	// If we got here, we didn't get a token
	err = fmt.Errorf("getToken error: no resume token received after short wait")
	golog.Error("GetToken no token error:", err)
	return nil, time.Time{}, err
}

// GetNewTokenAndProcessRemainingDocsOptions contains options for getting new token and processing remaining docs
type GetNewTokenAndProcessRemainingDocsOptions struct {
	FromCol       *gomongo.MongoCollection // Collection to process
	Query         bson.M                   // Query to find remaining documents
	ProcessOneFn  func(bson.M) error       // Function to process each document
	HighWaterMark int                      // Maximum number of documents to process at once
	Context       context.Context          // Context for the operation
	Cancel        context.CancelFunc       // Cancel function for the operation
}

// GetNewTokenAndProcessRemainingDocs gets a new token and processes remaining documents
func GetNewTokenAndProcessRemainingDocs(opt GetNewTokenAndProcessRemainingDocsOptions) (*bson.M, time.Time, error) {
	if opt.HighWaterMark == 0 {
		opt.HighWaterMark = 1
	}

	token, tokenClusterTs, err := GetToken(opt.FromCol)
	if err != nil {
		return nil, time.Time{}, err
	}

	golog.Info("getNewTokenAndProcessRemainingDocs, query:", opt.Query, "new token:", token)

	// Create cursor with maxTimeMS
	opts := options.Find().SetMaxTime(3 * time.Hour)
	cursor, err := opt.FromCol.Find(context.Background(), opt.Query, opts)
	if err != nil {
		return nil, time.Time{}, err
	}
	defer func() {
		if err := cursor.Close(context.Background()); err != nil {
			golog.Error("error closing cursor:", err)
		}
	}()

	oldRecordCnt := 0
	var processErr error
	var resultToken *bson.M
	var resultTokenClusterTs time.Time

	// Create streaming options
	streamOpts := StreamingOptions{
		Stream: cursor,
		Process: func(item interface{}) error {
			doc, ok := item.(bson.M)
			if !ok {
				return fmt.Errorf("invalid document type: %T", item)
			}
			oldRecordCnt++
			return opt.ProcessOneFn(doc)
		},
		End: func(err error) {
			processErr = err
			resultToken = token
			resultTokenClusterTs = tokenClusterTs
			golog.Info("finish getNewTokenAndProcessRemainingDocs", oldRecordCnt, "records, new token:", token)
		},
		Error: func(err error) {
			golog.Error("Processing error:", err)
		},
		High:    opt.HighWaterMark,
		Verbose: 3,
	}

	// Start streaming
	if err := Streaming(opt.Context, &streamOpts); err != nil {
		return nil, time.Time{}, err
	}

	if processErr != nil {
		return nil, time.Time{}, processErr
	}

	return resultToken, resultTokenClusterTs, nil
}

// UpdateFields contains the fields to update in the system data
type UpdateFields struct {
	Token          any
	TokenClusterTs time.Time
	TokenChangeTs  time.Time
	Status         string
	ResumeMt       time.Time
}

// UpdateSysDataFunc is the function type for updating system data
type UpdateSysDataFunc func(UpdateFields) error

// GetUpdateSysdataFunction returns a function to update system data
func GetUpdateSysdataFunction(
	SysData *gomongo.MongoCollection,
	SysDataId interface{},
) UpdateSysDataFunc {
	if SysData == nil || SysDataId == nil {
		golog.Error("SysData and sysDataId required")
		return nil
	}

	return func(fields UpdateFields) error {
		// Set default status
		status := fields.Status
		if status == "" {
			status = "running"
		}

		// Prepare update set
		set := bson.M{
			"_id":    SysDataId,
			"status": status,
		}

		// Add optional fields
		if !fields.TokenClusterTs.IsZero() {
			set["tokenClusterTs"] = fields.TokenClusterTs
		}
		if !fields.TokenChangeTs.IsZero() {
			set["tokenChangeTs"] = fields.TokenChangeTs
		}
		if fields.Token != nil {
			// Convert token to JSON string
			tokenBytes, err := bson.MarshalExtJSON(fields.Token, false, false)
			if err != nil {
				golog.Error("Error marshaling token to JSON:", "err", err)
				return err
			}
			set["token"] = string(tokenBytes)
		}
		if !fields.ResumeMt.IsZero() {
			set["resumeMt"] = fields.ResumeMt
		}

		// Prepare update operation
		update := bson.M{"$set": set}
		golog.Debug("updateMongoToken", update)

		// Execute update
		opts := options.Update().SetUpsert(true)
		_, err := SysData.UpdateOne(context.Background(), bson.M{"_id": SysDataId}, update, opts)
		if err != nil {
			golog.Error("Error updating system data:", err)
		}

		return err
	}
}
