package gohelper

import (
	"context"
	"fmt"
	"os"
	"sync"
	"time"

	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	FinishWithError    = "Finish with Error"
	FinishWithoutError = "Finish"
	RunningNormal      = "Running normal"
	RunningWithWarning = "Running with warning"
	RunningError       = "Running with Error"
	HasRunningProcess  = "there's a running process. please kill or use force to start"
)

// NormalStatusList contains statuses that are considered normal
var NormalStatusList = []string{
	FinishWithoutError,
	RunningNormal,
}

// WarningStatusList contains statuses that are considered warnings
var WarningStatusList = []string{
	RunningWithWarning,
}

// ErrorStatusList contains statuses that indicate errors
var ErrorStatusList = []string{
	FinishWithError,
	RunningError,
}

// AllStatusList contains all possible statuses
var AllStatusList = append(append(NormalStatusList, WarningStatusList...), ErrorStatusList...)

// ProcessMonitor tracks operation intervals and updates MongoDB
type ProcessMonitor struct {
	mu               sync.Mutex
	operationTimes   map[string][]time.Time
	maxIntervals     map[string]time.Duration
	updateInterval   time.Duration
	processStatusCol *gomongo.MongoCollection
	id               string
	fileName         string
	stopChan         chan struct{}
	multiplier       float64 // Multiplier for max interval tolerance
	lastStatus       string
}

// ProcessMonitorOptions contains options for creating a new ProcessMonitor
type ProcessMonitorOptions struct {
	ProcessStatusCol *gomongo.MongoCollection
	ID               string
	FileName         string
	UpdateInterval   time.Duration
	Multiplier       float64
}

// NewProcessMonitor creates a new process monitor with options
func NewProcessMonitor(opts ProcessMonitorOptions) (*ProcessMonitor, error) {
	if opts.ProcessStatusCol == nil {
		golog.Error("ProcessStatusCol required")
		return nil, fmt.Errorf("ProcessStatusCol required")
	}
	if opts.ID == "" {
		golog.Error("id required")
		return nil, fmt.Errorf("id required")
	}
	if opts.FileName == "" {
		golog.Error("fileName required")
		return nil, fmt.Errorf("fileName required")
	}

	interval := 5 * time.Minute // Default interval
	if opts.UpdateInterval > 0 {
		interval = opts.UpdateInterval
	}

	multiplier := 1.0 // Default multiplier
	if opts.Multiplier > 0 {
		multiplier = opts.Multiplier
	}

	newProcessMonitor := &ProcessMonitor{
		operationTimes:   make(map[string][]time.Time),
		maxIntervals:     make(map[string]time.Duration),
		updateInterval:   interval,
		processStatusCol: opts.ProcessStatusCol,
		id:               opts.ID,
		fileName:         opts.FileName,
		stopChan:         make(chan struct{}),
		multiplier:       multiplier,
	}
	newProcessMonitor.StartMonitoring()
	return newProcessMonitor, nil
}

// SetMultiplier sets the multiplier for max interval tolerance
func (pm *ProcessMonitor) SetMultiplier(multiplier float64) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	pm.multiplier = multiplier
}

// StartMonitoring runs the monitoring process in a separate goroutine
func (pm *ProcessMonitor) StartMonitoring() {
	go pm.monitorLoop()
}

// StopMonitoring stops the monitoring process
func (pm *ProcessMonitor) StopMonitoring() {
	close(pm.stopChan)
}

// RecordOperation records the time of an operation
func (pm *ProcessMonitor) RecordOperation(operation string) {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	now := time.Now()
	times := pm.operationTimes[operation]
	if len(times) >= 10 {
		newTimes := append(times[1:], now)
		pm.operationTimes[operation] = newTimes
	} else {
		pm.operationTimes[operation] = append(times, now)
	}

	var maxInterval time.Duration
	// Calculate max interval
	if len(times) > 1 {
		maxInterval = time.Duration(0)
		for i := 1; i < len(times); i++ {
			interval := times[i].Sub(times[i-1])
			if interval > maxInterval {
				maxInterval = interval
			}
		}
	} else {
		maxInterval = pm.updateInterval
	}
	pm.maxIntervals[operation] = maxInterval
}

// monitorLoop continuously monitors operations and updates MongoDB
func (pm *ProcessMonitor) monitorLoop() {
	ticker := time.NewTicker(pm.updateInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			func() {
				defer func() {
					if r := recover(); r != nil {
						golog.Error("Recovered from panic in checkOperations", "panic", r)
					}
				}()
				pm.checkOperations()
			}()
			// Only update to RunningNormal if checkOperations didn't set RunningError
			if pm.lastStatus == RunningNormal {
				opts := UpdateProcessStatusOptions{
					Status: RunningNormal,
				}
				if err := pm.UpdateProcessStatus(opts); err != nil {
					golog.Error("Error updating process status", "error", err)
				}
			}
		case <-pm.stopChan:
			return
		}
	}
}

// checkOperations checks if any operations have exceeded their max interval
func (pm *ProcessMonitor) checkOperations() {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	now := time.Now()
	hasError := false
	errorMsg := ""

	for operation, times := range pm.operationTimes {
		if len(times) == 0 {
			continue
		}

		lastTime := times[len(times)-1]
		maxInterval := pm.maxIntervals[operation]
		tolerance := time.Duration(float64(maxInterval) * pm.multiplier)
		if now.Sub(lastTime) > tolerance {
			hasError = true
			errorMsg = fmt.Sprintf("Operation '%s' has exceeded max interval (current: %v, max: %v, tolerance: %v)",
				operation, now.Sub(lastTime), maxInterval, tolerance)
			golog.Warn(errorMsg)
		}
	}

	if hasError {
		pm.lastStatus = RunningWithWarning
		opts := UpdateProcessStatusOptions{
			Status:   RunningWithWarning,
			ErrorMsg: &errorMsg,
		}
		if err := pm.UpdateProcessStatus(opts); err != nil {
			golog.Error("Error updating process status", "error", err)
		}
	} else {
		pm.lastStatus = RunningNormal
	}
}

// UpdateProcessStatusOptions contains options for updating the process status in MongoDB
type UpdateProcessStatusOptions struct {
	Status   string
	StartTs  *time.Time
	ErrorMsg *string
	Stats    interface{}
}

// UpdateProcessStatus updates the process status in MongoDB
func (pm *ProcessMonitor) UpdateProcessStatus(opts UpdateProcessStatusOptions) error {
	if opts.Status == "" {
		golog.Error("status required")
		return fmt.Errorf("status required")
	}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	now := time.Now()
	nextTs := now.Add(pm.updateInterval)
	update := bson.M{
		"$set": bson.M{
			"pid":      os.Getpid(),
			"ppid":     os.Getppid(),
			"host":     os.Getenv("HOSTNAME"),
			"fileName": pm.fileName,
			"status":   opts.Status,
			"nextTs":   nextTs,
		},
	}

	if opts.StartTs != nil {
		update["$set"].(bson.M)["startTs"] = opts.StartTs
	}

	if opts.ErrorMsg != nil && *opts.ErrorMsg != "" {
		update["$set"].(bson.M)["notify"] = *opts.ErrorMsg
	} else {
		update["$unset"] = bson.M{"notify": 1}
	}

	if opts.Stats != nil {
		update["$set"].(bson.M)["stats"] = opts.Stats
	}
	_, err := pm.processStatusCol.UpdateOne(
		ctx,
		bson.M{"_id": pm.id + "_" + os.Getenv("HOSTNAME")},
		update,
		options.Update().SetUpsert(true),
	)
	if err != nil {
		golog.Error("Error updating process status", "error", err)
		return err
	}
	return nil
}

// CheckRunningProcess checks if a process is running
func (pm *ProcessMonitor) CheckRunningProcess() (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	processId := pm.id + "_" + os.Getenv("HOSTNAME")
	golog.Debug("CheckRunningProcess", "id", processId, "hostname", os.Getenv("HOSTNAME"))

	query := bson.M{"_id": processId}
	golog.Debug("MongoDB query", "query", query)

	res := pm.processStatusCol.FindOne(ctx, query)
	if res == nil {
		golog.Debug("No process found", "id", processId)
		return false, nil
	}

	raw, err := res.Raw()
	if err != nil {
		if err.Error() == "mongo: no documents in result" {
			golog.Debug("No process document found", "id", processId)
			return false, nil
		}
		golog.Error("Error getting raw data",
			"error", err,
			"id", processId,
			"error_type", fmt.Sprintf("%T", err),
			"error_string", err.Error())
		return false, fmt.Errorf("failed to get raw data: %v", err)
	}

	status, err := raw.LookupErr("status")
	if err != nil {
		golog.Error("Error getting status",
			"error", err,
			"id", processId,
			"error_type", fmt.Sprintf("%T", err),
			"error_string", err.Error())
		return false, fmt.Errorf("failed to get status: %v", err)
	}

	statusStr := status.StringValue()
	golog.Debug("Process status", "id", processId, "status", statusStr)
	return statusStr == RunningNormal || statusStr == RunningWithWarning || statusStr == RunningError, nil
}

// updateStatusWhenAllFinish updates the status and calls the exit function
func (pm *ProcessMonitor) updateStatusWhenAllFinish(errorMsg string, exitFn interface{}) {
	status := FinishWithoutError
	if errorMsg != "" {
		status = FinishWithError
	}
	now := time.Now()
	opts := UpdateProcessStatusOptions{
		Status:   status,
		StartTs:  &now,
		ErrorMsg: &errorMsg,
	}
	err := pm.UpdateProcessStatus(opts)
	if err != nil {
		golog.Error("Error updating process status", "error", err)
	}

	switch fn := exitFn.(type) {
	case func(interface{}, ...int):
		fn(errorMsg)
	case func(string):
		fn(errorMsg)
	default:
		golog.Error("exitFn is not a valid function", "exitFn", exitFn)
	}
}

// GetHandleExitFn returns the appropriate exit handler based on whether updateSysData is provided
func (pm *ProcessMonitor) GetHandleExitFn(exitFn interface{}, updateSysData func(UpdateFields) error) interface{} {
	if updateSysData != nil {
		return pm.getHandleExitFnWithUpdateSysdata(updateSysData)
	}
	return pm.getHandleExitFnBasic(exitFn)
}

// getHandleExitFnBasic returns a basic exit handler
func (pm *ProcessMonitor) getHandleExitFnBasic(exitFn interface{}) func(interface{}) {
	return func(errorMsg interface{}) {
		var errorStr string
		if errorObj, ok := errorMsg.(map[string]interface{}); ok {
			if err, ok := errorObj["error"].(string); ok {
				errorStr = err
			}
		} else if err, ok := errorMsg.(string); ok {
			errorStr = err
		} else if err, ok := errorMsg.(error); ok {
			errorStr = err.Error()
		}
		pm.updateStatusWhenAllFinish(errorStr, exitFn)
	}
}

// getHandleExitFnWithUpdateSysdata returns a function to handle process exit with system data update
func (pm *ProcessMonitor) getHandleExitFnWithUpdateSysdata(
	updateSysData func(UpdateFields) error,
) func(map[string]interface{}) {
	return func(params map[string]interface{}) {
		errorMsg := ""
		if msg, ok := params["error"].(string); ok {
			errorMsg = msg
		}

		watchedObject, ok := params["watchedObject"].(*WatchObject)
		if !ok {
			golog.Warn("watchedObject not found in params", "error", ok)
			switch fn := params["exitFn"].(type) {
			case func(interface{}, ...int):
				pm.updateStatusWhenAllFinish(errorMsg, fn)
			case func(string):
				pm.updateStatusWhenAllFinish(errorMsg, fn)
			default:
				golog.Error("exitFn is not a valid function", "exitFn", params["exitFn"])
			}
			return
		}

		checkedLoop := 0
		processingCounter := watchedObject.processingCounter

		// allFinished is a function that updates the system data and calls the exit function
		allFinished := func() {
			if updateSysData != nil {
				sysData := UpdateFields{
					Status: "complete",
				}
				if token := watchedObject.currentToken; token != nil {
					sysData.Token = token
				}
				if tokenClusterTs := watchedObject.currentTokenClusterTs; !tokenClusterTs.IsZero() {
					sysData.TokenClusterTs = tokenClusterTs
				}
				if tokenChangeTs := watchedObject.currentTokenChangeTs; !tokenChangeTs.IsZero() {
					sysData.TokenChangeTs = tokenChangeTs
				}

				golog.Info("updateSysData in getHandleExitFnForWatch token:",
					"currentToken", watchedObject.currentToken,
					"currentTokenClusterTs", watchedObject.currentTokenClusterTs,
					"currentTokenChangeTs", watchedObject.currentTokenChangeTs)

				err := updateSysData(sysData)
				if err != nil {
					golog.Error("Error updating system data", "error", err)
				}
			}

			// Cleanup after all operations are done
			if watchedObject != nil {
				watchedObject.End(nil)
			}
			switch fn := params["exitFn"].(type) {
			case func(interface{}, ...int):
				pm.updateStatusWhenAllFinish(errorMsg, fn)
			case func(string):
				pm.updateStatusWhenAllFinish(errorMsg, fn)
			default:
				golog.Error("exitFn is not a valid function", "exitFn", params["exitFn"])
			}
		}

		var doCheckAll func()
		// doCheckAll is a function that checks if all processes are finished
		doCheckAll = func() {
			golog.Info("Checking process completion",
				"processingCounter", processingCounter,
				"checkedLoop", checkedLoop,
				"ns", watchedObject.ns)

			if processingCounter <= 0 || checkedLoop > 10 {
				allFinished()
				return
			}
			// check if all processes are finished
			time.AfterFunc(time.Second, func() {
				checkedLoop++
				doCheckAll()
			})
		}

		// start the check
		doCheckAll()
	}
}

// Exit exits the program with an error message
func Exit(err interface{}, code ...int) {
	var exitCode = 0
	var errMsg error = nil

	// Handle error message
	switch v := err.(type) {
	case nil:
		// No error message, exit with success
		exitCode = 0
	case error:
		errMsg = v
		exitCode = 1
	case string:
		if v != "" {
			errMsg = fmt.Errorf("%s", v)
			exitCode = 1
		}
	default:
		golog.Fatal("Unknown exit input type", "input", v)
		os.Exit(1)
	}

	// Override code if provided
	if len(code) > 0 {
		exitCode = code[0]
	}

	// Log and exit
	if exitCode > 0 {
		if errMsg != nil {
			golog.Fatal("[CRITICAL] exit with error", "code", exitCode, "error", errMsg.Error())
		} else {
			golog.Fatal("[CRITICAL] exit with error code", "code", exitCode)
		}
		// TODO: add error notify
		// errorNotify(errMsg)
	} else {
		golog.Info("[INFO] exit with code", "code", exitCode)
	}

	os.Exit(exitCode)
}
