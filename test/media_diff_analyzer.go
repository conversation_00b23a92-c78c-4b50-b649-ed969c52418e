package goresodownload

import (
	"context"
	"fmt"
	"path/filepath"
	"sort"
	"strconv"
	"time"

	gofile "github.com/real-rm/gofile"
	"github.com/real-rm/gohelper"
	golog "github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	// Media types
	MediaTypePhoto = "Photo"
	MediaTypePDF   = "pdf"
	MediaTypeZip   = "zip"

	// Media categories
	MediaCategoryPhoto = "Photo"
	MediaCategoryDoc   = "Document"

	// Default file extension
	DefaultImageExt = ".jpg"
)

// BoardLogsTable maps board type to its corresponding logs table
var BoardLogsTable = map[string]string{
	"CAR": "mls_car_logs",
	"DDF": "reso_crea_logs",
	"BRE": "bridge_bcre_logs",
	"EDM": "mls_rae_logs",
}

// MediaInfo represents media information extracted from change document
type MediaInfo struct {
	ID           string
	Sid          string
	NewMediaList []bson.M
	Timestamp    time.Time
}

// MediaTaskInfo represents information needed to generate media tasks
type MediaTaskInfo struct {
	Sid       string
	NewMedia  []bson.M
	OldMedia  []bson.M
	Timestamp time.Time
	Board     string
}

// TrebMediaTaskInfo represents information needed to generate TREB media tasks
type TrebMediaTaskInfo struct {
	Sid        string
	NewMedias  []interface{}
	Timestamp  time.Time
	LocalPaths []string
}

// MediaTask represents a download task
type MediaTask struct {
	// TaskID   string
	Sid      string
	MediaKey string
	URL      string
	Type     string // photo/pdf/zip
	DestPath string
	IsPhoto  bool
}

// DeleteTask represents a file deletion task
type DeleteTask struct {
	Sid      string
	MediaKey string
	Path     string
}

// AnalysisResult represents the result of media analysis
type AnalysisResult struct {
	ID            string
	Sid           string
	DownloadTasks []MediaTask
	DeleteTasks   []DeleteTask
	PhoLH         []int // photo hash list
	DocLH         []int // document hash list
	PropTsD       int
}

// MediaItemInfo represents extracted information from a media item
type MediaItemInfo struct {
	MediaKey string
	URL      string
	Type     string
	IsPhoto  bool
	Category string
}

// MediaDiffAnalyzer analyzes differences between old and new media lists
type MediaDiffAnalyzer struct {
}

// NewMediaDiffAnalyzer creates a new MediaDiffAnalyzer instance
func NewMediaDiffAnalyzer() *MediaDiffAnalyzer {
	return &MediaDiffAnalyzer{}
}

// Analyze compares old and new media lists and generates download and delete tasks
func (a *MediaDiffAnalyzer) Analyze(changeDoc bson.M, board string) (AnalysisResult, error) {
	// For treb board, use special handling
	if board == "TRB" {
		localPaths := gofile.GetImageDir(board)
		if len(localPaths) == 0 {
			golog.Error("No local paths provided", "board", board)
			return AnalysisResult{}, fmt.Errorf("no local paths provided")
		}
		return a.analyzeTreb(changeDoc, localPaths)
	}

	// For regular boards, validate board type
	if _, exists := BoardLogsTable[board]; !exists {
		golog.Error("Invalid board", "board", board)
		return AnalysisResult{}, fmt.Errorf("invalid board: %s", board)
	}

	// Extract media information
	mediaInfo, err := a.getRegularMediaInfo(changeDoc)
	golog.Debug("getRegularMediaInfo return", "mediaInfo", mediaInfo, "err", err)
	if err != nil {
		golog.Error("Failed to get regular media info",
			"changeDoc", changeDoc,
			"error", err)
		return AnalysisResult{}, err
	}

	// Get old media from logs table
	oldMedia, err := a.getOldMediaFromLogs(mediaInfo.Sid, board)
	golog.Debug("getOldMediaFromLogs return", "oldMedia", oldMedia, "err", err)
	if err != nil {
		golog.Error("Failed to get old media from logs",
			"sid", mediaInfo.Sid,
			"error", err)
		return AnalysisResult{}, err
	}

	// Prepare tasks
	taskInfo := &MediaTaskInfo{
		Sid:       mediaInfo.Sid,
		NewMedia:  mediaInfo.NewMediaList,
		OldMedia:  oldMedia,
		Timestamp: mediaInfo.Timestamp,
		Board:     board,
	}
	downloadTasks, deleteTasks, err := a.prepareRegularMediaTasks(taskInfo)
	if err != nil {
		return AnalysisResult{}, err
	}

	// Generate hash lists
	phoLH := a.generatePhotoHashList(mediaInfo.NewMediaList)
	docLH := a.generateDocHashList(mediaInfo.NewMediaList)

	golog.Info("Regular board media analysis complete",
		"_id", mediaInfo.ID,
		"sid", mediaInfo.Sid,
		"downloadTasks", len(downloadTasks),
		"deleteTasks", len(deleteTasks),
		"phoLHCount", len(phoLH),
		"docLHCount", len(docLH),
		"propTsD", gohelper.TimeToDateInt(mediaInfo.Timestamp),
	)

	return AnalysisResult{
		ID:            mediaInfo.ID,
		Sid:           mediaInfo.Sid,
		DownloadTasks: downloadTasks,
		DeleteTasks:   deleteTasks,
		PhoLH:         phoLH,
		DocLH:         docLH,
		PropTsD:       gohelper.TimeToDateInt(mediaInfo.Timestamp),
	}, nil
}

// getRegularMediaInfo extracts media information from change document
func (a *MediaDiffAnalyzer) getRegularMediaInfo(changeDoc bson.M) (*MediaInfo, error) {
	var sid string
	var _id string
	var fullDoc bson.M
	var ok bool
	if fullDoc, ok = changeDoc["fullDocument"].(bson.M); ok {
		if key, ok := fullDoc["ListingId"].(string); ok {
			sid = key
			golog.Debug("getRegularMediaInfo1", "sid", sid)
		}
		if key, ok := fullDoc["_id"].(string); ok {
			_id = key
			golog.Debug("getRegularMediaInfo2", "_id", _id)
		}
	}
	if sid == "" || _id == "" {
		return nil, fmt.Errorf("invalid listingId or _id field format in change document")
	}

	// Get new media from fullDocument
	golog.Debug("getRegularMediaInfo3", "media", fullDoc["Media"])
	mediaField := fullDoc["Media"]
	if mediaField == nil {
		return nil, fmt.Errorf("no Media field in fullDocument")
	}

	// Convert media field to []bson.M
	var newMediaList []bson.M
	switch v := mediaField.(type) {
	case []bson.M:
		newMediaList = v
	case []interface{}:
		for _, m := range v {
			if mediaMap, ok := m.(bson.M); ok {
				newMediaList = append(newMediaList, mediaMap)
			}
		}
	case primitive.A:
		for _, m := range v {
			if mediaMap, ok := m.(bson.M); ok {
				newMediaList = append(newMediaList, mediaMap)
			}
		}
	default:
		golog.Error("Invalid media field type",
			"type", fmt.Sprintf("%T", mediaField))
		return nil, fmt.Errorf("invalid media field format in change document")
	}

	golog.Debug("getRegularMediaInfo4", "newMediaList", newMediaList)

	var ts time.Time
	if full, ok := changeDoc["fullDocument"].(bson.M); ok {
		switch v := full["ts"].(type) {
		case time.Time:
			ts = v
		case primitive.DateTime:
			ts = v.Time()
		default:
			return nil, fmt.Errorf("invalid ts field type: %T", v)
		}
	}
	golog.Debug("getRegularMediaInfo5", "ts", ts)

	return &MediaInfo{
		ID:           _id,
		Sid:          sid,
		NewMediaList: newMediaList,
		Timestamp:    ts,
	}, nil
}

// prepareRegularMediaTasks prepares media tasks for regular boards
func (a *MediaDiffAnalyzer) prepareRegularMediaTasks(info *MediaTaskInfo) ([]MediaTask, []DeleteTask, error) {
	// build map for comparison
	oldMap := make(map[string]bson.M)
	for _, m := range info.OldMedia {
		if mediaKey, ok := m["MediaKey"].(string); ok {
			oldMap[mediaKey] = m
		}
	}

	newMap := make(map[string]bson.M)
	for _, m := range info.NewMedia {
		if mediaKey, ok := m["MediaKey"].(string); ok {
			newMap[mediaKey] = m
		}
	}

	var (
		keepList   []bson.M
		toDownload []bson.M
		toDelete   []bson.M
	)

	// find media to keep, download and delete
	for _, m := range info.NewMedia {
		if mediaKey, ok := m["MediaKey"].(string); ok {
			if _, exists := oldMap[mediaKey]; exists {
				keepList = append(keepList, m) // keep
			} else {
				toDownload = append(toDownload, m) // new
			}
		}
	}

	for _, m := range info.OldMedia {
		if mediaKey, ok := m["MediaKey"].(string); ok {
			if _, exists := newMap[mediaKey]; !exists {
				toDelete = append(toDelete, m) // deleted
			}
		}
	}

	// Get full file path
	filePath, err := gofile.GetFullFilePath(info.Timestamp, info.Board, info.Sid)
	if err != nil {
		golog.Error("Failed to get full file path",
			"sid", info.Sid,
			"error", err)
		return nil, nil, err
	}

	golog.Debug("toDownload", "count", len(toDownload))
	golog.Debug("toDelete", "count", len(toDelete))
	golog.Debug("keepList", "count", len(keepList))

	// Generate download tasks
	downloadTasks := a.generateRegularDownloadTasks(toDownload, info.Sid, filePath)

	// Generate delete tasks
	deleteTasks := a.generateRegularDeleteTasks(toDelete, info.Sid, filePath)

	return downloadTasks, deleteTasks, nil
}

// generateRegularDownloadTasks generates download tasks for regular boards
func (a *MediaDiffAnalyzer) generateRegularDownloadTasks(toDownload []bson.M, sid string, filePath string) []MediaTask {
	var downloadTasks []MediaTask
	for _, m := range toDownload {
		info, err := a.extractMediaInfo(m, false)
		if err != nil {
			golog.Error("Failed to extract media info",
				"sid", sid,
				"error", err)
			continue
		}
		fileName, err := a.generateFileName(sid, m)
		if err != nil {
			golog.Error("Failed to generate file name",
				"sid", sid,
				"error", err)
			continue
		}

		task := MediaTask{
			Sid:      sid,
			MediaKey: info.MediaKey,
			URL:      info.URL,
			Type:     info.Type,
			IsPhoto:  info.IsPhoto,
			DestPath: filepath.Join(filePath, fileName),
		}
		downloadTasks = append(downloadTasks, task)
		golog.Debug("New media found for download",
			"sid", sid,
			"mediaKey", info.MediaKey,
			"order", m["Order"],
			"type", info.Type)
	}
	return downloadTasks
}

// generateRegularDeleteTasks generates delete tasks for regular boards
func (a *MediaDiffAnalyzer) generateRegularDeleteTasks(toDelete []bson.M, sid string, filePath string) []DeleteTask {
	var deleteTasks []DeleteTask
	for _, m := range toDelete {
		task := DeleteTask{
			Sid:      sid,
			MediaKey: m["MediaKey"].(string),
			Path:     filePath,
		}
		deleteTasks = append(deleteTasks, task)
		golog.Debug("Old media found for deletion",
			"sid", sid,
			"mediaKey", m["MediaKey"],
			"type", m["MimeType"])
	}
	return deleteTasks
}

// generateFileName generates a file name for a media item
func (a *MediaDiffAnalyzer) generateFileName(sid string, media bson.M) (string, error) {
	if media == nil {
		return "", fmt.Errorf("media info is nil")
	}

	// Get media key
	var mediaKey string
	if key, ok := media["MediaKey"].(string); ok {
		mediaKey = key
	} else if key, ok := media["key"].(string); ok {
		mediaKey = key
	}
	if mediaKey == "" {
		return "", fmt.Errorf("missing media key")
	}

	// Get URL for file extension
	var url string
	if u, ok := media["MediaURL"].(string); ok {
		url = u
	} else if u, ok := media["url"].(string); ok {
		url = u
	}
	if url == "" {
		return "", fmt.Errorf("missing URL")
	}

	// Get file extension from URL
	ext := filepath.Ext(url)
	if ext == "" {
		ext = ".jpg" // Default to jpg if no extension found
	}

	// Generate hash for filename
	hash := gofile.MurmurToInt32(mediaKey)
	fileName, err := gofile.Int32ToBase62(hash)
	if err != nil {
		return "", err
	}
	fileName = fmt.Sprintf("%s_%s%s", sid, fileName, ext)

	return fileName, nil
}

// checkFileExists checks if a file exists in any of the local paths
func (a *MediaDiffAnalyzer) checkFileExists(fileName string, localPaths []string, localFiles map[string]bool) bool {
	for _, path := range localPaths {
		if localFiles[filepath.Join(path, fileName)] {
			return true
		}
	}
	return false
}

// extractMediaInfo extracts common media information from a media item
func (a *MediaDiffAnalyzer) extractMediaInfo(media bson.M, isTreb bool) (*MediaItemInfo, error) {
	if media == nil {
		return nil, fmt.Errorf("media info is nil")
	}

	info := &MediaItemInfo{}

	// Get media key
	if isTreb {
		if key, ok := media["key"].(string); ok {
			info.MediaKey = key
		}
	} else {
		if key, ok := media["MediaKey"].(string); ok {
			info.MediaKey = key
		}
	}
	if info.MediaKey == "" {
		return nil, fmt.Errorf("missing media key")
	}

	// Get URL
	if isTreb {
		if u, ok := media["url"].(string); ok {
			info.URL = u
		}
	} else {
		// Try different URL field names
		if u, ok := media["MediaURL"].(string); ok {
			info.URL = u
		} else if u, ok := media["URL"].(string); ok {
			info.URL = u
		} else if u, ok := media["url"].(string); ok {
			info.URL = u
		}
	}
	if info.URL == "" {
		return nil, fmt.Errorf("missing URL")
	}

	// Get media type
	if isTreb {
		if t, ok := media["tp"].(string); ok {
			info.Type = t
		}
	} else {
		if t, ok := media["MimeType"].(string); ok {
			info.Type = t
		}
	}
	if info.Type == "" {
		golog.Error("Missing media type",
			"sid", info.MediaKey,
			"media", media)
		info.Type = "image/jpeg"
	}

	// Get media type
	if isTreb {
		if t, ok := media["cat"].(string); ok {
			info.Category = t
		}
	} else {
		if t, ok := media["MediaCategory"].(string); ok {
			info.Category = t
		}
	}
	if info.Category == "" {
		golog.Error("Missing media category",
			"sid", info.MediaKey,
			"media", media)
		info.Category = "Photo"
	}
	info.IsPhoto = info.Category == "Photo"

	return info, nil
}

// generateTrebMediaTasks generates download tasks for media items
func (a *MediaDiffAnalyzer) generateTrebMediaTasks(medias []bson.M, sid string, filePath string, localPaths []string, localFiles map[string]bool) []MediaTask {
	var tasks []MediaTask

	for _, m := range medias {
		info, err := a.extractMediaInfo(m, true)
		if err != nil {
			golog.Error("Failed to extract media info",
				"sid", sid,
				"error", err)
			continue
		}

		fileName, err := a.generateFileName(sid, m)
		if err != nil {
			golog.Error("Failed to generate file name",
				"sid", sid,
				"error", err)
			continue
		}

		// Check if file exists in any of the local paths
		if a.checkFileExists(fileName, localPaths, localFiles) {
			continue
		}

		task := MediaTask{
			Sid:      sid,
			MediaKey: info.MediaKey,
			URL:      info.URL,
			Type:     info.Type,
			IsPhoto:  info.IsPhoto,
			DestPath: filepath.Join(filePath, fileName),
		}
		tasks = append(tasks, task)
		golog.Debug("New media found for download in TREB",
			"sid", sid,
			"mediaKey", info.MediaKey,
			"type", info.Type)
	}

	return tasks
}

// generatePhotoHashList generates a list of hashes from media items
func (a *MediaDiffAnalyzer) generatePhotoHashList(medias []bson.M) []int {
	var hashList []int
	if len(medias) == 0 {
		return []int{} // Return empty slice instead of nil
	}

	for _, m := range medias {
		// Try to get key from either "key" or "MediaKey" field
		var keyStr string
		var ok bool

		// Try "key" first (for TRB)
		if key, exists := m["key"]; exists && key != nil {
			keyStr, ok = key.(string)
			if !ok || keyStr == "" {
				continue
			}
		} else if key, exists := m["MediaKey"]; exists && key != nil {
			// Try "MediaKey" (for regular boards)
			keyStr, ok = key.(string)
			if !ok || keyStr == "" {
				continue
			}
		} else {
			continue
		}

		// Check if it's a photo
		isPhoto := false
		if cat, ok := m["MediaCategory"].(string); ok {
			isPhoto = cat == "Photo"
		} else if cat, ok := m["cat"].(string); ok {
			isPhoto = cat == "Photo"
		}

		// Only add to hash list if it's a photo
		if isPhoto {
			hash := gofile.MurmurToInt32(keyStr)
			hashList = append(hashList, int(hash))
		}
	}
	return hashList
}

// generateDocHashList generates hash list for documents
func (a *MediaDiffAnalyzer) generateDocHashList(medias []bson.M) []int {
	var hashList []int
	for _, m := range medias {
		// Try to get key from either "key" or "MediaKey" field
		var keyStr string
		var ok bool
		if keyStr, ok = m["key"].(string); !ok {
			if keyStr, ok = m["MediaKey"].(string); !ok {
				continue
			}
		}

		// Check if it's a document
		isDoc := false
		if cat, ok := m["MediaCategory"].(string); ok {
			isDoc = cat == "Document"
		} else if cat, ok := m["cat"].(string); ok {
			isDoc = cat == "Document"
		}

		// Only add to hash list if it's a document
		if isDoc {
			hashList = append(hashList, int(gofile.MurmurToInt32(keyStr)))
		}
	}
	return hashList
}

// getNewTrebMediaInfo extracts media information from change document
func (a *MediaDiffAnalyzer) getNewTrebMediaInfo(changeDoc bson.M) (string, string, []interface{}, time.Time, error) {
	var sid string
	var _id string
	if fullDoc, ok := changeDoc["fullDocument"].(bson.M); ok {
		if key, ok := fullDoc["ListingId"].(string); ok {
			sid = key
		}
		if key, ok := fullDoc["_id"].(string); ok {
			_id = key
		}
	}
	if sid == "" || _id == "" {
		return "", "", nil, time.Time{}, fmt.Errorf("invalid listingId or _id field format in change document")
	}

	// Get new media from fullDocument
	var newMedias []interface{}
	if fullDoc, ok := changeDoc["fullDocument"].(bson.M); ok {
		if media, ok := fullDoc["media"].([]interface{}); ok {
			newMedias = media
		}
	}

	// Get timestamp for file path
	var ts time.Time
	if fullDoc, ok := changeDoc["fullDocument"].(bson.M); ok {
		switch v := fullDoc["ts"].(type) {
		case time.Time:
			ts = v
		case primitive.DateTime:
			ts = v.Time()
		default:
			return "", "", nil, time.Time{}, fmt.Errorf("invalid ts field type: %T", v)
		}
	}

	return sid, _id, newMedias, ts, nil
}

// prepareTrebMediaTasks prepares media tasks for TREB board
func (a *MediaDiffAnalyzer) prepareTrebMediaTasks(info *TrebMediaTaskInfo) ([]MediaTask, []DeleteTask, []bson.M, []bson.M, error) {
	// Get deleted media from reso_treb_evow_media table
	deletedMedias, err := a.getTrebMedia(info.Sid)
	if err != nil {
		golog.Error("Failed to get TREB media",
			"sid", info.Sid,
			"error", err)
		return nil, nil, nil, nil, err
	}
	// Get full file path
	filePath, err := gofile.GetFullFilePath(info.Timestamp, "TRB", info.Sid)
	if err != nil {
		golog.Error("Failed to get full file path",
			"sid", info.Sid,
			"error", err)
		return nil, nil, nil, nil, err
	}

	// Get existing local files
	localFiles := make(map[string]bool)
	if len(info.LocalPaths) > 0 {
		files, err := gofile.ListFiles(info.LocalPaths[0], filePath, info.Sid)
		if err != nil {
			golog.Error("Failed to list local files",
				"path", info.LocalPaths[0],
				"filePath", filePath,
				"sid", info.Sid,
				"error", err)
		}
		for _, file := range files {
			localFiles[file] = true
		}
	}
	// Filter media items
	var filteredMedias []bson.M
	var filteredDocs []bson.M
	if len(info.NewMedias) > 0 {
		var mediaList []bson.M
		for _, m := range info.NewMedias {
			if mediaMap, ok := m.(bson.M); ok {
				mediaList = append(mediaList, mediaMap)
			}
		}
		filteredMedias, filteredDocs = FilterMedias(mediaList)
	}

	// Generate tasks
	downloadTasks := a.generateTrebMediaTasks(filteredMedias, info.Sid, filePath, info.LocalPaths, localFiles)
	downloadTasks = append(downloadTasks, a.generateTrebMediaTasks(filteredDocs, info.Sid, filePath, info.LocalPaths, localFiles)...)

	deleteTasks := a.generateTrebDeleteTasks(deletedMedias, info.Sid, filePath)

	return downloadTasks, deleteTasks, filteredMedias, filteredDocs, nil
}

// generateTrebDeleteTasks generates delete tasks from deleted media
func (a *MediaDiffAnalyzer) generateTrebDeleteTasks(deletedMedias []bson.M, sid string, filePath string) []DeleteTask {
	var deleteTasks []DeleteTask
	for _, m := range deletedMedias {
		task := DeleteTask{
			Sid:      sid,
			MediaKey: m["MK"].(string),
			Path:     filePath,
		}
		deleteTasks = append(deleteTasks, task)
		golog.Debug("Deleted media found in TREB",
			"sid", sid,
			"mediaKey", m["MediaKey"])
	}
	return deleteTasks
}

// analyzeTreb handles analysis for TREB board
func (a *MediaDiffAnalyzer) analyzeTreb(changeDoc bson.M, localPaths []string) (AnalysisResult, error) {
	if len(localPaths) == 0 {
		return AnalysisResult{}, fmt.Errorf("no local paths provided")
	}

	// Extract media information
	sid, _id, newMedias, ts, err := a.getNewTrebMediaInfo(changeDoc)
	if err != nil {
		return AnalysisResult{}, err
	}

	// Prepare tasks
	taskInfo := &TrebMediaTaskInfo{
		Sid:        sid,
		NewMedias:  newMedias,
		Timestamp:  ts,
		LocalPaths: localPaths,
	}
	downloadTasks, deleteTasks, filteredMedias, filteredDocs, err := a.prepareTrebMediaTasks(taskInfo)
	if err != nil {
		return AnalysisResult{}, err
	}

	// Generate hash lists
	phoLH := a.generatePhotoHashList(filteredMedias)
	docLH := a.generateDocHashList(filteredDocs)

	golog.Info("TREB media analysis complete",
		"sid", sid,
		"downloadTasks", len(downloadTasks),
		"deleteTasks", len(deleteTasks),
		"phoLHCount", len(phoLH),
		"docLHCount", len(docLH))

	return AnalysisResult{
		ID:            _id,
		Sid:           sid,
		DownloadTasks: downloadTasks,
		DeleteTasks:   deleteTasks,
		PhoLH:         phoLH,
		DocLH:         docLH,
		PropTsD:       gohelper.TimeToDateInt(ts),
	}, nil
}

// getOldMediaFromLogs gets the old media list from logs table
func (a *MediaDiffAnalyzer) getOldMediaFromLogs(sid string, board string) ([]bson.M, error) {
	// get logs table name from board type
	logsTable, exists := BoardLogsTable[board]
	if !exists {
		return nil, fmt.Errorf("unsupported board type: %s", board)
	}

	// get logs collection
	logsCol := gomongo.Coll("rni", logsTable)
	if logsCol == nil {
		return nil, fmt.Errorf("failed to get logs collection: %s", logsTable)
	}

	query := bson.M{"sid": sid}
	sort := bson.D{{Key: "_mt", Value: -1}}
	skip := 1
	ctx := context.Background()

	var oldLog bson.M
	err := logsCol.FindOne(ctx, query, sort, skip).Decode(&oldLog)
	if err != nil {
		if err.Error() == "mongo: no documents in result" {
			golog.Info("No old media found in logs table",
				"board", board,
				"logsTable", logsTable,
				"sid", sid)
			return []bson.M{}, nil
		}
		return nil, fmt.Errorf("failed to get old media from logs table: %w", err)
	}

	golog.Debug("Found old log",
		"sid", sid,
		"board", board,
		"log", oldLog)

	// parse media field
	data, ok := oldLog["data"].(bson.M)
	if !ok {
		golog.Error("Invalid data field format",
			"sid", sid,
			"data", oldLog["data"])
		return nil, fmt.Errorf("invalid data field format in log for sid: %s", sid)
	}

	golog.Debug("Found data field",
		"sid", sid,
		"data", data)

	mediaField, ok := data["media"]
	if !ok {
		golog.Info("No media field found",
			"sid", sid)
		return []bson.M{}, nil
	}

	golog.Debug("Found media field",
		"sid", sid,
		"media", mediaField,
		"type", fmt.Sprintf("%T", mediaField))

	// Convert media field to []bson.M
	var mediaArray []bson.M
	switch v := mediaField.(type) {
	case []bson.M:
		mediaArray = v
	case []interface{}:
		if len(v) == 0 {
			mediaArray = []bson.M{}
		} else {
			for _, m := range v {
				if mediaMap, ok := m.(bson.M); ok {
					mediaArray = append(mediaArray, mediaMap)
				}
			}
		}
	case primitive.A:
		if len(v) == 0 {
			mediaArray = []bson.M{}
		} else {
			for _, m := range v {
				if mediaMap, ok := m.(bson.M); ok {
					mediaArray = append(mediaArray, mediaMap)
				}
			}
		}
	case nil:
		mediaArray = []bson.M{}
	default:
		golog.Error("Invalid media field type",
			"sid", sid,
			"type", fmt.Sprintf("%T", mediaField))
		return nil, fmt.Errorf("invalid media field format in log for sid: %s", sid)
	}

	golog.Debug("Converted media array",
		"sid", sid,
		"mediaArray", mediaArray)

	return mediaArray, nil
}

// getTrebMedia gets media from reso_treb_evow_media table
func (a *MediaDiffAnalyzer) getTrebMedia(sid string) ([]bson.M, error) {
	mediaCol := gomongo.Coll("rni", "reso_treb_evow_media")
	query := bson.M{"RRK": sid, "MS": "Deleted"}
	deletedMedias, err := mediaCol.FindToArray(context.Background(), query)
	if err != nil {
		return nil, fmt.Errorf("failed to get deleted media from reso_treb_evow_media table for sid %s: %w", sid, err)
	}

	golog.Debug("Got deleted media from reso_treb_evow_media",
		"sid", sid,
		"count", len(deletedMedias))

	return deletedMedias, nil
}

// FilterMedias filters medias by status, permission, and statement
func FilterMedias(medias []bson.M) ([]bson.M, []bson.M) {
	if len(medias) == 0 {
		return []bson.M{}, []bson.M{}
	}

	// Define size priority for photo selection
	sizePriority := []string{
		"LargestNoWatermark",
		"Largest",
		"Large",
		"media",
		"Thumbnail",
	}

	// Initialize result slices
	photoResult := make([]bson.M, 0)
	docResult := make([]bson.M, 0)

	// Group photos by ID
	groupedPhotos := make(map[string][]bson.M)
	for _, media := range medias {
		// Skip invalid media
		u, ok := media["url"].(string)
		if !ok || u == "" {
			golog.Warn("Skipping invalid media, url is empty",
				"media", media)
			continue
		}
		status, ok := media["status"].(string)
		if !ok || status != "Active" {
			golog.Warn("Skipping invalid media, status is not Active",
				"media", media)
			continue
		}

		// Handle non-photo media
		if media["tp"] != "image/jpeg" || media["cat"] != MediaCategoryPhoto {
			docResult = append(docResult, media)
			continue
		}

		// Group photos by ID
		id := media["id"].(string)
		groupedPhotos[id] = append(groupedPhotos[id], media)
	}

	// Sort photo IDs numerically
	sortedIDs := make([]string, 0, len(groupedPhotos))
	for id := range groupedPhotos {
		sortedIDs = append(sortedIDs, id)
	}
	sort.Slice(sortedIDs, func(i, j int) bool {
		ai, _ := strconv.Atoi(sortedIDs[i])
		bi, _ := strconv.Atoi(sortedIDs[j])
		return ai < bi
	})

	// Select best photo for each ID
	for _, id := range sortedIDs {
		photos := groupedPhotos[id]
		selectedPhoto := selectBestPhoto(photos, sizePriority)
		if selectedPhoto != nil {
			photoResult = append(photoResult, *selectedPhoto)
		}
	}

	return photoResult, docResult
}

// selectBestPhoto selects the best photo based on size priority
func selectBestPhoto(photos []bson.M, sizePriority []string) *bson.M {
	if len(photos) == 0 {
		return nil
	}

	// If no size description, return the first photo
	if photos[0]["sizeDesc"] == nil || photos[0]["sizeDesc"] == "" {
		return &photos[0]
	}

	// Try to find the highest priority size
	for _, size := range sizePriority {
		for _, photo := range photos {
			if photo["sizeDesc"] == size {
				return &photo
			}
		}
	}

	// If no matching size found, return the first photo
	return &photos[0]
}

// AnalyzeMediaDiff analyzes differences between old and new media lists
func (a *MediaDiffAnalyzer) AnalyzeMediaDiff(oldMedias []bson.M, newMedias []bson.M) ([]bson.M, []bson.M, error) {
	// build map for comparison
	oldMap := make(map[string]bson.M)
	for _, m := range oldMedias {
		key, ok := m["key"]
		if !ok || key == nil {
			continue
		}
		mediaKey, ok := key.(string)
		if !ok {
			continue
		}
		oldMap[mediaKey] = m
	}

	newMap := make(map[string]bson.M)
	for _, m := range newMedias {
		key, ok := m["key"]
		if !ok || key == nil {
			continue
		}
		mediaKey, ok := key.(string)
		if !ok {
			continue
		}
		newMap[mediaKey] = m
	}

	var (
		toDownload []bson.M
		toDelete   []bson.M
	)

	// find media to download and delete
	for _, m := range newMedias {
		key, ok := m["key"]
		if !ok || key == nil {
			continue
		}
		mediaKey, ok := key.(string)
		if !ok {
			continue
		}
		if _, exists := oldMap[mediaKey]; !exists {
			toDownload = append(toDownload, m) // new
		}
	}

	for _, m := range oldMedias {
		key, ok := m["key"]
		if !ok || key == nil {
			continue
		}
		mediaKey, ok := key.(string)
		if !ok {
			continue
		}
		if _, exists := newMap[mediaKey]; !exists {
			toDelete = append(toDelete, m) // deleted
		}
	}

	return toDownload, toDelete, nil
}
