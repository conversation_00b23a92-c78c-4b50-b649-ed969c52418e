package goresodownload

import (
	"testing"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

func TestExamplePriorityCalculation(t *testing.T) {
	// Test that the example function runs without panicking
	defer func() {
		if r := recover(); r != nil {
			t.<PERSON><PERSON><PERSON>("ExamplePriorityCalculation() panicked: %v", r)
		}
	}()

	// This function prints to stdout, so we just test it doesn't crash
	ExamplePriorityCalculation()
}

func TestGetPriorityBreakdown(t *testing.T) {
	tests := []struct {
		name            string
		record          bson.M
		boardType       string
		existMergedProp bson.M
		expectError     bool
	}{
		{
			name: "Valid TRB property",
			record: bson.M{
				"MlsStatus":           "Active",
				"ListingContractDate": time.Now().Format("2006-01-02"),
				"CountyOrParish":      "Toronto",
				"PropertyType":        "Residential",
				"StateOrProvince":     "ON",
				"DaysOnMarket":        5,
			},
			boardType: "TRB",
			existMergedProp: bson.M{
				"MlsStatus":           "Active",
				"ListingContractDate": time.Now().Format("2006-01-02"),
				"CountyOrParish":      "Toronto",
				"PropertyType":        "Residential",
				"StateOrProvince":     "ON",
				"DaysOnMarket":        5,
			},
			expectError: false,
		},
		{
			name: "Valid CAR property with photos",
			record: bson.M{
				"MlsStatus":           "Sold",
				"ListingContractDate": "2023-01-01",
				"CountyOrParish":      "Vancouver",
				"PropertyType":        "Condo",
				"StateOrProvince":     "BC",
				"DaysOnMarket":        30,
			},
			boardType: "CAR",
			existMergedProp: bson.M{
				"MlsStatus":           "Sold",
				"ListingContractDate": "2023-01-01",
				"CountyOrParish":      "Vancouver",
				"PropertyType":        "Condo",
				"StateOrProvince":     "BC",
				"DaysOnMarket":        30,
				"phoHL":               []interface{}{123, 456},
				"tnHL":                []interface{}{"hash1", "hash2"},
			},
			expectError: false,
		},
		{
			name:            "Invalid board type",
			record:          bson.M{"MlsStatus": "Active"},
			boardType:       "INVALID",
			existMergedProp: bson.M{"MlsStatus": "Active"},
			expectError:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			breakdown := GetPriorityBreakdown(tt.record, tt.boardType, tt.existMergedProp)

			if tt.expectError {
				if _, hasError := breakdown["error"]; !hasError {
					t.Errorf("GetPriorityBreakdown() expected error but got none")
				}
				return
			}

			if breakdown == nil {
				t.Errorf("GetPriorityBreakdown() returned nil breakdown")
				return
			}

			// Verify breakdown has some components
			if len(breakdown) == 0 {
				t.Errorf("GetPriorityBreakdown() returned empty breakdown")
			}

			// Check for common components
			totalPriority := 0
			for _, value := range breakdown {
				totalPriority += value
			}

			if totalPriority <= 0 {
				t.Errorf("GetPriorityBreakdown() total priority should be positive, got %d", totalPriority)
			}
		})
	}
}

func TestGetPriorityBreakdownEdgeCases(t *testing.T) {
	t.Run("Property with all maximum bonuses", func(t *testing.T) {
		record := bson.M{
			"MlsStatus":           "Active",
			"ListingContractDate": time.Now().Format("2006-01-02"),
			"CountyOrParish":      "Toronto",
			"PropertyType":        "Residential",
			"StateOrProvince":     "ON",
			"DaysOnMarket":        1,
		}
		existMergedProp := bson.M{
			"MlsStatus":           "Active",
			"ListingContractDate": time.Now().Format("2006-01-02"),
			"CountyOrParish":      "Toronto",
			"PropertyType":        "Residential",
			"StateOrProvince":     "ON",
			"DaysOnMarket":        1,
			// No photos for maximum bonus
		}

		breakdown := GetPriorityBreakdown(record, "TRB", existMergedProp)
		if breakdown == nil {
			t.Errorf("GetPriorityBreakdown() returned nil")
			return
		}

		// Calculate total priority
		totalPriority := 0
		for _, value := range breakdown {
			totalPriority += value
		}

		// Should have high priority due to all bonuses
		if totalPriority < 40000 {
			t.Errorf("GetPriorityBreakdown() expected high priority (>40000), got %d", totalPriority)
		}
	})

	t.Run("Property with minimum bonuses", func(t *testing.T) {
		record := bson.M{
			"MlsStatus":           "Expired",
			"ListingContractDate": "2020-01-01",
			"CountyOrParish":      "Vancouver",
			"PropertyType":        "Commercial",
			"StateOrProvince":     "BC",
			"DaysOnMarket":        100,
		}
		existMergedProp := bson.M{
			"MlsStatus":           "Expired",
			"ListingContractDate": "2020-01-01",
			"CountyOrParish":      "Vancouver",
			"PropertyType":        "Commercial",
			"StateOrProvince":     "BC",
			"DaysOnMarket":        100,
			"phoHL":               []interface{}{123, 456},
			"tnHL":                []interface{}{"hash1", "hash2"},
		}

		breakdown := GetPriorityBreakdown(record, "CAR", existMergedProp)
		if breakdown == nil {
			t.Errorf("GetPriorityBreakdown() returned nil")
			return
		}

		// Calculate total priority
		totalPriority := 0
		for _, value := range breakdown {
			totalPriority += value
		}

		// Should have low priority due to no bonuses
		if totalPriority > 1000 {
			t.Errorf("GetPriorityBreakdown() expected low priority (<1000), got %d", totalPriority)
		}
	})
}

// Helper function to check if a string contains a substring (case-insensitive)
func containsIgnoreCase(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					containsSubstring(s, substr)))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
