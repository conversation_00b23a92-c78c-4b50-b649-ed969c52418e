package goresodownload

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
)

func DecompressMediaZlib(compressed interface{}, isBase64 bool) (map[string]interface{}, error) {
	var buf []byte

	// 1. nil check
	if compressed == nil {
		return nil, errors.New("compressed data is required")
	}

	// 2. check base64 mode
	if isBase64 {
		str, ok := compressed.(string)
		if !ok {
			return nil, errors.New("base64 input must be a string")
		}
		decoded, err := base64.StdEncoding.DecodeString(str)
		if err != nil {
			return nil, fmt.Errorf("base64 decode error: %v", err)
		}
		buf = decoded
	} else {
		bytesVal, ok := compressed.([]byte)
		if !ok {
			return nil, errors.New("compressed data must be []byte when isBase64 is false")
		}
		buf = bytesVal
	}

	// 3. decompress gzip/zlib
	reader, err := gzip.NewReader(bytes.NewReader(buf))
	if err != nil {
		return nil, fmt.Errorf("gzip decompress error: %v", err)
	}
	defer reader.Close()

	var unzipped bytes.Buffer
	if _, err := io.Copy(&unzipped, reader); err != nil {
		return nil, fmt.Errorf("error reading unzipped data: %v", err)
	}

	// 4. parse JSON
	var result map[string]interface{}
	if err := json.Unmarshal(unzipped.Bytes(), &result); err != nil {
		return nil, fmt.Errorf("json parse error: %v", err)
	}

	return result, nil
}

func CompressMediaZlib(media interface{}, useBase64 bool) ([]byte, error) {
	if media == nil {
		log.Println("mediaArray is required for CompressMediaZlib")
		return nil, errors.New("media is nil")
	}

	// serialize media to JSON string
	mediaBytes, err := json.Marshal(media)
	if err != nil {
		log.Printf("JSON marshal error: %v\n", err)
		return nil, err
	}

	if len(mediaBytes) > 50*1024*1024 { // 50MB limit
		log.Println("mediaArray too large (> 50MB) for compression")
		return nil, errors.New("media too large")
	}

	// gzip compress
	var buf bytes.Buffer
	writer := gzip.NewWriter(&buf)
	_, err = writer.Write(mediaBytes)
	if err != nil {
		log.Printf("gzip write error: %v\n", err)
		return nil, err
	}
	writer.Close()

	compressed := buf.Bytes()

	if useBase64 {
		encoded := base64.StdEncoding.EncodeToString(compressed)
		return []byte(encoded), nil
	}

	return compressed, nil
}
