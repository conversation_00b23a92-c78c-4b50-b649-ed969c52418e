name: Go CI

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]
  workflow_dispatch:  # 允许手动触发

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        token: ${{ secrets.WORKFLOW_TOKEN_GITHUB }}  # 使用 GitHub Token 进行 HTTPS 克隆

    - name: Configure Git for private repos
      run: |
        git config --global url."https://${{ secrets.WORKFLOW_TOKEN_GITHUB }}@github.com/".insteadOf "https://github.com/"

    - name: Configure GOPRIVATE
      run: |
        echo "GOPRIVATE=github.com/real-rm/*" >> $GITHUB_ENV
        echo "GOPROXY=direct" >> $GITHUB_ENV

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: 'stable'
        check-latest: true

    - name: Download dependencies
      run: |
        cd src
        go mod tidy

    - name: Run golangci-lint
      uses: golangci/golangci-lint-action@v7
      with:
        version: latest
        args: --timeout=5m
        working-directory: src  # 进入 src 目录

  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        token: ${{ secrets.WORKFLOW_TOKEN_GITHUB }}  # 使用 GitHub Token 进行 HTTPS 克隆

    - name: Configure Git for private repos
      run: |
        git config --global url."https://${{ secrets.WORKFLOW_TOKEN_GITHUB }}@github.com/".insteadOf "https://github.com/"

    - name: Configure GOPRIVATE
      run: |
        echo "GOPRIVATE=github.com/real-rm/*" >> $GITHUB_ENV
        echo "GOPROXY=direct" >> $GITHUB_ENV

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: 'stable'
        check-latest: true

    - name: Tidy up Go modules
      run: |
        cd src
        go mod tidy

    - name: Build
      run: |
        cd src
        go build -v ./...

    - name: Run Tests
      run: |
        cd src
        go test -v ./...
