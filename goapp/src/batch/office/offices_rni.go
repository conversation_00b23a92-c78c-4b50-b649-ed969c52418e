/*
Description: Process office data from RNI databases and sync to vow.offices_rni
Usage:
	go run batch/office/offices_rni.go  -src=BRE -time=2025-03-27 -dryrun
  -src: Source to process (BRE, DDF, CLG, CAR, EDM)
    BRE: bridge_bcre_office
    DDF: reso_crea_office
    CLG: mls_creb_offices
    CAR: mls_car_offices
    EDM: mls_rae_offices
  -time: Time to process (format: 2006-01-02)
  -dryrun: Dry run mode - only log operations without executing them
newConfig:
	./start.sh -t batch -n offices_rni -cmd "batch/office/offices_rni.go -dryrun"
Author: Maggie
Run frequency: everyday
*/

package main

import (
	"context"
	"flag"
	"fmt"
	"sync"
	"time"

	goconfig "github.com/real-rm/goconfig"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	gospeedmeter "github.com/real-rm/gospeedmeter"
	gostreaming "github.com/real-rm/gostreaming"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	srcFlag    = flag.String("src", "", "Source to process (BRE, DDF, CLG, CAR, EDM)")
	timeFlag   = flag.String("time", "", "Time to process (format: 2006-01-02)")
	dryrunFlag = flag.Bool("dryrun", false, "Dry run mode - only log operations without executing them")
)

// Source mapping for RNI collections
var srcMapping = map[string]string{
	"BRE": "bridge_bcre_office",
	"DDF": "reso_crea_office",
	"CLG": "mls_creb_offices",
	"CAR": "mls_car_offices",
	"EDM": "mls_rae_offices",
}

// Define the order of processing sources
var srcOrder = []string{"BRE", "DDF", "CLG", "CAR", "EDM"}

// offices_rni is the target collection for storing RNI office data
var offices_rni *gomongo.MongoCollection

var speedMeter = gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})
var speedMutex sync.Mutex
var startTime = time.Now()

// setting up logging, and establishing MongoDB connection.
func init() {
	// Load configuration
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v", err)
	}

	// Initialize logging first
	if err := golog.InitLog(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}

	// Initialize MongoDB last (after config is loaded)
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}
	offices_rni = gomongo.Coll("vow", "offices_rni")
}

// getLatestMT gets the latest _mt from vow.offices_rni
func getLatestMT(ctx context.Context) (primitive.DateTime, error) {
	opts := gomongo.QueryOptions{
		Projection: bson.D{{Key: "_mt", Value: 1}},
		Sort:       bson.D{{Key: "_mt", Value: -1}},
		Limit:      1,
	}

	var result struct {
		MT primitive.DateTime `bson:"_mt"`
	}

	err := offices_rni.FindOne(ctx, bson.M{}, opts).Decode(&result)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return primitive.DateTime(0), nil
		}
		return primitive.DateTime(0), fmt.Errorf("failed to get latest _mt: %v", err)
	}

	return result.MT, nil
}

// processRNIOffice processes a single RNI office collection
func processRNIOffice(ctx context.Context, src, collection string, queryTime primitive.DateTime) error {
	golog.Infof("Processing collection: %s", collection)

	coll := gomongo.Coll("rni", collection)
	targetColl := offices_rni

	query := bson.M{
		"_mt": bson.M{"$gte": queryTime},
	}

	cur, err := coll.Find(ctx, query)
	if err != nil {
		return fmt.Errorf("failed to find documents: %v", err)
	}

	defer func() {
		if err := cur.Close(ctx); err != nil {
			golog.Errorf("Failed to close cursor: %v", err)
		}
	}()

	streamOpts := &gostreaming.StreamingOptions{
		Stream: cur,
		Process: func(item interface{}) error {
			speedMutex.Lock()
			speedMeter.Check("processed", 1)
			speedMutex.Unlock()

			// Convert interface{} to bson.M
			var doc bson.M
			switch v := item.(type) {
			case bson.M:
				doc = v
			case primitive.D:
				data, err := bson.Marshal(v)
				if err != nil {
					return fmt.Errorf("failed to marshal document: %v", err)
				}
				if err := bson.Unmarshal(data, &doc); err != nil {
					return fmt.Errorf("failed to unmarshal document: %v", err)
				}
			default:
				return fmt.Errorf("unsupported document type: %T", item)
			}

			// Create new document with transformed _id
			newDoc := bson.M{}
			for k, v := range doc {
				if k == "_id" {
					// Transform _id to src + original _id with type-safe conversion
					switch idVal := v.(type) {
					case string:
						newDoc["_id"] = fmt.Sprintf("%s%s", src, idVal)
					case primitive.ObjectID:
						newDoc["_id"] = fmt.Sprintf("%s%s", src, idVal.Hex())
					default:
						// For other types, convert to string representation
						newDoc["_id"] = fmt.Sprintf("%s%v", src, v)
						golog.Debugf("Converting non-standard _id type: %T", v)
					}
				} else {
					newDoc[k] = v
				}
			}

			// Add src field
			newDoc["src"] = src

			if *dryrunFlag {
				golog.Info("Dry run mode: Would upsert document", "id", newDoc["_id"], "newDoc", newDoc)
				speedKey := fmt.Sprintf("dryrun%s", src)
				speedMutex.Lock()
				speedMeter.Check(speedKey, 1)
				speedMutex.Unlock()
				return nil
			}

			// delete _mt and _ts from newDoc
			delete(newDoc, "_mt")
			delete(newDoc, "_ts")

			// upsert into target collection
			update := bson.M{
				"$set": newDoc,
			}
			upsert := true
			result, err := targetColl.UpdateOne(ctx, bson.M{"_id": newDoc["_id"]}, update, &options.UpdateOptions{Upsert: &upsert})
			if err != nil {
				return fmt.Errorf("failed to upsert document: %v", err)
			}
			golog.Debug("Upserted document", "id", newDoc["_id"], "result", result)
			speedKey := fmt.Sprintf("upserted%s", src)
			speedMutex.Lock()
			speedMeter.Check(speedKey, 1)
			speedMutex.Unlock()
			return nil
		},
		End: func(err error) {
			duration := time.Since(startTime)
			fmt.Println("Total process time:", duration)
			speedMutex.Lock()
			if err != nil {
				golog.Error("Stream ended with error", "error", err, speedMeter.ToString(gospeedmeter.UnitM, nil))
			} else {
				golog.Info("Completed", "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			}
			speedMutex.Unlock()
		},
		High: 30,
		// Low:           5,
		// SpeedInterval: 1000,
		Verbose: 2,
	}

	golog.Info("Starting stream processing", "collection", collection)
	if err := gostreaming.Streaming(ctx, streamOpts); err != nil {
		return fmt.Errorf("failed to process collection %s: %v", collection, err)
	}
	golog.Info("Stream processing completed", "collection", collection)

	return nil
}

// processRNIOffices processes offices from RNI collections
func processRNIOffices(ctx context.Context) error {
	// Parse command line flags
	flag.Parse()
	golog.Infof("srcFlag: %s, timeFlag: %s, dryrunFlag: %t", *srcFlag, *timeFlag, *dryrunFlag)

	// Get latest _mt from vow.offices_rni
	latestMT, err := getLatestMT(ctx)
	if err != nil {
		return err
	}

	// Calculate query time
	var queryTime primitive.DateTime
	if *timeFlag != "" {
		t, err := time.Parse("2006-01-02", *timeFlag)
		if err != nil {
			return fmt.Errorf("invalid time format: %v", err)
		}
		queryTime = primitive.NewDateTimeFromTime(t)
	} else {
		switch {
		case latestMT == 0:
			queryTime = primitive.NewDateTimeFromTime(time.Unix(0, 0))
		case latestMT.Time().After(time.Now().Add(-24 * time.Hour)):
			queryTime = primitive.NewDateTimeFromTime(time.Now().Add(-24 * time.Hour))
		default:
			queryTime = latestMT
		}
	}

	// Process collections based on src flag
	if *srcFlag != "" {
		collection, ok := srcMapping[*srcFlag]
		if !ok {
			return fmt.Errorf("invalid source: %s", *srcFlag)
		}
		queryTime = primitive.NewDateTimeFromTime(time.Unix(1970, 0))
		golog.Infof("Processing collection: %s, queryTime: %v", collection, queryTime.Time().UTC())
		return processRNIOffice(ctx, *srcFlag, collection, queryTime)
	}

	// Process all collections if no src specified
	for _, src := range srcOrder {
		collection, ok := srcMapping[src]
		if !ok {
			golog.Warnf("Source %s not found in mapping, skipping", src)
			continue
		}
		golog.Infof("Processing collection: %s, queryTime: %v", collection, queryTime.Time().UTC())
		if err := processRNIOffice(ctx, src, collection, queryTime); err != nil {
			return err
		}
		// Wait for the current collection to complete before processing the next one
		golog.Infof("Completed processing collection: %s", collection)
	}

	return nil
}

func main() {
	ctx := context.Background()
	if err := processRNIOffices(ctx); err != nil {
		golog.Fatal("Failed to process RNI offices", "error", err)
	}
}
