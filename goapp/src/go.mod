module goapp

go 1.24.2

require (
	github.com/elastic/go-elasticsearch/v8 v8.0.0
	github.com/real-rm/goconfig v0.0.0-20250501202444-47bb8cfe118f
	github.com/real-rm/gohelper v0.0.0-20250522214632-2a6c4ec2bb38
	github.com/real-rm/golog v0.0.0-20250508200600-5a27e4511a57
	github.com/real-rm/gomongo v0.0.0-20250501204550-1a32caf3b01b
	github.com/real-rm/gospeedmeter v0.0.0-20250509214336-11530e84d838
	github.com/real-rm/gostreaming v0.0.0-20250509214157-e3c0c300d016
	go.mongodb.org/mongo-driver v1.17.3
)

require (
	github.com/elastic/elastic-transport-go/v8 v8.0.0-alpha // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/klauspost/compress v1.17.2 // indirect
	github.com/montanaflynn/stats v0.7.1 // indirect
	github.com/real-rm/go-toml v0.0.0-20250401202034-5e664e72ac3e // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.1.2 // indirect
	github.com/xdg-go/stringprep v1.0.4 // indirect
	github.com/youmark/pkcs8 v0.0.0-20240726163527-a2c0da244d78 // indirect
	golang.org/x/crypto v0.26.0 // indirect
	golang.org/x/sync v0.12.0 // indirect
	golang.org/x/text v0.23.0 // indirect
)
