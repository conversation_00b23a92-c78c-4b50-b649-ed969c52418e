# RM new app using GoLang

## Project Structure

```bash
.
├── docs
│   ├── change_requests
│   │   ├── YYYYMMDD-[branch-name]-[change-request-title].md # every change needs a request document
│   ├── README.md # naming convention, documentation, and other useful information
├── build # output of the build process
│   ├── tools # helper executables
│   ├── bin # compiled binaries
│   ├── assets # static files, includes html
│       ├── css # css files
│       ├── js # js files
│       ├── images # images
│       └── favicon.ico
├── src
│   ├── README.md # information about the source code
│   ├── lib
│   │   ├── README.md # information about the library
│   │   ├── test_data # test data folder
│   │   ├── config.go # configuration. Instructions shall be at the top of the file. -> moved to golog
│   │   ├── db.go # database
│   │   ├── helper.go # helper functions
│   │   ├── log.go # logging -> moved to golog
│   │   └── mail.go # email -> moved to gomail
│   ├── controller
│   │   ├── README.md
│   │   ├── controller.go
│   │   └── controller_test.go # for unit testing
│   ├── entities # formerly model
│   │   ├── README.md
│   │   ├── test_data # test data folder
│   │   ├── user.go
│   │   └── post.go
│   ├── batch
│   │   ├── mls_import # mls import batches
│   │   │   ├── README.md
│   │   │   ├── lib
│   │   │   │   ├── reso # reso library
│   │   │   │   │   ├── README.md
│   │   │   │   │   ├── reso.go
│   │   │   │   │   └── reso_test.go # for unit testing
│   │   │   │   └── rets # rets library if needed
│   │   │   │       ├── README.md
│   │   │   │       ├── rets.go
│   │   │   │       └── rets_test.go # for unit testing
│   │   │   ├── trb
│   │   │   │   ├── README.md
│   │   │   │   ├── sample_data # folder for sample data
│   │   │   │   ├── test_data # folder for test data if needed
│   │   │   │   ├── trb_import.go
│   │   │   │   └── trb_import_test.go # for unit testing
│   │   │   ├── car
│   │   │   │   ├── README.md
│   │   │   │   ├── sample_data
│   │   │   │   ├── test_data # folder for test data if needed
│   │   │   │   ├── car_import.go
│   │   │   │   └── car_import_test.go # for unit testing
│   │   │   └── ...
│   │   ├── prop # property batches
│   │   │   ├── abc.go
│   │   │   ├── abc_test.go # for unit testing
│   │   │   └── ...
│   │   └── ... # other batches
│   ├── main.go # the main app, web application
│   ├── crm.go # crm app, web application
│   ├── ... # other apps
│   ├── assets # original static files, includes html. Program may change these files in build/assets.
│   │   ├── css # css files
│   │   ├── js # js files
│   │   ├── images # images
│   │   └── favicon.ico
│   ├── test # for integration testing
│   │   ├── README.md
│   │   ├── test_data # test data folder
│   │   ├── test.go # test all
│   │   └── web_app_test.go  # test web app
│   ├── go.mod # Go modules file
│   ├── go.sum # Go modules checksums
│   └── Makefile # Build and development commands
├── .gitignore
├── README.md # entry point with overview and links to other docs
```

## 2. Documentation

See [here](docs/README.md) for naming convention, documentation and other useful information, which is in the `docs` folder.

## 3. Makefile

A simple instruction on 'make' commands, see [here](https://opensource.com/article/18/8/what-how-makefile) or [here](https://www.gnu.org/software/make/manual/make.html) for the original documentation.

See [Makefile](src/Makefile) for building, running, testing, cleaning, etc.

## 4. General Best Practices

### 4.1. Keep documents, code, test code, and test data in sync and in one place

- When adding a new module, add documentation in the header of the file.
- If you add a new test, add the test data to the direct test_data folder below the test file.
- If you add a new function, add a test for it in the same folder.
- When depending resources in other folders, use relative paths and not absolute paths.
- Ask yourself, if I remove this module, can I simply delete the folder or file? If the answer is no, then you need to rethink your folder or code structure. **At least** leave enough information for others to know how to maintain the module.

### 4.2. Keep the code clean

- Keep functions small and focused.
- Keep variables and functions named meaningfully.
- Keep functions and variables scoped to the smallest possible unit.
- Functions should do one thing and do it well.
- Functions should be atomic, not have side effects, not have dependencies.
- Functions should be less than 50 lines of code. Max 100 lines of code.
- Files should be less than 500 lines of code. Max 1000 lines of code.
- Keep the code(functions, variables, etc.) DRY (Don't Repeat Yourself).
- Keep the code readable.

### 4.3. git

- Must use `git diff` to review changes before committing.
- Did I miss anything?
- Did I checked in something that shouldn't be checked in?
- Any typos?
- Any formatting issues?

### 4.4. Review

- Reply to the coderabbit if you think it's wrong. It'll adopt your changes in later review.
- If you think it's right, then follow the instructions of the review.

### 4.5. Keep it simple

- Keep it simple. Keep it stupid.
- Use html and simple css and js libraries for the web app. No frameworks.
- Use simple libraries. Avoid using libraries that have a lot of dependencies.

### 4.6. Ask for help

- If you don't know how to do something, ask for help.
- If you don't know the answer, say so.
- If you don't know the answer, don't make something up.
- If you don't know the answer, don't guess.

### 4.7. One meaning uses one name. One name refers to one meaning.

- One meaning uses one name. One name refers to one meaning.
- The name of the function should be the verb of what it does.
- The name of the variable should be the noun of what it holds.
- If a function has to change/copy the value of a variable, the variable is probably has a wrong name.
- Keep the same name for the same meaning, and vice versa, in your whole file.

### 4.8. Do NOT put unrelated logic into one function

- Use separate functions for different purposes.
- If there are common logic, put it in a separate function, and call it when needed.
- When there are too many parameters, use a struct or a map to pass the parameters. If they are all on the top level, it probably means this function is doing too much.
- If there are two levels or more of if/loop/switch statements, it probably means this function is doing too much. Three levels, it's definitely doing too much.

### 5. Package Setup and Development Tools

#### 5.1 Module Initialization

1. Initialize Go module:

```bash
# Create go.mod file
go mod init github.com/real-rm/gohelper
```

2. Create your source files and corresponding test files, for example:

mail.go
mail_test.go

4. When you need to load the configuration and set up `golog` and `gomongo`, you can place them in `init()`. Also, set the environment variable `RMBASE_FILE_CFG`, for example:"

func init() {
	// Load configuration
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v", err)
	}

	// Initialize logging first
	if err := golog.InitLog(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}

	// Initialize MongoDB last (after config is loaded)
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}
}


```bash
export GOPRIVATE=*      # this is for private repo, you can set this in your ~/.bashrc
# Download and install required packages
go get "github.com/real-rm/golog"
```

4. Install required dependencies:

```bash
# Download and install required packages
go mod tidy
```

5. Local pacakge usage 
If you are modifying two packages at the same time and need to test your changes, you can add the following line to your go.mod file:

```
replace github.com/real-rm/gomongo => ../gomongo
```
This allows Go to use your local version of the package instead of the remote one. 

6. Configure development environment:
- Follow `docs/vscode.md` for VSCode setup
- Follow `docs/github_workflow.md` for GitHub workflow configuration

#### 5.2 Development Tools Installation

The following tools are required for development:

1. golangci-lint: A fast Go linters runner

```bash
# Install golangci-lint
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# Run linter
golangci-lint run
```

2. gomarkdoc: Generate markdown documentation from Go code

```bash
# Install gomarkdoc
go install github.com/princjef/gomarkdoc/cmd/gomarkdoc@latest

# Generate API documentation
gomarkdoc --output api.md .
```

3. Running Tests

```bash
# Run all tests with verbose output
go test -v
```
