# 需求 [offices_rni]

## 反馈

1.  地产认证中——地产公司 选项不全 

## 需求提出人:   Bowen

## 修改人：      Maggie

## 提出日期:     2025-03-26

## 原因

1. 之前 dataImport: POST 'trb'，最后一条 _mt: ISODate('2024-11-14T00:08:02.051Z')
2. 使用的位置是brokerage.coffee，地产认证。 房源点contract没有找到联系人， 会需要office联系方式， treb没有给】

目前数据统计：都有OfficeName，其他字段对应不上。
bridge_bcre_office  总1616 条   
mls_creb_offices    总1330 条
mls_car_offices     总6088 条   
reso_crea_office    总4763 条   
reso_treb_evow_office 总8460 条 [只有名字 不可用]
mls_rae_master_records  listing

{
  _id: '498104',
  OfficeKey: '498104',
  OfficeName: 'RE/MAX CROSSROADS REALTY INC.',
  _mt: ISODate('2025-03-11T17:47:17.847Z'),
  ts: ISODate('2024-12-27T21:46:20.252Z')
}


## 方案
1. raeDownload.coffee 中添加保持office到mls_rae_offices表中
2. batch/saveRaeOffices 处理之前的rae数据，获取所有的office
3. 添加goapp/batch【每天运行一次 _mt 筛选 支持只跑某个src，某个时间点overwrite】，将5个来源office变化更新到vow.offices_rni【找出最大的_mt,至少大于1天】，添加src。_id: 'CAR8wekd'方便后期查询数据


   
## 影响范围
1. raeDownload

## 是否需要补充UT
1. 不需要

## 确认日期:    2025-03-26

## online-step

1. 重新启动raeDownload [appweb]
2. 运行batch saveRaeOffices [appweb]
```
./start.sh -n saveRaeOffices -cmd "lib/batchBase.coffee batch/prop/saveRaeOffices.coffee dryrun"
```
3. goapp batch
  a. goapp/src  路径下安装需要的包
  `go mod tidy`


  b. 合并rmconfig PR：https://github.com/real-rm/rmconfig/pull/11
  修改local.ini，添加：
  [serverBase]
      srcGoAppPath = "../go/goapp/src"
  [golog]
    dir = "/home/<USER>/github/go/goapp/logs"
    #level = "info"
    level = "debug"
    verbose = "verbose.log"
    info = "info.log"
    error = "error.log"
    format = "text"

  c. run batch
  ```
    可以先-dryrun测试：
    ./start.sh -t batch -n offices_rni -cmd "batch/office/offices_rni.go -dryrun"
    没问题后设置cronjob
  	./start.sh -t batch -n offices_rni -cmd "batch/office/offices_rni.go" '*-*-* 03:30:00'"

   