<!-- Generated by SpecStory -->

# Code Revision: Using Helper Interval (2025-04-15 23:21Z)

_**User**_

revise helper_watch to use helper_interval

---

_**Assistant**_

I'll help you revise the `helper_watch.go` file to use the `Interval` implementation from `helper_interval.go`. The main issue is that there are duplicate declarations of `Interval` and `StartInterval` in both files. We should remove these from `helper_watch.go` and use the ones from `helper_interval.go`.

Let me make the necessary changes:


---

```diff
  // WatchCallback is the callback function type for WatchTarget
  type WatchCallback func(error, *WatchObject)
- 
- // Interval represents a timer that can be started and stopped
- type Interval struct {
- 	ticker *time.Ticker
- 	done   chan struct{}
- }
- 
- // StartInterval creates and starts a new interval timer
- func StartInterval(seconds int, fn func()) *Interval {
- 	interval := &Interval{
- 		ticker: time.NewTicker(time.Duration(seconds) * time.Second),
- 		done:   make(chan struct{}),
- 	}
- 
- 	go func() {
- 		for {
- 			select {
- 			case <-interval.ticker.C:
- 				fn()
- 			case <-interval.done:
- 				return
- 			}
- 		}
- 	}()
- 
- 	return interval
- }
- 
- // Stop stops the interval timer
- func (i *Interval) Stop() {
- 	if i.ticker != nil {
- 		i.ticker.Stop()
- 	}
- 	if i.done != nil {
- 		close(i.done)
- 	}
- }
- 
- // saveChangedObj saves the changed object to the import collection
- func saveChangedObj(col *gomongo.MongoCollection, changedObj bson.M) error {
- 	if col == nil {
- 		return nil
- 	}
- 	insertObj := bson.M{
- 		"id":                changedObj["documentKey"],
- 		"operationType":     changedObj["operationType"],
- 		"ns":                changedObj["ns"],
- 		"updateDescription": changedObj["updateDescription"],
- 		"clusterTime":       changedObj["clusterTime"],
- 		"token":             changedObj["_id"],
- 	}
- 	_, err := col.InsertOne(context.Background(), insertObj)
- 	if err != nil {
- 		golog.Error("when insert into importWatchedRecordsCol, error:", err, "changedObj", changedObj)
- 	}
- 	return err
- }
- 
- type UpdateFields struct {
- 	Token          any
- 	TokenClusterTs time.Time
- 	TokenChangeTs  time.Time
- 	Status         string
- 	ResumeMt       time.Time
- }
- 
- type UpdateSysDataFunc func(UpdateFields) error
- 
- // GetUpdateSysdataFunction returns a function to update system data
- func GetUpdateSysdataFunction(
- 	SysData *gomongo.MongoCollection,
- 	SysDataId interface{},
- ) UpdateSysDataFunc {
- 	if SysData == nil || SysDataId == nil {
- 		golog.Error("SysData and sysDataId required")
- 		return nil
- 	}
- 
- 	return func(fields UpdateFields) error {
- 		// Set default status
- 		status := fields.Status
- 		if status == "" {
- 			status = "running"
- 		}
- 
- 		// Prepare update set
- 		set := bson.M{
- 			"_id":    SysDataId,
- 			"status": status,
- 		}
- 
- 		// Add optional fields
- 		if !fields.TokenClusterTs.IsZero() {
- 			set["tokenClusterTs"] = fields.TokenClusterTs
- 		}
- 		if !fields.TokenChangeTs.IsZero() {
- 			set["tokenChangeTs"] = fields.TokenChangeTs
- 		}
- 		if fields.Token != nil {
- 			// Convert token to JSON string
- 			tokenBytes, err := bson.MarshalExtJSON(fields.Token, false, false)
- 			if err != nil {
- 				golog.Error("Error marshaling token to JSON:", err)
- 				return err
- 			}
- 			set["token"] = string(tokenBytes)
- 		}
- 		if !fields.ResumeMt.IsZero() {
- 			set["resumeMt"] = fields.ResumeMt
- 		}
- 
- 		// Prepare update operation
- 		update := bson.M{"$set": set}
- 		golog.Debug("updateMongoToken", update)
- 
- 		// Execute update
- 		opts := options.Update().SetUpsert(true)
- 		_, err := SysData.UpdateOne(context.Background(), bson.M{"_id": SysDataId}, update, opts)
- 		if err != nil {
- 			golog.Error("Error updating system data:", err)
- 		}
- 
- 		return err
- 	}
- }
  
  // WatchTarget starts watching a MongoDB collection for changes
```

---

I notice that the edit accidentally removed some important functions. Let me restore those functions while keeping the Interval-related changes:



---

```diff
  	return resultToken, resultTokenClusterTs, nil
  }
+ 
+ // saveChangedObj saves the changed object to the import collection
+ func saveChangedObj(col *gomongo.MongoCollection, changedObj bson.M) error {
+ 	if col == nil {
+ 		return nil
+ 	}
+ 	insertObj := bson.M{
+ 		"id":                changedObj["documentKey"],
+ 		"operationType":     changedObj["operationType"],
+ 		"ns":                changedObj["ns"],
+ 		"updateDescription": changedObj["updateDescription"],
+ 		"clusterTime":       changedObj["clusterTime"],
+ 		"token":             changedObj["_id"],
+ 	}
+ 	_, err := col.InsertOne(context.Background(), insertObj)
+ 	if err != nil {
+ 		golog.Error("when insert into importWatchedRecordsCol, error:", err, "changedObj", changedObj)
+ 	}
+ 	return err
+ }
+ 
+ type UpdateFields struct {
+ 	Token          any
+ 	TokenClusterTs time.Time
+ 	TokenChangeTs  time.Time
+ 	Status         string
+ 	ResumeMt       time.Time
+ }
+ 
+ type UpdateSysDataFunc func(UpdateFields) error
+ 
+ // GetUpdateSysdataFunction returns a function to update system data
+ func GetUpdateSysdataFunction(
+ 	SysData *gomongo.MongoCollection,
+ 	SysDataId interface{},
+ ) UpdateSysDataFunc {
+ 	if SysData == nil || SysDataId == nil {
+ 		golog.Error("SysData and sysDataId required")
+ 		return nil
+ 	}
+ 
+ 	return func(fields UpdateFields) error {
+ 		// Set default status
+ 		status := fields.Status
+ 		if status == "" {
+ 			status = "running"
+ 		}
+ 
+ 		// Prepare update set
+ 		set := bson.M{
+ 			"_id":    SysDataId,
+ 			"status": status,
+ 		}
+ 
+ 		// Add optional fields
+ 		if !fields.TokenClusterTs.IsZero() {
+ 			set["tokenClusterTs"] = fields.TokenClusterTs
+ 		}
+ 		if !fields.TokenChangeTs.IsZero() {
+ 			set["tokenChangeTs"] = fields.TokenChangeTs
+ 		}
+ 		if fields.Token != nil {
+ 			// Convert token to JSON string
+ 			tokenBytes, err := bson.MarshalExtJSON(fields.Token, false, false)
+ 			if err != nil {
+ 				golog.Error("Error marshaling token to JSON:", err)
+ 				return err
+ 			}
+ 			set["token"] = string(tokenBytes)
+ 		}
+ 		if !fields.ResumeMt.IsZero() {
+ 			set["resumeMt"] = fields.ResumeMt
+ 		}
+ 
+ 		// Prepare update operation
+ 		update := bson.M{"$set": set}
+ 		golog.Debug("updateMongoToken", update)
+ 
+ 		// Execute update
+ 		opts := options.Update().SetUpsert(true)
+ 		_, err := SysData.UpdateOne(context.Background(), bson.M{"_id": SysDataId}, update, opts)
+ 		if err != nil {
+ 			golog.Error("Error updating system data:", err)
+ 		}
+ 
+ 		return err
+ 	}
+ }
  
```

---

The changes have been made successfully. Here's what was done:

1. Removed the duplicate `Interval` struct and `StartInterval` function declarations from `helper_watch.go`
2. Kept the timer-related functionality in `helper_watch.go` that uses the `Interval` type from `helper_interval.go`
3. Restored the accidentally removed functions (`saveChangedObj`, `UpdateFields`, `UpdateSysDataFunc`, and `GetUpdateSysdataFunction`)

The code should now be properly using the `Interval` implementation from `helper_interval.go` while maintaining all the necessary functionality in `helper_watch.go`. The linter errors related to duplicate declarations should be resolved.

The main changes were:
1. Removing the duplicate `Interval` struct and its methods
2. Using the `Interval` type from `helper_interval.go` in the timer-related functions
3. Keeping all the MongoDB-related functionality intact

Is there anything else you'd like me to help you with?

---

