<!-- Generated by SpecStory -->

# Token Handling Error in Change Stream (2025-05-07 20:05Z)

_**User**_

value="Start getting new token from DB"
2025-05-07T16:03:25.591-04:00 level=INFO msg=onTokenUpdate, token={
  "_data": {
    "_id": {
      "$oid": "681bbc8d728a61dce5b73df5"
    },
    "ts": {
      "T": 1746648205,
      "I": 1
    }
  }
}, gStat={}, from:="2025-05-07T16:03:25.423621085-04:00"
2025-05-07T16:03:25.615-04:00 level=ERROR msg=watchProp error, err={}
2025-05-07T16:03:25.615-04:00 level=ERROR msg=gracefulExit error, err="failed to create change stream: (Location40647) Bad resume token: _data of missing or of wrong type. Expected string, got {_data: {_id: {$oid: \"681bbc8d728a61dce5b73df5\"}, ts: Timestamp(1746648205, 1)}}"
panic: runtime error: invalid memory address or nil pointer dereference
[signal SIGSEGV: segmentation violation code=0x1 addr=0x48 pc=0x813f35]

goroutine 1 [running]:
main.init.0.(*ProcessMonitor).GetHandleExitFn.(*ProcessMonitor).getHandleExitFnWithUpdateSysdata.func1(0xc0003a8990)
	/home/<USER>/github/go/gohelper/helper_process.go:401 +0xd5
main.GracefulExit({0xa19dc0, 0xc0001124e0})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:268 +0x1ba
main.WatchProp({0xa1ddd8, 0xc00059e370}, 0xc00059cb40, 0xc0001340d0, {0xc1ff0d03633f1faa, 0xc052216, 0xd48b00}, {0x0, 0x0, 0x0})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:217 +0x4c5
main.GetTokenAndWatch({0xa1ddd8, 0xc00059e370}, 0xc00059cb40)
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:240 +0x173
main.main()
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:378 +0xa3f
exit status 2


---

_**Assistant**_

I'll help analyze and fix the error in the watch token functionality. The main issue appears to be with the token format and handling. Let me break down the error and propose a solution.

The key error message is:
```
Bad resume token: _data of missing or of wrong type. Expected string, got {_data: {_id: {$oid: \"681bbc8d728a61dce5b73df5\"}, ts: Timestamp(1746648205, 1)}}
```

This indicates that MongoDB's change stream is expecting the token in a different format than what we're providing. Let me check the token handling code and make necessary adjustments.

First, I'll look at the token creation code:



---

Read file: gohelper/helper_watch_token.go

---

I see the issue. The token format we're creating is not compatible with MongoDB's change stream resume token format. Let me fix this by modifying the token creation code:


---

```diff
  		return nil, time.Time{}, fmt.Errorf("failed to get MongoDB client")
  	}
- 
- 	// // Create a new session
- 	// session, err := client.StartSession()
- 	// if err != nil {
- 	// 	golog.Error("Failed to start session:", err)
- 	// 	return nil, time.Time{}, err
- 	// }
- 	// defer session.EndSession(context.Background())
  
  	// Create a token with current timestamp
  	token := &bson.M{
- 		"_data": bson.M{
- 			"_id": bson.M{
- 				"$oid": primitive.NewObjectID().Hex(),
- 			},
- 			"ts": primitive.Timestamp{
- 				T: uint32(time.Now().Unix()),
- 				I: 1,
- 			},
+ 		"_data": primitive.Binary{
+ 			Subtype: 0,
+ 			Data:    []byte(primitive.NewObjectID().Hex()),
  		},
  	}
```

---

Now I need to modify the token handling in the watch example to properly handle the token format:


---

```diff
  	// Parse and validate existing token
  	var parsedToken bson.M
- 	if err := json.Unmarshal([]byte(token.(string)), &parsedToken); err != nil {
+ 	if err := bson.UnmarshalExtJSON([]byte(token.(string)), false, &parsedToken); err != nil {
  		golog.Info("Invalid token, starting fresh", "token", token)
  		return GetTokenAndWatch(watchCtx, watchCancel)
```

---

The changes I've made address the token format issue by:

1. Modifying the `GetToken` function to create a token in the correct format for MongoDB change streams using `primitive.Binary` for the `_data` field.
2. Updating the token parsing in `handleTokenAndWatch` to use `bson.UnmarshalExtJSON` instead of `json.Unmarshal` to properly handle BSON format.

The linter error about `main` being redeclared is likely due to the file being in a package that already has a main function. However, since this is an example file, we can keep it as is since it's meant to be run as a standalone program.

These changes should fix the "Bad resume token" error by ensuring that:
1. The token is created in the correct format that MongoDB change streams expect
2. The token is properly serialized and deserialized when stored and retrieved from the database

The token now uses a binary format for the `_data` field, which is what MongoDB's change stream expects, rather than the previous nested BSON structure that was causing the error.

Would you like me to make any additional changes or would you like to test these changes to see if they resolve the issue?

---

_**User**_

2025-05-07T16:06:19.27-04:00 level=INFO msg=Continuing watch from existing token, tokenClusterTs="2025-05-07T20:03:25.591Z"
2025-05-07T16:06:19.27-04:00 level=INFO msg=onTokenUpdate, token={
  "_data": {
    "_id": "681bbc8d728a61dce5b73df5",
    "ts": {
      "T": 1746648205,
      "I": 1
    }
  }
}, gStat={}, from:="2025-05-07T16:06:19.080751368-04:00"
2025-05-07T16:06:19.282-04:00 level=ERROR msg=watchProp error, err={}
2025-05-07T16:06:19.282-04:00 level=ERROR msg=gracefulExit error, err="failed to create change stream: (Location40647) Bad resume token: _data of missing or of wrong type. Expected string, got {_data: {_id: 681bbc8d728a61dce5b73df5, ts: Timestamp(1746648205, 1)}}"
panic: runtime error: invalid memory address or nil pointer dereference
[signal SIGSEGV: segmentation violation code=0x1 addr=0x48 pc=0x81ee75]

goroutine 1 [running]:
main.init.0.(*ProcessMonitor).GetHandleExitFn.(*ProcessMonitor).getHandleExitFnWithUpdateSysdata.func1(0xc000126f30)
	/home/<USER>/github/go/gohelper/helper_process.go:401 +0xd5
main.GracefulExit({0xa28080, 0xc0000201e0})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:267 +0x1ba
main.WatchProp({0xa2c218, 0xc000046140}, 0xc00010e570, 0xc000218090, {0x2339f1c0, 0xedfadb38d, 0xd5db80}, {0xc00044dcd0, 0x1, 0x1})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:216 +0x4c5
main.handleTokenAndWatch({0xa2c218, 0xc000046140}, 0xc00010e570, 0xc0001ac900, {0xc000126b10?, 0x0?, 0xd5db80?})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:302 +0x65e
main.main()
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:385 +0xa85
exit status 2

---

_**Assistant**_

I see the issue is still persisting. The token format is still not correct. Let me check the MongoDB change stream documentation and make another adjustment to fix the token format.

The error message shows that MongoDB is expecting a string for the `_data` field, but we're providing a nested structure. Let me modify the token creation to match MongoDB's expected format:


---

```diff
  	// Create a token with current timestamp
  	token := &bson.M{
- 		"_data": primitive.Binary{
- 			Subtype: 0,
- 			Data:    []byte(primitive.NewObjectID().Hex()),
- 		},
+ 		"_data": primitive.NewObjectID().Hex(),
  	}
  
```

---

Now I need to update the token handling in the watch example to ensure we're properly handling the token format:


---

```diff
  	// Parse and validate existing token
  	var parsedToken bson.M
- 	if err := bson.UnmarshalExtJSON([]byte(token.(string)), false, &parsedToken); err != nil {
- 		golog.Info("Invalid token, starting fresh", "token", token)
+ 	tokenStr, ok := token.(string)
+ 	if !ok {
+ 		golog.Info("Invalid token type, starting fresh", "token", token)
+ 		return GetTokenAndWatch(watchCtx, watchCancel)
+ 	}
+ 
+ 	if err := bson.UnmarshalExtJSON([]byte(tokenStr), false, &parsedToken); err != nil {
+ 		golog.Info("Invalid token format, starting fresh", "token", tokenStr, "error", err)
+ 		return GetTokenAndWatch(watchCtx, watchCancel)
+ 	}
+ 
+ 	// Validate token structure
+ 	if _, ok := parsedToken["_data"]; !ok {
+ 		golog.Info("Token missing _data field, starting fresh", "token", parsedToken)
  		return GetTokenAndWatch(watchCtx, watchCancel)
  	}
```

---

The changes I've made:

1. Modified the `GetToken` function to create a token with a simple string value for the `_data` field, which is what MongoDB's change stream expects.
2. Updated the token handling in `handleTokenAndWatch` to:
   - Properly type check the token before parsing
   - Add better error handling and logging
   - Validate the token structure after parsing
   - Provide more detailed error messages

The linter error about `main` being redeclared is still present, but as mentioned before, this is expected since this is an example file meant to be run as a standalone program.

These changes should fix the "Bad resume token" error by:
1. Creating tokens in the correct string format that MongoDB change streams expect
2. Adding better validation and error handling for token parsing
3. Ensuring the token structure is correct before using it

Would you like to test these changes to see if they resolve the issue? If the error persists, we can try another approach or investigate further.

---

_**User**_

 msg=onTokenUpdate, token={
  "_data": {
    "_id": "681bbc8d728a61dce5b73df5",
    "ts": {
      "T": 1746648205,
      "I": 1
    }
  }
}, gStat={}, from:="2025-05-07T16:07:15.006950523-04:00"
2025-05-07T16:07:15.194-04:00 level=ERROR msg=watchProp error, err={}
2025-05-07T16:07:15.194-04:00 level=ERROR msg=gracefulExit error, err="failed to create change stream: (Location40647) Bad resume token: _data of missing or of wrong type. Expected string, got {_data: {_id: 681bbc8d728a61dce5b73df5, ts: Timestamp(1746648205, 1)}}"
panic: runtime error: invalid memory address or nil pointer dereference
[signal SIGSEGV: segmentation violation code=0x1 addr=0x48 pc=0x81ee15]

goroutine 1 [running]:
main.init.0.(*ProcessMonitor).GetHandleExitFn.(*ProcessMonitor).getHandleExitFnWithUpdateSysdata.func1(0xc0004163c0)
	/home/<USER>/github/go/gohelper/helper_process.go:401 +0xd5
main.GracefulExit({0xa280e0, 0xc000392000})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:267 +0x1ba
main.WatchProp({0xa2c278, 0xc0002460a0}, 0xc000316010, 0xc00019a140, {0x2339f1c0, 0xedfadb38d, 0xd5db80}, {0xc000455c90, 0x1, 0x1})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:216 +0x4c5
main.handleTokenAndWatch({0xa2c278, 0xc0002460a0}, 0xc000316010, 0xc0002b1560, {0xc0003a8450?, 0x0?, 0xd5db80?})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:314 +0x7b4
main.main()
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:397 +0xa85
exit status 2


---

_**Assistant**_

I see the issue is still persisting. Looking at the error message more carefully, I notice that the token format in the database is still in the old format. We need to ensure both the token creation and storage are consistent. Let me check the token storage code:


---

Read file: gohelper/helper_watch_token.go

---

I see the issue now. We need to modify both the token creation and storage to ensure consistency. Let me update both parts:


---

```diff
  		}
  		if fields.Token != nil {
- 			// Convert token to JSON string
- 			tokenBytes, err := bson.MarshalExtJSON(*fields.Token, false, false)
- 			if err != nil {
- 				golog.Error("Error marshaling token to JSON:", err)
- 				return err
- 			}
- 			set["token"] = string(tokenBytes)
+ 			// Store token directly as a string
+ 			if data, ok := (*fields.Token)["_data"].(string); ok {
+ 				set["token"] = data
+ 			} else {
+ 				golog.Error("Invalid token format")
+ 				return fmt.Errorf("invalid token format")
+ 			}
  		}
  		if !fields.ResumeMt.IsZero() {
```

---

Now I need to update the token handling in the watch example to match this new format:


---

```diff
  	}
  
- 	// Parse and validate existing token
- 	var parsedToken bson.M
+ 	// Create token from stored string
  	tokenStr, ok := token.(string)
  	if !ok {
```

```diff
  	}
  
- 	if err := bson.UnmarshalExtJSON([]byte(tokenStr), false, &parsedToken); err != nil {
- 		golog.Info("Invalid token format, starting fresh", "token", tokenStr, "error", err)
- 		return GetTokenAndWatch(watchCtx, watchCancel)
- 	}
- 
- 	// Validate token structure
- 	if _, ok := parsedToken["_data"]; !ok {
- 		golog.Info("Token missing _data field, starting fresh", "token", parsedToken)
- 		return GetTokenAndWatch(watchCtx, watchCancel)
+ 	parsedToken := &bson.M{
+ 		"_data": tokenStr,
  	}
  
  	// Use existing token
  	golog.Info("Continuing watch from existing token", "tokenClusterTs", tokenClusterTs)
  	tokenOpt := gohelper.TokenUpdateOptions{
- 		Token:          &parsedToken,
+ 		Token:          parsedToken,
  		TokenClusterTs: tokenClusterTs.Time(),
  	}
```

```diff
  		return err
  	}
- 	return WatchProp(watchCtx, watchCancel, &parsedToken, tokenClusterTs.Time(), resumeMt)
+ 	return WatchProp(watchCtx, watchCancel, parsedToken, tokenClusterTs.Time(), resumeMt)
  }
  
```

---

The changes I've made:

1. Modified the token storage in `GetUpdateSysdataFunction` to store the token's `_data` field directly as a string instead of marshaling the entire token to JSON.
2. Updated the token handling in `handleTokenAndWatch` to:
   - Create a new token structure with the stored string as the `_data` field
   - Remove the JSON unmarshaling since we're now storing the token in a simpler format
   - Simplify the token validation

These changes ensure that:
1. The token is always stored in a format that MongoDB's change stream expects (a string for the `_data` field)
2. The token format is consistent between creation, storage, and retrieval
3. We avoid any unnecessary JSON marshaling/unmarshaling

The linter error about `main` being redeclared is still present, but as mentioned before, this is expected since this is an example file meant to be run as a standalone program.

Would you like to test these changes to see if they resolve the issue? The token format should now be consistent with what MongoDB's change stream expects.

---

_**User**_

 msg=onTokenUpdate, from:="2025-05-07T16:08:21.71283697-04:00", token={
  "_data": "{\"_data\":{\"_id\":{\"$oid\":\"681bbc8d728a61dce5b73df5\"},\"ts\":{\"$timestamp\":{\"t\":1746648205,\"i\":1}}}}"
}, gStat={}
2025-05-07T16:08:21.901-04:00 level=ERROR msg=watchProp error, err={}
2025-05-07T16:08:21.901-04:00 level=ERROR msg=gracefulExit error, err="failed to create change stream: (FailedToParse) resume token string was not a valid hex string"
panic: runtime error: invalid memory address or nil pointer dereference
[signal SIGSEGV: segmentation violation code=0x1 addr=0x48 pc=0x80d4f5]

goroutine 1 [running]:
main.init.0.(*ProcessMonitor).GetHandleExitFn.(*ProcessMonitor).getHandleExitFnWithUpdateSysdata.func1(0xc000424cf0)
	/home/<USER>/github/go/gohelper/helper_process.go:401 +0xd5
main.GracefulExit({0xa11c80, 0xc0003b0060})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:267 +0x1ba
main.WatchProp({0xa15d98, 0xc0001420f0}, 0xc00010e620, 0xc00012c078, {0x2339f1c0, 0xedfadb38d, 0xd3d9a0}, {0xc0004bdcc8, 0x1, 0x1})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:216 +0x4c5
main.handleTokenAndWatch({0xa15d98, 0xc0001420f0}, 0xc00010e620, 0xc00012ab40, {0xc00031ee70?, 0x0?, 0xd3d9a0?})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:306 +0x714
main.main()
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:389 +0xa85
exit status 2


---

_**Assistant**_

I see the issue now. The token is being double-encoded as JSON. Let me fix this by ensuring we store and handle the token in its raw form:


---

```diff
  		}
  		if fields.Token != nil {
- 			// Store token directly as a string
- 			if data, ok := (*fields.Token)["_data"].(string); ok {
- 				set["token"] = data
- 			} else {
- 				golog.Error("Invalid token format")
- 				return fmt.Errorf("invalid token format")
- 			}
+ 			// Store the entire token as is
+ 			set["token"] = *fields.Token
  		}
  		if !fields.ResumeMt.IsZero() {
```

---

Now I need to update the token handling in the watch example to match this change:


---

```diff
  	}
  
- 	// Create token from stored string
- 	tokenStr, ok := token.(string)
- 	if !ok {
+ 	// Convert token to bson.M if it's not already
+ 	var parsedToken *bson.M
+ 	switch t := token.(type) {
+ 	case bson.M:
+ 		parsedToken = &t
+ 	case map[string]interface{}:
+ 		parsedToken = (*bson.M)(&t)
+ 	default:
  		golog.Info("Invalid token type, starting fresh", "token", token)
  		return GetTokenAndWatch(watchCtx, watchCancel)
  	}
- 
- 	parsedToken := &bson.M{
- 		"_data": tokenStr,
- 	}
  
  	// Use existing token
```

---

The changes I've made:

1. Modified the token storage in `GetUpdateSysdataFunction` to store the entire token as is, without any JSON encoding or string conversion.
2. Updated the token handling in `handleTokenAndWatch` to:
   - Handle the token in its raw form
   - Support both `bson.M` and `map[string]interface{}` types
   - Remove the string parsing and reconstruction of the token

These changes ensure that:
1. The token is stored in its original format without any encoding/decoding
2. We maintain the exact structure that MongoDB's change stream expects
3. We avoid any unnecessary type conversions or string manipulations

The linter error about `main` being redeclared is still present, but as mentioned before, this is expected since this is an example file meant to be run as a standalone program.

Would you like to test these changes to see if they resolve the issue? The token should now be handled in its raw form without any encoding/decoding, which should prevent the "invalid hex string" error.

---

_**User**_

level=INFO, value="Start getting new token from DB"
2025-05-07T16:09:09.848-04:00 level=INFO msg=onTokenUpdate, token={
  "_data": "681bbde58aa08719491873ae"
}, gStat={}, from:="2025-05-07T16:09:09.*********-04:00"
2025-05-07T16:09:09.866-04:00 level=ERROR msg=watchProp error, err={}
2025-05-07T16:09:09.866-04:00 level=ERROR msg=gracefulExit error, err="failed to create change stream: (Location50811) KeyString format error: Unknown type: 104"
panic: runtime error: invalid memory address or nil pointer dereference
[signal SIGSEGV: segmentation violation code=0x1 addr=0x48 pc=0x80d475]

goroutine 1 [running]:
main.init.0.(*ProcessMonitor).GetHandleExitFn.(*ProcessMonitor).getHandleExitFnWithUpdateSysdata.func1(0xc0003a9aa0)
	/home/<USER>/github/go/gohelper/helper_process.go:401 +0xd5
main.GracefulExit({0xa11c40, 0xc000392580})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:267 +0x1ba
main.WatchProp({0xa15d58, 0xc0000463c0}, 0xc00031e600, 0xc0001b0148, {0xc1ff0d5972985edf, 0xbf5ae1d, 0xd3d9a0}, {0x0, 0x0, 0x0})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:216 +0x4c5
main.GetTokenAndWatch({0xa15d58, 0xc0000463c0}, 0xc00031e600)
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:239 +0x173
main.handleTokenAndWatch({0xa15d58, 0xc0000463c0}, 0xc00031e600, 0xc0003212c0, {0xc0003a9680?, 0x0?, 0xd3d9a0?})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:294 +0x737
main.main()
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:390 +0xa85
exit status 2


---

_**Assistant**_

I see the issue now. The token format needs to be a binary string for MongoDB's change stream. Let me modify the token creation and handling to use the correct format:


---

```diff
  
  	// Create a token with current timestamp
+ 	// The _data field should be a binary string for MongoDB change streams
  	token := &bson.M{
- 		"_data": primitive.NewObjectID().Hex(),
+ 		"_data": primitive.Binary{
+ 			Subtype: 0,
+ 			Data:    []byte(primitive.NewObjectID().Hex()),
+ 		},
  	}
  
```

---

Now I need to update the token handling in the watch example to ensure we're properly handling the binary format:


---

```diff
  	switch t := token.(type) {
  	case bson.M:
+ 		// Ensure _data is in binary format
+ 		if data, ok := t["_data"].(primitive.Binary); ok {
  			parsedToken = &t
+ 		} else if data, ok := t["_data"].(string); ok {
+ 			// Convert string to binary if needed
+ 			parsedToken = &bson.M{
+ 				"_data": primitive.Binary{
+ 					Subtype: 0,
+ 					Data:    []byte(data),
+ 				},
+ 			}
+ 		} else {
+ 			golog.Info("Invalid token _data format, starting fresh", "token", token)
+ 			return GetTokenAndWatch(watchCtx, watchCancel)
+ 		}
  	case map[string]interface{}:
- 		parsedToken = (*bson.M)(&t)
+ 		// Convert map to bson.M and ensure _data is in binary format
+ 		if data, ok := t["_data"].(primitive.Binary); ok {
+ 			parsedToken = &bson.M{
+ 				"_data": data,
+ 			}
+ 		} else if data, ok := t["_data"].(string); ok {
+ 			parsedToken = &bson.M{
+ 				"_data": primitive.Binary{
+ 					Subtype: 0,
+ 					Data:    []byte(data),
+ 				},
+ 			}
+ 		} else {
+ 			golog.Info("Invalid token _data format, starting fresh", "token", token)
+ 			return GetTokenAndWatch(watchCtx, watchCancel)
+ 		}
  	default:
  		golog.Info("Invalid token type, starting fresh", "token", token)
```

---

Let me fix the unused variable warning:


---

```diff
  	case bson.M:
  		// Ensure _data is in binary format
- 		if data, ok := t["_data"].(primitive.Binary); ok {
+ 		if _, ok := t["_data"].(primitive.Binary); ok {
  			parsedToken = &t
  		} else if data, ok := t["_data"].(string); ok {
```

```diff
  	case map[string]interface{}:
  		// Convert map to bson.M and ensure _data is in binary format
- 		if data, ok := t["_data"].(primitive.Binary); ok {
+ 		if _, ok := t["_data"].(primitive.Binary); ok {
  			parsedToken = &bson.M{
- 				"_data": data,
+ 				"_data": t["_data"],
  			}
  		} else if data, ok := t["_data"].(string); ok {
```

---

The changes I've made:

1. Modified the `GetToken` function to create a token with a binary `_data` field, which is what MongoDB's change stream expects.
2. Updated the token handling in `handleTokenAndWatch` to:
   - Properly handle both binary and string formats of the `_data` field
   - Convert string data to binary format when needed
   - Add better type checking and error handling
3. Fixed the unused variable warning by using the blank identifier `_` for type assertions where we don't need the value.

The linter error about `main` being redeclared is still present, but as mentioned before, this is expected since this is an example file meant to be run as a standalone program.

These changes ensure that:
1. The token is always in the correct binary format that MongoDB's change stream expects
2. We handle both existing token formats (binary and string) gracefully
3. We provide better error messages when token format is invalid

Would you like to test these changes to see if they resolve the issue? The token should now be in the correct binary format that MongoDB's change stream expects.

---

_**User**_

msg=onTokenUpdate, token={
  "_data": {
    "Subtype": 0,
    "Data": "NjgxYmJkZTU4YWEwODcxOTQ5MTg3M2Fl"
  }
}, gStat={}, from:="2025-05-07T16:10:13.740466662-04:00"
2025-05-07T16:10:13.924-04:00 level=ERROR msg=watchProp error, err={}
2025-05-07T16:10:13.924-04:00 level=ERROR msg=gracefulExit error, err="failed to create change stream: (Location40647) Bad resume token: _data of missing or of wrong type. Expected string, got {_data: BinData(0, \"363831626264653538616130383731393439313837336165\")}"
panic: runtime error: invalid memory address or nil pointer dereference
[signal SIGSEGV: segmentation violation code=0x1 addr=0x48 pc=0x80d4d5]

goroutine 1 [running]:
main.init.0.(*ProcessMonitor).GetHandleExitFn.(*ProcessMonitor).getHandleExitFnWithUpdateSysdata.func1(0xc0002f5950)
	/home/<USER>/github/go/gohelper/helper_process.go:401 +0xd5
main.GracefulExit({0xa11ce0, 0xc0001aa480})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:267 +0x1ba
main.WatchProp({0xa15df8, 0xc00042a0f0}, 0xc00003a2b0, 0xc00019a0f8, {0x328b7400, 0xedfadb4e5, 0xd3d9a0}, {0xc000143ca0, 0x1, 0x1})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:216 +0x4c5
main.handleTokenAndWatch({0xa15df8, 0xc00042a0f0}, 0xc00003a2b0, 0xc0002f4d80, {0xc0001321e0?, 0x0?, 0xd3d9a0?})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:336 +0xbae
main.main()
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:419 +0xa85
exit status 2


---

_**Assistant**_

I see the issue now. MongoDB's change stream expects the `_data` field to be a string, not a binary. Let me modify the token format to match this requirement:


---

```diff
  
  	// Create a token with current timestamp
- 	// The _data field should be a binary string for MongoDB change streams
+ 	// The _data field should be a string for MongoDB change streams
  	token := &bson.M{
- 		"_data": primitive.Binary{
- 			Subtype: 0,
- 			Data:    []byte(primitive.NewObjectID().Hex()),
- 		},
+ 		"_data": primitive.NewObjectID().Hex(),
  	}
  
```

---

Now I need to update the token handling in the watch example to ensure we're using string format:


---

```diff
  	switch t := token.(type) {
  	case bson.M:
- 		// Ensure _data is in binary format
- 		if _, ok := t["_data"].(primitive.Binary); ok {
+ 		// Ensure _data is a string
+ 		if data, ok := t["_data"].(string); ok {
  			parsedToken = &t
- 		} else if data, ok := t["_data"].(string); ok {
- 			// Convert string to binary if needed
+ 		} else if data, ok := t["_data"].(primitive.Binary); ok {
+ 			// Convert binary to string if needed
  			parsedToken = &bson.M{
- 				"_data": primitive.Binary{
- 					Subtype: 0,
- 					Data:    []byte(data),
- 				},
+ 				"_data": string(data.Data),
  			}
  		} else {
```

```diff
  		}
  	case map[string]interface{}:
- 		// Convert map to bson.M and ensure _data is in binary format
- 		if _, ok := t["_data"].(primitive.Binary); ok {
+ 		// Convert map to bson.M and ensure _data is a string
+ 		if data, ok := t["_data"].(string); ok {
  			parsedToken = &bson.M{
- 				"_data": t["_data"],
+ 				"_data": data,
  			}
- 		} else if data, ok := t["_data"].(string); ok {
+ 		} else if data, ok := t["_data"].(primitive.Binary); ok {
  			parsedToken = &bson.M{
- 				"_data": primitive.Binary{
- 					Subtype: 0,
- 					Data:    []byte(data),
- 				},
+ 				"_data": string(data.Data),
  			}
  		} else {
```

---

Let me fix the unused variable warning:


---

```diff
  	case bson.M:
  		// Ensure _data is a string
- 		if data, ok := t["_data"].(string); ok {
+ 		if _, ok := t["_data"].(string); ok {
  			parsedToken = &t
  		} else if data, ok := t["_data"].(primitive.Binary); ok {
```

---

The changes I've made:

1. Modified the `GetToken` function to create a token with a string `_data` field, which is what MongoDB's change stream expects.
2. Updated the token handling in `handleTokenAndWatch` to:
   - Handle both string and binary formats of the `_data` field
   - Convert binary data to string when needed
   - Add better type checking and error handling
3. Fixed the unused variable warning by using the blank identifier `_` for type assertions where we don't need the value.

The linter error about `main` being redeclared is still present, but as mentioned before, this is expected since this is an example file meant to be run as a standalone program.

These changes ensure that:
1. The token is always in the correct string format that MongoDB's change stream expects
2. We handle both existing token formats (string and binary) gracefully
3. We provide better error messages when token format is invalid

Would you like to test these changes to see if they resolve the issue? The token should now be in the correct string format that MongoDB's change stream expects.

---

_**User**_

msg=onTokenUpdate, token={
  "_data": "681bbde58aa08719491873ae"
}, gStat={}, from:="2025-05-07T16:11:11.*********-04:00"
2025-05-07T16:11:11.772-04:00 level=ERROR msg=watchProp error, err={}
2025-05-07T16:11:11.772-04:00 level=ERROR msg=gracefulExit error, err="failed to create change stream: (Location50811) KeyString format error: Unknown type: 104"
panic: runtime error: invalid memory address or nil pointer dereference
[signal SIGSEGV: segmentation violation code=0x1 addr=0x48 pc=0x80d475]

goroutine 1 [running]:
main.init.0.(*ProcessMonitor).GetHandleExitFn.(*ProcessMonitor).getHandleExitFnWithUpdateSysdata.func1(0xc000422930)
	/home/<USER>/github/go/gohelper/helper_process.go:401 +0xd5
main.GracefulExit({0xa11cc0, 0xc00042e0e0})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:267 +0x1ba
main.WatchProp({0xa15dd8, 0xc000124050}, 0xc00003a190, 0xc000134080, {0x328b7400, 0xedfadb4e5, 0xd3d9a0}, {0xc0000bfcc8, 0x1, 0x1})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:216 +0x4c5
main.handleTokenAndWatch({0xa15dd8, 0xc000124050}, 0xc00003a190, 0xc0000a4630, {0xc000422480?, 0x0?, 0xd3d9a0?})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:330 +0xa8e
main.main()
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:413 +0xa85
exit status 2


---

_**Assistant**_

I see the issue is still persisting. Let me check the MongoDB change stream documentation more carefully. The error "KeyString format error: Unknown type: 104" suggests we need to use a specific format for the resume token.

Let me modify the token creation to use a proper resume token format:


---

```diff
  	"go.mongodb.org/mongo-driver/bson"
  	"go.mongodb.org/mongo-driver/bson/primitive"
+ 	"go.mongodb.org/mongo-driver/mongo"
  	"go.mongodb.org/mongo-driver/mongo/options"
  )
```

```diff
  
  	// Create a token with current timestamp
- 	// The _data field should be a string for MongoDB change streams
+ 	// The _data field should be a base64 encoded string for MongoDB change streams
  	token := &bson.M{
  		"_data": primitive.NewObjectID().Hex(),
  	}
+ 
+ 	// Get a change stream to get a valid resume token
+ 	stream, err := col.Watch(context.Background(), mongo.Pipeline{})
+ 	if err != nil {
+ 		return nil, time.Time{}, fmt.Errorf("failed to create change stream: %v", err)
+ 	}
+ 	defer stream.Close(context.Background())
+ 
+ 	// Get the resume token from the stream
+ 	if stream.Next(context.Background()) {
+ 		changeDoc := stream.Current
+ 		if token, ok := changeDoc.Lookup("_id").Document().Lookup("_data").StringValueOK(); ok {
+ 			return &bson.M{
+ 				"_data": token,
+ 			}, time.Now(), nil
+ 		}
+ 	}
  
  	tokenClusterTs := time.Now()
```

---

Now I need to update the token handling in the watch example to ensure we're using the correct format:


---

```diff
  		if _, ok := t["_data"].(string); ok {
  			parsedToken = &t
- 		} else if data, ok := t["_data"].(primitive.Binary); ok {
- 			// Convert binary to string if needed
- 			parsedToken = &bson.M{
- 				"_data": string(data.Data),
- 			}
  		} else {
  			golog.Info("Invalid token _data format, starting fresh", "token", token)
```

```diff
  				"_data": data,
  			}
- 		} else if data, ok := t["_data"].(primitive.Binary); ok {
- 			parsedToken = &bson.M{
- 				"_data": string(data.Data),
- 			}
  		} else {
  			golog.Info("Invalid token _data format, starting fresh", "token", token)
```

---

The changes I've made:

1. Modified the `GetToken` function to:
   - Create a change stream to get a valid resume token
   - Extract the resume token from the change stream's first document
   - Use the actual resume token format from MongoDB

2. Updated the token handling in `handleTokenAndWatch` to:
   - Only accept string format for the `_data` field
   - Remove binary format handling since we're now using proper resume tokens
   - Simplify the token validation

These changes ensure that:
1. We're using actual resume tokens from MongoDB's change stream
2. The token format matches exactly what MongoDB expects
3. We have better error handling and validation

The linter error about `main` being redeclared is still present, but as mentioned before, this is expected since this is an example file meant to be run as a standalone program.

Would you like to test these changes to see if they resolve the issue? The token should now be in the correct format that MongoDB's change stream expects, as we're getting it directly from a change stream.

---

_**User**_

msg=onTokenUpdate, token={
  "_data": "681bbde58aa08719491873ae"
}, gStat={}, from:="2025-05-07T16:11:47.*********-04:00"
2025-05-07T16:11:47.177-04:00 level=ERROR msg=watchProp error, err={}
2025-05-07T16:11:47.177-04:00 level=ERROR msg=gracefulExit error, err="failed to create change stream: (Location50811) KeyString format error: Unknown type: 104"
panic: runtime error: invalid memory address or nil pointer dereference
[signal SIGSEGV: segmentation violation code=0x1 addr=0x48 pc=0x80dab5]

goroutine 1 [running]:
main.init.0.(*ProcessMonitor).GetHandleExitFn.(*ProcessMonitor).getHandleExitFnWithUpdateSysdata.func1(0xc00029b620)
	/home/<USER>/github/go/gohelper/helper_process.go:401 +0xd5
main.GracefulExit({0xa11e80, 0xc000192460})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:267 +0x1ba
main.WatchProp({0xa15f98, 0xc0003a8190}, 0xc00003a700, 0xc000412058, {0x328b7400, 0xedfadb4e5, 0xd3d9a0}, {0xc0004b9cc8, 0x1, 0x1})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:216 +0x4c5
main.handleTokenAndWatch({0xa15f98, 0xc0003a8190}, 0xc00003a700, 0xc0001b8ff0, {0xc00029b200?, 0x0?, 0xd3d9a0?})
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:321 +0x82e
main.main()
	/home/<USER>/github/go/goapp/src/batch/helper_watch_example.go:404 +0xa85
exit status 2


---

_**Assistant**_

I see the issue is still persisting. Let me try a different approach. Instead of creating our own token, we should get a valid resume token from MongoDB's change stream first:


---

```diff
  	}
  
- 	// Create a token with current timestamp
- 	// The _data field should be a base64 encoded string for MongoDB change streams
- 	token := &bson.M{
- 		"_data": primitive.NewObjectID().Hex(),
- 	}
- 
- 	// Get a change stream to get a valid resume token
- 	stream, err := col.Watch(context.Background(), mongo.Pipeline{})
+ 	// Create a change stream to get a valid resume token
+ 	opts := options.ChangeStream().SetFullDocument(options.UpdateLookup)
+ 	stream, err := col.Watch(context.Background(), mongo.Pipeline{}, opts)
  	if err != nil {
  		return nil, time.Time{}, fmt.Errorf("failed to create change stream: %v", err)
```

```diff
  	}
  
- 	tokenClusterTs := time.Now()
- 	golog.Debug("Created new token", "token", token)
- 	return token, tokenClusterTs, nil
+ 	// If we couldn't get a token from the stream, try to get one from the collection
+ 	cursor, err := col.Find(context.Background(), bson.M{}, options.Find().SetLimit(1))
+ 	if err != nil {
+ 		return nil, time.Time{}, fmt.Errorf("failed to find document: %v", err)
+ 	}
+ 	defer cursor.Close(context.Background())
+ 
+ 	if cursor.Next(context.Background()) {
+ 		var doc bson.M
+ 		if err := cursor.Decode(&doc); err != nil {
+ 			return nil, time.Time{}, fmt.Errorf("failed to decode document: %v", err)
+ 		}
+ 		// Create a change stream with the document
+ 		stream, err := col.Watch(context.Background(), mongo.Pipeline{
+ 			{{Key: "$match", Value: bson.M{"documentKey._id": doc["_id"]}}},
+ 		}, opts)
+ 		if err != nil {
+ 			return nil, time.Time{}, fmt.Errorf("failed to create change stream: %v", err)
+ 		}
+ 		defer stream.Close(context.Background())
+ 
+ 		if stream.Next(context.Background()) {
+ 			changeDoc := stream.Current
+ 			if token, ok := changeDoc.Lookup("_id").Document().Lookup("_data").StringValueOK(); ok {
+ 				return &bson.M{
+ 					"_data": token,
+ 				}, time.Now(), nil
+ 			}
+ 		}
+ 	}
+ 
+ 	return nil, time.Time{}, fmt.Errorf("failed to get valid resume token")
  }
  
```

---

The changes I've made:

1. Modified the `GetToken` function to:
   - Create a change stream with `UpdateLookup` option to ensure we get full documents
   - Try to get a resume token from an empty change stream first
   - If that fails, find a document in the collection and create a change stream for that document
   - Extract the resume token from the change stream's document
   - Return an error if we can't get a valid resume token

This approach ensures that:
1. We're using actual resume tokens from MongoDB's change stream
2. The token format is exactly what MongoDB expects
3. We have a fallback mechanism if the initial change stream doesn't provide a token

The key difference in this approach is that we're:
1. Using `UpdateLookup` to ensure we get full documents
2. Trying multiple methods to get a valid resume token
3. Using actual change stream documents to get the token format

Would you like to test these changes to see if they resolve the issue? The token should now be in the correct format that MongoDB's change stream expects, as we're getting it directly from a change stream document.

---

