<!-- Generated by SpecStory -->

# 详细讲解 folder_sync 项目 (2025-07-02 21:50Z)

_**User**_

具体看一下 folder_sync 中的每个文件，然后具体的给我讲一下这个项目

---

_**Assistant**_

I'll explore the folder_sync project structure and analyze each file to give you a comprehensive overview of this project.


---

<details>
            <summary>Listed directory folder_sync • **19** results</summary>
        
| Name |
|-------|
| 📁 `.git` |
| 📁 `test` |
| 📄 `sample-config.yaml` |
| 📁 `scripts` |
| 📁 `pkg` |
| 📄 `folder-sync` |
| 📄 `go.mod` |
| 📄 `go.sum` |
| 📄 `main.go` |
| 📁 `examples` |
| 📁 `docker` |
| 📁 `docs` |
| 📁 `dev` |
| 📄 `Makefile` |
| 📄 `README.md` |
| 📄 `debug_sync.sh` |
| 📄 `.DS_Store` |
| 📄 `.gitignore` |
| 📄 `LICENSE` |

</details>

---

Read file: folder_sync/README.md

---

Read file: folder_sync/main.go

---

<details>
            <summary>Listed directory folder_sync/pkg • **11** results</summary>
        
| Name |
|-------|
| 📁 `sync` |
| 📁 `types` |
| 📁 `network` |
| 📁 `ingress` |
| 📁 `interfaces` |
| 📁 `logger` |
| 📁 `meta` |
| 📁 `fsutil` |
| 📁 `client` |
| 📁 `config` |
| 📁 `cli` |

</details>

---

Read file: folder_sync/go.mod

---

Read file: folder_sync/sample-config.yaml

---

<details>
            <summary>Listed directory folder_sync/pkg/config • **4** results</summary>
        
| Name |
|-------|
| 📄 `config.go` |
| 📄 `config_specification_test.go` |
| 📄 `config_test.go` |
| 📄 `ignore_patterns_test.go` |

</details>

---

Read file: folder_sync/pkg/config/config.go

---

<details>
            <summary>Listed directory folder_sync/pkg/sync • **13** results</summary>
        
| Name |
|-------|
| 📄 `sync_test.go` |
| 📄 `writethrough.go` |
| 📄 `writethrough_specification_test.go` |
| 📄 `deduplication_test.go` |
| 📄 `initialsync.go` |
| 📄 `integration_test.go` |
| 📄 `interfaces.go` |
| 📄 `mtime_test.go` |
| 📄 `ongoing.go` |
| 📄 `peer.go` |
| 📄 `symlink_sync_test.go` |
| 📄 `sync.go` |
| 📄 `sync_specification_test.go` |

</details>

---

Read file: folder_sync/pkg/sync/sync.go

---

<details>
            <summary>Listed directory folder_sync/pkg/network • **5** results</summary>
        
| Name |
|-------|
| 📄 `network_test.go` |
| 📄 `protocol.go` |
| 📄 `protocol_specification_test.go` |
| 📄 `protocol_test.go` |
| 📄 `network.go` |

</details>

---

Read file: folder_sync/pkg/network/network.go

---

<details>
            <summary>Listed directory folder_sync/pkg/ingress • **8** results</summary>
        
| Name |
|-------|
| 📄 `symlink_inotify_test.go` |
| 📄 `ingress_test.go` |
| 📄 `inotify.go` |
| 📄 `proxysocket.go` |
| 📄 `delete_ignore_test.go` |
| 📄 `ingress.go` |
| 📄 `ingress_specification_test.go` |
| 📄 `debounce_test.go` |

</details>

---

Read file: folder_sync/pkg/ingress/ingress.go

---

<details>
            <summary>Listed directory folder_sync/test • **17** results</summary>
        
| Name |
|-------|
| 📄 `debug_concurrent_dirs_simple.sh` |
| 📄 `debug_directory_sync.sh` |
| 📄 `debug_mkdir_issue.sh` |
| 📄 `debug_ongoing_sync.sh` |
| 📄 `e2e_deadlock_prevention_test.go` |
| 📄 `e2e_external_folder_copy_test.go` |
| 📄 `e2e_sync_test.go` |
| 📄 `local-test.sh` |
| 📄 `sequential_dir_test.sh` |
| 📄 `simple_dir_test.sh` |
| 📄 `sync_fixes_test.go` |
| 📄 `test-server1-config.yaml` |
| 📄 `test-server2-config.yaml` |
| 📄 `test_e2e_sync.sh` |
| 📄 `test_local_sync.sh` |
| 📄 `test_touch_deadlock_fix.sh` |
| 📄 `debug_concurrent_dirs.sh` |

</details>

---

<details>
            <summary>Listed directory folder_sync/docs • **8** results</summary>
        
| Name |
|-------|
| 📁 `fixes` |
| 📄 `01. Specification.md` |
| 📄 `02. Sync Protocol.md` |
| 📄 `03. Implementation Steps.md` |
| 📄 `04. File Structure.txt` |
| 📄 `90. PRODUCTION-CHECKLIST.md` |
| 📄 `constants.md` |
| 📄 `debounce-mechanism.md` |

</details>

---

Read file: folder_sync/docs/01. Specification.md

---

<details>
            <summary>Listed directory folder_sync/examples • **3** results</summary>
        
| Name |
|-------|
| 📄 `config-ca2.yaml` |
| 📄 `config-ca3.yaml` |
| 📄 `debounce-config.yaml` |

</details>

---

Read file: folder_sync/examples/config-ca2.yaml

---

Now let me provide you with a comprehensive overview of the **folder_sync** project based on my analysis of all the files:

## Folder Sync 项目概述

**Folder Sync** 是一个用 Go 语言编写的高性能、生产就绪的文件夹同步工具，支持多服务器之间的实时双向同步。

### 🎯 项目核心特性

1. **实时同步**: 使用 inotify 实现即时文件和目录同步
2. **多种同步策略**: 单向、双向、最新优先冲突解决
3. **Writethrough 模式**: 基于缓冲的高吞吐量同步
4. **生产就绪**: 全面的安全加固和监控
5. **高效协议**: 自定义二进制协议，支持可选压缩
6. **容错性**: 自动重连、队列持久化、优雅错误恢复
7. **代理套接字支持**: 通过 Unix 域套接字与外部应用集成
8. **Go 客户端库**: 原生 Go API 用于程序化集成

### 🏗️ 项目架构

项目采用模块化架构，主要组件包括：

#### 1. **主程序入口** (`main.go`)
- 命令行参数解析
- 配置加载和验证
- 组件初始化和协调
- 优雅关闭处理
- 内存使用监控

#### 2. **配置管理** (`pkg/config/`)
- YAML 配置文件解析
- 配置验证和默认值设置
- 忽略模式匹配器
- 支持多种配置选项（同步路径、网络设置、压缩等）

#### 3. **同步核心** (`pkg/sync/`)
- **SyncCore**: 中央协调逻辑
- **PeerHandler**: 管理每个对等节点的连接和状态
- **InitialSyncHandler**: 初始同步处理
- **OngoingSyncHandler**: 持续同步处理
- **WritethroughHandler**: 写透模式处理

#### 4. **网络通信** (`pkg/network/`)
- **Server**: 监听入站连接
- **Client**: 发起出站连接
- **Connection**: 连接管理和消息传输
- **Protocol**: 自定义二进制协议实现
- 支持认证、版本交换、文件传输等

#### 5. **变更监控** (`pkg/ingress/`)
- **InotifyWatcher**: 基于 inotify 的文件系统监控
- **ProxySocketListener**: 代理套接字监听
- **Debounce**: 事件去重和合并
- 将文件系统事件转换为 FileSyncEvent

#### 6. **元数据管理** (`pkg/meta/`)
- 持久化配对状态
- SQLite 队列管理
- 原子写入操作
- 崩溃恢复机制

#### 7. **文件系统工具** (`pkg/fsutil/`)
- 原子文件写入
- xxHash 校验和计算
- 符号链接处理
- 权限和所有权管理

#### 8. **日志系统** (`pkg/logger/`)
- 结构化日志记录
- 多级别日志支持
- 按对等节点分文件记录
- systemd 集成

### 📋 核心数据结构和类型

```go
// 文件同步事件
type FileSyncEvent struct {
    Type      FileSyncEventType
    Path      string
    Timestamp time.Time
    Size      uint64
    Checksum  string
}

// 配对配置
type PairConfig struct {
    Name                 string
    HostnameOrIP         string
    Direction            PairDirection
    InitialSyncStrategy  InitialSyncStrategy
    RemotePort           int
}

// 配对状态
type PairStatus struct {
    LastSyncedTimestamp  time.Time
    InitialSyncStatus    InitialSyncStatus
    CurrentQueueDBFile   string
    ConnectionRetries    int
    TotalSent           SyncOpCounts
    TotalReceived       SyncOpCounts
}
```

### ⚙️ 配置系统

配置文件使用 YAML 格式，主要配置项包括：

```yaml
folder:
  sync_path: "/path/to/sync/directory"
  meta_path: "/var/lib/folder-sync/meta"
  logs_path: "/var/log/folder-sync"
  compares: SIZE_ONLY  # SIZE_ONLY, FIRST_LAST_BLOCK_SAMPLE, FULL_FILE
  compression: NONE    # NONE, ZSTD
  concurrent: 10       # 并发传输数
  readonly: false      # 只读模式
  writethrough: false  # 写透模式

server:
  listen_address: "0.0.0.0:8080"
  auth_token: "secure-token-here"

pairs:
  - name: "remote-server"
    host: "*************"
    direction: BIDIRECTIONAL_NEWEST_WIN
    initial_sync_strategy: SYNC
    remote_port: 8080
```

### �� 构建和部署

项目使用 **Makefile** 进行构建自动化：

```bash
# 构建二进制文件
make build

# 安装为系统服务
sudo make install-service

# 运行测试
make test

# 清理构建产物
make clean
```

**安装脚本** (`scripts/install.sh`) 提供完整的系统集成：
- 复制二进制文件到 `/usr/local/bin/`
- 创建默认配置文件
- 设置 systemd 服务
- 创建专用服务用户
- 配置安全权限

### 🧪 测试体系

项目采用 **TDD（测试驱动开发）** 方法：

1. **单元测试**: 每个包都有对应的测试文件
2. **集成测试**: 测试组件间交互
3. **端到端测试**: 完整的同步场景测试
4. **性能测试**: 基准测试和压力测试

### �� 文档体系

- **规格说明**: 详细的功能规格和实现要求
- **协议文档**: 网络通信协议规范
- **实现步骤**: 开发阶段和里程碑
- **生产检查清单**: 部署前验证项目

### 🚀 使用场景

1. **多服务器文件同步**: 保持多个服务器上的文件一致
2. **备份和灾难恢复**: 实时备份重要数据
3. **负载均衡**: 在多个服务器间分发文件
4. **开发环境同步**: 保持开发、测试、生产环境一致
5. **高可用部署**: 确保服务的高可用性

### �� 安全特性

- 基于令牌的认证
- 连接限制和速率控制
- 系统用户隔离
- 安全加固的 systemd 服务配置
- 敏感信息脱敏

这个项目展现了现代 Go 应用程序的最佳实践，包括模块化设计、全面的测试覆盖、生产就绪的部署方案，以及详细的文档体系。

---

