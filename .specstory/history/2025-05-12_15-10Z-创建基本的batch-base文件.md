<!-- Generated by SpecStory -->

# 创建基本的batch_base文件 (2025-05-12 15:10Z)

_**User**_

我想写一个batch_base, 里面包含基本的load config，log步骤，并提供error_notify_email, error_notify_sms func【调用本地目录中gosms，gomail package】

---

_**Assistant**_

I'll help you create a `batch_base.go` file that contains common initialization steps and error notification functions. First, let me check the existing code structure and then create the base file.

Let me create the `batch_base.go` file with the common functionality:


---

```diff
- 
+ package main
+ 
+ import (
+ 	"fmt"
+ 
+ 	goconfig "github.com/real-rm/goconfig"
+ 	golog "github.com/real-rm/golog"
+ 	gomail "github.com/real-rm/gomail"
+ 	gosms "github.com/real-rm/gosms"
+ )
+ 
+ // InitBase initializes common components like config and logging
+ func InitBase() error {
+ 	// Load configuration
+ 	if err := goconfig.LoadConfig(); err != nil {
+ 		return fmt.Errorf("failed to load config: %v", err)
+ 	}
+ 
+ 	// Initialize logging
+ 	if err := golog.InitLog(); err != nil {
+ 		return fmt.Errorf("failed to initialize logging: %v", err)
+ 	}
+ 
+ 	return nil
+ }
+ 
+ // ErrorNotifyEmail sends error notification via email
+ func ErrorNotifyEmail(subject string, body string) error {
+ 	// Get email configuration from config
+ 	emailConfig := goconfig.GetEmailConfig()
+ 	
+ 	// Create email message
+ 	msg := gomail.NewMessage()
+ 	msg.SetHeader("From", emailConfig.From)
+ 	msg.SetHeader("To", emailConfig.To)
+ 	msg.SetHeader("Subject", subject)
+ 	msg.SetBody("text/plain", body)
+ 
+ 	// Send email
+ 	if err := gomail.Send(msg); err != nil {
+ 		return fmt.Errorf("failed to send error notification email: %v", err)
+ 	}
+ 
+ 	return nil
+ }
+ 
+ // ErrorNotifySMS sends error notification via SMS
+ func ErrorNotifySMS(message string) error {
+ 	// Get SMS configuration from config
+ 	smsConfig := goconfig.GetSMSConfig()
+ 	
+ 	// Send SMS
+ 	if err := gosms.Send(smsConfig.To, message); err != nil {
+ 		return fmt.Errorf("failed to send error notification SMS: %v", err)
+ 	}
+ 
+ 	return nil
+ }
+ 
```

---

I've created a `batch_base.go` file with the following components:

1. `InitBase()` function that handles common initialization:
   - Loading configuration
   - Initializing logging

2. `ErrorNotifyEmail()` function for sending error notifications via email:
   - Takes subject and body as parameters
   - Uses gomail package to send emails
   - Gets email configuration from config

3. `ErrorNotifySMS()` function for sending error notifications via SMS:
   - Takes message as parameter
   - Uses gosms package to send SMS
   - Gets SMS configuration from config

There are some linter errors because the required packages (`gomail` and `gosms`) are not available in the module. You'll need to:

1. Make sure the `gomail` and `gosms` packages are available in your local directory
2. Add the following functions to your `goconfig` package:
   - `GetEmailConfig()`
   - `GetSMSConfig()`

Would you like me to help you set up these dependencies or would you prefer to provide the correct package paths for `gomail` and `gosms`?

---

