<!-- Generated by SpecStory -->

# 添加Go助手函数的单元测试 (2025-04-26 00:19Z)

_**User**_

给helper string，array   添加ut

---

_**Assistant**_

I'll help you add unit tests for the helper string and array functions. I'll create test files for both packages.

First, let's create a test file for `helper_string.go`:


---

```diff
- 
+ package gohelper
+ 
+ import (
+ 	"testing"
+ 
+ 	"github.com/stretchr/testify/assert"
+ )
+ 
+ func TestIsEmail(t *testing.T) {
+ 	tests := []struct {
+ 		name     string
+ 		email    string
+ 		expected bool
+ 	}{
+ 		{
+ 			name:     "Valid email with letters",
+ 			email:    "<EMAIL>",
+ 			expected: true,
+ 		},
+ 		{
+ 			name:     "Valid email with numbers",
+ 			email:    "<EMAIL>",
+ 			expected: true,
+ 		},
+ 		{
+ 			name:     "Valid email with dots",
+ 			email:    "<EMAIL>",
+ 			expected: true,
+ 		},
+ 		{
+ 			name:     "Valid email with hyphens",
+ 			email:    "<EMAIL>",
+ 			expected: true,
+ 		},
+ 		{
+ 			name:     "Valid email with plus sign",
+ 			email:    "<EMAIL>",
+ 			expected: true,
+ 		},
+ 		{
+ 			name:     "Invalid email - missing @",
+ 			email:    "testexample.com",
+ 			expected: false,
+ 		},
+ 		{
+ 			name:     "Invalid email - missing domain",
+ 			email:    "test@",
+ 			expected: false,
+ 		},
+ 		{
+ 			name:     "Invalid email - missing local part",
+ 			email:    "@example.com",
+ 			expected: false,
+ 		},
+ 		{
+ 			name:     "Invalid email - empty string",
+ 			email:    "",
+ 			expected: false,
+ 		},
+ 		{
+ 			name:     "Invalid email - special characters",
+ 			email:    "test!@example.com",
+ 			expected: false,
+ 		},
+ 	}
+ 
+ 	for _, tt := range tests {
+ 		t.Run(tt.name, func(t *testing.T) {
+ 			result := IsEmail(tt.email)
+ 			assert.Equal(t, tt.expected, result)
+ 		})
+ 	}
+ } 
```

---

Now, let's create a test file for `helpers_array.go`:



---

```diff
- 
+ package gohelper
+ 
+ import (
+ 	"testing"
+ 
+ 	"github.com/stretchr/testify/assert"
+ )
+ 
+ func TestContains(t *testing.T) {
+ 	// Test with string slice
+ 	t.Run("String slice", func(t *testing.T) {
+ 		slice := []string{"apple", "banana", "orange"}
+ 		assert.True(t, Contains(slice, "apple"))
+ 		assert.True(t, Contains(slice, "banana"))
+ 		assert.True(t, Contains(slice, "orange"))
+ 		assert.False(t, Contains(slice, "grape"))
+ 		assert.False(t, Contains(slice, ""))
+ 	})
+ 
+ 	// Test with int slice
+ 	t.Run("Int slice", func(t *testing.T) {
+ 		slice := []int{1, 2, 3, 4, 5}
+ 		assert.True(t, Contains(slice, 1))
+ 		assert.True(t, Contains(slice, 3))
+ 		assert.True(t, Contains(slice, 5))
+ 		assert.False(t, Contains(slice, 0))
+ 		assert.False(t, Contains(slice, 6))
+ 	})
+ 
+ 	// Test with empty slice
+ 	t.Run("Empty slice", func(t *testing.T) {
+ 		slice := []string{}
+ 		assert.False(t, Contains(slice, "test"))
+ 		assert.False(t, Contains(slice, ""))
+ 	})
+ 
+ 	// Test with nil slice
+ 	t.Run("Nil slice", func(t *testing.T) {
+ 		var slice []string
+ 		assert.False(t, Contains(slice, "test"))
+ 		assert.False(t, Contains(slice, ""))
+ 	})
+ 
+ 	// Test with custom type
+ 	t.Run("Custom type", func(t *testing.T) {
+ 		type customType struct {
+ 			id   int
+ 			name string
+ 		}
+ 		slice := []customType{
+ 			{id: 1, name: "one"},
+ 			{id: 2, name: "two"},
+ 		}
+ 		assert.True(t, Contains(slice, customType{id: 1, name: "one"}))
+ 		assert.False(t, Contains(slice, customType{id: 3, name: "three"}))
+ 	})
+ } 
```

---

I've created two test files:

1. `helper_string_test.go` - Tests for the `IsEmail` function with various test cases including:
   - Valid email addresses with different formats (letters, numbers, dots, hyphens, plus signs)
   - Invalid email addresses (missing @, missing domain, missing local part, empty string, special characters)

2. `helpers_array_test.go` - Tests for the `Contains` function with different scenarios:
   - String slices
   - Integer slices
   - Empty slices
   - Nil slices
   - Custom type slices

Both test files use the `testify/assert` package for assertions and follow Go's testing best practices with table-driven tests and subtests. The tests cover both positive and negative cases to ensure the functions work as expected.

You can run these tests using:
```bash
go test ./gohelper/...
```

The tests will verify that both helper functions work correctly for various input scenarios.

---

