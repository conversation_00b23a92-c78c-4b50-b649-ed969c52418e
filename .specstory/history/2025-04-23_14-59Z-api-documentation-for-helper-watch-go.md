<!-- Generated by SpecStory -->

# API Documentation for helper_watch.go (2025-04-23 14:59Z)

_**User**_

根据helper_watch.go 写出api文档

---

_**Assistant**_

我将根据 `helper_watch.go` 文件的内容编写 API 文档。我会使用 Markdown 格式，并按照功能模块组织文档。

# GoHelper Watch API Documentation

## Overview

The `gohelper` package provides a robust MongoDB change stream watching mechanism with features like token management, water marking, and error handling.

## Constants

```go
const (
    ChangeStreamHistoryLost = 286 // Error when change stream history is lost
    InvalidToken            = 280 // Error when token is invalid
    CappedPositionLost      = 136 // Error when capped collection position is lost

    DefaultTokenDelayMs      = 5 * 60 * 1000 // 5 minutes delay for token updates
    DefaultLowWaterMark      = 5             // Minimum number of documents to process before resuming
    DefaultHighWaterMark     = 10            // Maximum number of documents to process before pausing
    DefaultWaterMarkerTimerS = 600           // 10 minutes timer for water mark checks
    DefaultUpdateTokenTimerS = 60            // 1 minute timer for token updates
)
```

## Types

### WatchOptions

Configuration options for watching MongoDB collections.

```go
type WatchOptions struct {
    ImportWatchedRecordsCol *gomongo.MongoCollection                     // Collection to import watched records
    WatchedColl             *gomongo.MongoCollection                     // Collection to watch for changes
    OnChange                func(bson.M, *gomongo.MongoCollection) error // Callback for document changes
    OnError                 func(error)                                  // Callback for error handling
    OnTokenUpdate           func(TokenUpdateOptions) error               // Callback for token updates
    QueryWhenInvalidToken   bson.M                                       // Query to use when token is invalid
    WatchPipeline           []bson.M                                     // MongoDB aggregation pipeline for change stream
    TokenDelayMs            int64                                        // Delay between token updates
    LowWaterMark            int                                          // Minimum documents to process before resuming
    HighWaterMark           int                                          // Maximum documents to process before pausing
    WaterMarkerTimerS       int                                          // Timer for water mark checks
    UpdateTokenTimerS       int                                          // Timer for token updates
    SavedToken              *bson.M                                      // Token to resume from
    UpdateProcessStatusFn   func() error                                 // Function to update process status
    OnClose                 func()                                       // Callback when watch is closed
}
```

### TokenUpdateOptions

Options for token update operations.

```go
type TokenUpdateOptions struct {
    Token          *bson.M     // The new token
    TokenClusterTs time.Time   // Timestamp when token was created
    TokenChangeTs  time.Time   // Timestamp when token was updated
    End            func(error) // Function to end the watch operation
    ResumeMt       time.Time   // Timestamp to resume from
}
```

### WatchObject

Represents the state of a watch operation.

```go
type WatchObject struct {
    ChangeStream          *mongo.ChangeStream
    CurrentToken          *bson.M
    SavedToken            *bson.M
    SavedTokenClusterTs   time.Time
    End                   func(error)
    ProcessingCounter     int
    ProcessedCounter      int
    NS                    string
    IsPaused              bool
    IsWatchEnd            bool
    ToWarnHighWaterLevel  bool
    ResumeMt              time.Time
    CurrentTokenClusterTs time.Time
    CurrentTokenChangeTs  time.Time
    waterMarkerTimer      *Interval
    updateTokenTimer      *Interval
    watchChangeTimer      *Interval
}
```

## Functions

### WatchTarget

Starts watching a MongoDB collection for changes.

```go
func WatchTarget(opt WatchOptions) (*WatchObject, error)
```

Parameters:
- `opt`: WatchOptions configuration

Returns:
- `*WatchObject`: The watch object representing the watch operation
- `error`: Any error that occurred during initialization

### GetToken

Gets a new token from a collection.

```go
func GetToken(col *gomongo.MongoCollection) (*bson.M, time.Time, error)
```

Parameters:
- `col`: The MongoDB collection to get token from

Returns:
- `*bson.M`: The token
- `time.Time`: The cluster timestamp
- `error`: Any error that occurred

### ProcessChangedObject

Processes a changed object from the change stream.

```go
func ProcessChangedObject(opt ProcessChangedObjectOptions) error
```

Parameters:
- `opt`: ProcessChangedObjectOptions containing processing configuration

Returns:
- `error`: Any error that occurred during processing

### GetNewTokenAndProcessRemainingDocs

Gets a new token and processes remaining documents.

```go
func GetNewTokenAndProcessRemainingDocs(opt GetNewTokenAndProcessRemainingDocsOptions) (*bson.M, time.Time, error)
```

Parameters:
- `opt`: GetNewTokenAndProcessRemainingDocsOptions containing processing configuration

Returns:
- `*bson.M`: The new token
- `time.Time`: The cluster timestamp
- `error`: Any error that occurred

## Error Handling

The package handles several MongoDB-specific errors:
- `ChangeStreamHistoryLost` (286): When change stream history is lost
- `InvalidToken` (280): When token is invalid
- `CappedPositionLost` (136): When capped collection position is lost

## Water Marking

The package implements a water marking system to control document processing:
- `LowWaterMark`: Minimum number of documents to process before resuming
- `HighWaterMark`: Maximum number of documents to process before pausing

## Timer Management

The package uses several timers for different operations:
- `waterMarkerTimer`: Checks processing status
- `updateTokenTimer`: Updates token periodically
- `watchChangeTimer`: Checks for watch changes

## Usage Example

```go
opt := WatchOptions{
    WatchedColl: collection,
    OnChange: func(changedObj bson.M, coll *gomongo.MongoCollection) error {
        // Process changed document
        return nil
    },
    OnError: func(err error) {
        // Handle error
    },
    OnTokenUpdate: func(opt TokenUpdateOptions) error {
        // Update token
        return nil
    },
}

watchObj, err := WatchTarget(opt)
if err != nil {
    // Handle error
}
```

## Notes

1. The package requires MongoDB driver and related dependencies
2. Proper error handling is essential for robust operation
3. Token management is crucial for resuming operations after interruptions
4. Water marking helps prevent overwhelming the system with too many documents

---

_**User**_

Running tool: /usr/local/go/bin/go test -timeout 30s -run ^TestLoadEmailWhitelist$ github.com/real-rm/gomail

flag provided but not defined: -test.testlogfile
Usage of /tmp/go-build788646889/b001/gomail.test:
  -config string
        path to config file
FAIL    github.com/real-rm/gomail       0.095s

---

_**Assistant**_

这个错误表明在运行测试时缺少必要的配置文件参数。错误信息显示：

1. 测试需要 `-config` 参数来指定配置文件路径
2. 当前测试命令没有提供这个参数

让我们修改测试命令来正确运行测试。我会使用 `run_terminal_cmd` 工具来执行正确的测试命令：


---

```bash
cd /home/<USER>/github/go && go test -v -config=/path/to/config.toml github.com/real-rm/gomail -run ^TestLoadEmailWhitelist$
```

---

_**User**_

-test.paniconexit0       这个是在哪里添加的？

---

_**Assistant**_

`-test.paniconexit0` 是 Go 测试框架的一个命令行参数，它用于控制测试程序在退出时的行为。这个参数可以在以下位置添加：

1. 在运行 `go test` 命令时直接添加：
```bash
go test -test.paniconexit0 ...
```

2. 在测试文件中通过 `TestMain` 函数设置：
```go
func TestMain(m *testing.M) {
    flag.Parse()
    os.Exit(m.Run())
}
```

让我查看一下测试文件的内容：


---

Tool call timed out after 5000ms

---

让我尝试另一种方式：



---

<details>
            <summary>Grep search for "TestLoadEmailWhitelist" • **4** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/.specstory/history/2025-04-23_14-59-api-documentation-for-helper-watch-go.md` | L220 | `Running tool: /usr/local/go/bin/go test -timeout 30s -run ^TestLoadEmailWhitelist$ github.com/real-rm/gomail` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/.specstory/history/2025-04-23_03-50-unit-tests-for-sendmailvalidate-function.md` | L51 | `+ func TestLoadEmailWhitelist(t *testing.T) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/.specstory/history/2025-04-23_03-50-unit-tests-for-sendmailvalidate-function.md` | L235 | `- `TestLoadEmailWhitelist`: Tests the initialization of the whitelist` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/.specstory/history/2025-04-23_03-50-unit-tests-for-sendmailvalidate-function.md` | L344 | `func TestLoadEmailWhitelist(t *testing.T) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/gomail/sendMailValidate_test.go` | L49 | `func TestLoadEmailWhitelist(t *testing.T) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/gomail/sendMailValidate_test.go` | L49 | `func TestLoadEmailWhitelist(t *testing.T) {` |

</details>

---

让我查看测试文件的具体内容：



---

Read file: gomail/sendMailValidate_test.go

---

根据测试文件的内容，我看到这个测试文件使用了 `gohelper.SetupTestEnv` 来设置测试环境，并且需要一个配置文件 `local.test.ini`。

要添加 `-test.paniconexit0` 参数，你可以：

1. 在运行测试时直接添加：
```bash
go test -test.paniconexit0 -config=/path/to/local.test.ini github.com/real-rm/gomail -run ^TestLoadEmailWhitelist$
```

2. 或者在测试文件中添加 `TestMain` 函数：

```go
func TestMain(m *testing.M) {
    flag.Set("test.paniconexit0", "true")
    flag.Parse()
    os.Exit(m.Run())
}
```

建议使用第一种方式，因为这样更灵活，不需要修改测试文件。同时，你需要确保：

1. 配置文件 `local.test.ini` 存在并且路径正确
2. 使用 `-config` 参数指定配置文件的路径

正确的测试命令应该是：

```bash
go test -test.paniconexit0 -config=/path/to/local.test.ini github.com/real-rm/gomail -run ^TestLoadEmailWhitelist$
```

其中 `/path/to/local.test.ini` 需要替换为实际的配置文件路径。

---

_**User**_

我在运行的时候，没有添加这个参数，但现在有错flag provided but not defined: -test.paniconexit0
Usage of /tmp/go-build2275465998/b001/gomail.test:
  -config string
        path to config file
exit status 2
FAIL    github.com/real-rm/gomail       0.148s

---

_**Assistant**_

这个错误表明 `-test.paniconexit0` 参数不是由你的测试代码定义的，而是由 Go 测试框架自动添加的。这个参数通常是由某些测试工具或 IDE 自动添加的。

让我们检查一下测试文件中的配置相关代码：

1. 首先，我看到测试文件中有 `setupTestSendMailValidate` 函数，它负责设置测试环境：

```go
func setupTestSendMailValidate(t *testing.T) func() {
    currentDir, err := os.Getwd()
    if err != nil {
        t.Fatalf("Failed to get current directory: %v", err)
    }
    configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
    if err != nil {
        t.Fatalf("Failed to get absolute path: %v", err)
    }
    if _, err := os.Stat(configPath); err != nil {
        t.Fatalf("Config file not found at %s: %v", configPath, err)
    }
    fmt.Println("configPath", configPath)
    gohelper.SetRmbaseFileCfg(configPath)
    // Initialize test environment
    if err := gohelper.SetupTestEnv(gohelper.TestOptions{
        UseEnvConfig: true,
    }); err != nil {
        t.Fatalf("Failed to setup test environment: %v", err)
    }

    return gohelper.CleanupTestEnv
}
```

要解决这个问题，你可以：

1. 确保配置文件存在：
```bash
ls -l gomail/local.test.ini
```

2. 使用正确的测试命令：
```bash
cd /home/<USER>/github/go
go test -config=gomail/local.test.ini github.com/real-rm/gomail -run ^TestLoadEmailWhitelist$
```

让我帮你检查配置文件是否存在：



---

<details>
            <summary>Listed directory gomail • **10** results</summary>
        
| Name |
|-------|
| 📄 `sendMailValidate_test.go` |
| 📄 `sendMailValidate.go` |
| 📁 `.git` |
| 📄 `local.test.ini` |
| 📄 `mail.go` |
| 📄 `go.mod` |
| 📄 `go.sum` |
| 📄 `engines.go` |
| 📄 `ses_mail.go` |
| 📄 `README.md` |

</details>

---


我看到 `local.test.ini` 文件确实存在。让我们尝试运行正确的测试命令：




---

```bash
cd /home/<USER>/github/go && go test -config=gomail/local.test.ini github.com/real-rm/gomail -run ^TestLoadEmailWhitelist$
```

---



---

_**User**_

For the code present, we get this error:
```
db.ListCollectionNames undefined (type *mongoDatabase has no field or method ListCollectionNames)
```
How can I resolve this? If you propose a fix, please make it concise.

---

_**Assistant**_

The error occurs because `mongoDatabase` is a wrapper type that doesn't expose the `ListCollectionNames` method. Let's fix this by adding the method to the `mongoDatabase` type:



---

```diff
  	return collections, nil
  }
+ 
+ // ListCollectionNames returns a list of collection names in the database
+ func (md *mongoDatabase) ListCollectionNames(ctx context.Context, filter interface{}, opts ...*options.ListCollectionsOptions) ([]string, error) {
+ 	return md.db.ListCollectionNames(ctx, filter, opts...)
+ }
  
```

---


This fix adds the `ListCollectionNames` method to the `mongoDatabase` type, which delegates to the underlying MongoDB driver's method. The error should now be resolved.

---

_**User**_

For the code present, we get this error:
```
cfgPreDef.DBName.client undefined (type string has no field or method client)
```
How can I resolve this? If you propose a fix, please make it concise.

---

_**Assistant**_

The error occurs because we're trying to access `client` on a string (`DBName`). We need to use the `Database` function to get the database instance. Here's the fix:



---

```diff
  		return nil
  	}
- 	return &MongoCollection{coll: cfgPreDef.DBName.client.Database(cfgPreDef.DBName).Collection(cfgPreDef.CollName)}
+ 	db := Database(cfgPreDef.DBName)
+ 	if db == nil {
+ 		return nil
+ 	}
+ 	return &MongoCollection{coll: db.db.Collection(cfgPreDef.CollName)}
  }
  
```

---

This fix properly uses the `Database` function to get the database instance and then accesses its collection. The error should now be resolved.

---

_**User**_

根据mongdb的修改，添加对于的ut

---

_**Assistant**_

我会为 MongoDB 的修改添加对应的单元测试。主要需要测试 `CreateCollection` 和 `PreDefColl` 的功能。

让我添加测试用例：




---

```diff
  	})
  }
+ 
+ func TestCreateCollection(t *testing.T) {
+ 	cleanup := setupTestMongoDB(t)
+ 	defer cleanup()
+ 
+ 	t.Run("Create new collection", func(t *testing.T) {
+ 		db := Database("tmp")
+ 		if db == nil {
+ 			t.Fatal("Failed to get database")
+ 		}
+ 
+ 		// Test creating a new collection
+ 		collectionName := "test_create_collection"
+ 		err := db.CreateCollection(context.Background(), collectionName, nil)
+ 		if err != nil {
+ 			t.Fatalf("Failed to create collection: %v", err)
+ 		}
+ 
+ 		// Verify collection exists
+ 		collections, err := db.ListCollectionNames(context.Background(), bson.M{})
+ 		if err != nil {
+ 			t.Fatalf("Failed to list collections: %v", err)
+ 		}
+ 		if !contains(collections, collectionName) {
+ 			t.Errorf("Collection %s not found in database", collectionName)
+ 		}
+ 
+ 		// Test creating existing collection (should not error)
+ 		err = db.CreateCollection(context.Background(), collectionName, nil)
+ 		if err != nil {
+ 			t.Errorf("Creating existing collection should not error: %v", err)
+ 		}
+ 
+ 		// Cleanup
+ 		coll := Coll("tmp", collectionName)
+ 		if err := coll.Drop(context.Background()); err != nil {
+ 			t.Errorf("Failed to drop collection: %v", err)
+ 		}
+ 	})
+ }
+ 
+ func TestPreDefColl(t *testing.T) {
+ 	cleanup := setupTestMongoDB(t)
+ 	defer cleanup()
+ 
+ 	t.Run("Get predefined collection", func(t *testing.T) {
+ 		// Test with valid collection name
+ 		coll := PreDefColl("test_coll")
+ 		if coll == nil {
+ 			t.Fatal("Failed to get predefined collection")
+ 		}
+ 
+ 		// Test with invalid collection name
+ 		invalidColl := PreDefColl("non_existent_coll")
+ 		if invalidColl != nil {
+ 			t.Error("Expected nil for non-existent collection")
+ 		}
+ 
+ 		// Test collection operations
+ 		doc := bson.M{"test": "value"}
+ 		_, err := coll.InsertOne(context.Background(), doc)
+ 		if err != nil {
+ 			t.Fatalf("Failed to insert document: %v", err)
+ 		}
+ 
+ 		// Verify document was inserted
+ 		var result bson.M
+ 		err = coll.FindOne(context.Background(), bson.M{"test": "value"}).Decode(&result)
+ 		if err != nil {
+ 			t.Fatalf("Failed to find document: %v", err)
+ 		}
+ 		if result["test"] != "value" {
+ 			t.Errorf("Wrong value in found document: got %v, want %v", result["test"], "value")
+ 		}
+ 
+ 		// Cleanup
+ 		if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
+ 			t.Errorf("Failed to cleanup collection: %v", err)
+ 		}
+ 	})
+ }
  
```

---

我添加了两个测试函数：

1. `TestCreateCollection`:
   - 测试创建新集合
   - 验证集合是否成功创建
   - 测试重复创建已存在的集合（不应报错）
   - 包含清理操作

2. `TestPreDefColl`:
   - 测试获取预定义集合
   - 测试无效集合名的情况
   - 测试集合的基本操作（插入和查询）
   - 包含清理操作

这些测试用例覆盖了新增功能的主要场景。每个测试都使用了 `setupTestMongoDB` 来设置测试环境，并在测试结束后进行清理。

---

