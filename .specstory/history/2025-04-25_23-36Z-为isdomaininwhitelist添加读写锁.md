<!-- Generated by SpecStory -->

# 为isDomainInWhitelist添加读写锁 (2025-04-25 23:36Z)

_**User**_

给isDomainInWhitelist 加上读写锁

---

_**Assistant**_

I'll help you add a read-write lock to protect the `whiteListDomainMap` access in the `isDomainInWhitelist` function. First, let me explain what I'm going to do:

1. Add a sync.RWMutex to protect the map
2. Use RLock for reading and Lock for writing operations
3. Ensure proper locking in all functions that access the map

Let me make these changes:


---

```diff
  	"net"
  	"strings"
+ 	"sync"
  	"time"
  
```

```diff
  	emailMXFailedCol   *gomongo.MongoCollection
  	whiteListDomainMap = make(map[string]bson.M)
+ 	whiteListMutex     sync.RWMutex
  	gIsInit            bool
  )
```

```diff
  	if emailWhitelistCol != nil {
  		if emailWhiteLists, err := emailWhitelistCol.FindToArray(ctx, bson.M{}); err == nil {
+ 			whiteListMutex.Lock()
  			for _, emailWhiteList := range emailWhiteLists {
  				whiteListDomainMap[emailWhiteList["_id"].(string)] = emailWhiteList
  			}
+ 			whiteListMutex.Unlock()
  		} else {
  			golog.Error("Failed to load email whitelist", "error", err)
```

```diff
  
  func isDomainInWhitelist(domain string) bool {
+ 	whiteListMutex.RLock()
  	ret := whiteListDomainMap[domain]
+ 	whiteListMutex.RUnlock()
  
  	if ret == nil {
```

```diff
  			return true
  		}
+ 		whiteListMutex.Lock()
  		delete(whiteListDomainMap, domain)
+ 		whiteListMutex.Unlock()
  		return false
  	}
```

```diff
  	_, err := emailWhitelistCol.UpdateOne(ctx, bson.M{"_id": domain}, bson.M{"$set": doc}, options.Update().SetUpsert(true))
  	if err == nil {
+ 		whiteListMutex.Lock()
  		whiteListDomainMap[domain] = doc
+ 		whiteListMutex.Unlock()
  	} else {
  		golog.Error("Failed to add domain to whitelist", "error", err)
```

---

I've made the following changes to add proper read-write locking to protect the `whiteListDomainMap`:

1. Added `sync` package import
2. Added a `whiteListMutex` RWMutex variable
3. Modified `isDomainInWhitelist` to:
   - Use RLock()/RUnlock() for reading the map
   - Use Lock()/Unlock() when deleting from the map
4. Modified `addDomainToWhitelist` to use Lock()/Unlock() when writing to the map
5. Modified `loadEmailWhitelist` to use Lock()/Unlock() when initializing the map

The changes ensure that:
- Multiple goroutines can read the map simultaneously using RLock()
- Only one goroutine can write to the map at a time using Lock()
- The map is properly protected during all operations

This implementation provides thread-safe access to the `whiteListDomainMap` while maintaining good performance by allowing concurrent reads.

---

_**User**_

send()    HTML 与 Text 回退逻辑容易出现重复或空白

当仅提供 Text 时会被赋给 html，而 text 也赋值 Text，导致两份相同内容；当两者皆为空则发送空邮件。建议：

若两者皆为空直接报错；
若仅 Text 存在，不必再写入 Html 字段。

---

_**Assistant**_

I'll help you modify the `Send` function in `sesEngine.go` to improve the HTML and Text fallback logic. Let me make these changes:



---

```diff
  		setName = engine.setName
  	}
- 	var html string
+ 
+ 	// Check if both HTML and Text are empty
+ 	if msg.HTML == "" && msg.Text == "" {
+ 		return "", fmt.Errorf("email content cannot be empty")
+ 	}
+ 
+ 	// Create message body
+ 	body := &ses.Body{}
  	if msg.HTML != "" {
- 		html = msg.HTML
- 	} else if msg.Text != "" {
- 		html = msg.Text
- 	} else {
- 		html = ""
- 	}
- 	var text string
+ 		body.Html = &ses.Content{
+ 			Charset: aws.String("UTF-8"),
+ 			Data:    aws.String(msg.HTML),
+ 		}
+ 	}
  	if msg.Text != "" {
- 		text = msg.Text
- 	} else {
- 		text = ""
+ 		body.Text = &ses.Content{
+ 			Charset: aws.String("UTF-8"),
+ 			Data:    aws.String(msg.Text),
+ 		}
  	}
  
```

```diff
  		},
  		Message: &ses.Message{
- 			Body: &ses.Body{
- 				Html: &ses.Content{
- 					Charset: aws.String("UTF-8"),
- 					Data:    aws.String(html),
- 				},
- 				Text: &ses.Content{
- 					Charset: aws.String("UTF-8"),
- 					Data:    aws.String(text),
- 				},
- 			},
+ 			Body: body,
  			Subject: &ses.Content{
  				Charset: aws.String("UTF-8"),
```

---

I've made the following improvements to the `Send` function:

1. Added validation to check if both HTML and Text are empty:
```go
if msg.HTML == "" && msg.Text == "" {
    return "", fmt.Errorf("email content cannot be empty")
}
```

2. Simplified the body creation logic:
   - Removed the fallback logic where Text was being copied to HTML
   - Created a `body` variable that only includes the content types that are actually present
   - HTML and Text are now set independently based on their presence

3. The new logic:
   - If only HTML is provided, only HTML content will be sent
   - If only Text is provided, only Text content will be sent
   - If both are provided, both will be sent
   - If neither is provided, an error will be returned

This change:
- Prevents sending empty emails
- Eliminates duplicate content when only Text is provided
- Makes the code more maintainable and clearer in its intent
- Follows AWS SES best practices by only including the content types that are actually needed

---

_**User**_

gIsInit 并发读写存在数据竞争
validateEmail 与其他协程可能同时修改/读取 gIsInit，但未使用原子操作或互斥锁保护，容易在高并发下触发 race。
推荐做法：

var whiteListOnce sync.Once

func ensureWhitelistLoaded(ctx context.Context) {
	whiteListOnce.Do(func() { loadEmailWhitelist(ctx) })
}
并在 validateEmail, GetValidatedEmails 等函数首行调用 ensureWhitelistLoaded(ctx)，彻底消除竞态。



---

_**Assistant**_

I'll help you modify the code to use `sync.Once` to ensure thread-safe initialization of the whitelist. Let me make these changes:



---

```diff
  	whiteListDomainMap = make(map[string]bson.M)
  	whiteListMutex     sync.RWMutex
- 	gIsInit            bool
+ 	whiteListOnce      sync.Once
  )
+ 
+ func ensureWhitelistLoaded(ctx context.Context) {
+ 	whiteListOnce.Do(func() { loadEmailWhitelist(ctx) })
+ }
  
  func loadEmailWhitelist(ctx context.Context) {
```

```diff
  		}
  	}
- 	gIsInit = true
  }
  
```

```diff
  
  func validateEmail(ctx context.Context, email string) bool {
- 	whiteListMutex.RLock()
- 	if !gIsInit {
- 		whiteListMutex.RUnlock()
- 		loadEmailWhitelist(ctx)
- 	} else {
- 		whiteListMutex.RUnlock()
- 	}
+ 	ensureWhitelistLoaded(ctx)
  	parts := strings.Split(email, "@")
  	if len(parts) != 2 {
```

```diff
  		return []string{}, nil
  	}
- 	whiteListMutex.RLock()
- 	if !gIsInit {
- 		whiteListMutex.RUnlock()
- 		loadEmailWhitelist(ctx)
- 	} else {
- 		whiteListMutex.RUnlock()
- 	}
+ 	ensureWhitelistLoaded(ctx)
  	validatedEmails := []string{}
  	for _, email := range emails {
```

---

I've made the following changes to ensure thread-safe initialization:

1. Removed the `gIsInit` boolean flag and replaced it with `whiteListOnce sync.Once`
2. Added the `ensureWhitelistLoaded` function that uses `sync.Once` to guarantee initialization happens exactly once:
```go
func ensureWhitelistLoaded(ctx context.Context) {
    whiteListOnce.Do(func() { loadEmailWhitelist(ctx) })
}
```

3. Removed the `gIsInit = true` assignment from `loadEmailWhitelist` since it's no longer needed
4. Simplified the initialization checks in `validateEmail` and `GetValidatedEmails` to just call `ensureWhitelistLoaded(ctx)`

These changes provide several benefits:
- Eliminates the data race condition on `gIsInit`
- Ensures the whitelist is loaded exactly once, even under high concurrency
- Simplifies the code by removing manual mutex locking/unlocking for initialization checks
- Maintains thread safety while being more efficient (no need for RLock/RUnlock on every check)

The `sync.Once` pattern is the idiomatic Go way to handle one-time initialization in a thread-safe manner, and it's more efficient than using mutexes for this specific use case.

---

