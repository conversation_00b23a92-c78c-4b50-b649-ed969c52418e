<!-- Generated by SpecStory -->

# 合并changeDoc和existMergedProp (2025-07-08 00:26Z)

_**User**_

帮我修改直接把目前changeDoc 和existMergedProp 合并成existMergedProp，因为已经不需要changeDoc 作为输入了

---

_**Assistant**_

I'll help you modify the code to merge `changeDoc` and `existMergedProp` directly into `existMergedProp` since `changeDoc` is no longer needed as input. Let me first examine the current code structure to understand how these are being used.


---

<details>
            <summary>Searched codebase "CalculatePriority function definition and usage" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `goresodownload/cmd/goresodownload/main.go` | L244-291 |
| `goresodownload/cmd/goresodownload/main.go` | L187-244 |
| `test/helper_streaming.go` | L1-42 |
| `gostreaming/streaming.go` | L1-42 |
| `gofile/levelStore/level_store_helper.go` | L1-83 |
| `gofile/levelStore/level_store_helper.go` | L258-329 |
| `gofile/levelStore/level_store_helper_test.go` | L101-199 |
| `goresodownload/resource_download_queue.go` | L71-104 |
| `goresodownload/media_diff_analyzer.go` | L856-936 |
| `goresodownload/media_diff_analyzer.go` | L936-969 |
| `goresodownload/resource_download_queue.go` | L1-71 |
| `folder_sync/pkg/meta/queue.go` | L88-131 |
| `gofile/levelStore/dirKeyStore.go` | L451-504 |
| `test/media_diff_analyzer copy.go` | L815-837 |
| `test/media_diff_analyzer copy.go` | L754-815 |
| `folder_sync/pkg/fsutil/fsutil.go` | L159-206 |
| `folder_sync/pkg/fsutil/fsutil.go` | L1-69 |
| `test/media_diff_analyzer.go` | L981-1069 |
| `gofile/levelStore/dirKeyStore.go` | L265-307 |
| `test/media_diff_analyzer.go` | L911-981 |
| `goresodownload/cmd/goresodownload/main.go` | L1-64 |
| `folder_sync/pkg/meta/queue.go` | L1-32 |
| `gofile/levelStore/level_store_helper_test.go` | L399-493 |
| `gofile/levelStore/level_store_helper.go` | L83-139 |
| `folder_sync/pkg/sync/ongoing.go` | L1108-1139 |

</details>

---

Now let me search for the `CalculatePriority` function definition to understand its current signature:


---

<details>
            <summary>Grep search for "func CalculatePriority" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goresodownload/priority_calculator.go` | L27 | `func CalculatePriority(boardType string, existMergedProp bson.M) (int, error) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goresodownload/priority_calculator.go` | L27 | `func CalculatePriority(boardType string, existMergedProp bson.M) (int, error) {` |

</details>

---

Let me examine the current `CalculatePriority` function and the test file to understand how it's currently being used:


---

Read file: goresodownload/priority_calculator.go

---

Now let me look at the test file to understand how the function is currently being tested and what the expected behavior should be:


---

Read file: goresodownload/priority_calculator_test.go

---

Now I understand the current structure. Looking at the test file, I can see that the tests are currently creating a `testMergedProp` by merging the `changeDoc` data with the `existMergedProp` data. The user wants to simplify this by directly merging `changeDoc` and `existMergedProp` into `existMergedProp` since `changeDoc` is no longer needed as a separate input.

Let me examine how the `getPriority` function in main.go currently works:


---

Read file: goresodownload/cmd/goresodownload/main.go

---

Now I understand the current flow. The issue is that in the `getPriority` function, it's only using `existMergedProp` (fetched from the database), but the tests show that we need to merge the current `changeDoc` data with the existing merged property data to get the complete picture for priority calculation.

Let me modify the code to merge `changeDoc` and `existMergedProp` directly into `existMergedProp`. I'll need to:

1. Modify the `getPriority` function to accept both `changeDoc` and `prop` and merge them
2. Update the `CalculatePriority` function to work with the merged data
3. Update the tests accordingly

Let's start by modifying the `getPriority` function in main.go:


---



---



---

