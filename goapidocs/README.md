# 自动根据go* repo的变化，整合并更新本repo的README 总体思路
每个 go* 仓库中的 main 分支 push 事件触发一个 workflow dispatch 到 goapidocs。
goapidocs 仓库监听这个 dispatch 事件，执行 workflow。

workflow脚本会：

获取所有以 go 开头的仓库列表。

克隆它们（使用 GitHub Token）。

依次读取 README.md 和 api.md，合并生成一个大 README.md。

🧩 各个仓库（如 gotel, gosms）的 workflow 示例
```
# .github/workflows/trigger-doc-update.yml
name: Trigger API Docs Update

on:
  push:
    branches:
      - main

jobs:
  notify-docs-repo:
    runs-on: ubuntu-latest
    steps:
      - name: Trigger docs repo update
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.WORKFLOW_TOKEN_GITHUB }}
          repository: real-rm/goapidocs
          event-type: update-docs
          client-payload: '{"source_repo": "${{ github.repository }}"}'
```

🧩 goapidocs 仓库的 workflow 见 本repo # .github/workflows/main.yml




# 正确生成方式（Classic Token + repo 权限）

1. 创建 Classic Personal Access Token
打开链接：
https://github.com/settings/tokens

点击：“Generate new token (classic)”

设置如下：

Note: Auto Push for goapidocs

Expiration: 建议 90 days 或 No expiration

Scopes 权限：
✅ 勾选 repo （这是关键）

生成后会显示一个 ghp_... 开头的 Token
⚠️ 请务必复制下来，只显示一次！

2. 添加为 Secret
打开：https://github.com/real-rm/goapidocs/settings/secrets/actions

创建 Secret：

名字：GH_PAT_WRITE

值：你刚刚复制的 ghp_... Token



# 自托管 runner 设置
你需要自己部署一台机器（物理机、云主机都行）并注册 Runner：
🔧 一分钟概览部署步骤（Linux）
进入仓库：https://github.com/real-rm/goapidocs/actions/runners?tab=self-hosted

点击右上角：New runner, 跳转后点击New self-hosted runner

选择 Linux 平台，复制显示的终端命令，大致是：

```
Download
# Create a folder
$ mkdir actions-runner && cd actions-runner# Download the latest runner package
$ curl -o actions-runner-linux-x64-2.324.0.tar.gz -L https://github.com/actions/runner/releases/download/v2.324.0/actions-runner-linux-x64-2.324.0.tar.gz# Optional: Validate the hash
$ echo "e8e24a3477da17040b4d6fa6d34c6ecb9a2879e800aa532518ec21e49e21d7b4  actions-runner-linux-x64-2.324.0.tar.gz" | shasum -a 256 -c# Extract the installer
$ tar xzf ./actions-runner-linux-x64-2.324.0.tar.gz
Configure
# Create the runner and start the configuration experience
$ ./config.sh --url https://github.com/real-rm/goapidocs --token AE6VAM6YUJG7CMGNQI5JEZDIEYJQY# Last step, run it!
$ ./run.sh
Using your self-hosted runner
# Use this YAML in your workflow file for each job
runs-on: self-hosted
```