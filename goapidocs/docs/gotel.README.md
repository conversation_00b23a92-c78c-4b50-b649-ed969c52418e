# Gotel

Gotel is a Go package that provides a simple and flexible interface for making phone calls. It currently supports Twilio as a call provider.

## Features

- Simple and clean interface for making phone calls
- Support for Twilio call provider
- Easy to extend with new providers
- Comprehensive error handling
- Detailed logging

## Installation

```bash
go get github.com/real-rm/gotel
```

## Usage

### Basic Usage with Twilio

```go
package main

import (
	"context"
	"log"

	"github.com/real-rm/gotel"
)

func main() {
	// Create a new Twilio caller
	twilioCaller, err := gotel.NewTwilioCaller("your_account_sid", "your_auth_token")
	if err != nil {
		log.Fatalf("Failed to create Twilio caller: %v", err)
	}
	
	// Create a dialer
	dialer, err := gotel.NewDialer(twilioCaller)
	if err != nil {
		log.Fatalf("Failed to create dialer: %v", err)
	}
	
	// Make a call
	params := gotel.CallParams{
		To:    "+**********",
		From:  "+**********",
		Twiml: "<Response><Say>Hello!</Say></Response>",
	}
	
	err = dialer.Call(context.Background(), params)
	if err != nil {
		log.Fatalf("Call failed: %v", err)
	}
}
```

### Using Mock for Testing

```go
// Create a mock caller
mockCaller := &gotel.MockCaller{}

// Create a dialer with the mock caller
dialer, err := gotel.NewDialer(mockCaller)
if err != nil {
	log.Fatalf("Failed to create dialer: %v", err)
}

// Make a test call
params := gotel.CallParams{
	To:    "+**********",
	From:  "+**********",
	Twiml: "<Response><Say>This is a test!</Say></Response>",
}

err = dialer.Call(context.Background(), params)
if err != nil {
	log.Fatalf("Test call failed: %v", err)
}
```

## Error Handling

The package provides comprehensive error handling:

- Invalid call parameters (empty to/from)
- Missing required parameters (URL or Twiml)
- Provider-specific errors
- Missing provider configuration

## Logging

The package uses the `golog` package for logging. It logs:
- Successful call attempts
- Provider responses
- Error conditions

## License

MIT License