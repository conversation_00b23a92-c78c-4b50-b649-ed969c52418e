<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# goconfig

```go
import "github.com/real-rm/goconfig"
```

Package lib provides core functionality and utilities for the application. This file contains configuration management functionality including loading and accessing configuration values from TOML files.

## Index

- [Variables](<#variables>)
- [func Config\(path string\) interface\{\}](<#Config>)
- [func ConfigBool\(path string\) \(bool, error\)](<#ConfigBool>)
- [func ConfigFloat\(path string\) \(float64, error\)](<#ConfigFloat>)
- [func ConfigInt\(path string\) \(int, error\)](<#ConfigInt>)
- [func ConfigString\(path string\) \(string, error\)](<#ConfigString>)
- [func LoadConfig\(\) error](<#LoadConfig>)
- [func ResetConfig\(\)](<#ResetConfig>)


## Variables

<a name="L2_FOLDER_LIST"></a>

```go
var L2_FOLDER_LIST = []string{
    "6ff4b", "6aa9d", "c45ab", "c93c7",
    "577a5", "54fd4", "ae459", "95f8a",
    "e3e17", "0a72b", "6ac7f", "13a1e",
    "64787", "1f277", "ce437", "51b20",
    "6ff08", "12bf4", "9497f", "d928f",
    "960ba", "c6ec7", "00a34", "96c3b",
    "e1633", "f891a", "b8bfa", "76ab1",
    "b889e", "421b9", "e8494", "7da6c",
    "95168", "0af5a", "90544", "a6fd2",
    "345b0", "54f61", "329ac", "049a5",
    "f91b6", "555dd", "97ab3", "2f714",
    "6e147", "e383c", "7186f", "1d341",
    "e0261", "1bf19", "10413", "08f2e",
    "c7b46", "3d293", "ee2a2", "3a18c",
    "1a6a7", "c0a58", "670da", "de239",
    "aeed1", "fe096", "3bf59", "91878",
    "65fd5", "ee0b1", "8f804", "d32cc",
    "178cc", "d77a1", "dcb76", "f4202",
    "aca0b", "737bb", "76df1", "0ff2b",
    "4384f", "df0c6", "d384c", "776d4",
    "1cc36", "15dbf", "74353", "824a5",
    "9673b", "15785", "9d3c5", "ab0ae",
    "5cb96", "dc070", "dddef", "2c298",
    "9c455", "247a8", "5a752", "f7409",
    "f6be4", "432a9", "c6cde", "530aa",
    "83edc", "6db53", "df450", "32c54",
    "41ce4", "64592", "818dc", "1c2e6",
    "e75a0", "9362c", "abbce", "f5c58",
    "ef56c", "7df1f", "ba34d", "c27f1",
    "96c59", "4e758", "ad0e7", "68092",
    "b863b", "a33c1", "8178b", "f2b48",
    "48eb8", "e6c57", "0fa4a", "e518c",
    "ccbb8", "287c1", "57610", "99df2",
    "6722e", "a5df7", "d621f", "3099f",
    "832af", "388b5", "f9a4a", "27e53",
    "afa75", "be7ce", "26d62", "6eaa4",
    "b00b5", "6e703", "fd647", "38bd6",
    "ec610", "92409", "6d611", "3ee53",
    "b7d88", "4af4b", "6d619", "3f4cf",
    "30f26", "48eb8", "2022d", "5c035",
    "c6732", "5ecdb", "68099", "a0747",
    "56c64", "48689", "40a04", "8a177",
    "8a3bd", "b4fa6", "afdb3", "9e7a5",
    "fb336", "2fce9", "c6a3e", "bdee4",
    "5d687", "1c41f", "9c18a", "a2813",
    "ed5cb", "24a07", "9b3d6", "6c6fd",
    "d2dbd", "a57eb", "31d96", "9ae4c",
    "7aef3", "a520d", "48b36", "c3b84",
    "7e421", "e7294", "7baac", "069cf",
    "bdd37", "9c849", "46ff0", "4b240",
    "8d087", "ff4fa", "dd406", "eb212",
    "52481", "9ecb5", "6498d", "2e3b2",
    "73abe", "42360", "e3936", "609b9",
    "47cad", "452fc", "e5838", "c2015",
    "329db", "dc6f1", "0ef0e", "ce7d7",
    "2e4d8", "11344", "7a493", "bbc11",
    "55048", "f2dc4", "bf90a", "e9c88",
    "be886", "0553e", "1e640", "5e4aa",
    "9f33f", "5d6cb", "f6de8", "57a1e",
    "b88be", "be2aa", "2bfbb", "b3bfb",
    "450e5", "e167b", "a916d", "d9bb8",
    "9d298", "9e5ab", "ec1cb", "63fc4",
    "0a219", "14f76", "69db1", "0347b",
    "16a7e", "c6f4d", "499ef", "3dc40",
    "60bdc", "750ea", "494bb", "6256c",
    "88e88", "bd190", "a45b4", "1d008",
    "63bd8", "89ec3", "e8288", "acef9",
    "dff08", "5e90f", "b04aa", "18ff5",
    "6f27e", "d5fcd", "e45e7", "2c99e",
    "8a7ab", "cdc90", "ee50a", "5cd13",
    "c148e", "c9da6", "782ac", "bf75d",
    "2d482", "88ad1", "f4d00", "1a2e9",
    "2af61", "5d695", "982b2", "dc979",
    "7ab8a", "3cedc", "952c8", "db6dc",
    "a7bef", "2acb6", "f3f2b", "e6d02",
    "2d641", "f1bd3", "377b6", "ea9b6",
    "a4a9e", "bbbce", "57246", "9fa29",
    "f9e45", "00ae9", "bc677", "08179",
    "effc3", "b4c67", "b097a", "62972",
    "b9860", "150b6", "8ed43", "0863b",
    "d333f", "4a70f", "94e8d", "06a92",
    "95f7e", "a6d06", "3a16e", "79a39",
    "b902f", "d3332", "15e15", "407ac",
    "9aad9", "b49a9", "400cf", "3a1a7",
    "d5a32", "e6f09", "6edbb", "3ce72",
    "bacd5", "6b691", "e9c45", "08f00",
    "0670f", "a2337", "69662", "85e4d",
    "ce2f4", "a1005", "51a31", "2377e",
    "5e4c3", "664ba", "a2350", "0594c",
    "b0668", "f7cbc", "c9a01", "a4ff3",
    "dfef0", "17ae8", "207bf", "38688",
    "72eb1", "e1e12", "a3b16", "52a9a",
    "b599d", "b5e19", "3a779", "6299e",
    "d74d4", "ee67c", "0c7ed", "c9406",
    "b567a", "b6941", "069a1", "d95ee",
    "4402c", "1fa4e", "60507", "f884c",
    "b9af8", "cee40", "7ac2e", "798b5",
    "c3244", "a84ae", "d8013", "55822",
    "302cb", "0602d", "e4f99", "ad0e3",
    "a8944", "fff9a", "043d6", "db1ca",
    "18a66", "2df9f", "95c8e", "43798",
    "d7374", "683bb", "7ba21", "53418",
    "713b1", "761ab", "17994", "72b45",
    "09d1b", "9a0be", "f10e9", "3d53a",
    "215a3", "f3992", "5dc07", "e7855",
    "0506e", "09105", "852b4", "3fbfc",
    "c8e86", "a39bf", "43cf7", "60897",
    "f0577", "d905e", "e7c74", "49b47",
    "56696", "37452", "572fc", "9f50d",
    "aacc4", "d7793", "97955", "257a5",
    "10321", "0f555", "4a5f6", "61715",
    "e08ee", "4d2c2", "69fb8", "9ce59",
    "2ec8a", "bb131", "1927d", "6c5fc",
    "a65ec", "7230b", "f0446", "14230",
    "f4dc7", "358f6", "32e85", "798b9",
    "6652b", "f722e", "822bd", "42c73",
    "7d59d", "b42d4", "8ba79", "fd40e",
    "8c6fd", "f3e83", "9d9a6", "bc05d",
    "38733", "8d4c2", "073d9", "719e7",
    "9e964", "6596a", "6bd6a", "fb8e6",
    "66237", "56693", "b0abc", "b7a82",
    "7873d", "ec36c", "3450a", "538f0",
    "6ee19", "43466", "e4aad", "1430a",
    "24629", "c8999", "7421d", "02594",
    "f0df9", "4b344", "b7adb", "0006c",
    "073b5", "f9d07", "58d56", "cd1ae",
    "7c0b2", "c399e", "72c1e", "d148b",
    "6ef74", "c10e3", "f2c07", "610ad",
    "23c64", "2b281", "c0c3c", "303c6",
    "4bbe5", "d2030", "ce7db", "b25a3",
    "0294f", "a4f1d", "732bb", "14f98",
    "cf2ca", "62cbd", "e8f48", "4d33c",
    "c576f", "bc558", "2f3a3", "e89fb",
    "eb260", "f5853", "b177f", "592fa",
    "ec67c", "4c30f", "590f5", "f360b",
    "7cc9a", "e056d", "1496b", "d1b14",
    "8e5e9", "7269b", "6d32b", "2a7db",
    "c7a40", "380cc", "cf8bd", "41947",
    "a1626", "14f29", "16830", "eec13",
    "7dae3", "778b9", "e0f79", "78ea7",
    "ff14d", "75a43", "cb0db", "8028c",
    "39575", "0326f", "4e4b0", "ec020",
    "2959b", "84746", "e6815", "42e12",
    "6c771", "1fb1a", "43270", "a39d9",
    "fe961", "b959a", "cb9a4", "172f6",
    "dd6d5", "d090b", "f20e3", "b9347",
    "6f441", "d3fb5", "22f94", "d690e",
    "2337d", "2ba8b", "f39bd", "cc66d",
    "da460", "d16da", "055ba", "7f19a",
    "00d1a", "a5004", "e8d9c", "24e65",
    "640b4", "a89cb", "a06f7", "30223",
    "1cb17", "6781a", "05abd", "f6450",
    "264e1", "8990c", "3a6c1", "19b77",
    "d57de", "a7238", "dcd6e", "96311",
    "d741c", "1a2c1", "36e2f", "abb2b",
    "64041", "6e337", "b9ffa", "9d6ee",
    "58dea", "76418", "a8eb6", "54766",
    "45bbd", "7b740", "2fa96", "582f5",
    "61c9b", "4311d", "5f604", "ada8c",
    "d58f3", "9021b", "a6642", "ae3e5",
    "fea35", "602f5", "0c0d3", "8d4c3",
    "8900c", "9856b", "2aff3", "b9b72",
    "ff72d", "b4a6a", "2c124", "c941c",
    "2759e", "51c76", "21509", "cf882",
    "fd593", "ceb4e", "f4dea", "22039",
    "b80a5", "6b98c", "0774d", "97125",
    "922fa", "affd7", "aa716", "503a3",
    "48f16", "96fea", "2c261", "28529",
    "bb57c", "8ee86", "b9d42", "b0500",
    "abf7b", "48d23", "7077e", "ef660",
    "8d89c", "19774", "07ec4", "4212c",
    "6e4f7", "222cf", "17e9f", "b7d83",
    "cac99", "28a68", "f6b96", "efd3f",
    "c0441", "c8440", "5d183", "49ba8",
    "de628", "40b2a", "76653", "7e93a",
    "8ebbf", "143c0", "6b237", "df4a8",
    "c9313", "5ffcb", "5818d", "76aac",
    "a6702", "de6fb", "b38ff", "b025b",
    "74d0c", "e2f1d", "b7bfa", "740ed",
    "70338", "dbdb0", "795d1", "9a4e2",
    "ed36b", "b64bc", "2e82b", "d926e",
    "219ac", "c13a5", "aa6c7", "2255a",
    "e93ac", "3d38f", "29b6a", "14098",
    "973b7", "1ff66", "988ae", "59871",
    "cb65f", "7029e", "9d633", "ffe97",
    "3f51e", "e49ad", "6b1e1", "98407",
    "efe39", "007e3", "02263", "9c4bb",
    "62e10", "e93f1", "87c89", "5ad69",
    "ef48d", "5bf9e", "df4e9", "ab0fc",
    "67a34", "4f15b", "b7aa4", "0280c",
    "dd22d", "37e74", "8504b", "ee762",
    "68b8d", "6372f", "c063c", "18a23",
    "5adde", "47d1b", "b4d2c", "312a2",
    "edec9", "06866", "9bac9", "eadf3",
    "add55", "a8ed0", "49288", "376b3",
    "c0cbf", "bb019", "40140", "e7909",
    "34db8", "258c9", "304c7", "fc242",
    "90605", "65cd8", "7ccb6", "f3601",
    "b5f92", "76f23", "493b5", "6759a",
    "116dc", "2573c", "663ef", "2ec91",
    "266ba", "d3c42", "7c705", "0c088",
    "82828", "8e988", "c239a", "68fb9",
    "c7f35", "4888c", "c41a2", "5bb9f",
    "503d3", "d400b", "a929e", "1a290",
    "a64c0", "69c91", "34044", "b5966",
    "d40ad", "eb1b1", "e5d51", "0ad53",
    "5b3f3", "c5ee4", "3b3ce", "8b878",
    "05292", "772f4", "d02cc", "fd637",
    "61a74", "d0b12", "e3383", "e4bac",
    "efc34", "f507e", "fe40d", "b6a0d",
    "ad04a", "b733e", "bea9b", "a090c",
    "476af", "5952d", "5028f", "62053",
    "881a6", "39ec3", "ed31a", "c7e34",
    "1fbe3", "f61ad", "0a319", "209e5",
    "1f1fc", "4f289", "eb710", "e6422",
    "43d47", "2d30c", "2c813", "f861b",
    "a8e2c", "1d6cd", "1b116", "818c8",
    "d441f", "5bfa1", "440bf", "2fe73",
    "4054c", "db9e8", "58a49", "cd1ca",
    "30e8e", "d0893", "a239a", "7ad65",
    "b6b08", "d786a", "b9492", "8b323",
    "486ce", "b57bb", "9c54a", "1ed4c",
    "a3f08", "39fd2", "71391", "46aa8",
    "dbc39", "276d3", "6d2d3", "d041e",
    "699db", "e9b55", "96fee", "dc078",
    "79f68", "e9b69", "567f8", "88bf1",
    "79358", "aa915", "033af", "8b83e",
    "b5f09", "4f7c3", "ab955", "50072",
    "8b39f", "8dd23", "744d4", "4a089",
    "5005e", "16108", "55f04", "538c1",
    "e98f5", "6131b", "a55a8", "5a141",
    "cd367", "8cc52", "76a88", "7ee83",
    "8c227", "a75eb", "61ad8", "b06c4",
    "100f4", "c1b1d", "5c432", "c6260",
    "0d81a", "61b30", "e8e65", "3748a",
    "d80da", "abad5", "f7145", "57daf",
    "71ab0", "50f21", "04940", "c8a19",
    "a5eab", "02c28", "eab74", "ca962",
    "445e3", "b7da7", "695ac", "95c0a",
    "2735f", "108fa", "3b3c0", "101a3",
    "43030", "d19d4", "7b412", "1ff64",
    "19ec2", "5cad5", "59a08", "822a1",
    "a8819", "6688b", "3e336", "3e5b7",
    "cb169", "9cc01", "a24e6", "5302e",
    "ba56a", "4ec83", "da985", "60d44",
    "f4ca9", "930c5", "4d3c6", "c15e2",
    "ebf2e", "4ce27", "8b280", "b0925",
    "ff8ca", "99746", "1b269", "c3cc7",
    "e0a72", "15be7", "9db6f", "afabc",
    "2b60e", "b0a5f", "380ec", "301f2",
    "d6d36", "9ff3f", "d4dec", "531fe",
    "bc1b2", "9651b", "69fe5", "fc72a",
    "90c5e", "1f153", "57a8c", "93e35",
    "3d94f", "a1257", "0a357", "25dea",
    "43517", "a8a63", "2c0e5", "85058",
    "2c331", "5f924", "33309", "88f49",
    "b3fe6", "c6c41", "61e2f", "2b750",
    "06199", "01da9", "f1c74", "84ac9",
    "2a309", "3a161", "e7ec4", "116ad",
    "81ecb", "c1cd4", "f0f15", "92460",
    "e4a3d", "2d397", "cf61f", "71468",
    "94779", "abfbd", "4597b", "9c5a3",
    "01801", "1c292", "53e28", "7b66d",
    "eeaf7", "d271c", "07ac1", "87c07",
    "f5392", "5b026", "1bf2b", "ebb39",
    "fea7a", "006b0", "f91af", "2a0d0",
    "f1e1b", "93f35", "bd9c6", "5f2bf",
    "d8b1b", "86a0d", "3c05a", "78614",
    "4fbf1", "5aa12", "b3a6d", "c8b28",
    "2271f", "3c35f", "63127", "8d837",
    "03728", "f38d9", "07001", "d0caf",
    "599b0", "21cbc", "8a66d", "059a5",
    "2f783", "5f9a0", "5db10", "79705",
    "0d3a2", "062c4", "ea8f1", "357e6",
    "191c9", "1b0f0", "fbebc", "5d0ed",
    "a48d1", "72cc5", "f220d", "7dcd0",
    "c0c29", "11ec1", "ea263", "245ca",
    "01e06", "b13bc", "5d13c", "da52c",
    "90e0f", "8a5c0", "8e5f0", "74505",
    "265d2", "3ed28", "b1407", "fe5f3",
    "07fba", "ec19c", "ebfec", "844fd",
    "26482", "3b0a3", "75c12", "9c9d6",
    "db539", "9a250", "b9e42", "2fae8",
    "fffbd", "23fb4", "cc991", "e9c74",
    "7e24a", "23c0a", "a0494", "79926",
    "04b7f", "f4e7f", "f3f93", "9f2a1",
    "e8bff", "07f7f", "ac2e2", "6ab0d",
    "c956a", "15e87", "8cfce", "9e5f1",
    "52701", "45ba0", "af5ca", "f060a",
    "d6e70", "dd3e9", "b234b", "1959d",
    "f157a", "15c6d", "226e9", "a1182",
    "c44e3", "14d6e", "43e47", "dda0b",
    "30690", "291bc", "bda81", "b584b",
    "ca282", "7892c", "61237", "127c6",
    "50473", "975ef", "c7fef", "c8184",
    "bd1a7", "103a8", "8ce35", "c1ab2",
    "75f8f", "57584", "35877", "8deef",
    "95348", "03ee1", "0ebd6", "307fd",
    "b2705", "facfa", "a844e", "d27f5",
    "5f051", "54134", "cee69", "9f049",
    "733c5", "b77a8", "d3cd3", "ca06c",
    "acba2", "04dcc", "c156a", "4d99b",
    "967e2", "973f6", "4ed3e", "66f4a",
    "71f64", "912c9", "3e908", "d9f17",
    "f5b47", "b430e", "60083", "78834",
    "6a73f", "999a9", "d8ed2", "b4826",
    "2e89b", "14ec4", "fecfc", "3d6e3",
    "512ae", "1d163", "8b95a", "4f078",
    "c5117", "2eeb8", "b9238", "d1629",
    "a7226", "5484a", "694ed", "7b468",
    "a59a2", "2e5dc", "ecbe3", "ad194",
    "ef253", "bb664", "7f737", "1c9de",
    "5b1c9", "e4ff4", "7f36e", "378d8",
    "9ad1d", "9909b", "ca81e", "090ca",
    "25230", "1ab91", "2dc0d", "bf110",
    "8feb5", "09dbc", "ec4d3", "8a04c",
    "d22e2", "2182f", "d4659", "f4133",
    "88542", "b4f30", "ce764", "01d4d",
    "bc9e6", "0bbad", "4ce12", "29999",
    "0f692", "b067b", "416eb", "29160",
    "0a507", "894b6", "8c9d7", "ddb65",
    "8e46a", "0e102", "b3a9c", "b6a23",
    "eb8d3", "9633f", "e9eb9", "644ae",
    "89949", "64b7e", "de026", "8871f",
    "ff093", "163d5", "c50ce", "7d457",
    "8e708", "95378", "f694f", "0fbac",
    "549db", "ecce8", "d6658", "78665",
    "62e53", "966d7", "d585b", "0a943",
    "09f75", "54db3", "57911", "13eca",
    "a01d9", "ceece", "2ae51", "e1948",
    "616ca", "b3d20", "e05ac", "d5f51",
    "4df1a", "948cd", "91734", "99173",
    "60e00", "03dbc", "e0c52", "6efa9",
    "8f6c4", "1cb3c", "517e9", "81387",
    "d51d7", "bc864", "1c04c", "3c9df",
    "fa815", "7734e", "17c02", "98e68",
    "fa344", "ffc77", "a7e6c", "56f62",
    "02c30", "455f1", "14625", "a5c61",
    "88952", "fbfc9", "2ccef", "bfcec",
    "ef6c5", "626fd", "9ce0e", "10904",
    "6b636", "220f0", "977b2", "7bf67",
    "5b159", "61001", "0e0f4", "8aa10",
    "b60d9", "53c43", "165a3", "49ae8",
    "01673", "0eafb", "53611", "9f4a9",
    "96375", "504f8", "4f035", "e151a",
    "944b7", "aef99", "0f6cd", "478d5",
    "9b923", "d7919", "98f7b", "293a5",
    "679d6", "4b84b", "08e34", "7148c",
    "c3c38", "3432f", "7b2a4", "b3a04",
    "83f2d", "49916", "52c33", "540c1",
    "28904", "cfdbb", "a1b85", "dba8b",
    "452f4", "a828c", "50819", "24ea0",
    "49cd5", "9e51f", "420db", "5d505",
    "ed6c9", "3cdd0", "674e4", "f08ab",
    "4069f", "a74f0", "03888", "68757",
    "f25bb", "a94df", "28e0a", "94d18",
    "82aec", "dc918", "36949", "77ff3",
    "ecd7f", "a29d8", "edfda", "b004c",
    "c3c19", "685f9", "ec0ca", "602cb",
    "7e875", "a82d5", "76a40", "fe9be",
    "76e43", "ddbfc", "fb917", "da972",
    "1ca7f", "b9490", "043c7", "729ae",
    "4603c", "286aa", "6a5cf", "bc030",
    "b7491", "a523e", "a34cd", "27ac2",
    "ce473", "9802b", "89810", "d5bd0",
    "9b33f", "4d752", "81063", "ff16b",
    "5207f", "0275d", "0e338", "99372",
    "2d518", "05123", "24ea2", "a511f",
    "d3e00", "d72ac", "b684c", "436da",
    "0ed70", "e7aca", "d3f09", "d7628",
    "c1713", "aea61", "d7945", "2a191",
    "0ac54", "aa544", "731be", "cb72c",
    "33923", "0946f", "41742", "31360",
    "a910b", "4f679", "5eef4", "2d2b5",
    "3c7b2", "989ba", "a0b14", "73e74",
    "738ea", "c7837", "b300a", "7783c",
    "691b9", "60ee2", "702a4", "5c376",
    "f5546", "c0301", "f8c9d", "4a4eb",
    "f0e47", "5591a", "2b367", "60bdb",
    "24a2b", "55881", "3d72e", "42963",
    "3769c", "c847a", "b83ed", "b309a",
    "20b7d", "cd6ae", "bfe97", "1ebce",
    "13c7f", "c41a0", "ddd18", "57903",
    "a1c0b", "f6f71", "b2611", "fa0d6",
    "4d081", "18826", "5ee4e", "9cb71",
    "806f7", "fcfa7", "96e07", "fe628",
    "3ff40", "7109f", "6c300", "f00b0",
    "464d5", "99c5e", "1d1e6", "d8886",
    "df83c", "89378", "be7ce", "1a75c",
    "ad57a", "eb1f6", "1ab6f", "39277",
    "652e1", "11d9e", "c0da7", "eb8b9",
    "22ba7", "b30dd", "36f1b", "b8f3e",
    "392ee", "e2c6e", "78a77", "8c569",
    "b2d2b", "c7b0b", "488e6", "a4d79",
    "bfde8", "eb430", "201b7", "a91a9",
    "866b3", "3a2b0", "16b5a", "def06",
    "33d11", "2bff2", "478f4", "6e7fd",
    "f0635", "49180", "36869", "338f3",
    "6c233", "8246a", "ea36c", "31727",
    "a1bfe", "ffc41", "bc15a", "f8469",
    "b9c5e", "0796e", "ec07f", "5490c",
    "11577", "2ed46", "79f17", "906f3",
    "463e9", "4a6c9", "5aa29", "06fc1",
    "41e25", "b4529", "77aff", "59046",
    "73d05", "3d08c", "55409", "96930",
    "926d1", "7bdaf", "48c40", "0232d",
    "6b9dd", "0626d", "c78c3", "9e03e",
    "f0a54", "287c8", "09500", "30238",
    "851bc", "4a302", "aa486", "60e15",
    "0dbcb", "178c7", "99f5c", "6e54b",
    "5bb34", "5f86e", "99f99", "2e848",
    "b4893", "ba4a6", "ee66b", "3ae56",
    "2b3e6", "23fe9", "c5860", "0eacc",
    "116bb", "475ba", "261bd", "2196f",
    "6f3cc", "6aefe", "a1632", "73eda",
    "b99c8", "a196f", "82d83", "887e1",
    "65c72", "c776a", "e3fca", "be936",
    "4ec5d", "9d9e0", "764dd", "9db96",
    "a5a6a", "8b3c5", "9b67e", "044c9",
    "40b34", "50edf", "b6d4c", "d759c",
    "82e32", "1fa4b", "22d62", "0f044",
    "6c727", "9d04a", "a4473", "79614",
    "b67b4", "b4304", "570e5", "3b67b",
    "ad011", "2b559", "6d7b9", "b916a",
    "01200", "f67cc", "37104", "b2b13",
    "20e9f", "41f07", "32614", "5bc08",
    "f8821", "4daa3", "a41c6", "89b2a",
    "0ac1e", "9698e", "12f35", "b03e7",
    "c9226", "544af", "f8185", "9e696",
    "66ce8", "29b0e", "464f4", "915fe",
    "74037", "48043", "6835d", "95703",
    "c72bd", "3d08f", "738fc", "c903b",
    "f2013", "886c6", "7ee2a", "a3c43",
    "d3d6d", "3ef99", "a72c3", "c5b3d",
    "dfc32", "2c0be", "57a2b", "ef3e3",
    "c3f13", "71e74", "f5dbe", "5c507",
    "5ef28", "f0704", "4ea96", "0ce26",
    "878a8", "22b66", "6ab45", "47a02",
    "5219e", "87233", "e52e9", "a0da5",
    "e752f", "150cf", "de8df", "25fea",
    "ab4e3", "fd3d9", "c2c1f", "49385",
    "4eed6", "b2583", "de9ad", "a0d86",
    "187d4", "fbdd2", "73fa5", "5b418",
    "070f3", "5ff69", "ce5f5", "55a2c",
    "ff771", "b360c", "c108d", "a5b40",
    "9adf8", "3b51e", "38e27", "f6b9d",
    "48d30", "c2674", "21dfa", "64a43",
    "e0587", "bf860", "0245e", "97549",
    "ffaa2", "c3655", "8db19", "ed5a1",
    "9e20e", "6860d", "8b759", "bdab7",
    "f9ec8", "f96d0", "7e808", "b5e42",
    "229d9", "9d920", "150f7", "5b994",
    "8eb2e", "45613", "93dd9", "d06ce",
    "ccaa1", "6a471", "aec6f", "4e961",
    "a22c0", "8912f", "ca5d8", "bc375",
    "c0f60", "00970", "91068", "c0fa5",
    "c9ccc", "67253", "d181a", "ad0b0",
    "f8a50", "18235", "3630e", "7546c",
    "1adea", "7a27a", "088c5", "ad61e",
    "bc76b", "a2134", "6f71f", "25382",
    "f163b", "04962", "b22e1", "2dc66",
    "fab57", "190e0", "446ba", "8d1c1",
    "4b9ba", "cfbc4", "6787c", "728a6",
    "c89fd", "2532c", "aa93c", "19d68",
    "a9cf1", "80890", "25ac0", "76240",
    "15a84", "ecd1e", "2ab4a", "1eac5",
    "efa26", "2c416", "e6ab4", "c6b8a",
    "0068b", "3c217", "c2c91", "39a57",
    "b0ef3", "37975", "41175", "dd30d",
    "f0fa0", "500e9", "f7e1e", "70f7d",
    "f3868", "cb20c", "96734", "8b83f",
    "27692", "592c8", "fd915", "9f87f",
    "58b89", "acd43", "c8b89", "c8172",
    "ec532", "9e2b6", "5f334", "c6828",
    "1869a", "40a6f", "fad74", "d473b",
    "020c3", "1e50a", "60b14", "6de9b",
    "56d23", "08dc0", "22341", "caa43",
    "3eaa0", "c68b0", "2b459", "5df1c",
    "2ff60", "e1c4e", "59cf2", "6b80f",
    "8fc23", "3bdef", "c6423", "49246",
    "031a1", "752ea", "6599e", "44639",
    "d7af8", "a44ee", "b37e3", "d2219",
    "066de", "5e2e5", "80789", "d88f9",
    "55dc7", "35901", "99092", "b3dab",
    "ac4f3", "faed6", "95ec6", "43ea9",
    "ebea4", "9ee1b", "97e76", "df2d4",
    "f042d", "54788", "16592", "e4cee",
    "4af6b", "e4c80", "2e0be", "eb0c4",
    "6124f", "2e095", "c0dcd", "5a480",
    "e9af6", "95f77", "7803a", "eff36",
    "eb3d9", "45b86", "36ef4", "63e95",
    "39e7f", "bfd5e", "7ed9c", "1d636",
    "079fc", "9de93", "bbb05", "7dc2b",
    "03780", "b29d8", "e8c9c", "f7c7a",
    "85654", "05c88", "d1711", "d6b4f",
    "ab0a8", "347bc", "a885c", "f0663",
    "5c154", "35a77", "bd342", "45be6",
    "96206", "cb0e9", "12f54", "4ef80",
    "a8e77", "37431", "6926d", "9b1ac",
    "a613b", "f2912", "5982a", "dcb52",
    "c2beb", "d04f2", "fab3a", "53f4e",
    "adcc2", "8786c", "9b152", "df21a",
    "46ed6", "81d73", "b0148", "97718",
    "f6334", "d5a47", "2c5fe", "52e73",
    "17ba2", "1351e", "dce6f", "0d198",
    "b3e6b", "589c9", "60276", "ed672",
    "ea117", "9bdfd", "e8c47", "438ab",
    "9acb8", "e2432", "61675", "bdff5",
    "a2d80", "4d318", "502d0", "f2a12",
    "4a1d0", "500a7", "33a3b", "84547",
    "aa615", "f9da6", "df344", "0b9d9",
    "db0c2", "86477", "39042", "1c681",
}
```

<a name="SOURCE_L2_SIZE"></a>

```go
var SOURCE_L2_SIZE = map[string]int{
    "TRB":  512,
    "DDF":  1024,
    "BRE":  64,
    "CLG":  64,
    "OTW":  64,
    "EDM":  64,
    "CAR":  256,
    "USER": 512,
}
```

<a name="Config"></a>
## func [Config](<https://github.com/real-rm/goconfig/blob/main/config.go#L66>)

```go
func Config(path string) interface{}
```

Config retrieves configuration values using dot notation

<a name="ConfigBool"></a>
## func [ConfigBool](<https://github.com/real-rm/goconfig/blob/main/config.go#L156>)

```go
func ConfigBool(path string) (bool, error)
```



<a name="ConfigFloat"></a>
## func [ConfigFloat](<https://github.com/real-rm/goconfig/blob/main/config.go#L138>)

```go
func ConfigFloat(path string) (float64, error)
```



<a name="ConfigInt"></a>
## func [ConfigInt](<https://github.com/real-rm/goconfig/blob/main/config.go#L120>)

```go
func ConfigInt(path string) (int, error)
```



<a name="ConfigString"></a>
## func [ConfigString](<https://github.com/real-rm/goconfig/blob/main/config.go#L107>)

```go
func ConfigString(path string) (string, error)
```



<a name="LoadConfig"></a>
## func [LoadConfig](<https://github.com/real-rm/goconfig/blob/main/config.go#L34>)

```go
func LoadConfig() error
```

LoadConfig initializes the configuration from either command line flag or environment variable

<a name="ResetConfig"></a>
## func [ResetConfig](<https://github.com/real-rm/goconfig/blob/main/config.go#L27>)

```go
func ResetConfig()
```

ResetConfig resets the configuration state

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
