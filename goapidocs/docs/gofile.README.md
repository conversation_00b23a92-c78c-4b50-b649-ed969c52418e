# gofile

A Go language file handling package that provides a simple and efficient way to download, save, and convert images.

## Features

- Simple and intuitive API for image file operations
- Automatic retry mechanism for downloads
- Support for JPEG and WebP image formats
- Multiple directory saving support
- Built-in logging and error handling
- Configurable retry attempts
- Concurrent download support

## Installation

```bash
go get github.com/real-rm/gofile
```

## Configuration

The package uses a configuration system that supports TOML format. You need to configure your logging settings in your config file:

```toml
[golog]
dir = "/path/to/logs"
level = "debug"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"
```

## Usage

### Initialization

```go
import (
    "github.com/real-rm/gofile"
    "github.com/real-rm/golog"
    "github.com/real-rm/goconfig"
)

func main() {
    // Load configuration
    if err := goconfig.LoadConfig(); err != nil {
        log.Fatal(err)
    }
    
    // Initialize logger
    if err := golog.InitLog(); err != nil {
        log.Fatal(err)
    }
}
```

### Basic Operations

#### Download and Save Image

```go
// Download and save as JPEG
filePath, err := gofile.DownloadAndSaveImage(
    "https://example.com/image.jpg",
    "/path/to/save",
    "myimage",
    false, // false for JPEG, true for WebP
)
```

#### Download with Retry

```go
// Download with custom retry attempts
resp, err := gofile.DownloadWithRetry("https://example.com/image.jpg", 5)
if err != nil {
    log.Printf("Failed to download: %v", err)
    return
}
defer resp.Body.Close()
```

#### Save to Multiple Directories

```go
// Save the same image to multiple directories
paths, err := gofile.DownloadAndSaveImageInDirs(
    "https://example.com/image.jpg",
    []string{"/path1", "/path2"},
    "myimage",
    false,
)
```

## API Reference

### Main Functions

- `DownloadAndSaveImage(url string, savePath string, fileName string, compressWebP bool, maxRetries ...int) (string, error)`
- `DownloadAndSaveImageInDirs(url string, savePaths []string, fileName string, compressWebP bool, maxRetries ...int) (map[string]string, error)`
- `DownloadWithRetry(url string, maxRetries ...int) (*http.Response, error)`

### Helper Functions

- `SaveAsJPEG(img image.Image, savePath string, fileName string) (string, error)`
- `SaveAsWebP(img image.Image, savePath string, fileName string) (string, error)`

## Error Handling

All operations return errors that should be checked:

```go
filePath, err := gofile.DownloadAndSaveImage(url, savePath, fileName, false)
if err != nil {
    log.Printf("Error downloading and saving image: %v", err)
    return
}
```

## Retry Mechanism

The package includes a built-in retry mechanism for downloads:
- Default retry attempts: 3
- Exponential backoff delay: 1s, 2s, 3s
- Configurable max retries

## Dependencies

- github.com/HugoSmits86/nativewebp
- github.com/real-rm/goconfig
- github.com/real-rm/golog

## Example

```go
package main

import (
    "github.com/real-rm/gofile"
    "github.com/real-rm/golog"
    "github.com/real-rm/goconfig"
    "log"
)

func main() {
    // Initialize configuration and logging
    if err := goconfig.LoadConfig(); err != nil {
        log.Fatal(err)
    }
    if err := golog.InitLog(); err != nil {
        log.Fatal(err)
    }

    // Download and save an image
    filePath, err := gofile.DownloadAndSaveImage(
        "https://example.com/image.jpg",
        "./images",
        "example",
        false,
    )
    if err != nil {
        log.Printf("Failed to download and save image: %v", err)
        return
    }
    log.Printf("Image saved to: %s", filePath)
}
```
