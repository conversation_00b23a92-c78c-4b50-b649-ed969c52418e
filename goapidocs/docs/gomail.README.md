# go-mail

A flexible and feature-rich email service package for Go applications.

## Features

- Multiple email engine support:
  - Gmail SMTP
  - Amazon SES
  - Sendmail
  - RM Mail
  - Mock Mail (for testing)
- HTML and plain text email support
- Email validation
- Parameter replacement in email templates
- HTML to text conversion
- Email logging and tracking
- Priority email support
- Custom headers support
- Reply-to address support

## Installation

```bash
go mod init github.com/real-rm/gomail
go get github.com/real-rm/golog
```

## Quick Start

```go
package main

import (
    "github.com/real-rm/gomail"
)

func main() {
    // Get mailer instance
    mailer, err := gomail.GetSendMailObj()
    if err != nil {
        panic(err)
    }

    // Create email message
    msg := &gomail.EmailMessage{
        From:    "<EMAIL>",
        To:      []string{"<EMAIL>"},
        Subject: "Test Email",
        Text:    "This is a test email",
        HTML:    "<p>This is a test email</p>",
    }

    // Send email using Gmail engine
    err = mailer.SendMail("gmail", msg)
    if err != nil {
        panic(err)
    }
}
```

## Configuration

The package supports multiple email engines that can be configured through your application's configuration:

- Gmail SMTP
- Amazon SES
- Sendmail
- RM Mail
- Mock Mail (for testing)

Each engine can be configured with its own settings like SMTP credentials, rate limits, and default sender addresses.

## API Documentation

For detailed API documentation, please refer to the [api.md](api.md) file.

## Dependencies

- github.com/real-rm/golog - For logging
- github.com/real-rm/goconfig - For configuration management
- github.com/jaytaylor/html2text - For HTML to text conversion
- go.mongodb.org/mongo-driver - For email logging (optional)

## License

This project is licensed under the MIT License - see the LICENSE file for details. 
