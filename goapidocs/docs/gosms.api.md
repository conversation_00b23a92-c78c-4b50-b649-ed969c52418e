<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gosms

```go
import "github.com/real-rm/gosms"
```

## Index

- [type SMSSender](<#SMSSender>)
  - [func NewSMSSender\(s Sender\) \(\*SMSSender, error\)](<#NewSMSSender>)
  - [func \(s \*SMSSender\) Send\(opt SendOption\) error](<#SMSSender.Send>)
- [type SendOption](<#SendOption>)
- [type Sender](<#Sender>)
  - [func NewTwilioEngine\(accountSID, authToken string\) \(Sender, error\)](<#NewTwilioEngine>)
- [type TwilioClient](<#TwilioClient>)
- [type TwilioEngine](<#TwilioEngine>)
  - [func \(t \*TwilioEngine\) Send\(to string, message string, from string\) error](<#TwilioEngine.Send>)


<a name="SMSSender"></a>
## type [SMSSender](<https://github.com/real-rm/gosms/blob/main/gosms.go#L23-L25>)

SMSSender is the main struct that handles SMS sending

```go
type SMSSender struct {
    // contains filtered or unexported fields
}
```

<a name="NewSMSSender"></a>
### func [NewSMSSender](<https://github.com/real-rm/gosms/blob/main/gosms.go#L28>)

```go
func NewSMSSender(s Sender) (*SMSSender, error)
```

NewSMSSender creates a new SMSSender instance with the given sender

<a name="SMSSender.Send"></a>
### func \(\*SMSSender\) [Send](<https://github.com/real-rm/gosms/blob/main/gosms.go#L38>)

```go
func (s *SMSSender) Send(opt SendOption) error
```

Send sends an SMS message using the configured engine

<a name="SendOption"></a>
## type [SendOption](<https://github.com/real-rm/gosms/blob/main/gosms.go#L11-L15>)

SendOption contains all options for sending an SMS

```go
type SendOption struct {
    To      string // Recipient phone number
    From    string // Sender phone number
    Message string // Message content
}
```

<a name="Sender"></a>
## type [Sender](<https://github.com/real-rm/gosms/blob/main/gosms.go#L18-L20>)

Sender is the interface for sending SMS messages

```go
type Sender interface {
    Send(to string, message string, from string) error
}
```

<a name="NewTwilioEngine"></a>
### func [NewTwilioEngine](<https://github.com/real-rm/gosms/blob/main/twilio.go#L32>)

```go
func NewTwilioEngine(accountSID, authToken string) (Sender, error)
```

NewTwilioEngine creates a new Twilio engine with the given credentials

<a name="TwilioClient"></a>
## type [TwilioClient](<https://github.com/real-rm/gosms/blob/main/twilio.go#L13-L15>)

TwilioClient is the interface for Twilio client operations

```go
type TwilioClient interface {
    CreateMessage(params *v2010.CreateMessageParams) (*v2010.ApiV2010Message, error)
}
```

<a name="TwilioEngine"></a>
## type [TwilioEngine](<https://github.com/real-rm/gosms/blob/main/twilio.go#L27-L29>)

TwilioEngine implements the Sender interface using Twilio

```go
type TwilioEngine struct {
    // contains filtered or unexported fields
}
```

<a name="TwilioEngine.Send"></a>
### func \(\*TwilioEngine\) [Send](<https://github.com/real-rm/gosms/blob/main/twilio.go#L48>)

```go
func (t *TwilioEngine) Send(to string, message string, from string) error
```

Send implements the Sender interface

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
