<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gotel

```go
import "github.com/real-rm/gotel"
```

## Index

- [type CallParams](<#CallParams>)
- [type Caller](<#Caller>)
- [type Dialer](<#Dialer>)
  - [func NewDialer\(c Caller\) \(\*Dialer, error\)](<#NewDialer>)
  - [func \(d \*Dialer\) Call\(ctx context.Context, params CallParams\) error](<#Dialer.Call>)
- [type TwilioCaller](<#TwilioCaller>)
  - [func NewTwilioCaller\(accountSID, authToken string\) \(\*TwilioCaller, error\)](<#NewTwilioCaller>)
  - [func \(t \*TwilioCaller\) Call\(ctx context.Context, params CallParams\) error](<#TwilioCaller.Call>)
  - [func \(t \*TwilioCaller\) Name\(\) string](<#TwilioCaller.Name>)


<a name="CallParams"></a>
## type [CallParams](<https://github.com/real-rm/gotel/blob/main/gotel.go#L13-L18>)

CallParams contains all parameters for making a phone call

```go
type CallParams struct {
    To    string // Target phone number (required)
    From  string // Source phone number (required)
    URL   string // Voice content URL (optional, mutually exclusive with Twiml)
    Twiml string // Direct Twiml XML (optional, takes precedence over URL)
}
```

<a name="Caller"></a>
## type [Caller](<https://github.com/real-rm/gotel/blob/main/gotel.go#L21-L26>)

Caller is the interface that all call providers must implement

```go
type Caller interface {
    // Call makes a phone call with the given parameters
    Call(ctx context.Context, params CallParams) error
    // Name returns the name of the call provider
    Name() string
}
```

<a name="Dialer"></a>
## type [Dialer](<https://github.com/real-rm/gotel/blob/main/gotel.go#L29-L31>)

Dialer is the main struct that handles phone calls

```go
type Dialer struct {
    // contains filtered or unexported fields
}
```

<a name="NewDialer"></a>
### func [NewDialer](<https://github.com/real-rm/gotel/blob/main/gotel.go#L34>)

```go
func NewDialer(c Caller) (*Dialer, error)
```

NewDialer creates a new Dialer instance with the given caller

<a name="Dialer.Call"></a>
### func \(\*Dialer\) [Call](<https://github.com/real-rm/gotel/blob/main/gotel.go#L44>)

```go
func (d *Dialer) Call(ctx context.Context, params CallParams) error
```

Call makes a phone call using the configured engine

<a name="TwilioCaller"></a>
## type [TwilioCaller](<https://github.com/real-rm/gotel/blob/main/twilio.go#L13-L15>)

TwilioCaller implements the Caller interface for Twilio

```go
type TwilioCaller struct {
    // contains filtered or unexported fields
}
```

<a name="NewTwilioCaller"></a>
### func [NewTwilioCaller](<https://github.com/real-rm/gotel/blob/main/twilio.go#L18>)

```go
func NewTwilioCaller(accountSID, authToken string) (*TwilioCaller, error)
```

NewTwilioCaller creates a new TwilioCaller instance

<a name="TwilioCaller.Call"></a>
### func \(\*TwilioCaller\) [Call](<https://github.com/real-rm/gotel/blob/main/twilio.go#L34>)

```go
func (t *TwilioCaller) Call(ctx context.Context, params CallParams) error
```

Call implements the Caller interface for TwilioCaller

<a name="TwilioCaller.Name"></a>
### func \(\*TwilioCaller\) [Name](<https://github.com/real-rm/gotel/blob/main/twilio.go#L73>)

```go
func (t *TwilioCaller) Name() string
```

Name implements the Caller interface for TwilioCaller

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
