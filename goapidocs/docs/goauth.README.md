# goauth
authentication service for go, including login/regiser/jwt_util etc...

backend uses go gin as http server.

to dun the server:
```bash
go mod tidy
go run main.go
```

TODO:
- [ ] +goconfig,golog,gomongo 的使用，区分src/log/doc/configs folder structures
- [ ] make this package and handle jwt utils
- [ ] 想办法部署，需要测试不同部署方法
- [ ] move rent_report login/register/auth to this package
- [ ] 增加测试
- [ ] 增加github actions