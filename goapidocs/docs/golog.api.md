<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# golog

```go
import "github.com/real-rm/golog"
```

## Index

- [Variables](<#variables>)
- [func Debug\(msg interface\{\}, args ...interface\{\}\)](<#Debug>)
- [func Debugf\(format string, args ...interface\{\}\)](<#Debugf>)
- [func Error\(msg interface\{\}, args ...interface\{\}\)](<#Error>)
- [func Errorf\(format string, args ...interface\{\}\)](<#Errorf>)
- [func Fatal\(msg string, args ...interface\{\}\)](<#Fatal>)
- [func Fatalf\(format string, args ...interface\{\}\)](<#Fatalf>)
- [func Info\(msg interface\{\}, args ...interface\{\}\)](<#Info>)
- [func Infof\(format string, args ...interface\{\}\)](<#Infof>)
- [func Init\(logFile string\) error](<#Init>)
- [func InitLog\(\) error](<#InitLog>)
- [func SetStdoutWriter\(w io.Writer\)](<#SetStdoutWriter>)
- [func Verbose\(msg interface\{\}, args ...interface\{\}\)](<#Verbose>)
- [func Verbosef\(format string, args ...interface\{\}\)](<#Verbosef>)
- [func Warn\(msg interface\{\}, args ...interface\{\}\)](<#Warn>)
- [func Warnf\(format string, args ...interface\{\}\)](<#Warnf>)


## Variables

<a name="Logger"></a>Logger is the global logger instance

```go
var (
    Logger *slog.Logger
)
```

<a name="Debug"></a>
## func [Debug](<https://github.com/real-rm/golog/blob/main/log.go#L284>)

```go
func Debug(msg interface{}, args ...interface{})
```

Debug logs a message at debug level

<a name="Debugf"></a>
## func [Debugf](<https://github.com/real-rm/golog/blob/main/log.go#L300>)

```go
func Debugf(format string, args ...interface{})
```

Debugf logs a formatted message at debug level

<a name="Error"></a>
## func [Error](<https://github.com/real-rm/golog/blob/main/log.go#L371>)

```go
func Error(msg interface{}, args ...interface{})
```

Error logs a message at error level

<a name="Errorf"></a>
## func [Errorf](<https://github.com/real-rm/golog/blob/main/log.go#L389>)

```go
func Errorf(format string, args ...interface{})
```

Errorf logs a formatted message at error level

<a name="Fatal"></a>
## func [Fatal](<https://github.com/real-rm/golog/blob/main/log.go#L395>)

```go
func Fatal(msg string, args ...interface{})
```

Fatal logs a message at error level

<a name="Fatalf"></a>
## func [Fatalf](<https://github.com/real-rm/golog/blob/main/log.go#L402>)

```go
func Fatalf(format string, args ...interface{})
```

Fatalf logs a formatted message at error level

<a name="Info"></a>
## func [Info](<https://github.com/real-rm/golog/blob/main/log.go#L326>)

```go
func Info(msg interface{}, args ...interface{})
```

Info logs a message at info level

<a name="Infof"></a>
## func [Infof](<https://github.com/real-rm/golog/blob/main/log.go#L342>)

```go
func Infof(format string, args ...interface{})
```

Infof logs a formatted message at info level

<a name="Init"></a>
## func [Init](<https://github.com/real-rm/golog/blob/main/log.go#L164>)

```go
func Init(logFile string) error
```

Init initializes the logger with the specified log file

<a name="InitLog"></a>
## func [InitLog](<https://github.com/real-rm/golog/blob/main/log.go#L174>)

```go
func InitLog() error
```

InitLog initializes the logger with default log file path

<a name="SetStdoutWriter"></a>
## func [SetStdoutWriter](<https://github.com/real-rm/golog/blob/main/log.go#L410>)

```go
func SetStdoutWriter(w io.Writer)
```

SetStdoutWriter sets the stdout writer for the logger

<a name="Verbose"></a>
## func [Verbose](<https://github.com/real-rm/golog/blob/main/log.go#L305>)

```go
func Verbose(msg interface{}, args ...interface{})
```

Verbose logs a message at info level \(alias for Info\)

<a name="Verbosef"></a>
## func [Verbosef](<https://github.com/real-rm/golog/blob/main/log.go#L321>)

```go
func Verbosef(format string, args ...interface{})
```

Verbosef logs a formatted message at info level \(alias for Infof\)

<a name="Warn"></a>
## func [Warn](<https://github.com/real-rm/golog/blob/main/log.go#L347>)

```go
func Warn(msg interface{}, args ...interface{})
```

Warn logs a message at warn level

<a name="Warnf"></a>
## func [Warnf](<https://github.com/real-rm/golog/blob/main/log.go#L365>)

```go
func Warnf(format string, args ...interface{})
```

Warnf logs a formatted message at warn level

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
