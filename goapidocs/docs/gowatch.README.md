# GoWatch

GoWatch is a Go package that provides a robust and efficient way to watch and process MongoDB change streams. It's designed to handle real-time data synchronization with built-in error recovery, token management, and performance monitoring.

## Features

- Real-time MongoDB change stream monitoring
- Automatic token management and recovery
- Configurable high water mark for flow control
- Built-in error handling and recovery mechanisms
- Support for various operation types (insert, update, delete, replace)
- Detailed logging with configurable verbosity
- Thread-safe operations
- Performance monitoring with SpeedMeter integration

## Installation

```bash
go get github.com/real-rm/gowatch
```

## Usage

### Basic Usage

```go
package main

import (
    "context"
    "log"

    "github.com/real-rm/gowatch"
    "github.com/real-rm/gomongo"
)

func main() {
    // Create context with cancellation
    ctx, cancel := context.WithCancel(context.Background())
    defer cancel()

    // Create watch options
    opts := gowatch.WatchOptions{
        WatchedColl: yourMongoCollection,
        OnChange: func(changedObj bson.M, coll *gomongo.MongoCollection) error {
            // Process the changed document
            log.Printf("Processing change: %v", changedObj)
            return nil
        },
        OnError: func(err error) {
            log.Printf("Watch error: %v", err)
        },
        Context: ctx,
        Cancel:  cancel,
    }

    // Start watching
    watchObj, err := gowatch.WatchTarget(opts)
    if err != nil {
        log.Fatalf("Failed to start watch: %v", err)
    }

    // Watch will continue until context is cancelled
    select {
    case <-ctx.Done():
        watchObj.End(nil)
    }
}
```

### Advanced Usage with Custom Options

```go
opts := gowatch.WatchOptions{
    WatchedColl: yourMongoCollection,
    ImportWatchedRecordsCol: importCollection, // Optional collection to store change records
    OnChange: func(changedObj bson.M, coll *gomongo.MongoCollection) error {
        return processChange(changedObj)
    },
    OnError: func(err error) {
        handleError(err)
    },
    OnTokenUpdate: func(tokenOpts gowatch.TokenUpdateOptions) error {
        return updateToken(tokenOpts)
    },
    QueryWhenInvalidToken: bson.M{"_mt": bson.M{"$gte": lastValidTimestamp}},
    WatchPipeline: []bson.M{
        {"$match": bson.M{"operationType": "update"}},
    },
    HighWaterMark: 20,           // Maximum concurrent processing
    WaterMarkerTimerS: 300,      // Water mark check interval (5 minutes)
    UpdateTokenTimerS: 60,       // Token update interval (1 minute)
    Context: ctx,
    Cancel: cancel,
}
```

### Processing Changed Objects

```go
processOpts := gowatch.ProcessChangedObjectOptions{
    ChangedObj: changedObj,
    WatchedColl: watchedCollection,
    DeleteOneFn: func(id interface{}) error {
        return targetCollection.DeleteOne(context.Background(), bson.M{"_id": id})
    },
    UpdateOneFn: func(doc bson.M) error {
        return targetCollection.UpdateOne(context.Background(), 
            bson.M{"_id": doc["_id"]}, 
            bson.M{"$set": doc})
    },
    ReplaceOneFn: func(doc bson.M) error {
        return targetCollection.ReplaceOne(context.Background(), 
            bson.M{"_id": doc["_id"]}, 
            doc)
    },
    InsertOneFn: func(doc bson.M) error {
        return targetCollection.InsertOne(context.Background(), doc)
    },
}

err := gowatch.ProcessChangedObject(processOpts)
```

## Configuration Options

### WatchOptions

```go
type WatchOptions struct {
    ImportWatchedRecordsCol *gomongo.MongoCollection
    WatchedColl            *gomongo.MongoCollection
    OnChange               func(bson.M, *gomongo.MongoCollection) error
    OnError                func(error)
    OnTokenUpdate          func(TokenUpdateOptions) error
    QueryWhenInvalidToken  bson.M
    WatchPipeline          []bson.M
    HighWaterMark          int
    WaterMarkerTimerS      float64
    UpdateTokenTimerS      float64
    SavedToken             *bson.M
    UpdateProcessStatusFn  func() error
    OnClose               func()
    Context               context.Context
    Cancel                context.CancelFunc
}
```

### Default Values

- `HighWaterMark`: 10 (maximum concurrent processing)
- `WaterMarkerTimerS`: 600 (10 minutes)
- `UpdateTokenTimerS`: 60 (1 minute)
- `ChangeChanSize`: 100
- `ErrorChanSize`: 10

## Error Handling

The package provides comprehensive error handling for various scenarios:
- Invalid tokens
- Change stream history loss
- Capped position loss
- Network errors
- Processing errors

## Token Management

- Automatic token updates
- Token recovery on errors
- Support for resuming from saved tokens
- Cluster time tracking

## Operation Types

Supported MongoDB change stream operations:
- Insert
- Update
- Delete
- Replace
- Invalidate (handled as error)

## Best Practices

1. Always provide a context for cancellation support
2. Implement proper error handling in callbacks
3. Use appropriate HighWaterMark value based on system resources
4. Monitor memory usage with large concurrent processing
5. Implement proper cleanup in OnClose callback
6. Use ImportWatchedRecordsCol for debugging and recovery
7. Configure appropriate timer intervals for your use case

## Thread Safety

- All operations are thread-safe
- Uses mutex locks for shared resources
- Atomic operations for counters
- Safe logging with buffered channels

## License

MIT License