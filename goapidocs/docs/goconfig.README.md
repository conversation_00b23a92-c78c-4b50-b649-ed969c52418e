# goconfig

A simple and flexible configuration management package for Go applications that provides easy access to configuration values from TOML files and environment variables.

## Features

- TOML configuration file support
- Environment variable support for config file path
- Command-line flag support for config file path
- Dot notation for accessing nested configuration values
- Simple and intuitive API

## Installation

```bash
go get github.com/real-rm/goconfig
```

## Configuration

The package supports configuration through:
1. TOML configuration files
2. Environment variable `RMBASE_FILE_CFG` for config file path
3. Command-line flag `--config` for config file path

### TOML Configuration File Example

```toml
[database]
host = "localhost"
port = 5432
name = "mydb"

[redis]
host = "localhost"
port = 6379
password = "secret"

[api]
port = 8080
timeout = 30
```

### Environment Variable

The package uses a single environment variable to specify the configuration file path:
```bash
export RMBASE_FILE_CFG=/path/to/config.toml
```

## Usage

### Initialization

```go
import "github.com/real-rm/goconfig"

func main() {
    err := goconfig.LoadConfig()
    if err != nil {
        log.Fatal(err)
    }
}
```

### Accessing Configuration Values

```go
// Get a specific configuration value
dbHost := goconfig.Config("database.host")
dbPort := goconfig.Config("database.port")

// Get all settings
allConfig := goconfig.Config("")

// Type assertions for specific values
if host, ok := goconfig.Config("database.host").(string); ok {
    fmt.Printf("Database host: %s\n", host)
}

if port, ok := goconfig.Config("database.port").(int64); ok {
    fmt.Printf("Database port: %d\n", port)
}
```

### Command Line Usage

```bash
# Specify config file using command line flag
./myapp --config /path/to/config.toml

# Or using environment variable
export RMBASE_FILE_CFG=/path/to/config.toml
./myapp
```

## Configuration Priority

The package follows this priority order for configuration file path:
1. Command-line flag `--config`
2. Environment variable `RMBASE_FILE_CFG`

## Example Usage

```go
package main

import (
    "fmt"
    "log"
    "github.com/real-rm/goconfig"
)

type Config struct {
    Database struct {
        Host string
        Port int
        Name string
    }
    Redis struct {
        Host     string
        Port     int
        Password string
    }
    API struct {
        Port    int
        Timeout int
    }
}

func main() {
    // Load configuration
    if err := goconfig.LoadConfig(); err != nil {
        log.Fatal(err)
    }

    // Access configuration values
    dbHost := goconfig.Config("database.host")
    dbPort := goconfig.Config("database.port")
    dbName := goconfig.Config("database.name")

    fmt.Printf("Database connection: %s:%d/%s\n", dbHost, dbPort, dbName)

    // Access nested configuration
    apiPort := goconfig.Config("api.port")
    apiTimeout := goconfig.Config("api.timeout")

    fmt.Printf("API server: port=%d, timeout=%d\n", apiPort, apiTimeout)
}
```

## Dependencies

- github.com/spf13/viper

## Best Practices

1. Always initialize the configuration at the start of your application
2. Use meaningful section names in your TOML configuration
3. Document all configuration options
4. Use type assertions when accessing configuration values
5. Handle configuration errors gracefully

## Error Handling

The package provides clear error messages for common configuration issues:
- Missing configuration file
- Invalid file format
- File read errors

Always check the error returned by `LoadConfig()`:
```go
if err := goconfig.LoadConfig(); err != nil {
    log.Fatalf("Failed to load configuration: %v", err)
}
```

## Testing

Run tests using:
```bash
go test
go test -v  # for verbose output
```

## Linting

Using golangci-lint for code linting. The project includes a `.golangci.yml` configuration file with predefined rules.

### Installation

```bash
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
```

### Usage

Run linter using make command:
```bash
make lint
```

Or run directly with our configuration:
```bash
golangci-lint run
```

Our `.golangci.yml` includes:
- 12 enabled linters (govet, errcheck, staticcheck, etc.)
- Skip rules for test files and vendor directories
- Custom linter settings for each enabled linter

For detailed configuration, check `.golangci.yml` in the project root.
