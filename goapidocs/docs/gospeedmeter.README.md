# GoSpeedMeter

GoSpeedMeter is a Go package that provides a simple and efficient way to monitor and measure performance metrics in your applications.

## Features

- Track multiple metrics simultaneously
- Configurable measurement intervals
- Customizable callback functions
- Support for various time units (ms, s, m, h, d)
- Thread-safe operations
- Automatic number formatting with K/M/B/T units
- Time estimation for target values

## Installation

```bash
go get github.com/real-rm/gospeedmeter
```

## Usage

### Basic Usage

```go
package main

import (
    "log"
    "time"

    "github.com/real-rm/gospeedmeter"
)

func main() {
    // Create a new speed meter with default options
    sm := gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})

    // Track a metric
    sm.Check("requests", 1)

    // Get current speed
    speed := sm.GetSpeed(gospeedmeter.UnitS)
    log.Printf("Current speed: %v requests/second", speed["requests"])

    // Get formatted string representation
    log.Printf("Metrics: %s", sm.ToString(gospeedmeter.UnitM, nil))
}
```

### Advanced Usage

```go
// Create a speed meter with custom options
sm := gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{
    Values: map[string]float64{
        "requests": 0,
        "errors": 0,
    },
    IntervalCallback: func(sm *gospeedmeter.SpeedMeter) {
        // Log metrics every 1000 operations
        log.Printf("Performance metrics: %s", sm.ToString(gospeedmeter.UnitM, nil))
    },
    IntervalTriggerCount: 1000,
})

// Track multiple metrics
sm.Check("requests", 1)

// Example of error handling (assuming some operation that might fail)
resp, err := someOperation()
if err != nil {
    sm.Check("errors", 1)
    log.Printf("Error: %v", err)
}

// Estimate time to reach target
targetValue := 1000.0
estimate := sm.Estimate("requests", targetValue, gospeedmeter.UnitM)
log.Printf("Estimated time to reach %v requests: %.2f minutes", targetValue, estimate)
```

### Available Time Units

- `UnitMS`: Milliseconds
- `UnitS`: Seconds
- `UnitM`: Minutes
- `UnitH`: Hours
- `UnitD`: Days

### Number Formatting

The package automatically formats large numbers using K/M/B/T units:
- K: Thousands (1,000)
- M: Millions (1,000,000)
- B: Billions (1,000,000,000)
- T: Trillions (1,000,000,000,000)

Example output:
```
requests(1000):1.5K/s
errors(10):0.1K/s
```

## API Reference

### SpeedMeter

```go
type SpeedMeter struct {
    // ... internal fields
}

type SpeedMeterOptions struct {
    Values               map[string]float64
    IntervalCallback     func(*SpeedMeter)
    IntervalTriggerCount int
}
```

### Methods

- `NewSpeedMeter(options SpeedMeterOptions) *SpeedMeter`: Create a new speed meter
- `Reset(values ...map[string]float64)`: Reset the speed meter with optional initial values
- `Check(name string, value float64)`: Update a metric value
- `GetSpeed(unit Unit) map[string]float64`: Get current speeds for all metrics
- `ToString(unit Unit, toBeEstimated map[string]float64) string`: Get formatted string representation
- `Estimate(name string, targetValue float64, unit Unit) float64`: Estimate time to reach target
- `GetCounters() map[string]float64`: Get current counter values

## Thread Safety

All operations are thread-safe using mutex locks to prevent race conditions when updating metrics from multiple goroutines.

## License

MIT License