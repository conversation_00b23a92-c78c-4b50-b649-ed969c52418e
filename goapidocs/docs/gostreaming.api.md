<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gostreaming

```go
import "github.com/real-rm/gostreaming"
```

## Index

- [func Streaming\(ctx context.Context, opts \*StreamingOptions\) error](<#Streaming>)
- [type StreamingOptions](<#StreamingOptions>)


<a name="Streaming"></a>
## func [Streaming](<https://github.com/real-rm/gostreaming/blob/main/streaming.go#L43>)

```go
func Streaming(ctx context.Context, opts *StreamingOptions) error
```

Streaming processes items from a stream using concurrent workers, up to High

<a name="StreamingOptions"></a>
## type [StreamingOptions](<https://github.com/real-rm/gostreaming/blob/main/streaming.go#L14-L23>)

StreamingOptions defines the configuration for stream processing

```go
type StreamingOptions struct {
    Stream        interface{}             // Stream object with Next(), Decode() and Err() methods
    Process       func(interface{}) error // Process function for each item
    End           func(error)             // Callback when streaming ends
    Error         func(error)             // Error handler
    High          int                     // Max number of concurrent processing goroutines
    SpeedInterval int64                   // Interval for speed logging (in milliseconds)
    Verbose       int                     // Verbosity level
    Event         string                  // Event name (default: "data")
}
```

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
