# Real-RM Go API Docs

### README:
# goauth
authentication service for go, including login/regiser/jwt_util etc...

backend uses go gin as http server.

to dun the server:
```bash
go mod tidy
go run main.go
```

TODO:
- [ ] +goconfig,golog,gomongo 的使用，区分src/log/doc/configs folder structures
- [ ] make this package and handle jwt utils
- [ ] 想办法部署，需要测试不同部署方法
- [ ] move rent_report login/register/auth to this package
- [ ] 增加测试
- [ ] 增加github actions

---

### README:
# goconfig

A simple and flexible configuration management package for Go applications that provides easy access to configuration values from TOML files and environment variables.

## Features

- TOML configuration file support
- Environment variable support for config file path
- Command-line flag support for config file path
- Dot notation for accessing nested configuration values
- Simple and intuitive API

## Installation

```bash
go get github.com/real-rm/goconfig
```

## Configuration

The package supports configuration through:
1. TOML configuration files
2. Environment variable `RMBASE_FILE_CFG` for config file path
3. Command-line flag `--config` for config file path

### TOML Configuration File Example

```toml
[database]
host = "localhost"
port = 5432
name = "mydb"

[redis]
host = "localhost"
port = 6379
password = "secret"

[api]
port = 8080
timeout = 30
```

### Environment Variable

The package uses a single environment variable to specify the configuration file path:
```bash
export RMBASE_FILE_CFG=/path/to/config.toml
```

## Usage

### Initialization

```go
import "github.com/real-rm/goconfig"

func main() {
    err := goconfig.LoadConfig()
    if err != nil {
        log.Fatal(err)
    }
}
```

### Accessing Configuration Values

```go
// Get a specific configuration value
dbHost := goconfig.Config("database.host")
dbPort := goconfig.Config("database.port")

// Get all settings
allConfig := goconfig.Config("")

// Type assertions for specific values
if host, ok := goconfig.Config("database.host").(string); ok {
    fmt.Printf("Database host: %s\n", host)
}

if port, ok := goconfig.Config("database.port").(int64); ok {
    fmt.Printf("Database port: %d\n", port)
}
```

### Command Line Usage

```bash
# Specify config file using command line flag
./myapp --config /path/to/config.toml

# Or using environment variable
export RMBASE_FILE_CFG=/path/to/config.toml
./myapp
```

## Configuration Priority

The package follows this priority order for configuration file path:
1. Command-line flag `--config`
2. Environment variable `RMBASE_FILE_CFG`

## Example Usage

```go
package main

import (
    "fmt"
    "log"
    "github.com/real-rm/goconfig"
)

type Config struct {
    Database struct {
        Host string
        Port int
        Name string
    }
    Redis struct {
        Host     string
        Port     int
        Password string
    }
    API struct {
        Port    int
        Timeout int
    }
}

func main() {
    // Load configuration
    if err := goconfig.LoadConfig(); err != nil {
        log.Fatal(err)
    }

    // Access configuration values
    dbHost := goconfig.Config("database.host")
    dbPort := goconfig.Config("database.port")
    dbName := goconfig.Config("database.name")

    fmt.Printf("Database connection: %s:%d/%s\n", dbHost, dbPort, dbName)

    // Access nested configuration
    apiPort := goconfig.Config("api.port")
    apiTimeout := goconfig.Config("api.timeout")

    fmt.Printf("API server: port=%d, timeout=%d\n", apiPort, apiTimeout)
}
```

## Dependencies

- github.com/spf13/viper

## Best Practices

1. Always initialize the configuration at the start of your application
2. Use meaningful section names in your TOML configuration
3. Document all configuration options
4. Use type assertions when accessing configuration values
5. Handle configuration errors gracefully

## Error Handling

The package provides clear error messages for common configuration issues:
- Missing configuration file
- Invalid file format
- File read errors

Always check the error returned by `LoadConfig()`:
```go
if err := goconfig.LoadConfig(); err != nil {
    log.Fatalf("Failed to load configuration: %v", err)
}
```

## Testing

Run tests using:
```bash
go test
go test -v  # for verbose output
```

## Linting

Using golangci-lint for code linting. The project includes a `.golangci.yml` configuration file with predefined rules.

### Installation

```bash
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
```

### Usage

Run linter using make command:
```bash
make lint
```

Or run directly with our configuration:
```bash
golangci-lint run
```

Our `.golangci.yml` includes:
- 12 enabled linters (govet, errcheck, staticcheck, etc.)
- Skip rules for test files and vendor directories
- Custom linter settings for each enabled linter

For detailed configuration, check `.golangci.yml` in the project root.

### API.md:
<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# goconfig

```go
import "github.com/real-rm/goconfig"
```

Package lib provides core functionality and utilities for the application. This file contains configuration management functionality including loading and accessing configuration values from TOML files.

## Index

- [Variables](<#variables>)
- [func Config\(path string\) interface\{\}](<#Config>)
- [func ConfigBool\(path string\) \(bool, error\)](<#ConfigBool>)
- [func ConfigFloat\(path string\) \(float64, error\)](<#ConfigFloat>)
- [func ConfigInt\(path string\) \(int, error\)](<#ConfigInt>)
- [func ConfigString\(path string\) \(string, error\)](<#ConfigString>)
- [func LoadConfig\(\) error](<#LoadConfig>)
- [func ResetConfig\(\)](<#ResetConfig>)


## Variables

<a name="L2_FOLDER_LIST"></a>

```go
var L2_FOLDER_LIST = []string{
    "6ff4b", "6aa9d", "c45ab", "c93c7",
    "577a5", "54fd4", "ae459", "95f8a",
    "e3e17", "0a72b", "6ac7f", "13a1e",
    "64787", "1f277", "ce437", "51b20",
    "6ff08", "12bf4", "9497f", "d928f",
    "960ba", "c6ec7", "00a34", "96c3b",
    "e1633", "f891a", "b8bfa", "76ab1",
    "b889e", "421b9", "e8494", "7da6c",
    "95168", "0af5a", "90544", "a6fd2",
    "345b0", "54f61", "329ac", "049a5",
    "f91b6", "555dd", "97ab3", "2f714",
    "6e147", "e383c", "7186f", "1d341",
    "e0261", "1bf19", "10413", "08f2e",
    "c7b46", "3d293", "ee2a2", "3a18c",
    "1a6a7", "c0a58", "670da", "de239",
    "aeed1", "fe096", "3bf59", "91878",
    "65fd5", "ee0b1", "8f804", "d32cc",
    "178cc", "d77a1", "dcb76", "f4202",
    "aca0b", "737bb", "76df1", "0ff2b",
    "4384f", "df0c6", "d384c", "776d4",
    "1cc36", "15dbf", "74353", "824a5",
    "9673b", "15785", "9d3c5", "ab0ae",
    "5cb96", "dc070", "dddef", "2c298",
    "9c455", "247a8", "5a752", "f7409",
    "f6be4", "432a9", "c6cde", "530aa",
    "83edc", "6db53", "df450", "32c54",
    "41ce4", "64592", "818dc", "1c2e6",
    "e75a0", "9362c", "abbce", "f5c58",
    "ef56c", "7df1f", "ba34d", "c27f1",
    "96c59", "4e758", "ad0e7", "68092",
    "b863b", "a33c1", "8178b", "f2b48",
    "48eb8", "e6c57", "0fa4a", "e518c",
    "ccbb8", "287c1", "57610", "99df2",
    "6722e", "a5df7", "d621f", "3099f",
    "832af", "388b5", "f9a4a", "27e53",
    "afa75", "be7ce", "26d62", "6eaa4",
    "b00b5", "6e703", "fd647", "38bd6",
    "ec610", "92409", "6d611", "3ee53",
    "b7d88", "4af4b", "6d619", "3f4cf",
    "30f26", "48eb8", "2022d", "5c035",
    "c6732", "5ecdb", "68099", "a0747",
    "56c64", "48689", "40a04", "8a177",
    "8a3bd", "b4fa6", "afdb3", "9e7a5",
    "fb336", "2fce9", "c6a3e", "bdee4",
    "5d687", "1c41f", "9c18a", "a2813",
    "ed5cb", "24a07", "9b3d6", "6c6fd",
    "d2dbd", "a57eb", "31d96", "9ae4c",
    "7aef3", "a520d", "48b36", "c3b84",
    "7e421", "e7294", "7baac", "069cf",
    "bdd37", "9c849", "46ff0", "4b240",
    "8d087", "ff4fa", "dd406", "eb212",
    "52481", "9ecb5", "6498d", "2e3b2",
    "73abe", "42360", "e3936", "609b9",
    "47cad", "452fc", "e5838", "c2015",
    "329db", "dc6f1", "0ef0e", "ce7d7",
    "2e4d8", "11344", "7a493", "bbc11",
    "55048", "f2dc4", "bf90a", "e9c88",
    "be886", "0553e", "1e640", "5e4aa",
    "9f33f", "5d6cb", "f6de8", "57a1e",
    "b88be", "be2aa", "2bfbb", "b3bfb",
    "450e5", "e167b", "a916d", "d9bb8",
    "9d298", "9e5ab", "ec1cb", "63fc4",
    "0a219", "14f76", "69db1", "0347b",
    "16a7e", "c6f4d", "499ef", "3dc40",
    "60bdc", "750ea", "494bb", "6256c",
    "88e88", "bd190", "a45b4", "1d008",
    "63bd8", "89ec3", "e8288", "acef9",
    "dff08", "5e90f", "b04aa", "18ff5",
    "6f27e", "d5fcd", "e45e7", "2c99e",
    "8a7ab", "cdc90", "ee50a", "5cd13",
    "c148e", "c9da6", "782ac", "bf75d",
    "2d482", "88ad1", "f4d00", "1a2e9",
    "2af61", "5d695", "982b2", "dc979",
    "7ab8a", "3cedc", "952c8", "db6dc",
    "a7bef", "2acb6", "f3f2b", "e6d02",
    "2d641", "f1bd3", "377b6", "ea9b6",
    "a4a9e", "bbbce", "57246", "9fa29",
    "f9e45", "00ae9", "bc677", "08179",
    "effc3", "b4c67", "b097a", "62972",
    "b9860", "150b6", "8ed43", "0863b",
    "d333f", "4a70f", "94e8d", "06a92",
    "95f7e", "a6d06", "3a16e", "79a39",
    "b902f", "d3332", "15e15", "407ac",
    "9aad9", "b49a9", "400cf", "3a1a7",
    "d5a32", "e6f09", "6edbb", "3ce72",
    "bacd5", "6b691", "e9c45", "08f00",
    "0670f", "a2337", "69662", "85e4d",
    "ce2f4", "a1005", "51a31", "2377e",
    "5e4c3", "664ba", "a2350", "0594c",
    "b0668", "f7cbc", "c9a01", "a4ff3",
    "dfef0", "17ae8", "207bf", "38688",
    "72eb1", "e1e12", "a3b16", "52a9a",
    "b599d", "b5e19", "3a779", "6299e",
    "d74d4", "ee67c", "0c7ed", "c9406",
    "b567a", "b6941", "069a1", "d95ee",
    "4402c", "1fa4e", "60507", "f884c",
    "b9af8", "cee40", "7ac2e", "798b5",
    "c3244", "a84ae", "d8013", "55822",
    "302cb", "0602d", "e4f99", "ad0e3",
    "a8944", "fff9a", "043d6", "db1ca",
    "18a66", "2df9f", "95c8e", "43798",
    "d7374", "683bb", "7ba21", "53418",
    "713b1", "761ab", "17994", "72b45",
    "09d1b", "9a0be", "f10e9", "3d53a",
    "215a3", "f3992", "5dc07", "e7855",
    "0506e", "09105", "852b4", "3fbfc",
    "c8e86", "a39bf", "43cf7", "60897",
    "f0577", "d905e", "e7c74", "49b47",
    "56696", "37452", "572fc", "9f50d",
    "aacc4", "d7793", "97955", "257a5",
    "10321", "0f555", "4a5f6", "61715",
    "e08ee", "4d2c2", "69fb8", "9ce59",
    "2ec8a", "bb131", "1927d", "6c5fc",
    "a65ec", "7230b", "f0446", "14230",
    "f4dc7", "358f6", "32e85", "798b9",
    "6652b", "f722e", "822bd", "42c73",
    "7d59d", "b42d4", "8ba79", "fd40e",
    "8c6fd", "f3e83", "9d9a6", "bc05d",
    "38733", "8d4c2", "073d9", "719e7",
    "9e964", "6596a", "6bd6a", "fb8e6",
    "66237", "56693", "b0abc", "b7a82",
    "7873d", "ec36c", "3450a", "538f0",
    "6ee19", "43466", "e4aad", "1430a",
    "24629", "c8999", "7421d", "02594",
    "f0df9", "4b344", "b7adb", "0006c",
    "073b5", "f9d07", "58d56", "cd1ae",
    "7c0b2", "c399e", "72c1e", "d148b",
    "6ef74", "c10e3", "f2c07", "610ad",
    "23c64", "2b281", "c0c3c", "303c6",
    "4bbe5", "d2030", "ce7db", "b25a3",
    "0294f", "a4f1d", "732bb", "14f98",
    "cf2ca", "62cbd", "e8f48", "4d33c",
    "c576f", "bc558", "2f3a3", "e89fb",
    "eb260", "f5853", "b177f", "592fa",
    "ec67c", "4c30f", "590f5", "f360b",
    "7cc9a", "e056d", "1496b", "d1b14",
    "8e5e9", "7269b", "6d32b", "2a7db",
    "c7a40", "380cc", "cf8bd", "41947",
    "a1626", "14f29", "16830", "eec13",
    "7dae3", "778b9", "e0f79", "78ea7",
    "ff14d", "75a43", "cb0db", "8028c",
    "39575", "0326f", "4e4b0", "ec020",
    "2959b", "84746", "e6815", "42e12",
    "6c771", "1fb1a", "43270", "a39d9",
    "fe961", "b959a", "cb9a4", "172f6",
    "dd6d5", "d090b", "f20e3", "b9347",
    "6f441", "d3fb5", "22f94", "d690e",
    "2337d", "2ba8b", "f39bd", "cc66d",
    "da460", "d16da", "055ba", "7f19a",
    "00d1a", "a5004", "e8d9c", "24e65",
    "640b4", "a89cb", "a06f7", "30223",
    "1cb17", "6781a", "05abd", "f6450",
    "264e1", "8990c", "3a6c1", "19b77",
    "d57de", "a7238", "dcd6e", "96311",
    "d741c", "1a2c1", "36e2f", "abb2b",
    "64041", "6e337", "b9ffa", "9d6ee",
    "58dea", "76418", "a8eb6", "54766",
    "45bbd", "7b740", "2fa96", "582f5",
    "61c9b", "4311d", "5f604", "ada8c",
    "d58f3", "9021b", "a6642", "ae3e5",
    "fea35", "602f5", "0c0d3", "8d4c3",
    "8900c", "9856b", "2aff3", "b9b72",
    "ff72d", "b4a6a", "2c124", "c941c",
    "2759e", "51c76", "21509", "cf882",
    "fd593", "ceb4e", "f4dea", "22039",
    "b80a5", "6b98c", "0774d", "97125",
    "922fa", "affd7", "aa716", "503a3",
    "48f16", "96fea", "2c261", "28529",
    "bb57c", "8ee86", "b9d42", "b0500",
    "abf7b", "48d23", "7077e", "ef660",
    "8d89c", "19774", "07ec4", "4212c",
    "6e4f7", "222cf", "17e9f", "b7d83",
    "cac99", "28a68", "f6b96", "efd3f",
    "c0441", "c8440", "5d183", "49ba8",
    "de628", "40b2a", "76653", "7e93a",
    "8ebbf", "143c0", "6b237", "df4a8",
    "c9313", "5ffcb", "5818d", "76aac",
    "a6702", "de6fb", "b38ff", "b025b",
    "74d0c", "e2f1d", "b7bfa", "740ed",
    "70338", "dbdb0", "795d1", "9a4e2",
    "ed36b", "b64bc", "2e82b", "d926e",
    "219ac", "c13a5", "aa6c7", "2255a",
    "e93ac", "3d38f", "29b6a", "14098",
    "973b7", "1ff66", "988ae", "59871",
    "cb65f", "7029e", "9d633", "ffe97",
    "3f51e", "e49ad", "6b1e1", "98407",
    "efe39", "007e3", "02263", "9c4bb",
    "62e10", "e93f1", "87c89", "5ad69",
    "ef48d", "5bf9e", "df4e9", "ab0fc",
    "67a34", "4f15b", "b7aa4", "0280c",
    "dd22d", "37e74", "8504b", "ee762",
    "68b8d", "6372f", "c063c", "18a23",
    "5adde", "47d1b", "b4d2c", "312a2",
    "edec9", "06866", "9bac9", "eadf3",
    "add55", "a8ed0", "49288", "376b3",
    "c0cbf", "bb019", "40140", "e7909",
    "34db8", "258c9", "304c7", "fc242",
    "90605", "65cd8", "7ccb6", "f3601",
    "b5f92", "76f23", "493b5", "6759a",
    "116dc", "2573c", "663ef", "2ec91",
    "266ba", "d3c42", "7c705", "0c088",
    "82828", "8e988", "c239a", "68fb9",
    "c7f35", "4888c", "c41a2", "5bb9f",
    "503d3", "d400b", "a929e", "1a290",
    "a64c0", "69c91", "34044", "b5966",
    "d40ad", "eb1b1", "e5d51", "0ad53",
    "5b3f3", "c5ee4", "3b3ce", "8b878",
    "05292", "772f4", "d02cc", "fd637",
    "61a74", "d0b12", "e3383", "e4bac",
    "efc34", "f507e", "fe40d", "b6a0d",
    "ad04a", "b733e", "bea9b", "a090c",
    "476af", "5952d", "5028f", "62053",
    "881a6", "39ec3", "ed31a", "c7e34",
    "1fbe3", "f61ad", "0a319", "209e5",
    "1f1fc", "4f289", "eb710", "e6422",
    "43d47", "2d30c", "2c813", "f861b",
    "a8e2c", "1d6cd", "1b116", "818c8",
    "d441f", "5bfa1", "440bf", "2fe73",
    "4054c", "db9e8", "58a49", "cd1ca",
    "30e8e", "d0893", "a239a", "7ad65",
    "b6b08", "d786a", "b9492", "8b323",
    "486ce", "b57bb", "9c54a", "1ed4c",
    "a3f08", "39fd2", "71391", "46aa8",
    "dbc39", "276d3", "6d2d3", "d041e",
    "699db", "e9b55", "96fee", "dc078",
    "79f68", "e9b69", "567f8", "88bf1",
    "79358", "aa915", "033af", "8b83e",
    "b5f09", "4f7c3", "ab955", "50072",
    "8b39f", "8dd23", "744d4", "4a089",
    "5005e", "16108", "55f04", "538c1",
    "e98f5", "6131b", "a55a8", "5a141",
    "cd367", "8cc52", "76a88", "7ee83",
    "8c227", "a75eb", "61ad8", "b06c4",
    "100f4", "c1b1d", "5c432", "c6260",
    "0d81a", "61b30", "e8e65", "3748a",
    "d80da", "abad5", "f7145", "57daf",
    "71ab0", "50f21", "04940", "c8a19",
    "a5eab", "02c28", "eab74", "ca962",
    "445e3", "b7da7", "695ac", "95c0a",
    "2735f", "108fa", "3b3c0", "101a3",
    "43030", "d19d4", "7b412", "1ff64",
    "19ec2", "5cad5", "59a08", "822a1",
    "a8819", "6688b", "3e336", "3e5b7",
    "cb169", "9cc01", "a24e6", "5302e",
    "ba56a", "4ec83", "da985", "60d44",
    "f4ca9", "930c5", "4d3c6", "c15e2",
    "ebf2e", "4ce27", "8b280", "b0925",
    "ff8ca", "99746", "1b269", "c3cc7",
    "e0a72", "15be7", "9db6f", "afabc",
    "2b60e", "b0a5f", "380ec", "301f2",
    "d6d36", "9ff3f", "d4dec", "531fe",
    "bc1b2", "9651b", "69fe5", "fc72a",
    "90c5e", "1f153", "57a8c", "93e35",
    "3d94f", "a1257", "0a357", "25dea",
    "43517", "a8a63", "2c0e5", "85058",
    "2c331", "5f924", "33309", "88f49",
    "b3fe6", "c6c41", "61e2f", "2b750",
    "06199", "01da9", "f1c74", "84ac9",
    "2a309", "3a161", "e7ec4", "116ad",
    "81ecb", "c1cd4", "f0f15", "92460",
    "e4a3d", "2d397", "cf61f", "71468",
    "94779", "abfbd", "4597b", "9c5a3",
    "01801", "1c292", "53e28", "7b66d",
    "eeaf7", "d271c", "07ac1", "87c07",
    "f5392", "5b026", "1bf2b", "ebb39",
    "fea7a", "006b0", "f91af", "2a0d0",
    "f1e1b", "93f35", "bd9c6", "5f2bf",
    "d8b1b", "86a0d", "3c05a", "78614",
    "4fbf1", "5aa12", "b3a6d", "c8b28",
    "2271f", "3c35f", "63127", "8d837",
    "03728", "f38d9", "07001", "d0caf",
    "599b0", "21cbc", "8a66d", "059a5",
    "2f783", "5f9a0", "5db10", "79705",
    "0d3a2", "062c4", "ea8f1", "357e6",
    "191c9", "1b0f0", "fbebc", "5d0ed",
    "a48d1", "72cc5", "f220d", "7dcd0",
    "c0c29", "11ec1", "ea263", "245ca",
    "01e06", "b13bc", "5d13c", "da52c",
    "90e0f", "8a5c0", "8e5f0", "74505",
    "265d2", "3ed28", "b1407", "fe5f3",
    "07fba", "ec19c", "ebfec", "844fd",
    "26482", "3b0a3", "75c12", "9c9d6",
    "db539", "9a250", "b9e42", "2fae8",
    "fffbd", "23fb4", "cc991", "e9c74",
    "7e24a", "23c0a", "a0494", "79926",
    "04b7f", "f4e7f", "f3f93", "9f2a1",
    "e8bff", "07f7f", "ac2e2", "6ab0d",
    "c956a", "15e87", "8cfce", "9e5f1",
    "52701", "45ba0", "af5ca", "f060a",
    "d6e70", "dd3e9", "b234b", "1959d",
    "f157a", "15c6d", "226e9", "a1182",
    "c44e3", "14d6e", "43e47", "dda0b",
    "30690", "291bc", "bda81", "b584b",
    "ca282", "7892c", "61237", "127c6",
    "50473", "975ef", "c7fef", "c8184",
    "bd1a7", "103a8", "8ce35", "c1ab2",
    "75f8f", "57584", "35877", "8deef",
    "95348", "03ee1", "0ebd6", "307fd",
    "b2705", "facfa", "a844e", "d27f5",
    "5f051", "54134", "cee69", "9f049",
    "733c5", "b77a8", "d3cd3", "ca06c",
    "acba2", "04dcc", "c156a", "4d99b",
    "967e2", "973f6", "4ed3e", "66f4a",
    "71f64", "912c9", "3e908", "d9f17",
    "f5b47", "b430e", "60083", "78834",
    "6a73f", "999a9", "d8ed2", "b4826",
    "2e89b", "14ec4", "fecfc", "3d6e3",
    "512ae", "1d163", "8b95a", "4f078",
    "c5117", "2eeb8", "b9238", "d1629",
    "a7226", "5484a", "694ed", "7b468",
    "a59a2", "2e5dc", "ecbe3", "ad194",
    "ef253", "bb664", "7f737", "1c9de",
    "5b1c9", "e4ff4", "7f36e", "378d8",
    "9ad1d", "9909b", "ca81e", "090ca",
    "25230", "1ab91", "2dc0d", "bf110",
    "8feb5", "09dbc", "ec4d3", "8a04c",
    "d22e2", "2182f", "d4659", "f4133",
    "88542", "b4f30", "ce764", "01d4d",
    "bc9e6", "0bbad", "4ce12", "29999",
    "0f692", "b067b", "416eb", "29160",
    "0a507", "894b6", "8c9d7", "ddb65",
    "8e46a", "0e102", "b3a9c", "b6a23",
    "eb8d3", "9633f", "e9eb9", "644ae",
    "89949", "64b7e", "de026", "8871f",
    "ff093", "163d5", "c50ce", "7d457",
    "8e708", "95378", "f694f", "0fbac",
    "549db", "ecce8", "d6658", "78665",
    "62e53", "966d7", "d585b", "0a943",
    "09f75", "54db3", "57911", "13eca",
    "a01d9", "ceece", "2ae51", "e1948",
    "616ca", "b3d20", "e05ac", "d5f51",
    "4df1a", "948cd", "91734", "99173",
    "60e00", "03dbc", "e0c52", "6efa9",
    "8f6c4", "1cb3c", "517e9", "81387",
    "d51d7", "bc864", "1c04c", "3c9df",
    "fa815", "7734e", "17c02", "98e68",
    "fa344", "ffc77", "a7e6c", "56f62",
    "02c30", "455f1", "14625", "a5c61",
    "88952", "fbfc9", "2ccef", "bfcec",
    "ef6c5", "626fd", "9ce0e", "10904",
    "6b636", "220f0", "977b2", "7bf67",
    "5b159", "61001", "0e0f4", "8aa10",
    "b60d9", "53c43", "165a3", "49ae8",
    "01673", "0eafb", "53611", "9f4a9",
    "96375", "504f8", "4f035", "e151a",
    "944b7", "aef99", "0f6cd", "478d5",
    "9b923", "d7919", "98f7b", "293a5",
    "679d6", "4b84b", "08e34", "7148c",
    "c3c38", "3432f", "7b2a4", "b3a04",
    "83f2d", "49916", "52c33", "540c1",
    "28904", "cfdbb", "a1b85", "dba8b",
    "452f4", "a828c", "50819", "24ea0",
    "49cd5", "9e51f", "420db", "5d505",
    "ed6c9", "3cdd0", "674e4", "f08ab",
    "4069f", "a74f0", "03888", "68757",
    "f25bb", "a94df", "28e0a", "94d18",
    "82aec", "dc918", "36949", "77ff3",
    "ecd7f", "a29d8", "edfda", "b004c",
    "c3c19", "685f9", "ec0ca", "602cb",
    "7e875", "a82d5", "76a40", "fe9be",
    "76e43", "ddbfc", "fb917", "da972",
    "1ca7f", "b9490", "043c7", "729ae",
    "4603c", "286aa", "6a5cf", "bc030",
    "b7491", "a523e", "a34cd", "27ac2",
    "ce473", "9802b", "89810", "d5bd0",
    "9b33f", "4d752", "81063", "ff16b",
    "5207f", "0275d", "0e338", "99372",
    "2d518", "05123", "24ea2", "a511f",
    "d3e00", "d72ac", "b684c", "436da",
    "0ed70", "e7aca", "d3f09", "d7628",
    "c1713", "aea61", "d7945", "2a191",
    "0ac54", "aa544", "731be", "cb72c",
    "33923", "0946f", "41742", "31360",
    "a910b", "4f679", "5eef4", "2d2b5",
    "3c7b2", "989ba", "a0b14", "73e74",
    "738ea", "c7837", "b300a", "7783c",
    "691b9", "60ee2", "702a4", "5c376",
    "f5546", "c0301", "f8c9d", "4a4eb",
    "f0e47", "5591a", "2b367", "60bdb",
    "24a2b", "55881", "3d72e", "42963",
    "3769c", "c847a", "b83ed", "b309a",
    "20b7d", "cd6ae", "bfe97", "1ebce",
    "13c7f", "c41a0", "ddd18", "57903",
    "a1c0b", "f6f71", "b2611", "fa0d6",
    "4d081", "18826", "5ee4e", "9cb71",
    "806f7", "fcfa7", "96e07", "fe628",
    "3ff40", "7109f", "6c300", "f00b0",
    "464d5", "99c5e", "1d1e6", "d8886",
    "df83c", "89378", "be7ce", "1a75c",
    "ad57a", "eb1f6", "1ab6f", "39277",
    "652e1", "11d9e", "c0da7", "eb8b9",
    "22ba7", "b30dd", "36f1b", "b8f3e",
    "392ee", "e2c6e", "78a77", "8c569",
    "b2d2b", "c7b0b", "488e6", "a4d79",
    "bfde8", "eb430", "201b7", "a91a9",
    "866b3", "3a2b0", "16b5a", "def06",
    "33d11", "2bff2", "478f4", "6e7fd",
    "f0635", "49180", "36869", "338f3",
    "6c233", "8246a", "ea36c", "31727",
    "a1bfe", "ffc41", "bc15a", "f8469",
    "b9c5e", "0796e", "ec07f", "5490c",
    "11577", "2ed46", "79f17", "906f3",
    "463e9", "4a6c9", "5aa29", "06fc1",
    "41e25", "b4529", "77aff", "59046",
    "73d05", "3d08c", "55409", "96930",
    "926d1", "7bdaf", "48c40", "0232d",
    "6b9dd", "0626d", "c78c3", "9e03e",
    "f0a54", "287c8", "09500", "30238",
    "851bc", "4a302", "aa486", "60e15",
    "0dbcb", "178c7", "99f5c", "6e54b",
    "5bb34", "5f86e", "99f99", "2e848",
    "b4893", "ba4a6", "ee66b", "3ae56",
    "2b3e6", "23fe9", "c5860", "0eacc",
    "116bb", "475ba", "261bd", "2196f",
    "6f3cc", "6aefe", "a1632", "73eda",
    "b99c8", "a196f", "82d83", "887e1",
    "65c72", "c776a", "e3fca", "be936",
    "4ec5d", "9d9e0", "764dd", "9db96",
    "a5a6a", "8b3c5", "9b67e", "044c9",
    "40b34", "50edf", "b6d4c", "d759c",
    "82e32", "1fa4b", "22d62", "0f044",
    "6c727", "9d04a", "a4473", "79614",
    "b67b4", "b4304", "570e5", "3b67b",
    "ad011", "2b559", "6d7b9", "b916a",
    "01200", "f67cc", "37104", "b2b13",
    "20e9f", "41f07", "32614", "5bc08",
    "f8821", "4daa3", "a41c6", "89b2a",
    "0ac1e", "9698e", "12f35", "b03e7",
    "c9226", "544af", "f8185", "9e696",
    "66ce8", "29b0e", "464f4", "915fe",
    "74037", "48043", "6835d", "95703",
    "c72bd", "3d08f", "738fc", "c903b",
    "f2013", "886c6", "7ee2a", "a3c43",
    "d3d6d", "3ef99", "a72c3", "c5b3d",
    "dfc32", "2c0be", "57a2b", "ef3e3",
    "c3f13", "71e74", "f5dbe", "5c507",
    "5ef28", "f0704", "4ea96", "0ce26",
    "878a8", "22b66", "6ab45", "47a02",
    "5219e", "87233", "e52e9", "a0da5",
    "e752f", "150cf", "de8df", "25fea",
    "ab4e3", "fd3d9", "c2c1f", "49385",
    "4eed6", "b2583", "de9ad", "a0d86",
    "187d4", "fbdd2", "73fa5", "5b418",
    "070f3", "5ff69", "ce5f5", "55a2c",
    "ff771", "b360c", "c108d", "a5b40",
    "9adf8", "3b51e", "38e27", "f6b9d",
    "48d30", "c2674", "21dfa", "64a43",
    "e0587", "bf860", "0245e", "97549",
    "ffaa2", "c3655", "8db19", "ed5a1",
    "9e20e", "6860d", "8b759", "bdab7",
    "f9ec8", "f96d0", "7e808", "b5e42",
    "229d9", "9d920", "150f7", "5b994",
    "8eb2e", "45613", "93dd9", "d06ce",
    "ccaa1", "6a471", "aec6f", "4e961",
    "a22c0", "8912f", "ca5d8", "bc375",
    "c0f60", "00970", "91068", "c0fa5",
    "c9ccc", "67253", "d181a", "ad0b0",
    "f8a50", "18235", "3630e", "7546c",
    "1adea", "7a27a", "088c5", "ad61e",
    "bc76b", "a2134", "6f71f", "25382",
    "f163b", "04962", "b22e1", "2dc66",
    "fab57", "190e0", "446ba", "8d1c1",
    "4b9ba", "cfbc4", "6787c", "728a6",
    "c89fd", "2532c", "aa93c", "19d68",
    "a9cf1", "80890", "25ac0", "76240",
    "15a84", "ecd1e", "2ab4a", "1eac5",
    "efa26", "2c416", "e6ab4", "c6b8a",
    "0068b", "3c217", "c2c91", "39a57",
    "b0ef3", "37975", "41175", "dd30d",
    "f0fa0", "500e9", "f7e1e", "70f7d",
    "f3868", "cb20c", "96734", "8b83f",
    "27692", "592c8", "fd915", "9f87f",
    "58b89", "acd43", "c8b89", "c8172",
    "ec532", "9e2b6", "5f334", "c6828",
    "1869a", "40a6f", "fad74", "d473b",
    "020c3", "1e50a", "60b14", "6de9b",
    "56d23", "08dc0", "22341", "caa43",
    "3eaa0", "c68b0", "2b459", "5df1c",
    "2ff60", "e1c4e", "59cf2", "6b80f",
    "8fc23", "3bdef", "c6423", "49246",
    "031a1", "752ea", "6599e", "44639",
    "d7af8", "a44ee", "b37e3", "d2219",
    "066de", "5e2e5", "80789", "d88f9",
    "55dc7", "35901", "99092", "b3dab",
    "ac4f3", "faed6", "95ec6", "43ea9",
    "ebea4", "9ee1b", "97e76", "df2d4",
    "f042d", "54788", "16592", "e4cee",
    "4af6b", "e4c80", "2e0be", "eb0c4",
    "6124f", "2e095", "c0dcd", "5a480",
    "e9af6", "95f77", "7803a", "eff36",
    "eb3d9", "45b86", "36ef4", "63e95",
    "39e7f", "bfd5e", "7ed9c", "1d636",
    "079fc", "9de93", "bbb05", "7dc2b",
    "03780", "b29d8", "e8c9c", "f7c7a",
    "85654", "05c88", "d1711", "d6b4f",
    "ab0a8", "347bc", "a885c", "f0663",
    "5c154", "35a77", "bd342", "45be6",
    "96206", "cb0e9", "12f54", "4ef80",
    "a8e77", "37431", "6926d", "9b1ac",
    "a613b", "f2912", "5982a", "dcb52",
    "c2beb", "d04f2", "fab3a", "53f4e",
    "adcc2", "8786c", "9b152", "df21a",
    "46ed6", "81d73", "b0148", "97718",
    "f6334", "d5a47", "2c5fe", "52e73",
    "17ba2", "1351e", "dce6f", "0d198",
    "b3e6b", "589c9", "60276", "ed672",
    "ea117", "9bdfd", "e8c47", "438ab",
    "9acb8", "e2432", "61675", "bdff5",
    "a2d80", "4d318", "502d0", "f2a12",
    "4a1d0", "500a7", "33a3b", "84547",
    "aa615", "f9da6", "df344", "0b9d9",
    "db0c2", "86477", "39042", "1c681",
}
```

<a name="SOURCE_L2_SIZE"></a>

```go
var SOURCE_L2_SIZE = map[string]int{
    "TRB":  512,
    "DDF":  1024,
    "BRE":  64,
    "CLG":  64,
    "OTW":  64,
    "EDM":  64,
    "CAR":  256,
    "USER": 512,
}
```

<a name="Config"></a>
## func [Config](<https://github.com/real-rm/goconfig/blob/main/config.go#L66>)

```go
func Config(path string) interface{}
```

Config retrieves configuration values using dot notation

<a name="ConfigBool"></a>
## func [ConfigBool](<https://github.com/real-rm/goconfig/blob/main/config.go#L156>)

```go
func ConfigBool(path string) (bool, error)
```



<a name="ConfigFloat"></a>
## func [ConfigFloat](<https://github.com/real-rm/goconfig/blob/main/config.go#L138>)

```go
func ConfigFloat(path string) (float64, error)
```



<a name="ConfigInt"></a>
## func [ConfigInt](<https://github.com/real-rm/goconfig/blob/main/config.go#L120>)

```go
func ConfigInt(path string) (int, error)
```



<a name="ConfigString"></a>
## func [ConfigString](<https://github.com/real-rm/goconfig/blob/main/config.go#L107>)

```go
func ConfigString(path string) (string, error)
```



<a name="LoadConfig"></a>
## func [LoadConfig](<https://github.com/real-rm/goconfig/blob/main/config.go#L34>)

```go
func LoadConfig() error
```

LoadConfig initializes the configuration from either command line flag or environment variable

<a name="ResetConfig"></a>
## func [ResetConfig](<https://github.com/real-rm/goconfig/blob/main/config.go#L27>)

```go
func ResetConfig()
```

ResetConfig resets the configuration state

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)


---

### README:
# gofile

A Go language file handling package that provides a simple and efficient way to download, save, and convert images.

## Features

- Simple and intuitive API for image file operations
- Automatic retry mechanism for downloads
- Support for JPEG and WebP image formats
- Multiple directory saving support
- Built-in logging and error handling
- Configurable retry attempts
- Concurrent download support

## Installation

```bash
go get github.com/real-rm/gofile
```

## Configuration

The package uses a configuration system that supports TOML format. You need to configure your logging settings in your config file:

```toml
[golog]
dir = "/path/to/logs"
level = "debug"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"
```

## Usage

### Initialization

```go
import (
    "github.com/real-rm/gofile"
    "github.com/real-rm/golog"
    "github.com/real-rm/goconfig"
)

func main() {
    // Load configuration
    if err := goconfig.LoadConfig(); err != nil {
        log.Fatal(err)
    }
    
    // Initialize logger
    if err := golog.InitLog(); err != nil {
        log.Fatal(err)
    }
}
```

### Basic Operations

#### Download and Save Image

```go
// Download and save as JPEG
filePath, err := gofile.DownloadAndSaveImage(
    "https://example.com/image.jpg",
    "/path/to/save",
    "myimage",
    false, // false for JPEG, true for WebP
)
```

#### Download with Retry

```go
// Download with custom retry attempts
resp, err := gofile.DownloadWithRetry("https://example.com/image.jpg", 5)
if err != nil {
    log.Printf("Failed to download: %v", err)
    return
}
defer resp.Body.Close()
```

#### Save to Multiple Directories

```go
// Save the same image to multiple directories
paths, err := gofile.DownloadAndSaveImageInDirs(
    "https://example.com/image.jpg",
    []string{"/path1", "/path2"},
    "myimage",
    false,
)
```

## API Reference

### Main Functions

- `DownloadAndSaveImage(url string, savePath string, fileName string, compressWebP bool, maxRetries ...int) (string, error)`
- `DownloadAndSaveImageInDirs(url string, savePaths []string, fileName string, compressWebP bool, maxRetries ...int) (map[string]string, error)`
- `DownloadWithRetry(url string, maxRetries ...int) (*http.Response, error)`

### Helper Functions

- `SaveAsJPEG(img image.Image, savePath string, fileName string) (string, error)`
- `SaveAsWebP(img image.Image, savePath string, fileName string) (string, error)`

## Error Handling

All operations return errors that should be checked:

```go
filePath, err := gofile.DownloadAndSaveImage(url, savePath, fileName, false)
if err != nil {
    log.Printf("Error downloading and saving image: %v", err)
    return
}
```

## Retry Mechanism

The package includes a built-in retry mechanism for downloads:
- Default retry attempts: 3
- Exponential backoff delay: 1s, 2s, 3s
- Configurable max retries

## Dependencies

- github.com/HugoSmits86/nativewebp
- github.com/real-rm/goconfig
- github.com/real-rm/golog

## Example

```go
package main

import (
    "github.com/real-rm/gofile"
    "github.com/real-rm/golog"
    "github.com/real-rm/goconfig"
    "log"
)

func main() {
    // Initialize configuration and logging
    if err := goconfig.LoadConfig(); err != nil {
        log.Fatal(err)
    }
    if err := golog.InitLog(); err != nil {
        log.Fatal(err)
    }

    // Download and save an image
    filePath, err := gofile.DownloadAndSaveImage(
        "https://example.com/image.jpg",
        "./images",
        "example",
        false,
    )
    if err != nil {
        log.Printf("Failed to download and save image: %v", err)
        return
    }
    log.Printf("Image saved to: %s", filePath)
}
```


---

### README:
# gofileserver
## 用于后续用户上传和查看protected文件

---

### README:
# gohelper

A collection of utility functions and helpers for Go applications that provide common programming tasks and operations.

## Features

- UUID Generation
  - Secure random UUID generation using crypto/rand
  - Customizable length support
  - Hexadecimal format output
  - Thread-safe for concurrent use
- String Operations
  - String manipulation utilities
  - Text processing functions
- Numeric Operations
  - Number formatting
  - Mathematical utilities
- Time Operations
  - Time formatting and parsing
  - Duration calculations
- File Operations
  - File handling utilities
  - Path manipulation

## Streaming Helper

The `Streaming` helper provides a way to process streams of data with flow control and speed monitoring. It's particularly useful when you need to process large amounts of data while controlling memory usage and monitoring processing speed.

### Features

- Flow control with high/low water marks
- Speed monitoring and reporting
- Error handling with optional continuation on error
- Verbose logging options
- Customizable processing function

## Installation

```bash
go get github.com/real-rm/gohelper
```

## Usage

### UUID Generation

```go
import "github.com/real-rm/gohelper"

// Generate standard length UUID (32 characters)
uuid := gohelper.GenUUID(32)
// Example output: "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"

// Generate short UUID (8 characters)
shortUUID := gohelper.GenUUID(8)
// Example output: "1a2b3c4d"

// Handle invalid length
emptyUUID := gohelper.GenUUID(0) // Returns empty string
```

### String Operations

```go
// String manipulation examples coming soon
```

### Numeric Operations

```go
// Numeric operation examples coming soon
```

### Time Operations

```go
// Time operation examples coming soon
```

### File Operations

```go
// File operation examples coming soon
```

### Streaming Helper

```go
package main

import (
    "fmt"
    "time"
    "github.com/your-username/gohelper"
)

func main() {
    // Create a channel for data streaming
    stream := make(chan interface{}, 100)

    // Configure streaming options
    opts := &gohelper.StreamingOptions{
        Stream: stream,
        // Process function for each item
        Process: func(item interface{}) error {
            // Your processing logic here
            value, ok := item.(string)
            if !ok {
                return fmt.Errorf("expected string, got %T", item)
            }
            fmt.Printf("Processing: %s\n", value)
            return nil
        },
        // Called when streaming ends
        End: func(err error) {
            if err != nil {
                fmt.Printf("Streaming ended with error: %v\n", err)
            } else {
                fmt.Println("Streaming completed successfully")
            }
        },
        // Error handler for individual items when NoStopOnError is true
        Error: func(err error) {
            fmt.Printf("Error processing item: %v\n", err)
        },
        High: 1000,           // Pause when queue reaches 1000 items
        Low: 100,             // Resume when queue drops to 100 items
        SpeedInterval: 5000,  // Report speed every 5 seconds
        Verbose: 2,           // Logging verbosity level
        NoStopOnError: true,  // Continue processing on errors
    }

    // Start streaming
    gohelper.Streaming(opts)

    // Simulate sending data
    go func() {
        for i := 0; i < 10000; i++ {
            stream <- fmt.Sprintf("item-%d", i)
            time.Sleep(time.Millisecond * 10)
        }
        close(stream)
    }()

    // Wait for streaming to complete
    time.Sleep(time.Second * 120)
}
```

## Performance Considerations

- Uses crypto/rand for high-quality randomness in UUID generation
- Optimized for concurrent scenarios
- Memory allocation optimized to avoid unnecessary usage
- Reuse generated UUIDs for frequent-call scenarios

## Testing

The package includes comprehensive test coverage:
- Functional tests: Verify operations across various inputs
- Concurrency tests: Ensure thread safety
- Performance tests: Provide benchmark data
- Edge case tests: Handle exceptional inputs

## Dependencies

- Standard library only:
  - crypto/rand
  - encoding/hex
  - strings
  - time
  - os
  - path/filepath

## Best Practices

1. Use appropriate UUID lengths for your use case
2. Implement error handling for all operations
3. Consider performance implications in high-concurrency scenarios
4. Follow Go's standard practices for error handling
5. Use the provided utilities instead of implementing custom solutions

## Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.

## License

This project is licensed under the MIT License.

## Private Repository Setup

```bash
export GOPRIVATE=*
vi ~/.gitconfig
```

Add:
```
[url "ssh://**************"]
    insteadOf = https://github.com
```

### API.md:
<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gohelper

```go
import "github.com/real-rm/gohelper"
```

Package gohelper provides utility functions and helpers for the application.

## Index

- [Constants](<#constants>)
- [func CallOnce\(fn func\(\)\) func\(\)](<#CallOnce>)
- [func CleanupTestEnv\(\)](<#CleanupTestEnv>)
- [func Contains\[T comparable\]\(slice \[\]T, item T\) bool](<#Contains>)
- [func DateToTime\(dateInt int\) time.Time](<#DateToTime>)
- [func GenUUID\(length int\) \(string, error\)](<#GenUUID>)
- [func IsEmail\(eml string\) bool](<#IsEmail>)
- [func SetRmbaseFileCfg\(configPath string\)](<#SetRmbaseFileCfg>)
- [func SetupTestEnv\(opts TestOptions\) error](<#SetupTestEnv>)
- [func StrToTime\(s string\) \(time.Time, error\)](<#StrToTime>)
- [func TestUtilMain\(m \*testing.M, opts ...TestOptions\)](<#TestUtilMain>)
- [func TimeToDateInt\(timestamp time.Time\) int](<#TimeToDateInt>)
- [func TimeToStr\(t time.Time\) string](<#TimeToStr>)
- [type Interval](<#Interval>)
  - [func StartInterval\(intervalSec float64, task func\(\)\) \*Interval](<#StartInterval>)
  - [func \(i \*Interval\) Stop\(\)](<#Interval.Stop>)
- [type TestOptions](<#TestOptions>)
  - [func DefaultTestOptions\(\) TestOptions](<#DefaultTestOptions>)


## Constants

<a name="TimeFormat"></a>

```go
const TimeFormat = "2006-01-02 15:04:05"
```

<a name="CallOnce"></a>
## func [CallOnce](<https://github.com/real-rm/gohelper/blob/main/helper_function.go#L6>)

```go
func CallOnce(fn func()) func()
```

CallOnce only call the function once

<a name="CleanupTestEnv"></a>
## func [CleanupTestEnv](<https://github.com/real-rm/gohelper/blob/main/test_util.go#L124>)

```go
func CleanupTestEnv()
```

CleanupTestEnv cleans up the test environment

<a name="Contains"></a>
## func [Contains](<https://github.com/real-rm/gohelper/blob/main/helpers_array.go#L4>)

```go
func Contains[T comparable](slice []T, item T) bool
```

Contains checks if an item is in a slice

<a name="DateToTime"></a>
## func [DateToTime](<https://github.com/real-rm/gohelper/blob/main/helper_date.go#L24>)

```go
func DateToTime(dateInt int) time.Time
```

DateToTime converts a date integer in the format YYYYMMDD to a time.Time object. For example, 20150101 becomes January 1, 2015 00:00:00. Uses local timezone to match the local filesystem operations.

<a name="GenUUID"></a>
## func [GenUUID](<https://github.com/real-rm/gohelper/blob/main/helper_uuid.go#L12>)

```go
func GenUUID(length int) (string, error)
```

GenUUID generates a random UUID\-like string of specified length If length is less than or equal to 0, returns an empty string

<a name="IsEmail"></a>
## func [IsEmail](<https://github.com/real-rm/gohelper/blob/main/helper_string.go#L9>)

```go
func IsEmail(eml string) bool
```



<a name="SetRmbaseFileCfg"></a>
## func [SetRmbaseFileCfg](<https://github.com/real-rm/gohelper/blob/main/test_util.go#L141>)

```go
func SetRmbaseFileCfg(configPath string)
```

SetRmbaseFileCfg sets the RMBASE\_FILE\_CFG environment variable to the given config path

<a name="SetupTestEnv"></a>
## func [SetupTestEnv](<https://github.com/real-rm/gohelper/blob/main/test_util.go#L34>)

```go
func SetupTestEnv(opts TestOptions) error
```

SetupTestEnv initializes the test environment including config and logging

<a name="StrToTime"></a>
## func [StrToTime](<https://github.com/real-rm/gohelper/blob/main/helper_date.go#L17>)

```go
func StrToTime(s string) (time.Time, error)
```

StrToTime converts a string to a time.Time object. Uses local timezone to match the local filesystem operations.

<a name="TestUtilMain"></a>
## func [TestUtilMain](<https://github.com/real-rm/gohelper/blob/main/test_util.go#L171>)

```go
func TestUtilMain(m *testing.M, opts ...TestOptions)
```

TestUtilMain is a common test main function that can be used across different test files

<a name="TimeToDateInt"></a>
## func [TimeToDateInt](<https://github.com/real-rm/gohelper/blob/main/helper_date.go#L40>)

```go
func TimeToDateInt(timestamp time.Time) int
```

TimeToDateInt converts a time.Time object to a date integer in the format YYYYMMDD. For example, January 1, 2015 becomes 20150101.

<a name="TimeToStr"></a>
## func [TimeToStr](<https://github.com/real-rm/gohelper/blob/main/helper_date.go#L11>)

```go
func TimeToStr(t time.Time) string
```

TimeToStr converts a time.Time object to a string. Uses local timezone to match the local filesystem operations.

<a name="Interval"></a>
## type [Interval](<https://github.com/real-rm/gohelper/blob/main/helper_interval.go#L15-L18>)

define Interval struct

```go
type Interval struct {
    // contains filtered or unexported fields
}
```

<a name="StartInterval"></a>
### func [StartInterval](<https://github.com/real-rm/gohelper/blob/main/helper_interval.go#L21>)

```go
func StartInterval(intervalSec float64, task func()) *Interval
```

StartInterval \- start a timer, execute \`task\` function every \`intervalSec\` seconds \(supports fractional seconds\)

<a name="Interval.Stop"></a>
### func \(\*Interval\) [Stop](<https://github.com/real-rm/gohelper/blob/main/helper_interval.go#L43>)

```go
func (i *Interval) Stop()
```

Stop \- stop the timer

<a name="TestOptions"></a>
## type [TestOptions](<https://github.com/real-rm/gohelper/blob/main/test_util.go#L19-L24>)

TestOptions defines options for test environment setup

```go
type TestOptions struct {
    // UseEnvConfig indicates whether to use existing environment config file
    // If true, will use RMBASE_FILE_CFG environment variable
    // If false, will create temporary config file
    UseEnvConfig bool
}
```

<a name="DefaultTestOptions"></a>
### func [DefaultTestOptions](<https://github.com/real-rm/gohelper/blob/main/test_util.go#L27>)

```go
func DefaultTestOptions() TestOptions
```

DefaultTestOptions returns default test options

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)


---

### README:
# golog

A flexible and powerful logging package for Go applications that provides structured logging with multiple log levels and output formats.

## Features

- Multiple log levels (Debug, Verbose, Info, Warn, Error, Fatal)
- Structured logging with key-value pairs
- Support for both JSON and text log formats
- Separate log files for different log levels
- Automatic caller information for error logs
- Configurable log directory and file paths
- Built on top of Go's `slog` package for modern logging capabilities

## Installation

```bash
go get github.com/real-rm/golog
```

## Configuration

The package uses a TOML configuration format. Configure your logging settings in your config file:

```toml
[golog]
dir = "/var/log/myapp"      # Log directory
level = "info"             # Global log level (debug, info, warn, error)
verbose = "verbose.log"    # Verbose log file
info = "info.log"         # Info log file
error = "error.log"       # Error log file
format = "json"           # Log format (json or text)
```

## Usage

### Initialization

```go
import "github.com/real-rm/golog"

func main() {
    err := golog.InitLog()
    if err != nil {
        panic(err)
    }
}
```

### Basic Logging

```go
// Debug level logging
golog.Debug("Debug message", "key", "value")

// Verbose level logging
golog.Verbose("Verbose message", "key", "value")

// Info level logging
golog.Info("Info message", "key", "value")

// Warning level logging
golog.Warn("Warning message", "key", "value")

// Error level logging (includes caller information)
golog.Error("Error message", "key", "value")

// Fatal level logging (exits the program)
golog.Fatal("Fatal message", "key", "value")
```

### Formatted Logging

```go
// Debug with formatting
golog.Debugf("Debug message: %s", "formatted")

// Info with formatting
golog.Infof("Info message: %s", "formatted")

// Error with formatting
golog.Errorf("Error message: %s", "formatted")

// Fatal with formatting
golog.Fatalf("Fatal message: %s", "formatted")
```

## Log Levels

The package supports the following log levels:

- `Debug`: Detailed information for debugging
- `Verbose`: More detailed information than Info
- `Info`: General operational information
- `Warn`: Warning messages for potentially harmful situations
- `Error`: Error messages for failures
- `Fatal`: Critical errors that cause program termination

## Log Format

### JSON Format Example
```json
{"time":"2024-03-14T12:00:00Z","level":"INFO","msg":"Info message","key":"value"}
```

### Text Format Example
```
time=2024-03-14T12:00:00Z level=INFO msg="Info message" key=value
```

## Error Logging

Error logs automatically include caller information:
- File name
- Line number
- Function name

Example error log:
```json
{
    "time": "2024-03-14T12:00:00Z",
    "level": "ERROR",
    "msg": "Database connection failed",
    "error": "connection timeout",
    "file": "main.go",
    "line": 42
}
```

## Dependencies

- log/slog (Go standard library)
- github.com/real-rm/goconfig

## Best Practices

1. Always initialize the logger at the start of your application
2. Use appropriate log levels for different types of messages
3. Include relevant context using key-value pairs
4. Use structured logging for better log analysis
5. Configure separate log files for different levels to manage log volume
6. Use JSON format when logs need to be processed by log aggregation tools
7. Use text format for human-readable logs during development

## Example Configuration

```toml
[golog]
dir = "/var/log/myapp"
level = "info"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "json"
```

This configuration will:
- Store logs in `/var/log/myapp`
- Set the global log level to "info"
- Create separate files for verbose, info, and error logs
- Use JSON format for structured logging

```bash
export GOPRIVATE=*
vi ~/.gitconfig

```
add 
```
[url "ssh://**************"]
    insteadOf = https://github.com
```
### API.md:
<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# golog

```go
import "github.com/real-rm/golog"
```

## Index

- [Variables](<#variables>)
- [func Debug\(msg interface\{\}, args ...interface\{\}\)](<#Debug>)
- [func Debugf\(format string, args ...interface\{\}\)](<#Debugf>)
- [func Error\(msg interface\{\}, args ...interface\{\}\)](<#Error>)
- [func Errorf\(format string, args ...interface\{\}\)](<#Errorf>)
- [func Fatal\(msg string, args ...interface\{\}\)](<#Fatal>)
- [func Fatalf\(format string, args ...interface\{\}\)](<#Fatalf>)
- [func Info\(msg interface\{\}, args ...interface\{\}\)](<#Info>)
- [func Infof\(format string, args ...interface\{\}\)](<#Infof>)
- [func Init\(logFile string\) error](<#Init>)
- [func InitLog\(\) error](<#InitLog>)
- [func SetStdoutWriter\(w io.Writer\)](<#SetStdoutWriter>)
- [func Verbose\(msg interface\{\}, args ...interface\{\}\)](<#Verbose>)
- [func Verbosef\(format string, args ...interface\{\}\)](<#Verbosef>)
- [func Warn\(msg interface\{\}, args ...interface\{\}\)](<#Warn>)
- [func Warnf\(format string, args ...interface\{\}\)](<#Warnf>)


## Variables

<a name="Logger"></a>Logger is the global logger instance

```go
var (
    Logger *slog.Logger
)
```

<a name="Debug"></a>
## func [Debug](<https://github.com/real-rm/golog/blob/main/log.go#L284>)

```go
func Debug(msg interface{}, args ...interface{})
```

Debug logs a message at debug level

<a name="Debugf"></a>
## func [Debugf](<https://github.com/real-rm/golog/blob/main/log.go#L300>)

```go
func Debugf(format string, args ...interface{})
```

Debugf logs a formatted message at debug level

<a name="Error"></a>
## func [Error](<https://github.com/real-rm/golog/blob/main/log.go#L371>)

```go
func Error(msg interface{}, args ...interface{})
```

Error logs a message at error level

<a name="Errorf"></a>
## func [Errorf](<https://github.com/real-rm/golog/blob/main/log.go#L389>)

```go
func Errorf(format string, args ...interface{})
```

Errorf logs a formatted message at error level

<a name="Fatal"></a>
## func [Fatal](<https://github.com/real-rm/golog/blob/main/log.go#L395>)

```go
func Fatal(msg string, args ...interface{})
```

Fatal logs a message at error level

<a name="Fatalf"></a>
## func [Fatalf](<https://github.com/real-rm/golog/blob/main/log.go#L402>)

```go
func Fatalf(format string, args ...interface{})
```

Fatalf logs a formatted message at error level

<a name="Info"></a>
## func [Info](<https://github.com/real-rm/golog/blob/main/log.go#L326>)

```go
func Info(msg interface{}, args ...interface{})
```

Info logs a message at info level

<a name="Infof"></a>
## func [Infof](<https://github.com/real-rm/golog/blob/main/log.go#L342>)

```go
func Infof(format string, args ...interface{})
```

Infof logs a formatted message at info level

<a name="Init"></a>
## func [Init](<https://github.com/real-rm/golog/blob/main/log.go#L164>)

```go
func Init(logFile string) error
```

Init initializes the logger with the specified log file

<a name="InitLog"></a>
## func [InitLog](<https://github.com/real-rm/golog/blob/main/log.go#L174>)

```go
func InitLog() error
```

InitLog initializes the logger with default log file path

<a name="SetStdoutWriter"></a>
## func [SetStdoutWriter](<https://github.com/real-rm/golog/blob/main/log.go#L410>)

```go
func SetStdoutWriter(w io.Writer)
```

SetStdoutWriter sets the stdout writer for the logger

<a name="Verbose"></a>
## func [Verbose](<https://github.com/real-rm/golog/blob/main/log.go#L305>)

```go
func Verbose(msg interface{}, args ...interface{})
```

Verbose logs a message at info level \(alias for Info\)

<a name="Verbosef"></a>
## func [Verbosef](<https://github.com/real-rm/golog/blob/main/log.go#L321>)

```go
func Verbosef(format string, args ...interface{})
```

Verbosef logs a formatted message at info level \(alias for Infof\)

<a name="Warn"></a>
## func [Warn](<https://github.com/real-rm/golog/blob/main/log.go#L347>)

```go
func Warn(msg interface{}, args ...interface{})
```

Warn logs a message at warn level

<a name="Warnf"></a>
## func [Warnf](<https://github.com/real-rm/golog/blob/main/log.go#L365>)

```go
func Warnf(format string, args ...interface{})
```

Warnf logs a formatted message at warn level

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)


---

### README:
# go-mail

A flexible and feature-rich email service package for Go applications.

## Features

- Multiple email engine support:
  - Gmail SMTP
  - Amazon SES
  - Sendmail
  - RM Mail
  - Mock Mail (for testing)
- HTML and plain text email support
- Email validation
- Parameter replacement in email templates
- HTML to text conversion
- Email logging and tracking
- Priority email support
- Custom headers support
- Reply-to address support

## Installation

```bash
go mod init github.com/real-rm/gomail
go get github.com/real-rm/golog
```

## Quick Start

```go
package main

import (
    "github.com/real-rm/gomail"
)

func main() {
    // Get mailer instance
    mailer, err := gomail.GetSendMailObj()
    if err != nil {
        panic(err)
    }

    // Create email message
    msg := &gomail.EmailMessage{
        From:    "<EMAIL>",
        To:      []string{"<EMAIL>"},
        Subject: "Test Email",
        Text:    "This is a test email",
        HTML:    "<p>This is a test email</p>",
    }

    // Send email using Gmail engine
    err = mailer.SendMail("gmail", msg)
    if err != nil {
        panic(err)
    }
}
```

## Configuration

The package supports multiple email engines that can be configured through your application's configuration:

- Gmail SMTP
- Amazon SES
- Sendmail
- RM Mail
- Mock Mail (for testing)

Each engine can be configured with its own settings like SMTP credentials, rate limits, and default sender addresses.

## API Documentation

For detailed API documentation, please refer to the [api.md](api.md) file.

## Dependencies

- github.com/real-rm/golog - For logging
- github.com/real-rm/goconfig - For configuration management
- github.com/jaytaylor/html2text - For HTML to text conversion
- go.mongodb.org/mongo-driver - For email logging (optional)

## License

This project is licensed under the MIT License - see the LICENSE file for details. 

### API.md:
<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gomail

```go
import "github.com/real-rm/gomail"
```

## Index

- [Constants](<#constants>)
- [Variables](<#variables>)
- [func GetValidatedEmails\(ctx context.Context, emails \[\]string\) \(\[\]string, error\)](<#GetValidatedEmails>)
- [func LogEmail\(msg \*EmailMessage\) \(string, error\)](<#LogEmail>)
- [type BaseMailEngine](<#BaseMailEngine>)
  - [func NewBaseMailEngine\(engineName string\) \*BaseMailEngine](<#NewBaseMailEngine>)
  - [func \(e \*BaseMailEngine\) GetFromEmail\(\) string](<#BaseMailEngine.GetFromEmail>)
  - [func \(e \*BaseMailEngine\) GetName\(\) string](<#BaseMailEngine.GetName>)
  - [func \(e \*BaseMailEngine\) Init\(config map\[string\]interface\{\}\) error](<#BaseMailEngine.Init>)
  - [func \(e \*BaseMailEngine\) Send\(msg \*EmailMessage\) \(string, error\)](<#BaseMailEngine.Send>)
- [type EmailMessage](<#EmailMessage>)
- [type GomailEngine](<#GomailEngine>)
  - [func NewGomailEngine\(smtpConfig \*ServiceConfig\) \(\*GomailEngine, error\)](<#NewGomailEngine>)
  - [func \(e \*GomailEngine\) Init\(config map\[string\]interface\{\}\) error](<#GomailEngine.Init>)
  - [func \(e \*GomailEngine\) Send\(msg \*EmailMessage\) \(string, error\)](<#GomailEngine.Send>)
- [type MailEngine](<#MailEngine>)
- [type Mailer](<#Mailer>)
  - [func GetSendMailObj\(\) \(\*Mailer, error\)](<#GetSendMailObj>)
  - [func \(m \*Mailer\) CheckAndFixFrom\(engineName string, mail \*EmailMessage\) string](<#Mailer.CheckAndFixFrom>)
  - [func \(m \*Mailer\) GetEngine\(engineName string, msg \*EmailMessage\) MailEngine](<#Mailer.GetEngine>)
  - [func \(m \*Mailer\) GetName\(engineName string, msg \*EmailMessage\) string](<#Mailer.GetName>)
  - [func \(m \*Mailer\) NotifyAdmin\(msg string\) error](<#Mailer.NotifyAdmin>)
  - [func \(m \*Mailer\) SendMail\(engineName string, msg \*EmailMessage\) error](<#Mailer.SendMail>)
- [type MockMailEngine](<#MockMailEngine>)
  - [func NewMockMailEngine\(\) \*MockMailEngine](<#NewMockMailEngine>)
  - [func \(engine \*MockMailEngine\) GetMails\(subject string, email string\) \(EmailMessage, error\)](<#MockMailEngine.GetMails>)
  - [func \(e \*MockMailEngine\) Init\(config map\[string\]interface\{\}\) error](<#MockMailEngine.Init>)
  - [func \(engine \*MockMailEngine\) Reset\(\)](<#MockMailEngine.Reset>)
  - [func \(engine \*MockMailEngine\) Send\(msg \*EmailMessage\) \(string, error\)](<#MockMailEngine.Send>)
- [type RMMailEngine](<#RMMailEngine>)
  - [func NewRMMailEngine\(\) \*RMMailEngine](<#NewRMMailEngine>)
  - [func \(e \*RMMailEngine\) Init\(config map\[string\]interface\{\}\) error](<#RMMailEngine.Init>)
  - [func \(engine \*RMMailEngine\) Send\(msg \*EmailMessage\) \(string, error\)](<#RMMailEngine.Send>)
- [type SESEngine](<#SESEngine>)
  - [func NewSESEngine\(engineName string\) \*SESEngine](<#NewSESEngine>)
  - [func \(e \*SESEngine\) Init\(config map\[string\]interface\{\}\) error](<#SESEngine.Init>)
  - [func \(engine \*SESEngine\) Send\(msg \*EmailMessage\) \(string, error\)](<#SESEngine.Send>)
- [type SendmailEngine](<#SendmailEngine>)
  - [func NewSendmailEngine\(path string\) \*SendmailEngine](<#NewSendmailEngine>)
  - [func \(e \*SendmailEngine\) Init\(config map\[string\]interface\{\}\) error](<#SendmailEngine.Init>)
  - [func \(e \*SendmailEngine\) Send\(msg \*EmailMessage\) \(string, error\)](<#SendmailEngine.Send>)
- [type ServiceConfig](<#ServiceConfig>)


## Constants

<a name="DefaultFromEmail"></a>

```go
const (
    // Default values
    DefaultFromEmail = "<EMAIL>"
    DefaultFromName  = "RealMaster"
)
```

<a name="RM_MAIL"></a>

```go
const (
    RM_MAIL               = "rmMail"
    GMAIL                 = "gmail"
    SES_ENGINE            = "SES"
    SEND_MAIL             = "sendMail"
    MOCK_MAIL             = "mockmail"
    DEFAULT_FROM_NAME     = "RealMaster"
    GMAIL_HOST            = "smtp.gmail.com"
    GMAIL_PORT            = 587
    DEFAULT_SENDMAIL_PATH = "/usr/sbin/sendmail"
)
```

## Variables

<a name="ENGINES"></a>

```go
var (
    ENGINES            []string
    HOST_NAME          string
    EMAIL_ENGINE_MAP   map[string]map[string]interface{}
    VALID_EMAIL_REGEXP = regexp.MustCompile(`^[a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,}$`)
)
```

<a name="GetValidatedEmails"></a>
## func [GetValidatedEmails](<https://github.com/real-rm/gomail/blob/main/sendMailValidate.go#L153>)

```go
func GetValidatedEmails(ctx context.Context, emails []string) ([]string, error)
```



<a name="LogEmail"></a>
## func [LogEmail](<https://github.com/real-rm/gomail/blob/main/mail.go#L274>)

```go
func LogEmail(msg *EmailMessage) (string, error)
```

LogEmail logs the email to the database

<a name="BaseMailEngine"></a>
## type [BaseMailEngine](<https://github.com/real-rm/gomail/blob/main/baseMailEngine.go#L22-L25>)

BaseMailEngine provides common functionality for all mail engines

```go
type BaseMailEngine struct {
    // contains filtered or unexported fields
}
```

<a name="NewBaseMailEngine"></a>
### func [NewBaseMailEngine](<https://github.com/real-rm/gomail/blob/main/baseMailEngine.go#L27>)

```go
func NewBaseMailEngine(engineName string) *BaseMailEngine
```



<a name="BaseMailEngine.GetFromEmail"></a>
### func \(\*BaseMailEngine\) [GetFromEmail](<https://github.com/real-rm/gomail/blob/main/baseMailEngine.go#L56>)

```go
func (e *BaseMailEngine) GetFromEmail() string
```

GetFromEmail returns the from email address

<a name="BaseMailEngine.GetName"></a>
### func \(\*BaseMailEngine\) [GetName](<https://github.com/real-rm/gomail/blob/main/baseMailEngine.go#L51>)

```go
func (e *BaseMailEngine) GetName() string
```

GetName returns the engine name

<a name="BaseMailEngine.Init"></a>
### func \(\*BaseMailEngine\) [Init](<https://github.com/real-rm/gomail/blob/main/baseMailEngine.go#L34>)

```go
func (e *BaseMailEngine) Init(config map[string]interface{}) error
```

Init initializes the base mail engine

<a name="BaseMailEngine.Send"></a>
### func \(\*BaseMailEngine\) [Send](<https://github.com/real-rm/gomail/blob/main/baseMailEngine.go#L45>)

```go
func (e *BaseMailEngine) Send(msg *EmailMessage) (string, error)
```

Send implements the MailEngine interface for BaseMailEngine

<a name="EmailMessage"></a>
## type [EmailMessage](<https://github.com/real-rm/gomail/blob/main/mail.go#L32-L50>)

EmailMessage represents an email to be sent

```go
type EmailMessage struct {
    From     string            `json:"from"`
    To       []string          `json:"to"`
    Subject  string            `json:"subject"`
    Text     string            `json:"text,omitempty"`
    HTML     string            `json:"html,omitempty"`
    ReplyTo  string            `json:"replyTo,omitempty"`
    Headers  map[string]string `json:"headers"`
    Priority bool              `json:"priority,omitempty"`
    Engine   string            `json:"-"`

    SetName        string    `json:"setName,omitempty"`
    EventType      string    `json:"eventType,omitempty"`
    LogID          string    `json:"logID,omitempty"`
    ListID         string    `json:"listID,omitempty"`
    Ts             time.Time `json:"ts,omitempty"`
    IsNeedValidate bool      `json:"-"`
    // contains filtered or unexported fields
}
```

<a name="GomailEngine"></a>
## type [GomailEngine](<https://github.com/real-rm/gomail/blob/main/gmailEngine.go#L9-L12>)



```go
type GomailEngine struct {
    BaseMailEngine
    // contains filtered or unexported fields
}
```

<a name="NewGomailEngine"></a>
### func [NewGomailEngine](<https://github.com/real-rm/gomail/blob/main/gmailEngine.go#L14>)

```go
func NewGomailEngine(smtpConfig *ServiceConfig) (*GomailEngine, error)
```



<a name="GomailEngine.Init"></a>
### func \(\*GomailEngine\) [Init](<https://github.com/real-rm/gomail/blob/main/gmailEngine.go#L28>)

```go
func (e *GomailEngine) Init(config map[string]interface{}) error
```



<a name="GomailEngine.Send"></a>
### func \(\*GomailEngine\) [Send](<https://github.com/real-rm/gomail/blob/main/gmailEngine.go#L32>)

```go
func (e *GomailEngine) Send(msg *EmailMessage) (string, error)
```



<a name="MailEngine"></a>
## type [MailEngine](<https://github.com/real-rm/gomail/blob/main/baseMailEngine.go#L16-L19>)

MailEngine defines the interface that all mail engines must implement

```go
type MailEngine interface {
    Init(config map[string]interface{}) error
    Send(msg *EmailMessage) (string, error)
}
```

<a name="Mailer"></a>
## type [Mailer](<https://github.com/real-rm/gomail/blob/main/mail.go#L53-L81>)

Mailer is the main struct for sending emails

```go
type Mailer struct {
    // Engine configurations
    Sendmail     MailEngine
    SendmailFrom string
    SendmailItvl int

    Gmail     MailEngine
    GmailFrom string
    GmailItvl int

    RMMail     MailEngine
    RMMailFrom string

    SES     MailEngine
    SESH    MailEngine
    SESFrom string
    SESItvl int

    Dummy     MailEngine
    DummyFrom string
    DummyItvl int

    // Helper functions
    ReplaceParam         func(mail *EmailMessage, data map[string]string) *EmailMessage
    HTMLToText           func(string) string
    GetEngines           func() []string
    GetFromEmailByEngine func(engineName string) string
    MockMail             MailEngine
}
```

<a name="GetSendMailObj"></a>
### func [GetSendMailObj](<https://github.com/real-rm/gomail/blob/main/mail.go#L147>)

```go
func GetSendMailObj() (*Mailer, error)
```

GetSendMailObj returns the sendMail object with all configured engines

<a name="Mailer.CheckAndFixFrom"></a>
### func \(\*Mailer\) [CheckAndFixFrom](<https://github.com/real-rm/gomail/blob/main/mail.go#L222>)

```go
func (m *Mailer) CheckAndFixFrom(engineName string, mail *EmailMessage) string
```

CheckAndFixFrom checks and fixes the from email address for the specified engine

<a name="Mailer.GetEngine"></a>
### func \(\*Mailer\) [GetEngine](<https://github.com/real-rm/gomail/blob/main/mail.go#L403>)

```go
func (m *Mailer) GetEngine(engineName string, msg *EmailMessage) MailEngine
```

GetEngine returns the engine for the specified engine name

<a name="Mailer.GetName"></a>
### func \(\*Mailer\) [GetName](<https://github.com/real-rm/gomail/blob/main/mail.go#L238>)

```go
func (m *Mailer) GetName(engineName string, msg *EmailMessage) string
```

GetName returns the name of the engine

<a name="Mailer.NotifyAdmin"></a>
### func \(\*Mailer\) [NotifyAdmin](<https://github.com/real-rm/gomail/blob/main/mail.go#L254>)

```go
func (m *Mailer) NotifyAdmin(msg string) error
```

NotifyAdmin sends a notification email to the admin

<a name="Mailer.SendMail"></a>
### func \(\*Mailer\) [SendMail](<https://github.com/real-rm/gomail/blob/main/mail.go#L296>)

```go
func (m *Mailer) SendMail(engineName string, msg *EmailMessage) error
```

SendMail sends an email using the specified engine

<a name="MockMailEngine"></a>
## type [MockMailEngine](<https://github.com/real-rm/gomail/blob/main/mockMailEngine.go#L13-L17>)

MockMailEngine is a mock mail engine for testing

```go
type MockMailEngine struct {
    BaseMailEngine
    Verbose    int
    SEND_MAILS []EmailMessage
}
```

<a name="NewMockMailEngine"></a>
### func [NewMockMailEngine](<https://github.com/real-rm/gomail/blob/main/mockMailEngine.go#L20>)

```go
func NewMockMailEngine() *MockMailEngine
```

NewMockMailEngine creates a new mock mail engine

<a name="MockMailEngine.GetMails"></a>
### func \(\*MockMailEngine\) [GetMails](<https://github.com/real-rm/gomail/blob/main/mockMailEngine.go#L61>)

```go
func (engine *MockMailEngine) GetMails(subject string, email string) (EmailMessage, error)
```

GetMails gets emails from the mock engine

<a name="MockMailEngine.Init"></a>
### func \(\*MockMailEngine\) [Init](<https://github.com/real-rm/gomail/blob/main/mockMailEngine.go#L26>)

```go
func (e *MockMailEngine) Init(config map[string]interface{}) error
```



<a name="MockMailEngine.Reset"></a>
### func \(\*MockMailEngine\) [Reset](<https://github.com/real-rm/gomail/blob/main/mockMailEngine.go#L85>)

```go
func (engine *MockMailEngine) Reset()
```



<a name="MockMailEngine.Send"></a>
### func \(\*MockMailEngine\) [Send](<https://github.com/real-rm/gomail/blob/main/mockMailEngine.go#L36>)

```go
func (engine *MockMailEngine) Send(msg *EmailMessage) (string, error)
```

Send implements the MailEngine interface for MockMailEngine

<a name="RMMailEngine"></a>
## type [RMMailEngine](<https://github.com/real-rm/gomail/blob/main/rmMailEngine.go#L15-L19>)

RMMailEngine implements the RealMaster mail service

```go
type RMMailEngine struct {
    BaseMailEngine
    // contains filtered or unexported fields
}
```

<a name="NewRMMailEngine"></a>
### func [NewRMMailEngine](<https://github.com/real-rm/gomail/blob/main/rmMailEngine.go#L22>)

```go
func NewRMMailEngine() *RMMailEngine
```

NewRMMailEngine creates a new RealMaster mail engine

<a name="RMMailEngine.Init"></a>
### func \(\*RMMailEngine\) [Init](<https://github.com/real-rm/gomail/blob/main/rmMailEngine.go#L30>)

```go
func (e *RMMailEngine) Init(config map[string]interface{}) error
```

Init implements the MailEngine interface for RMMailEngine

<a name="RMMailEngine.Send"></a>
### func \(\*RMMailEngine\) [Send](<https://github.com/real-rm/gomail/blob/main/rmMailEngine.go#L44>)

```go
func (engine *RMMailEngine) Send(msg *EmailMessage) (string, error)
```

Send implements the MailEngine interface for RMMailEngine

<a name="SESEngine"></a>
## type [SESEngine](<https://github.com/real-rm/gomail/blob/main/sesEngine.go#L14-L19>)

SESEngine implements the AWS SES mail service

```go
type SESEngine struct {
    BaseMailEngine
    // contains filtered or unexported fields
}
```

<a name="NewSESEngine"></a>
### func [NewSESEngine](<https://github.com/real-rm/gomail/blob/main/sesEngine.go#L22>)

```go
func NewSESEngine(engineName string) *SESEngine
```

NewSESEngine creates a new AWS SES mail engine

<a name="SESEngine.Init"></a>
### func \(\*SESEngine\) [Init](<https://github.com/real-rm/gomail/blob/main/sesEngine.go#L29>)

```go
func (e *SESEngine) Init(config map[string]interface{}) error
```

Init implements the MailEngine interface for SESEngine

<a name="SESEngine.Send"></a>
### func \(\*SESEngine\) [Send](<https://github.com/real-rm/gomail/blob/main/sesEngine.go#L65>)

```go
func (engine *SESEngine) Send(msg *EmailMessage) (string, error)
```

Send implements the MailEngine interface for SESEngine

<a name="SendmailEngine"></a>
## type [SendmailEngine](<https://github.com/real-rm/gomail/blob/main/sendmailEngine.go#L12-L14>)



```go
type SendmailEngine struct {
    Path string
}
```

<a name="NewSendmailEngine"></a>
### func [NewSendmailEngine](<https://github.com/real-rm/gomail/blob/main/sendmailEngine.go#L16>)

```go
func NewSendmailEngine(path string) *SendmailEngine
```



<a name="SendmailEngine.Init"></a>
### func \(\*SendmailEngine\) [Init](<https://github.com/real-rm/gomail/blob/main/sendmailEngine.go#L25>)

```go
func (e *SendmailEngine) Init(config map[string]interface{}) error
```



<a name="SendmailEngine.Send"></a>
### func \(\*SendmailEngine\) [Send](<https://github.com/real-rm/gomail/blob/main/sendmailEngine.go#L29>)

```go
func (e *SendmailEngine) Send(msg *EmailMessage) (string, error)
```



<a name="ServiceConfig"></a>
## type [ServiceConfig](<https://github.com/real-rm/gomail/blob/main/mail_utils.go#L208-L214>)

ServiceConfig holds the extracted service config

```go
type ServiceConfig struct {
    Service string
    User    string
    Pass    string
    Host    string
    Port    int
}
```

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)


---

### README:
# go-mongo

A Go language MongoDB API wrapper that provides a simple and efficient way to interact with MongoDB databases.

## Features

- Simple and intuitive API for MongoDB operations
- Automatic timestamp management for documents
- Configurable database connections
- Predefined collection support
- Comprehensive CRUD operations
- Support for MongoDB aggregation and change streams
- Built-in logging and performance monitoring

## Installation

```bash
go get github.com/real-rm/gomongo
```

## Configuration

The package uses a configuration system that supports TOML format. You need to configure your MongoDB connections and predefined collections in your config file:

```toml
[dbs]
verbose = 1  # Logging verbosity level
[dbs.mydb]
uri = "mongodb://localhost:27017/mydatabase"


```

## Usage

### Initialization

```go
import "github.com/real-rm/gomongo"

func main() {
    err := gomongo.InitMongoDB()
    if err != nil {
        log.Fatal(err)
    }
}
```

### Basic Operations

#### Get a Collection

```go
// Get a collection directly
collection := gomongo.Coll("mydb", "users")

// Or get a database first
db := gomongo.Database("mydb")
collection := db.Coll("users")
```

#### Insert Documents

```go
ctx := context.Background()
doc := bson.M{"name": "John", "age": 30}
result, err := collection.InsertOne(ctx, doc)
```

#### Find Documents

```go
// Find with options
opts := []interface{}{
    gomongo.QueryOptions{
        Projection: bson.M{"name": 1},
        Sort:      bson.M{"age": -1},
        Limit:     10,
        Skip:      0,
    },
}
cursor, err := collection.Find(ctx, bson.M{"age": bson.M{"$gt": 18}}, opts...)
```

#### Update Documents

```go
filter := bson.M{"name": "John"}
update := bson.M{"$set": bson.M{"age": 31}}
result, err := collection.UpdateOne(ctx, filter, update)
```

#### Delete Documents

```go
filter := bson.M{"name": "John"}
result, err := collection.DeleteOne(ctx, filter)
```

## API Reference

### Collection Methods

- `InsertOne(ctx context.Context, document interface{}, opts ...*options.InsertOneOptions)`
- `InsertMany(ctx context.Context, documents []interface{}, opts ...*options.InsertManyOptions)`
- `Find(ctx context.Context, filter interface{}, opts ...interface{})`
- `FindOne(ctx context.Context, filter interface{}, opts ...interface{})`
- `UpdateOne(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions)`
- `UpdateMany(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions)`
- `DeleteOne(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions)`
- `DeleteMany(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions)`
- `FindOneAndUpdate(ctx context.Context, filter interface{}, update interface{}, opts ...*options.FindOneAndUpdateOptions)`
- `ReplaceOne(ctx context.Context, filter interface{}, replacement interface{}, opts ...*options.ReplaceOptions)`
- `FindOneAndReplace(ctx context.Context, filter interface{}, replacement interface{}, opts ...*options.FindOneAndReplaceOptions)`
- `Distinct(ctx context.Context, fieldName string, filter interface{}, opts ...*options.DistinctOptions)`
- `CountDocuments(ctx context.Context, filter interface{}, opts ...*options.CountOptions)`
- `EstimatedDocumentCount(ctx context.Context, opts ...*options.EstimatedDocumentCountOptions)`
- `CreateIndex(ctx context.Context, model mongo.IndexModel, opts ...*options.CreateIndexesOptions)`
- `CreateIndexes(ctx context.Context, models []mongo.IndexModel, opts ...*options.CreateIndexesOptions)`
- `DropIndex(ctx context.Context, name string, opts ...*options.DropIndexesOptions)`
- `Aggregate(ctx context.Context, pipeline interface{}, opts ...*options.AggregateOptions)`
- `Watch(ctx context.Context, pipeline interface{}, opts ...*options.ChangeStreamOptions)`
- `Rename(ctx context.Context, newName string)`
- `FindToArray(ctx context.Context, filter interface{}, opts ...interface{})`
- `Drop(ctx context.Context)`
- `BulkWrite(ctx context.Context, models []mongo.WriteModel, opts ...*options.BulkWriteOptions)`

### Query Options

```go
type QueryOptions struct {
    Projection interface{}  // Fields to return
    Sort       interface{}  // Sort order
    Limit      int64       // Maximum number of documents to return
    Skip       int64       // Number of documents to skip
}
```

## Automatic Timestamps

The package automatically adds timestamps to documents:
- `_ts`: Creation timestamp
- `_mt`: Last modification timestamp

## Error Handling

All operations return errors that should be checked:

```go
result, err := collection.InsertOne(ctx, doc)
if err != nil {
    log.Printf("Error inserting document: %v", err)
    return
}
```

## Performance Monitoring

The package includes built-in performance monitoring that logs slow operations. The verbosity level can be configured in the database configuration.

## Dependencies

- go.mongodb.org/mongo-driver/mongo
- github.com/real-rm/goconfig
- github.com/real-rm/golog

### API.md:
<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gomongo

```go
import "github.com/real-rm/gomongo"
```

## Index

- [func InitMongoDB\(\) error](<#InitMongoDB>)
- [func ListCollections\(dbName string\) \(\[\]string, error\)](<#ListCollections>)
- [func ToBSONDoc\(v interface\{\}\) \(bson.M, error\)](<#ToBSONDoc>)
- [type MongoCollection](<#MongoCollection>)
  - [func Coll\(dbName, collName string\) \*MongoCollection](<#Coll>)
  - [func PreDefColl\(collName string\) \*MongoCollection](<#PreDefColl>)
  - [func \(mc \*MongoCollection\) Aggregate\(ctx context.Context, pipeline interface\{\}, opts ...\*options.AggregateOptions\) \(\*mongo.Cursor, error\)](<#MongoCollection.Aggregate>)
  - [func \(mc \*MongoCollection\) BulkWrite\(ctx context.Context, models \[\]mongo.WriteModel, opts ...\*options.BulkWriteOptions\) \(\*mongo.BulkWriteResult, error\)](<#MongoCollection.BulkWrite>)
  - [func \(mc \*MongoCollection\) CountDocuments\(ctx context.Context, filter interface\{\}, opts ...\*options.CountOptions\) \(int64, error\)](<#MongoCollection.CountDocuments>)
  - [func \(mc \*MongoCollection\) CreateIndex\(ctx context.Context, model mongo.IndexModel, opts ...\*options.CreateIndexesOptions\) \(string, error\)](<#MongoCollection.CreateIndex>)
  - [func \(mc \*MongoCollection\) CreateIndexes\(ctx context.Context, models \[\]mongo.IndexModel, opts ...\*options.CreateIndexesOptions\) \(\[\]string, error\)](<#MongoCollection.CreateIndexes>)
  - [func \(mc \*MongoCollection\) DBName\(\) string](<#MongoCollection.DBName>)
  - [func \(mc \*MongoCollection\) DeleteMany\(ctx context.Context, filter interface\{\}, opts ...\*options.DeleteOptions\) \(\*mongo.DeleteResult, error\)](<#MongoCollection.DeleteMany>)
  - [func \(mc \*MongoCollection\) DeleteOne\(ctx context.Context, filter interface\{\}, opts ...\*options.DeleteOptions\) \(\*mongo.DeleteResult, error\)](<#MongoCollection.DeleteOne>)
  - [func \(mc \*MongoCollection\) Distinct\(ctx context.Context, fieldName string, filter interface\{\}, opts ...\*options.DistinctOptions\) \(\[\]interface\{\}, error\)](<#MongoCollection.Distinct>)
  - [func \(mc \*MongoCollection\) Drop\(ctx context.Context\) error](<#MongoCollection.Drop>)
  - [func \(mc \*MongoCollection\) DropIndex\(ctx context.Context, name string, opts ...\*options.DropIndexesOptions\) error](<#MongoCollection.DropIndex>)
  - [func \(mc \*MongoCollection\) EstimatedDocumentCount\(ctx context.Context, opts ...\*options.EstimatedDocumentCountOptions\) \(int64, error\)](<#MongoCollection.EstimatedDocumentCount>)
  - [func \(mc \*MongoCollection\) Find\(ctx context.Context, filter interface\{\}, opts ...interface\{\}\) \(\*mongo.Cursor, error\)](<#MongoCollection.Find>)
  - [func \(mc \*MongoCollection\) FindOne\(ctx context.Context, filter interface\{\}, opts ...interface\{\}\) \*mongo.SingleResult](<#MongoCollection.FindOne>)
  - [func \(mc \*MongoCollection\) FindOneAndReplace\(ctx context.Context, filter interface\{\}, replacement interface\{\}, opts ...\*options.FindOneAndReplaceOptions\) \*mongo.SingleResult](<#MongoCollection.FindOneAndReplace>)
  - [func \(mc \*MongoCollection\) FindOneAndUpdate\(ctx context.Context, filter interface\{\}, update interface\{\}, opts ...\*options.FindOneAndUpdateOptions\) \*mongo.SingleResult](<#MongoCollection.FindOneAndUpdate>)
  - [func \(mc \*MongoCollection\) FindToArray\(ctx context.Context, filter interface\{\}, opts ...interface\{\}\) \(\[\]bson.M, error\)](<#MongoCollection.FindToArray>)
  - [func \(mc \*MongoCollection\) InsertMany\(ctx context.Context, documents \[\]interface\{\}, opts ...\*options.InsertManyOptions\) \(\*mongo.InsertManyResult, error\)](<#MongoCollection.InsertMany>)
  - [func \(mc \*MongoCollection\) InsertOne\(ctx context.Context, document interface\{\}, opts ...\*options.InsertOneOptions\) \(\*mongo.InsertOneResult, error\)](<#MongoCollection.InsertOne>)
  - [func \(mc \*MongoCollection\) Name\(\) string](<#MongoCollection.Name>)
  - [func \(mc \*MongoCollection\) Rename\(ctx context.Context, newName string\) error](<#MongoCollection.Rename>)
  - [func \(mc \*MongoCollection\) ReplaceOne\(ctx context.Context, filter interface\{\}, replacement interface\{\}, opts ...\*options.ReplaceOptions\) \(\*mongo.UpdateResult, error\)](<#MongoCollection.ReplaceOne>)
  - [func \(mc \*MongoCollection\) UpdateMany\(ctx context.Context, filter interface\{\}, update interface\{\}, opts ...\*options.UpdateOptions\) \(\*mongo.UpdateResult, error\)](<#MongoCollection.UpdateMany>)
  - [func \(mc \*MongoCollection\) UpdateOne\(ctx context.Context, filter interface\{\}, update interface\{\}, opts ...\*options.UpdateOptions\) \(\*mongo.UpdateResult, error\)](<#MongoCollection.UpdateOne>)
  - [func \(mc \*MongoCollection\) Watch\(ctx context.Context, pipeline interface\{\}, opts ...\*options.ChangeStreamOptions\) \(\*mongo.ChangeStream, error\)](<#MongoCollection.Watch>)
- [type QueryOptions](<#QueryOptions>)


<a name="InitMongoDB"></a>
## func [InitMongoDB](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L65>)

```go
func InitMongoDB() error
```

InitMongoDB initializes MongoDB connection using configuration

<a name="ListCollections"></a>
## func [ListCollections](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L679>)

```go
func ListCollections(dbName string) ([]string, error)
```



<a name="ToBSONDoc"></a>
## func [ToBSONDoc](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L657>)

```go
func ToBSONDoc(v interface{}) (bson.M, error)
```

ToBSONDoc converts a struct to a BSON document using struct tags. It marshals and unmarshals the struct to ensure BSON tags are properly applied.

<a name="MongoCollection"></a>
## type [MongoCollection](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L41-L43>)



```go
type MongoCollection struct {
    // contains filtered or unexported fields
}
```

<a name="Coll"></a>
### func [Coll](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L203>)

```go
func Coll(dbName, collName string) *MongoCollection
```

Coll returns a wrapped MongoDB collection

<a name="PreDefColl"></a>
### func [PreDefColl](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L213>)

```go
func PreDefColl(collName string) *MongoCollection
```

PreDefColl returns a wrapped MongoDB collection

<a name="MongoCollection.Aggregate"></a>
### func \(\*MongoCollection\) [Aggregate](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L556>)

```go
func (mc *MongoCollection) Aggregate(ctx context.Context, pipeline interface{}, opts ...*options.AggregateOptions) (*mongo.Cursor, error)
```



<a name="MongoCollection.BulkWrite"></a>
### func \(\*MongoCollection\) [BulkWrite](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L648>)

```go
func (mc *MongoCollection) BulkWrite(ctx context.Context, models []mongo.WriteModel, opts ...*options.BulkWriteOptions) (*mongo.BulkWriteResult, error)
```



<a name="MongoCollection.CountDocuments"></a>
### func \(\*MongoCollection\) [CountDocuments](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L521>)

```go
func (mc *MongoCollection) CountDocuments(ctx context.Context, filter interface{}, opts ...*options.CountOptions) (int64, error)
```



<a name="MongoCollection.CreateIndex"></a>
### func \(\*MongoCollection\) [CreateIndex](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L535>)

```go
func (mc *MongoCollection) CreateIndex(ctx context.Context, model mongo.IndexModel, opts ...*options.CreateIndexesOptions) (string, error)
```



<a name="MongoCollection.CreateIndexes"></a>
### func \(\*MongoCollection\) [CreateIndexes](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L542>)

```go
func (mc *MongoCollection) CreateIndexes(ctx context.Context, models []mongo.IndexModel, opts ...*options.CreateIndexesOptions) ([]string, error)
```



<a name="MongoCollection.DBName"></a>
### func \(\*MongoCollection\) [DBName](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L239>)

```go
func (mc *MongoCollection) DBName() string
```

DBName returns the name of the database that contains this collection

<a name="MongoCollection.DeleteMany"></a>
### func \(\*MongoCollection\) [DeleteMany](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L472>)

```go
func (mc *MongoCollection) DeleteMany(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) (*mongo.DeleteResult, error)
```



<a name="MongoCollection.DeleteOne"></a>
### func \(\*MongoCollection\) [DeleteOne](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L465>)

```go
func (mc *MongoCollection) DeleteOne(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) (*mongo.DeleteResult, error)
```



<a name="MongoCollection.Distinct"></a>
### func \(\*MongoCollection\) [Distinct](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L514>)

```go
func (mc *MongoCollection) Distinct(ctx context.Context, fieldName string, filter interface{}, opts ...*options.DistinctOptions) ([]interface{}, error)
```



<a name="MongoCollection.Drop"></a>
### func \(\*MongoCollection\) [Drop](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L641>)

```go
func (mc *MongoCollection) Drop(ctx context.Context) error
```



<a name="MongoCollection.DropIndex"></a>
### func \(\*MongoCollection\) [DropIndex](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L549>)

```go
func (mc *MongoCollection) DropIndex(ctx context.Context, name string, opts ...*options.DropIndexesOptions) error
```



<a name="MongoCollection.EstimatedDocumentCount"></a>
### func \(\*MongoCollection\) [EstimatedDocumentCount](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L528>)

```go
func (mc *MongoCollection) EstimatedDocumentCount(ctx context.Context, opts ...*options.EstimatedDocumentCountOptions) (int64, error)
```



<a name="MongoCollection.Find"></a>
### func \(\*MongoCollection\) [Find](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L422>)

```go
func (mc *MongoCollection) Find(ctx context.Context, filter interface{}, opts ...interface{}) (*mongo.Cursor, error)
```



<a name="MongoCollection.FindOne"></a>
### func \(\*MongoCollection\) [FindOne](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L445>)

```go
func (mc *MongoCollection) FindOne(ctx context.Context, filter interface{}, opts ...interface{}) *mongo.SingleResult
```



<a name="MongoCollection.FindOneAndReplace"></a>
### func \(\*MongoCollection\) [FindOneAndReplace](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L507>)

```go
func (mc *MongoCollection) FindOneAndReplace(ctx context.Context, filter interface{}, replacement interface{}, opts ...*options.FindOneAndReplaceOptions) *mongo.SingleResult
```



<a name="MongoCollection.FindOneAndUpdate"></a>
### func \(\*MongoCollection\) [FindOneAndUpdate](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L479>)

```go
func (mc *MongoCollection) FindOneAndUpdate(ctx context.Context, filter interface{}, update interface{}, opts ...*options.FindOneAndUpdateOptions) *mongo.SingleResult
```



<a name="MongoCollection.FindToArray"></a>
### func \(\*MongoCollection\) [FindToArray](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L586>)

```go
func (mc *MongoCollection) FindToArray(ctx context.Context, filter interface{}, opts ...interface{}) ([]bson.M, error)
```

FindToArray executes a find operation and returns the results as a slice of bson.M

<a name="MongoCollection.InsertMany"></a>
### func \(\*MongoCollection\) [InsertMany](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L625>)

```go
func (mc *MongoCollection) InsertMany(ctx context.Context, documents []interface{}, opts ...*options.InsertManyOptions) (*mongo.InsertManyResult, error)
```



<a name="MongoCollection.InsertOne"></a>
### func \(\*MongoCollection\) [InsertOne](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L368>)

```go
func (mc *MongoCollection) InsertOne(ctx context.Context, document interface{}, opts ...*options.InsertOneOptions) (*mongo.InsertOneResult, error)
```

Collection operation wrappers with timing and timestamps

<a name="MongoCollection.Name"></a>
### func \(\*MongoCollection\) [Name](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L234>)

```go
func (mc *MongoCollection) Name() string
```

Name returns the name of the collection

<a name="MongoCollection.Rename"></a>
### func \(\*MongoCollection\) [Rename](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L570>)

```go
func (mc *MongoCollection) Rename(ctx context.Context, newName string) error
```



<a name="MongoCollection.ReplaceOne"></a>
### func \(\*MongoCollection\) [ReplaceOne](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L500>)

```go
func (mc *MongoCollection) ReplaceOne(ctx context.Context, filter interface{}, replacement interface{}, opts ...*options.ReplaceOptions) (*mongo.UpdateResult, error)
```



<a name="MongoCollection.UpdateMany"></a>
### func \(\*MongoCollection\) [UpdateMany](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L401>)

```go
func (mc *MongoCollection) UpdateMany(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error)
```



<a name="MongoCollection.UpdateOne"></a>
### func \(\*MongoCollection\) [UpdateOne](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L380>)

```go
func (mc *MongoCollection) UpdateOne(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error)
```



<a name="MongoCollection.Watch"></a>
### func \(\*MongoCollection\) [Watch](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L563>)

```go
func (mc *MongoCollection) Watch(ctx context.Context, pipeline interface{}, opts ...*options.ChangeStreamOptions) (*mongo.ChangeStream, error)
```



<a name="QueryOptions"></a>
## type [QueryOptions](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L45-L51>)



```go
type QueryOptions struct {
    Projection interface{}
    Sort       interface{}
    Limit      int64
    Skip       int64
}
```

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)


---

### README:
# gooauth
go lang oauth utils for appweb


负责oauth相关util, 包括jwt验证部分，（不需要session）

登录注册需要用模块goauth


---

### README:
# goprocess

---

### README:
# goresodownload

---

### README:
# GoSMS

GoSMS is a Go package that provides a simple and flexible interface for sending SMS messages. It currently supports Twilio as a messaging provider.

## Features

- Simple and clean interface for sending SMS messages
- Support for Twilio SMS provider
- Easy to extend with new providers
- Comprehensive error handling
- Detailed logging

## Installation

```bash
go get github.com/real-rm/gosms
```

## Usage

### Basic Usage with Twilio

```go
package main

import (
	"github.com/real-rm/gosms"
)

func main() {
	// Create a new Twilio engine with your credentials
	engine, err := gosms.NewTwilioEngine("your-account-sid", "your-auth-token")
	if err != nil {
		panic(err)
	}
	
	// Create an SMS sender with the engine
	sender, err := gosms.NewSMSSender(engine)
	if err != nil {
		panic(err)
	}
	
	// Send an SMS
	opt := gosms.SendOption{
		To:      "+**********",
		From:    "+**********",
		Message: "Hello from GoSMS!",
	}
	
	err = sender.Send(opt)
	if err != nil {
		panic(err)
	}
}
```

### Creating Custom Providers

You can create custom SMS providers by implementing the `Sender` interface:

```go
type Sender interface {
	Send(to string, message string, from string) error
}
```

## Error Handling

The package provides comprehensive error handling:

- Invalid send options (empty to/from/message)
- Provider-specific errors
- Missing provider configuration

## Logging

The package uses the `golog` package for logging. It logs:
- Successful message delivery
- Provider responses
- Error conditions

## Testing

The library has high test coverage. Run the tests with:

```bash
go test -cover
```

## License

MIT License
### API.md:
<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gosms

```go
import "github.com/real-rm/gosms"
```

## Index

- [type SMSSender](<#SMSSender>)
  - [func NewSMSSender\(s Sender\) \(\*SMSSender, error\)](<#NewSMSSender>)
  - [func \(s \*SMSSender\) Send\(opt SendOption\) error](<#SMSSender.Send>)
- [type SendOption](<#SendOption>)
- [type Sender](<#Sender>)
  - [func NewTwilioEngine\(accountSID, authToken string\) \(Sender, error\)](<#NewTwilioEngine>)
- [type TwilioClient](<#TwilioClient>)
- [type TwilioEngine](<#TwilioEngine>)
  - [func \(t \*TwilioEngine\) Send\(to string, message string, from string\) error](<#TwilioEngine.Send>)


<a name="SMSSender"></a>
## type [SMSSender](<https://github.com/real-rm/gosms/blob/main/gosms.go#L23-L25>)

SMSSender is the main struct that handles SMS sending

```go
type SMSSender struct {
    // contains filtered or unexported fields
}
```

<a name="NewSMSSender"></a>
### func [NewSMSSender](<https://github.com/real-rm/gosms/blob/main/gosms.go#L28>)

```go
func NewSMSSender(s Sender) (*SMSSender, error)
```

NewSMSSender creates a new SMSSender instance with the given sender

<a name="SMSSender.Send"></a>
### func \(\*SMSSender\) [Send](<https://github.com/real-rm/gosms/blob/main/gosms.go#L38>)

```go
func (s *SMSSender) Send(opt SendOption) error
```

Send sends an SMS message using the configured engine

<a name="SendOption"></a>
## type [SendOption](<https://github.com/real-rm/gosms/blob/main/gosms.go#L11-L15>)

SendOption contains all options for sending an SMS

```go
type SendOption struct {
    To      string // Recipient phone number
    From    string // Sender phone number
    Message string // Message content
}
```

<a name="Sender"></a>
## type [Sender](<https://github.com/real-rm/gosms/blob/main/gosms.go#L18-L20>)

Sender is the interface for sending SMS messages

```go
type Sender interface {
    Send(to string, message string, from string) error
}
```

<a name="NewTwilioEngine"></a>
### func [NewTwilioEngine](<https://github.com/real-rm/gosms/blob/main/twilio.go#L32>)

```go
func NewTwilioEngine(accountSID, authToken string) (Sender, error)
```

NewTwilioEngine creates a new Twilio engine with the given credentials

<a name="TwilioClient"></a>
## type [TwilioClient](<https://github.com/real-rm/gosms/blob/main/twilio.go#L13-L15>)

TwilioClient is the interface for Twilio client operations

```go
type TwilioClient interface {
    CreateMessage(params *v2010.CreateMessageParams) (*v2010.ApiV2010Message, error)
}
```

<a name="TwilioEngine"></a>
## type [TwilioEngine](<https://github.com/real-rm/gosms/blob/main/twilio.go#L27-L29>)

TwilioEngine implements the Sender interface using Twilio

```go
type TwilioEngine struct {
    // contains filtered or unexported fields
}
```

<a name="TwilioEngine.Send"></a>
### func \(\*TwilioEngine\) [Send](<https://github.com/real-rm/gosms/blob/main/twilio.go#L48>)

```go
func (t *TwilioEngine) Send(to string, message string, from string) error
```

Send implements the Sender interface

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)


---

### README:
# GoSpeedMeter

GoSpeedMeter is a Go package that provides a simple and efficient way to monitor and measure performance metrics in your applications.

## Features

- Track multiple metrics simultaneously
- Configurable measurement intervals
- Customizable callback functions
- Support for various time units (ms, s, m, h, d)
- Thread-safe operations
- Automatic number formatting with K/M/B/T units
- Time estimation for target values

## Installation

```bash
go get github.com/real-rm/gospeedmeter
```

## Usage

### Basic Usage

```go
package main

import (
    "log"
    "time"

    "github.com/real-rm/gospeedmeter"
)

func main() {
    // Create a new speed meter with default options
    sm := gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})

    // Track a metric
    sm.Check("requests", 1)

    // Get current speed
    speed := sm.GetSpeed(gospeedmeter.UnitS)
    log.Printf("Current speed: %v requests/second", speed["requests"])

    // Get formatted string representation
    log.Printf("Metrics: %s", sm.ToString(gospeedmeter.UnitM, nil))
}
```

### Advanced Usage

```go
// Create a speed meter with custom options
sm := gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{
    Values: map[string]float64{
        "requests": 0,
        "errors": 0,
    },
    IntervalCallback: func(sm *gospeedmeter.SpeedMeter) {
        // Log metrics every 1000 operations
        log.Printf("Performance metrics: %s", sm.ToString(gospeedmeter.UnitM, nil))
    },
    IntervalTriggerCount: 1000,
})

// Track multiple metrics
sm.Check("requests", 1)

// Example of error handling (assuming some operation that might fail)
resp, err := someOperation()
if err != nil {
    sm.Check("errors", 1)
    log.Printf("Error: %v", err)
}

// Estimate time to reach target
targetValue := 1000.0
estimate := sm.Estimate("requests", targetValue, gospeedmeter.UnitM)
log.Printf("Estimated time to reach %v requests: %.2f minutes", targetValue, estimate)
```

### Available Time Units

- `UnitMS`: Milliseconds
- `UnitS`: Seconds
- `UnitM`: Minutes
- `UnitH`: Hours
- `UnitD`: Days

### Number Formatting

The package automatically formats large numbers using K/M/B/T units:
- K: Thousands (1,000)
- M: Millions (1,000,000)
- B: Billions (1,000,000,000)
- T: Trillions (1,000,000,000,000)

Example output:
```
requests(1000):1.5K/s
errors(10):0.1K/s
```

## API Reference

### SpeedMeter

```go
type SpeedMeter struct {
    // ... internal fields
}

type SpeedMeterOptions struct {
    Values               map[string]float64
    IntervalCallback     func(*SpeedMeter)
    IntervalTriggerCount int
}
```

### Methods

- `NewSpeedMeter(options SpeedMeterOptions) *SpeedMeter`: Create a new speed meter
- `Reset(values ...map[string]float64)`: Reset the speed meter with optional initial values
- `Check(name string, value float64)`: Update a metric value
- `GetSpeed(unit Unit) map[string]float64`: Get current speeds for all metrics
- `ToString(unit Unit, toBeEstimated map[string]float64) string`: Get formatted string representation
- `Estimate(name string, targetValue float64, unit Unit) float64`: Estimate time to reach target
- `GetCounters() map[string]float64`: Get current counter values

## Thread Safety

All operations are thread-safe using mutex locks to prevent race conditions when updating metrics from multiple goroutines.

## License

MIT License
### API.md:
<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gospeedmeter

```go
import "github.com/real-rm/gospeedmeter"
```

## Index

- [Constants](<#constants>)
- [type SpeedMeter](<#SpeedMeter>)
  - [func NewSpeedMeter\(options SpeedMeterOptions\) \*SpeedMeter](<#NewSpeedMeter>)
  - [func \(sm \*SpeedMeter\) Check\(name string, value float64\)](<#SpeedMeter.Check>)
  - [func \(sm \*SpeedMeter\) Estimate\(name string, targetValue float64, unit Unit\) float64](<#SpeedMeter.Estimate>)
  - [func \(sm \*SpeedMeter\) GetCounters\(\) map\[string\]float64](<#SpeedMeter.GetCounters>)
  - [func \(sm \*SpeedMeter\) GetSpeed\(unit Unit\) map\[string\]float64](<#SpeedMeter.GetSpeed>)
  - [func \(sm \*SpeedMeter\) Reset\(values ...map\[string\]float64\)](<#SpeedMeter.Reset>)
  - [func \(sm \*SpeedMeter\) ToString\(unit Unit, toBeEstimated map\[string\]float64\) string](<#SpeedMeter.ToString>)
- [type SpeedMeterOptions](<#SpeedMeterOptions>)
- [type Unit](<#Unit>)


## Constants

<a name="AMOUNT_UNIT"></a>

```go
const AMOUNT_UNIT = "KMBT"
```

<a name="SpeedMeter"></a>
## type [SpeedMeter](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L34-L41>)

SpeedMeter handles speed calculations for multiple meters

```go
type SpeedMeter struct {
    // contains filtered or unexported fields
}
```

<a name="NewSpeedMeter"></a>
### func [NewSpeedMeter](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L51>)

```go
func NewSpeedMeter(options SpeedMeterOptions) *SpeedMeter
```

NewSpeedMeter creates a new SpeedMeter instance

<a name="SpeedMeter.Check"></a>
### func \(\*SpeedMeter\) [Check](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L87>)

```go
func (sm *SpeedMeter) Check(name string, value float64)
```

Check updates a single meter value and triggers callback if needed

<a name="SpeedMeter.Estimate"></a>
### func \(\*SpeedMeter\) [Estimate](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L138>)

```go
func (sm *SpeedMeter) Estimate(name string, targetValue float64, unit Unit) float64
```

Estimate calculates estimated time to reach target value for a single meter

<a name="SpeedMeter.GetCounters"></a>
### func \(\*SpeedMeter\) [GetCounters](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L148>)

```go
func (sm *SpeedMeter) GetCounters() map[string]float64
```

GetCounters returns current counter values

<a name="SpeedMeter.GetSpeed"></a>
### func \(\*SpeedMeter\) [GetSpeed](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L100>)

```go
func (sm *SpeedMeter) GetSpeed(unit Unit) map[string]float64
```

GetSpeed returns the current speed for all meters in the specified unit

<a name="SpeedMeter.Reset"></a>
### func \(\*SpeedMeter\) [Reset](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L77>)

```go
func (sm *SpeedMeter) Reset(values ...map[string]float64)
```

Reset resets the speed meter with optional initial values

<a name="SpeedMeter.ToString"></a>
### func \(\*SpeedMeter\) [ToString](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L119>)

```go
func (sm *SpeedMeter) ToString(unit Unit, toBeEstimated map[string]float64) string
```

ToString returns a formatted string representation of the speed meter

<a name="SpeedMeterOptions"></a>
## type [SpeedMeterOptions](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L44-L48>)

SpeedMeterOptions defines the configuration options for SpeedMeter

```go
type SpeedMeterOptions struct {
    Values               map[string]float64
    IntervalCallback     func(*SpeedMeter)
    IntervalTriggerCount int
}
```

<a name="Unit"></a>
## type [Unit](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L13>)

Unit represents time units for speed calculation

```go
type Unit string
```

<a name="UnitMS"></a>

```go
const (
    UnitMS Unit = "ms"
    UnitS  Unit = "s"
    UnitM  Unit = "m"
    UnitH  Unit = "h"
    UnitD  Unit = "d"
)
```

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)


---

### README:
# GoStreaming

GoStreaming is a Go package that provides a powerful and efficient way to process streaming data with concurrent workers. It's designed to handle high-throughput data processing with built-in performance monitoring and error handling.

## Features

- Concurrent stream processing with configurable worker count
- Built-in performance monitoring and speed calculation
- Configurable error handling and callbacks
- Thread-safe operations
- Support for custom stream implementations
- Detailed logging with configurable verbosity
- Context-based cancellation support

## Installation

```bash
go get github.com/real-rm/gostreaming
```

## Usage

### Basic Usage

```go
package main

import (
    "context"
    "log"

    "github.com/real-rm/gostreaming"
)

func main() {
    // Create streaming options
    opts := &gostreaming.StreamingOptions{
        Stream: yourStream, // Your stream implementation
        Process: func(item interface{}) error {
            // Process each item
            log.Printf("Processing item: %v", item)
            return nil
        },
        High: 100, // Maximum concurrent workers
    }

    // Start streaming
    err := gostreaming.Streaming(context.Background(), opts)
    if err != nil {
        log.Printf("Streaming error: %v", err)
    }
}
```

### Advanced Usage with Custom Options

```go
opts := &gostreaming.StreamingOptions{
    Stream: yourStream,
    Process: func(item interface{}) error {
        // Process each item
        return processItem(item)
    },
    End: func(err error) {
        // Called when streaming ends
        if err != nil {
            log.Printf("Stream ended with error: %v", err)
        } else {
            log.Println("Stream completed successfully")
        }
    },
    Error: func(err error) {
        // Custom error handling
        log.Printf("Error processing item: %v", err)
    },
    High: 200,                    // Maximum concurrent workers
    SpeedInterval: 5000,          // Speed logging interval (ms)
    Verbose: 2,                   // Verbosity level
    Event: "custom_event_name",   // Custom event name
}

err := gostreaming.Streaming(context.Background(), opts)
```

### Stream Interface

Your stream implementation must satisfy the following interface:

```go
type Stream interface {
    Next(context.Context) bool    // Move to next item
    Decode(interface{}) error     // Decode current item
    Err() error                   // Get stream error
}
```

## Configuration Options

### StreamingOptions

```go
type StreamingOptions struct {
    Stream        interface{}             // Stream object with Next(), Decode() and Err() methods
    Process       func(interface{}) error // Process function for each item
    End           func(error)             // Callback when streaming ends
    Error         func(error)             // Error handler
    High          int                     // Max number of concurrent processing goroutines
    SpeedInterval int64                   // Interval for speed logging (in milliseconds)
    Verbose       int                     // Verbosity level
    Event         string                  // Event name (default: "data")
}
```

### Default Values

- `High`: 100 (maximum concurrent workers)
- `SpeedInterval`: 1000ms (speed logging interval)
- `Event`: "data" (default event name)
- `Verbose`: 0 (no verbose logging)

## Performance Monitoring

The package automatically calculates and logs:
- Current processing speed (items/second)
- Average processing speed
- Total items processed
- Success/failure counts

Example log output:
```
Speed: 150.25/s (current), 145.80/s (average)
Stream done. Processed 1000/1000 items
```

## Error Handling

The package provides comprehensive error handling:
- Stream errors (via `Err()` method)
- Processing errors (via `Process` function)
- Custom error handling (via `Error` callback)
- Context cancellation

## Thread Safety

- All operations are thread-safe
- Uses mutex locks for shared resources
- Atomic operations for counters
- Safe logging with buffered channel

## Best Practices

1. Always provide a context for cancellation support
2. Implement proper error handling in your Process function
3. Use appropriate High value based on your system resources
4. Monitor memory usage with large concurrent worker counts
5. Implement proper cleanup in the End callback

## License

MIT License
### API.md:
<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gostreaming

```go
import "github.com/real-rm/gostreaming"
```

## Index

- [func Streaming\(ctx context.Context, opts \*StreamingOptions\) error](<#Streaming>)
- [type StreamingOptions](<#StreamingOptions>)


<a name="Streaming"></a>
## func [Streaming](<https://github.com/real-rm/gostreaming/blob/main/streaming.go#L43>)

```go
func Streaming(ctx context.Context, opts *StreamingOptions) error
```

Streaming processes items from a stream using concurrent workers, up to High

<a name="StreamingOptions"></a>
## type [StreamingOptions](<https://github.com/real-rm/gostreaming/blob/main/streaming.go#L14-L23>)

StreamingOptions defines the configuration for stream processing

```go
type StreamingOptions struct {
    Stream        interface{}             // Stream object with Next(), Decode() and Err() methods
    Process       func(interface{}) error // Process function for each item
    End           func(error)             // Callback when streaming ends
    Error         func(error)             // Error handler
    High          int                     // Max number of concurrent processing goroutines
    SpeedInterval int64                   // Interval for speed logging (in milliseconds)
    Verbose       int                     // Verbosity level
    Event         string                  // Event name (default: "data")
}
```

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)


---

### README:
# Gotel

Gotel is a Go package that provides a simple and flexible interface for making phone calls. It currently supports Twilio as a call provider.

## Features

- Simple and clean interface for making phone calls
- Support for Twilio call provider
- Easy to extend with new providers
- Comprehensive error handling
- Detailed logging

## Installation

```bash
go get github.com/real-rm/gotel
```

## Usage

### Basic Usage with Twilio

```go
package main

import (
	"context"
	"log"

	"github.com/real-rm/gotel"
)

func main() {
	// Create a new Twilio caller
	twilioCaller, err := gotel.NewTwilioCaller("your_account_sid", "your_auth_token")
	if err != nil {
		log.Fatalf("Failed to create Twilio caller: %v", err)
	}
	
	// Create a dialer
	dialer, err := gotel.NewDialer(twilioCaller)
	if err != nil {
		log.Fatalf("Failed to create dialer: %v", err)
	}
	
	// Make a call
	params := gotel.CallParams{
		To:    "+**********",
		From:  "+**********",
		Twiml: "<Response><Say>Hello!</Say></Response>",
	}
	
	err = dialer.Call(context.Background(), params)
	if err != nil {
		log.Fatalf("Call failed: %v", err)
	}
}
```

### Using Mock for Testing

```go
// Create a mock caller
mockCaller := &gotel.MockCaller{}

// Create a dialer with the mock caller
dialer, err := gotel.NewDialer(mockCaller)
if err != nil {
	log.Fatalf("Failed to create dialer: %v", err)
}

// Make a test call
params := gotel.CallParams{
	To:    "+**********",
	From:  "+**********",
	Twiml: "<Response><Say>This is a test!</Say></Response>",
}

err = dialer.Call(context.Background(), params)
if err != nil {
	log.Fatalf("Test call failed: %v", err)
}
```

## Error Handling

The package provides comprehensive error handling:

- Invalid call parameters (empty to/from)
- Missing required parameters (URL or Twiml)
- Provider-specific errors
- Missing provider configuration

## Logging

The package uses the `golog` package for logging. It logs:
- Successful call attempts
- Provider responses
- Error conditions

## License

MIT License
### API.md:
<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gotel

```go
import "github.com/real-rm/gotel"
```

## Index

- [type CallParams](<#CallParams>)
- [type Caller](<#Caller>)
- [type Dialer](<#Dialer>)
  - [func NewDialer\(c Caller\) \(\*Dialer, error\)](<#NewDialer>)
  - [func \(d \*Dialer\) Call\(ctx context.Context, params CallParams\) error](<#Dialer.Call>)
- [type TwilioCaller](<#TwilioCaller>)
  - [func NewTwilioCaller\(accountSID, authToken string\) \(\*TwilioCaller, error\)](<#NewTwilioCaller>)
  - [func \(t \*TwilioCaller\) Call\(ctx context.Context, params CallParams\) error](<#TwilioCaller.Call>)
  - [func \(t \*TwilioCaller\) Name\(\) string](<#TwilioCaller.Name>)


<a name="CallParams"></a>
## type [CallParams](<https://github.com/real-rm/gotel/blob/main/gotel.go#L13-L18>)

CallParams contains all parameters for making a phone call

```go
type CallParams struct {
    To    string // Target phone number (required)
    From  string // Source phone number (required)
    URL   string // Voice content URL (optional, mutually exclusive with Twiml)
    Twiml string // Direct Twiml XML (optional, takes precedence over URL)
}
```

<a name="Caller"></a>
## type [Caller](<https://github.com/real-rm/gotel/blob/main/gotel.go#L21-L26>)

Caller is the interface that all call providers must implement

```go
type Caller interface {
    // Call makes a phone call with the given parameters
    Call(ctx context.Context, params CallParams) error
    // Name returns the name of the call provider
    Name() string
}
```

<a name="Dialer"></a>
## type [Dialer](<https://github.com/real-rm/gotel/blob/main/gotel.go#L29-L31>)

Dialer is the main struct that handles phone calls

```go
type Dialer struct {
    // contains filtered or unexported fields
}
```

<a name="NewDialer"></a>
### func [NewDialer](<https://github.com/real-rm/gotel/blob/main/gotel.go#L34>)

```go
func NewDialer(c Caller) (*Dialer, error)
```

NewDialer creates a new Dialer instance with the given caller

<a name="Dialer.Call"></a>
### func \(\*Dialer\) [Call](<https://github.com/real-rm/gotel/blob/main/gotel.go#L44>)

```go
func (d *Dialer) Call(ctx context.Context, params CallParams) error
```

Call makes a phone call using the configured engine

<a name="TwilioCaller"></a>
## type [TwilioCaller](<https://github.com/real-rm/gotel/blob/main/twilio.go#L13-L15>)

TwilioCaller implements the Caller interface for Twilio

```go
type TwilioCaller struct {
    // contains filtered or unexported fields
}
```

<a name="NewTwilioCaller"></a>
### func [NewTwilioCaller](<https://github.com/real-rm/gotel/blob/main/twilio.go#L18>)

```go
func NewTwilioCaller(accountSID, authToken string) (*TwilioCaller, error)
```

NewTwilioCaller creates a new TwilioCaller instance

<a name="TwilioCaller.Call"></a>
### func \(\*TwilioCaller\) [Call](<https://github.com/real-rm/gotel/blob/main/twilio.go#L34>)

```go
func (t *TwilioCaller) Call(ctx context.Context, params CallParams) error
```

Call implements the Caller interface for TwilioCaller

<a name="TwilioCaller.Name"></a>
### func \(\*TwilioCaller\) [Name](<https://github.com/real-rm/gotel/blob/main/twilio.go#L73>)

```go
func (t *TwilioCaller) Name() string
```

Name implements the Caller interface for TwilioCaller

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)


---

### README:
# GoWatch

GoWatch is a Go package that provides a robust and efficient way to watch and process MongoDB change streams. It's designed to handle real-time data synchronization with built-in error recovery, token management, and performance monitoring.

## Features

- Real-time MongoDB change stream monitoring
- Automatic token management and recovery
- Configurable high water mark for flow control
- Built-in error handling and recovery mechanisms
- Support for various operation types (insert, update, delete, replace)
- Detailed logging with configurable verbosity
- Thread-safe operations
- Performance monitoring with SpeedMeter integration

## Installation

```bash
go get github.com/real-rm/gowatch
```

## Usage

### Basic Usage

```go
package main

import (
    "context"
    "log"

    "github.com/real-rm/gowatch"
    "github.com/real-rm/gomongo"
)

func main() {
    // Create context with cancellation
    ctx, cancel := context.WithCancel(context.Background())
    defer cancel()

    // Create watch options
    opts := gowatch.WatchOptions{
        WatchedColl: yourMongoCollection,
        OnChange: func(changedObj bson.M, coll *gomongo.MongoCollection) error {
            // Process the changed document
            log.Printf("Processing change: %v", changedObj)
            return nil
        },
        OnError: func(err error) {
            log.Printf("Watch error: %v", err)
        },
        Context: ctx,
        Cancel:  cancel,
    }

    // Start watching
    watchObj, err := gowatch.WatchTarget(opts)
    if err != nil {
        log.Fatalf("Failed to start watch: %v", err)
    }

    // Watch will continue until context is cancelled
    select {
    case <-ctx.Done():
        watchObj.End(nil)
    }
}
```

### Advanced Usage with Custom Options

```go
opts := gowatch.WatchOptions{
    WatchedColl: yourMongoCollection,
    ImportWatchedRecordsCol: importCollection, // Optional collection to store change records
    OnChange: func(changedObj bson.M, coll *gomongo.MongoCollection) error {
        return processChange(changedObj)
    },
    OnError: func(err error) {
        handleError(err)
    },
    OnTokenUpdate: func(tokenOpts gowatch.TokenUpdateOptions) error {
        return updateToken(tokenOpts)
    },
    QueryWhenInvalidToken: bson.M{"_mt": bson.M{"$gte": lastValidTimestamp}},
    WatchPipeline: []bson.M{
        {"$match": bson.M{"operationType": "update"}},
    },
    HighWaterMark: 20,           // Maximum concurrent processing
    WaterMarkerTimerS: 300,      // Water mark check interval (5 minutes)
    UpdateTokenTimerS: 60,       // Token update interval (1 minute)
    Context: ctx,
    Cancel: cancel,
}
```

### Processing Changed Objects

```go
processOpts := gowatch.ProcessChangedObjectOptions{
    ChangedObj: changedObj,
    WatchedColl: watchedCollection,
    DeleteOneFn: func(id interface{}) error {
        return targetCollection.DeleteOne(context.Background(), bson.M{"_id": id})
    },
    UpdateOneFn: func(doc bson.M) error {
        return targetCollection.UpdateOne(context.Background(), 
            bson.M{"_id": doc["_id"]}, 
            bson.M{"$set": doc})
    },
    ReplaceOneFn: func(doc bson.M) error {
        return targetCollection.ReplaceOne(context.Background(), 
            bson.M{"_id": doc["_id"]}, 
            doc)
    },
    InsertOneFn: func(doc bson.M) error {
        return targetCollection.InsertOne(context.Background(), doc)
    },
}

err := gowatch.ProcessChangedObject(processOpts)
```

## Configuration Options

### WatchOptions

```go
type WatchOptions struct {
    ImportWatchedRecordsCol *gomongo.MongoCollection
    WatchedColl            *gomongo.MongoCollection
    OnChange               func(bson.M, *gomongo.MongoCollection) error
    OnError                func(error)
    OnTokenUpdate          func(TokenUpdateOptions) error
    QueryWhenInvalidToken  bson.M
    WatchPipeline          []bson.M
    HighWaterMark          int
    WaterMarkerTimerS      float64
    UpdateTokenTimerS      float64
    SavedToken             *bson.M
    UpdateProcessStatusFn  func() error
    OnClose               func()
    Context               context.Context
    Cancel                context.CancelFunc
}
```

### Default Values

- `HighWaterMark`: 10 (maximum concurrent processing)
- `WaterMarkerTimerS`: 600 (10 minutes)
- `UpdateTokenTimerS`: 60 (1 minute)
- `ChangeChanSize`: 100
- `ErrorChanSize`: 10

## Error Handling

The package provides comprehensive error handling for various scenarios:
- Invalid tokens
- Change stream history loss
- Capped position loss
- Network errors
- Processing errors

## Token Management

- Automatic token updates
- Token recovery on errors
- Support for resuming from saved tokens
- Cluster time tracking

## Operation Types

Supported MongoDB change stream operations:
- Insert
- Update
- Delete
- Replace
- Invalidate (handled as error)

## Best Practices

1. Always provide a context for cancellation support
2. Implement proper error handling in callbacks
3. Use appropriate HighWaterMark value based on system resources
4. Monitor memory usage with large concurrent processing
5. Implement proper cleanup in OnClose callback
6. Use ImportWatchedRecordsCol for debugging and recovery
7. Configure appropriate timer intervals for your use case

## Thread Safety

- All operations are thread-safe
- Uses mutex locks for shared resources
- Atomic operations for counters
- Safe logging with buffered channels

## License

MIT License
### API.md:
<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gowatch

```go
import "github.com/real-rm/gowatch"
```

## Index

- [Constants](<#constants>)
- [func GetNewTokenAndProcessRemainingDocs\(opt GetNewTokenAndProcessRemainingDocsOptions\) \(\*bson.M, time.Time, error\)](<#GetNewTokenAndProcessRemainingDocs>)
- [func GetToken\(col \*gomongo.MongoCollection\) \(\*bson.M, time.Time, error\)](<#GetToken>)
- [func ProcessChangedObject\(opt ProcessChangedObjectOptions\) error](<#ProcessChangedObject>)
- [type GetNewTokenAndProcessRemainingDocsOptions](<#GetNewTokenAndProcessRemainingDocsOptions>)
- [type ProcessChangedObjectOptions](<#ProcessChangedObjectOptions>)
- [type ProcessUpdateOptions](<#ProcessUpdateOptions>)
- [type TokenUpdateOptions](<#TokenUpdateOptions>)
- [type UpdateFields](<#UpdateFields>)
- [type UpdateSysDataFunc](<#UpdateSysDataFunc>)
  - [func GetUpdateSysdataFunction\(SysData \*gomongo.MongoCollection, SysDataId interface\{\}\) UpdateSysDataFunc](<#GetUpdateSysdataFunction>)
- [type WatchObject](<#WatchObject>)
  - [func WatchTarget\(opt WatchOptions\) \(\*WatchObject, error\)](<#WatchTarget>)
  - [func \(w \*WatchObject\) End\(err error\)](<#WatchObject.End>)
  - [func \(w \*WatchObject\) FinishAndUpdateSysdata\(updateSysData UpdateSysDataFunc\) error](<#WatchObject.FinishAndUpdateSysdata>)
  - [func \(w \*WatchObject\) Pause\(\)](<#WatchObject.Pause>)
  - [func \(w \*WatchObject\) ProcessChanges\(opt WatchOptions\)](<#WatchObject.ProcessChanges>)
  - [func \(w \*WatchObject\) Resume\(\)](<#WatchObject.Resume>)
- [type WatchOptions](<#WatchOptions>)
- [type WatchState](<#WatchState>)


## Constants

<a name="ChangeStreamHistoryLost"></a>

```go
const (
    // Error codes for MongoDB change stream operations
    ChangeStreamHistoryLost = 286 // Error when change stream history is lost
    InvalidToken            = 280 // Error when token is invalid
    CappedPositionLost      = 136 // Error when capped collection position is lost

    // Default configuration values
    DefaultHighWaterMark     = 10  // Maximum number of documents to process before pausing
    DefaultWaterMarkerTimerS = 600 // 10 minutes timer for water mark checks
    DefaultUpdateTokenTimerS = 60  // 1 minute timer for token updates
    DefaultChangeChanSize    = 100 // Default size for change channel buffer
    DefaultErrorChanSize     = 10  // Default size for error channel buffer
)
```

<a name="GetNewTokenAndProcessRemainingDocs"></a>
## func [GetNewTokenAndProcessRemainingDocs](<https://github.com/real-rm/gowatch/blob/main/watch_token.go#L99>)

```go
func GetNewTokenAndProcessRemainingDocs(opt GetNewTokenAndProcessRemainingDocsOptions) (*bson.M, time.Time, error)
```

GetNewTokenAndProcessRemainingDocs gets a new token and processes remaining documents

<a name="GetToken"></a>
## func [GetToken](<https://github.com/real-rm/gowatch/blob/main/watch_token.go#L28>)

```go
func GetToken(col *gomongo.MongoCollection) (*bson.M, time.Time, error)
```

GetToken gets a new token from a collection

<a name="ProcessChangedObject"></a>
## func [ProcessChangedObject](<https://github.com/real-rm/gowatch/blob/main/watch_processor.go#L31>)

```go
func ProcessChangedObject(opt ProcessChangedObjectOptions) error
```

ProcessChangedObject processes a changed object from the change stream

<a name="GetNewTokenAndProcessRemainingDocsOptions"></a>
## type [GetNewTokenAndProcessRemainingDocsOptions](<https://github.com/real-rm/gowatch/blob/main/watch_token.go#L89-L96>)

GetNewTokenAndProcessRemainingDocsOptions contains options for getting new token and processing remaining docs

```go
type GetNewTokenAndProcessRemainingDocsOptions struct {
    FromCol       *gomongo.MongoCollection // Collection to process
    Query         bson.M                   // Query to find remaining documents
    ProcessOneFn  func(bson.M) error       // Function to process each document
    HighWaterMark int                      // Maximum number of documents to process at once
    Context       context.Context          // Context for the operation
    Cancel        context.CancelFunc       // Cancel function for the operation
}
```

<a name="ProcessChangedObjectOptions"></a>
## type [ProcessChangedObjectOptions](<https://github.com/real-rm/gowatch/blob/main/watch_processor.go#L16-L28>)

ProcessChangedObjectOptions contains options for processing changed objects

```go
type ProcessChangedObjectOptions struct {
    ChangedObj            bson.M
    WatchedColl           *gomongo.MongoCollection
    DeleteOneFn           func(interface{}) error
    IgnoreDelete          bool
    UpdateOneFn           func(bson.M) error
    UpdateOneFnWithFields func(bson.M, bson.M) error
    ReplaceOneFn          func(bson.M) error
    InsertOneFn           func(bson.M) error
    ChangeStatusOnlyFn    func(bson.M) error
    SkipChangeMtOnly      bool
    WatchedStream         *WatchObject
}
```

<a name="ProcessUpdateOptions"></a>
## type [ProcessUpdateOptions](<https://github.com/real-rm/gowatch/blob/main/watch_processor.go#L243-L251>)

ProcessUpdateOptions contains options for processing updates

```go
type ProcessUpdateOptions struct {
    UpdateOneFn           func(bson.M) error
    UpdateOneFnWithFields func(bson.M, bson.M) error
    InsertOneFn           func(bson.M) error
    ReplaceOneFn          func(bson.M) error
    OperationType         string
    FullDocument          bson.M
    UpdatedFields         bson.M
}
```

<a name="TokenUpdateOptions"></a>
## type [TokenUpdateOptions](<https://github.com/real-rm/gowatch/blob/main/watch_token.go#L19-L25>)

TokenUpdateOptions contains token update information

```go
type TokenUpdateOptions struct {
    Token          *bson.M     // The new token
    TokenClusterTs time.Time   // Timestamp when token was created
    TokenChangeTs  time.Time   // Timestamp when token was updated
    End            func(error) // Function to end the watch operation
    ResumeMt       time.Time   // Timestamp to resume from
}
```

<a name="UpdateFields"></a>
## type [UpdateFields](<https://github.com/real-rm/gowatch/blob/main/watch_token.go#L177-L183>)

UpdateFields contains the fields to update in the system data

```go
type UpdateFields struct {
    Token          *bson.M
    TokenClusterTs time.Time
    TokenChangeTs  time.Time
    Status         string
    ResumeMt       time.Time
}
```

<a name="UpdateSysDataFunc"></a>
## type [UpdateSysDataFunc](<https://github.com/real-rm/gowatch/blob/main/watch_token.go#L186>)

UpdateSysDataFunc is the function type for updating system data

```go
type UpdateSysDataFunc func(UpdateFields) error
```

<a name="GetUpdateSysdataFunction"></a>
### func [GetUpdateSysdataFunction](<https://github.com/real-rm/gowatch/blob/main/watch_token.go#L189-L192>)

```go
func GetUpdateSysdataFunction(SysData *gomongo.MongoCollection, SysDataId interface{}) UpdateSysDataFunc
```

GetUpdateSysdataFunction returns a function to update system data

<a name="WatchObject"></a>
## type [WatchObject](<https://github.com/real-rm/gowatch/blob/main/watch.go#L62-L89>)

WatchObject represents the watch operation state

```go
type WatchObject struct {
    // contains filtered or unexported fields
}
```

<a name="WatchTarget"></a>
### func [WatchTarget](<https://github.com/real-rm/gowatch/blob/main/watch.go#L102>)

```go
func WatchTarget(opt WatchOptions) (*WatchObject, error)
```

WatchTarget starts watching a MongoDB collection for changes

<a name="WatchObject.End"></a>
### func \(\*WatchObject\) [End](<https://github.com/real-rm/gowatch/blob/main/watch.go#L453>)

```go
func (w *WatchObject) End(err error)
```

End stops the watch operation

<a name="WatchObject.FinishAndUpdateSysdata"></a>
### func \(\*WatchObject\) [FinishAndUpdateSysdata](<https://github.com/real-rm/gowatch/blob/main/watch_token.go#L253>)

```go
func (w *WatchObject) FinishAndUpdateSysdata(updateSysData UpdateSysDataFunc) error
```

FinishAndUpdateSysdata finishes the watch operation and updates the sysdata

<a name="WatchObject.Pause"></a>
### func \(\*WatchObject\) [Pause](<https://github.com/real-rm/gowatch/blob/main/watch.go#L690>)

```go
func (w *WatchObject) Pause()
```

Pause pauses the watch operation

<a name="WatchObject.ProcessChanges"></a>
### func \(\*WatchObject\) [ProcessChanges](<https://github.com/real-rm/gowatch/blob/main/watch.go#L308>)

```go
func (w *WatchObject) ProcessChanges(opt WatchOptions)
```

ProcessChanges processes changes from the change stream

<a name="WatchObject.Resume"></a>
### func \(\*WatchObject\) [Resume](<https://github.com/real-rm/gowatch/blob/main/watch.go#L695>)

```go
func (w *WatchObject) Resume()
```

Resume resumes the watch operation

<a name="WatchOptions"></a>
## type [WatchOptions](<https://github.com/real-rm/gowatch/blob/main/watch.go#L43-L59>)

WatchOptions defines options for watching MongoDB collections

```go
type WatchOptions struct {
    ImportWatchedRecordsCol *gomongo.MongoCollection                     // Collection to import watched records
    WatchedColl             *gomongo.MongoCollection                     // Collection to watch for changes
    OnChange                func(bson.M, *gomongo.MongoCollection) error // Callback for document changes
    OnError                 func(error)                                  // Callback for error handling
    OnTokenUpdate           func(TokenUpdateOptions) error               // Callback for token updates
    QueryWhenInvalidToken   bson.M                                       // Query to use when token is invalid
    WatchPipeline           []bson.M                                     // MongoDB aggregation pipeline for change stream
    HighWaterMark           int                                          // Maximum documents to process before pausing
    WaterMarkerTimerS       float64                                      // Timer for water mark checks
    UpdateTokenTimerS       float64                                      // Timer for token updates
    SavedToken              *bson.M                                      // Token to resume from
    UpdateProcessStatusFn   func() error                                 // Function to update process status
    OnClose                 func()                                       // Callback when watch is closed
    Context                 context.Context                              // Context for controlling watch lifecycle
    Cancel                  context.CancelFunc                           // Cancel function for the context
}
```

<a name="WatchState"></a>
## type [WatchState](<https://github.com/real-rm/gowatch/blob/main/watch.go#L34>)

WatchState represents the current state of the watch operation

```go
type WatchState int32
```

<a name="WatchStateRunning"></a>

```go
const (
    WatchStateRunning WatchState = iota
    WatchStatePaused
    WatchStateStopped
)
```

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)


---

