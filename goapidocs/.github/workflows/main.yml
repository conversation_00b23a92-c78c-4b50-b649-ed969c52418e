name: Generate API Docs

on:
  repository_dispatch:
    types: [update-docs]

jobs:
  generate-docs:
    runs-on: self-hosted
    steps:
      - name: Checkout goapidocs with PAT
        uses: actions/checkout@v3
        with:
          token: ${{ secrets.GH_PAT_WRITE }}

      - name: Set up Git user
        run: |
          git config --global user.name "doc-bot"
          git config --global user.email "<EMAIL>"

      - name: Check exclude_repos.txt exists
        run: |
          if [ ! -f exclude_repos.txt ]; then
            echo "exclude_repos.txt not found!"
            exit 1
          fi

      - name: Clone all go* repos except excluded
        env:
          GITHUB_TOKEN: ${{ secrets.WORKFLOW_TOKEN_GITHUB }}
        run: |
          mkdir repos
          cd repos
          
          # 获取所有 real-rm 下的 repo 并筛选 go 开头
          curl -s -H "Authorization: token $GITHUB_TOKEN" \
            https://api.github.com/orgs/real-rm/repos?per_page=100 |
            jq -r '.[] | select(.name | startswith("go")) | .name' |
            sort > ../repo_list.txt

          # 过滤掉 exclude_repos.txt 中的 repo
          grep -v -F -f ../exclude_repos.txt ../repo_list.txt > ../repo_list_filtered.txt

          while read repo; do
            echo "Cloning $repo"
            git clone --depth=1 https://$<EMAIL>/real-rm/$repo.git || echo "Failed to clone $repo"
          done < ../repo_list_filtered.txt

      - name: Merge docs
        run: |
          echo "# Real-RM Go API Docs" > docs/all.README.md
          echo "" >> docs/all.README.md

          while read repo; do
            if [ -f repos/$repo/README.md ]; then
              echo "### README:" >> docs/all.README.md
              cat repos/$repo/README.md >> docs/all.README.md
              echo "" >> docs/all.README.md

              cp "repos/$repo/README.md" "docs/${repo}.README.md"
            fi

            if [ -f repos/$repo/api.md ]; then
              echo "### API.md:" >> docs/all.README.md
              cat repos/$repo/api.md >> docs/all.README.md
              echo "" >> docs/all.README.md

              cp "repos/$repo/api.md" "docs/${repo}.api.md"
            fi

            echo -e "\n---\n" >> docs/all.README.md
          done < repo_list_filtered.txt
          cat docs/all.README.md

      - name: Commit & Push changes
        env:
          GH_PAT_WRITE: ${{ secrets.GH_PAT_WRITE }}
        run: |
          git remote remove origin
          git remote add origin https://${GH_PAT_WRITE}@github.com/real-rm/goapidocs.git
          ls docs
          git add docs/*
          git commit -m "Auto-update combined README from go repos"
          git push origin main
        
