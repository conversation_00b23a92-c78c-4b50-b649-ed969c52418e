# GoSMS

GoSMS is a Go package that provides a simple and flexible interface for sending SMS messages. It currently supports Twilio as a messaging provider.

## Features

- Simple and clean interface for sending SMS messages
- Support for Twilio SMS provider
- Easy to extend with new providers
- Comprehensive error handling
- Detailed logging

## Installation

```bash
go get github.com/real-rm/gosms
```

## Usage

### Basic Usage with Twilio

```go
package main

import (
	"github.com/real-rm/gosms"
)

func main() {
	// Create a new Twilio engine with your credentials
	engine, err := gosms.NewTwilioEngine("your-account-sid", "your-auth-token")
	if err != nil {
		panic(err)
	}
	
	// Create an SMS sender with the engine
	sender, err := gosms.NewSMSSender(engine)
	if err != nil {
		panic(err)
	}
	
	// Send an SMS
	opt := gosms.SendOption{
		To:      "+**********",
		From:    "+**********",
		Message: "Hello from GoSMS!",
	}
	
	err = sender.Send(opt)
	if err != nil {
		panic(err)
	}
}
```

### Creating Custom Providers

You can create custom SMS providers by implementing the `Sender` interface:

```go
type Sender interface {
	Send(to string, message string, from string) error
}
```

## Error Handling

The package provides comprehensive error handling:

- Invalid send options (empty to/from/message)
- Provider-specific errors
- Missing provider configuration

## Logging

The package uses the `golog` package for logging. It logs:
- Successful message delivery
- Provider responses
- Error conditions

## Testing

The library has high test coverage. Run the tests with:

```bash
go test -cover
```

## License

MIT License