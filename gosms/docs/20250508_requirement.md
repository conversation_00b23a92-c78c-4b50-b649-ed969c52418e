gosms 技术文档
项目简介
gosms 是一个用 Go 编写的、面向接口设计的短信发送库，支持多个短信服务提供商（如 Twilio），通过统一的抽象接口屏蔽底层差异，便于扩展、测试和维护。

功能特性
支持通过统一接口发送短信

多服务商实现（目前支持 Twilio，后续可扩展阿里云、腾讯云等）

清晰的 Provider 接口

依赖注入，便于测试

提供单元测试（UT）框架

项目结构
go
Copy
Edit
gosms/
├── gosms.go                   // 核心接口与发送器实现
├── provider/
│   └── twilio/
│       ├── twilio.go         // Twilio Provider 实现
│       └── twilio_test.go    // Twilio 单元测试
├── gosms_test.go             // SMSSender 单元测试
核心模块说明
1. Provider 接口
go
Copy
Edit
type Provider interface {
    Send(to string, message string) error
}
所有短信服务商都需要实现该接口。

2. SMSSender 结构体
go
Copy
Edit
type SMSSender struct {
    provider Provider
}
构造函数
go
Copy
Edit
func NewSMSSender(p Provider) *SMSSender
用于初始化短信发送器。

发送函数
go
Copy
Edit
func (s *SMSSender) Send(to string, message string) error
调用具体 Provider 实现发送短信。

Twilio Provider 实现
路径：provider/twilio/twilio.go

go
Copy
Edit
package twilio

import (
    "github.com/twilio/twilio-go"
    openapi "github.com/twilio/twilio-go/rest/api/v2010"
)

type TwilioClient struct {
    client *twilio.RestClient
    from   string
}

func NewTwilioClient(accountSID, authToken, from string) *TwilioClient {
    client := twilio.NewRestClientWithParams(twilio.ClientParams{
        Username: accountSID,
        Password: authToken,
    })
    return &TwilioClient{
        client: client,
        from:   from,
    }
}

func (t *TwilioClient) Send(to string, message string) error {
    params := &openapi.CreateMessageParams{}
    params.SetTo(to)
    params.SetFrom(t.from)
    params.SetBody(message)

    _, err := t.client.Api.CreateMessage(params)
    return err
}
使用示例
go
Copy
Edit
package main

import (
    "github.com/yourusername/gosms"
    "github.com/yourusername/gosms/provider/twilio"
)

func main() {
    twilioClient := twilio.NewTwilioClient("ACCOUNT_SID", "AUTH_TOKEN", "+*********")
    sender := gosms.NewSMSSender(twilioClient)

    err := sender.Send("+**********", "验证码是：123456")
    if err != nil {
        panic(err)
    }
}
单元测试（UT）
1. 接口 Mock 示例
go
Copy
Edit
type MockProvider struct {
    SentTo     string
    SentMsg    string
    ShouldFail bool
}

func (m *MockProvider) Send(to, message string) error {
    m.SentTo = to
    m.SentMsg = message
    if m.ShouldFail {
        return errors.New("mock send failed")
    }
    return nil
}
2. SMSSender 单元测试：gosms_test.go
go
Copy
Edit
func TestSMSSender_Send_Success(t *testing.T) {
    mock := &MockProvider{}
    sender := NewSMSSender(mock)

    err := sender.Send("123", "hello")
    if err != nil {
        t.Fatalf("expected no error, got %v", err)
    }

    if mock.SentTo != "123" || mock.SentMsg != "hello" {
        t.Errorf("message not sent correctly")
    }
}

func TestSMSSender_Send_Fail(t *testing.T) {
    mock := &MockProvider{ShouldFail: true}
    sender := NewSMSSender(mock)

    err := sender.Send("123", "hello")
    if err == nil {
        t.Fatalf("expected error, got nil")
    }
}
3. Twilio 发送测试（建议使用 mock）
使用真实 Twilio 账户测试或使用 httpmock/gomock 等方式进行 API 屏蔽测试。

如何扩展新服务商
创建目录：provider/<new_vendor>/

实现 Provider 接口

在主程序中通过 NewSMSSender 注入即可

依赖说明
twilio-go - Twilio 官方 SDK

单元测试使用标准库 testing