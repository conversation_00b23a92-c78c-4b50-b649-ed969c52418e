package gosms

import (
	"errors"
	"testing"
)

// MockSender implements the Sender interface for testing
type MockSender struct {
	To         string
	From       string
	Message    string
	ShouldFail bool
}

func (m *MockSender) Send(to string, message string, from string) error {
	m.To = to
	m.From = from
	m.Message = message
	if m.ShouldFail {
		return errors.New("mock send failed")
	}
	return nil
}

func TestNewSMSSender(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := &MockSender{}
		sender, err := NewSMSSender(mock)
		if err != nil {
			t.Fatalf("expected no error, got %v", err)
		}
		if sender == nil {
			t.Fatal("expected non-nil sender")
		}
		if sender.engine != mock {
			t.Error("engine not set correctly")
		}
	})

	t.Run("nil sender", func(t *testing.T) {
		sender, err := NewSMSSender(nil)
		if err == nil {
			t.Fatal("expected error for nil sender")
		}
		if sender != nil {
			t.<PERSON>rror("expected nil sender")
		}
	})
}

func TestSMSSender_Send_Success(t *testing.T) {
	mock := &MockSender{}
	sender, err := NewSMSSender(mock)
	if err != nil {
		t.Fatalf("expected no error, got %v", err)
	}

	opt := SendOption{
		To:      "123",
		From:    "+**********",
		Message: "hello",
	}

	err = sender.Send(opt)
	if err != nil {
		t.Fatalf("expected no error, got %v", err)
	}

	if mock.To != opt.To || mock.From != opt.From || mock.Message != opt.Message {
		t.Errorf("message not sent correctly, got %v, expected %v", mock, opt)
	}
}

func TestSMSSender_Send_Fail(t *testing.T) {
	mock := &MockSender{ShouldFail: true}
	sender, err := NewSMSSender(mock)
	if err != nil {
		t.Fatalf("expected no error, got %v", err)
	}

	opt := SendOption{
		To:      "123",
		From:    "+**********",
		Message: "hello",
	}

	err = sender.Send(opt)
	if err == nil {
		t.Fatalf("expected error, got nil")
	}
	if err.Error() != "mock send failed" {
		t.Errorf("unexpected error message: %v", err)
	}
}

func TestSMSSender_NoProvider(t *testing.T) {
	sender := &SMSSender{}

	opt := SendOption{
		To:      "123",
		From:    "+**********",
		Message: "hello",
	}

	err := sender.Send(opt)
	if err == nil {
		t.Fatalf("expected error, got nil")
	}
	if err.Error() != "no engine configured" {
		t.Errorf("unexpected error message: %v", err)
	}
}

func TestSMSSender_InvalidOptions(t *testing.T) {
	mock := &MockSender{}
	sender, err := NewSMSSender(mock)
	if err != nil {
		t.Fatalf("expected no error, got %v", err)
	}

	testCases := []struct {
		name string
		opt  SendOption
	}{
		{
			name: "empty to",
			opt: SendOption{
				To:      "",
				From:    "+**********",
				Message: "hello",
			},
		},
		{
			name: "empty from",
			opt: SendOption{
				To:      "123",
				From:    "",
				Message: "hello",
			},
		},
		{
			name: "empty message",
			opt: SendOption{
				To:      "123",
				From:    "+**********",
				Message: "",
			},
		},
		{
			name: "all empty",
			opt: SendOption{
				To:      "",
				From:    "",
				Message: "",
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := sender.Send(tc.opt)
			if err == nil {
				t.Errorf("expected error for %s, got nil", tc.name)
			}
			expectedErr := "invalid send option"
			if err.Error()[:len(expectedErr)] != expectedErr {
				t.Errorf("unexpected error message: %v", err)
			}
		})
	}
}
