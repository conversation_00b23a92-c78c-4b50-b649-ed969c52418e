package gosms

import (
	"errors"
	"testing"

	v2010 "github.com/twilio/twilio-go/rest/api/v2010"
)

// MockTwilioClient is a mock implementation of the Twilio client
type MockTwilioClient struct {
	ShouldFail bool
}

func (m *MockTwilioClient) CreateMessage(params *v2010.CreateMessageParams) (*v2010.ApiV2010Message, error) {
	if m.ShouldFail {
		return nil, errors.New("mock twilio error")
	}
	return &v2010.ApiV2010Message{Sid: strPtr("test-sid")}, nil
}

func strPtr(s string) *string {
	return &s
}

func TestNewTwilioEngine(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		engine, err := NewTwilioEngine("test-sid", "test-token")
		if err != nil {
			t.Fatalf("expected no error, got %v", err)
		}
		if engine == nil {
			t.Fatal("expected non-nil engine")
		}
	})

	t.Run("empty account sid", func(t *testing.T) {
		engine, err := NewTwilioEngine("", "test-token")
		if err == nil {
			t.Fatal("expected error for empty account sid")
		}
		if engine != nil {
			t.Error("expected nil engine")
		}
	})

	t.Run("empty auth token", func(t *testing.T) {
		engine, err := NewTwilioEngine("test-sid", "")
		if err == nil {
			t.Fatal("expected error for empty auth token")
		}
		if engine != nil {
			t.Error("expected nil engine")
		}
	})
}

func TestTwilioEngine_Send(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		engine := &TwilioEngine{
			client: &MockTwilioClient{},
		}
		err := engine.Send("+**********", "test message", "+**********")
		if err != nil {
			t.Fatalf("expected no error, got %v", err)
		}
	})

	t.Run("no client", func(t *testing.T) {
		engine := &TwilioEngine{}
		err := engine.Send("+**********", "test message", "+**********")
		if err == nil {
			t.Fatal("expected error for no client")
		}
		if err.Error() != "no twilio client configured" {
			t.Errorf("unexpected error message: %v", err)
		}
	})

	t.Run("empty to", func(t *testing.T) {
		engine := &TwilioEngine{
			client: &MockTwilioClient{},
		}
		err := engine.Send("", "test message", "+**********")
		if err == nil {
			t.Fatal("expected error for empty to")
		}
		if err.Error() != "receiver number is empty" {
			t.Errorf("unexpected error message: %v", err)
		}
	})

	t.Run("empty message", func(t *testing.T) {
		engine := &TwilioEngine{
			client: &MockTwilioClient{},
		}
		err := engine.Send("+**********", "", "+**********")
		if err == nil {
			t.Fatal("expected error for empty message")
		}
		if err.Error() != "message is empty" {
			t.Errorf("unexpected error message: %v", err)
		}
	})

	t.Run("empty from", func(t *testing.T) {
		engine := &TwilioEngine{
			client: &MockTwilioClient{},
		}
		err := engine.Send("+**********", "test message", "")
		if err == nil {
			t.Fatal("expected error for empty from")
		}
		if err.Error() != "sender number is empty" {
			t.Errorf("unexpected error message: %v", err)
		}
	})

	t.Run("client error", func(t *testing.T) {
		engine := &TwilioEngine{
			client: &MockTwilioClient{ShouldFail: true},
		}
		err := engine.Send("+**********", "test message", "+**********")
		if err == nil {
			t.Fatal("expected error for client error")
		}
		if err.Error() != "mock twilio error" {
			t.Errorf("unexpected error message: %v", err)
		}
	})

	// marshal error 分支
	t.Run("marshal error", func(t *testing.T) {
		engine := &TwilioEngine{
			client: &MockTwilioClientMarshalErr{},
		}
		err := engine.Send("+**********", "test message", "+**********")
		if err != nil {
			t.Fatalf("expected no error, got %v", err)
		}
	})
}

// MockTwilioClientMarshalErr 用于制造 marshal 出错
// 让 Sid 字段为一个不能被 json.Marshal 的类型
// 这里用 channel 类型制造 marshal 错误

type MockTwilioClientMarshalErr struct{}

func (m *MockTwilioClientMarshalErr) CreateMessage(params *v2010.CreateMessageParams) (*v2010.ApiV2010Message, error) {
	return &v2010.ApiV2010Message{Sid: nil}, nil
}

// Note: This test requires actual Twilio credentials to run
// func TestTwilioEngine_Send(t *testing.T) {
// 	engine := NewTwilioEngine("your-account-sid", "your-auth-token")
// 	err := engine.Send("+**********", "Test message", "+**********")
// 	if err != nil {
// 		t.Errorf("failed to send message: %v", err)
// 	}
// }
