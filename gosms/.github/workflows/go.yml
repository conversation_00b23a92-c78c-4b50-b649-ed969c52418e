name: Go CI

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]
  workflow_dispatch:

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        token: ${{ secrets.WORKFLOW_TOKEN_GITHUB }}  # 使用 GitHub Token 进行 HTTPS 克隆

    - name: Configure Git for private repos
      run: |
        git config --global url."https://${{ secrets.WORKFLOW_TOKEN_GITHUB }}@github.com/".insteadOf "https://github.com/"

    - name: Configure GOPRIVATE
      run: echo "GOPRIVATE=github.com/real-rm/*" >> $GITHUB_ENV

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: 'stable'

    - name: Run golangci-lint
      uses: golangci/golangci-lint-action@v7
      with:
        version: latest
        args: --timeout=5m

  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        token: ${{ secrets.WORKFLOW_TOKEN_GITHUB }}  # 使用 GitHub Token 进行 HTTPS 克隆

    - name: Configure Git for private repos
      run: |
        git config --global url."https://${{ secrets.WORKFLOW_TOKEN_GITHUB }}@github.com/".insteadOf "https://github.com/"

    - name: Configure GOPRIVATE
      run: echo "GOPRIVATE=github.com/real-rm/*" >> $GITHUB_ENV

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: 'stable'

    - name: Set up MongoDB
      uses: supercharge/mongodb-github-action@1.10.0
      with:
        mongodb-version: '6.0'

    - name: Set environment variables
      run: |
        echo "MONGODB_URI=mongodb://localhost:27017" >> $GITHUB_ENV
        echo "MONGODB_DATABASE=test" >> $GITHUB_ENV

    - name: Tidy up Go modules
      run: go mod tidy

    - name: Build
      run: go build -v ./...

    - name: Run Tests and Generate Coverage
      run: |
        go test -coverprofile=coverage.out ./...

    - name: Install bc
      run: sudo apt-get update && sudo apt-get install -y bc

    - name: Check Coverage Threshold (>= 80%)
      run: |
        coverage=$(go tool cover -func=coverage.out | grep total: | awk '{print substr($3, 1, length($3)-1)}')
        echo "Total coverage: $coverage%"
        threshold=80.0
        above=$(echo "$coverage >= $threshold" | bc -l)
        if [ "$above" -ne 1 ]; then
          echo "❌ Code coverage ($coverage%) is below threshold ($threshold%)"
          exit 1
        else
          echo "✅ Code coverage ($coverage%) meets threshold ($threshold%)"
        fi

