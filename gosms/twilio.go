package gosms

import (
	"encoding/json"
	"errors"

	"github.com/real-rm/golog"
	"github.com/twilio/twilio-go"
	v2010 "github.com/twilio/twilio-go/rest/api/v2010"
)

// TwilioClient is the interface for Twilio client operations
type TwilioClient interface {
	CreateMessage(params *v2010.CreateMessageParams) (*v2010.ApiV2010Message, error)
}

// twilioClientWrapper wraps the Twilio client to implement our interface
type twilioClientWrapper struct {
	client *twilio.RestClient
}

func (w *twilioClientWrapper) CreateMessage(params *v2010.CreateMessageParams) (*v2010.ApiV2010Message, error) {
	return w.client.Api.CreateMessage(params)
}

// TwilioEngine implements the Sender interface using Twilio
type TwilioEngine struct {
	client TwilioClient
}

// NewTwilioEngine creates a new Twilio engine with the given credentials
func NewTwilioEngine(accountSID, authToken string) (Sender, error) {
	if accountSID == "" || authToken == "" {
		errMsg := "invalid twilio credentials, accountSID: " + accountSID + ", authToken: " + authToken
		golog.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	client := twilio.NewRestClientWithParams(twilio.ClientParams{
		Username: accountSID,
		Password: authToken,
	})
	return &TwilioEngine{
		client: &twilioClientWrapper{client: client},
	}, nil
}

// Send implements the Sender interface
func (t *TwilioEngine) Send(to string, message string, from string) error {
	if t.client == nil {
		errMsg := "no twilio client configured"
		golog.Error(errMsg)
		return errors.New(errMsg)
	}
	if to == "" {
		errMsg := "receiver number is empty"
		golog.Error(errMsg)
		return errors.New(errMsg)
	}
	if message == "" {
		errMsg := "message is empty"
		golog.Error(errMsg)
		return errors.New(errMsg)
	}
	if from == "" {
		errMsg := "sender number is empty"
		golog.Error(errMsg)
		return errors.New(errMsg)
	}
	params := &v2010.CreateMessageParams{}
	params.SetTo(to)
	params.SetFrom(from)
	params.SetBody(message)

	resp, err := t.client.CreateMessage(params)
	if err != nil {
		golog.Error("Error sending SMS message: "+err.Error(), "to", to, "from", from, "message_length", len(message))
		return err
	}
	response, jsonErr := json.Marshal(*resp)
	if jsonErr != nil {
		golog.Warn("failed to marshal twilio response: " + jsonErr.Error())
		golog.Info("SMS message sent successfully",
			"to", to,
			"from", from,
			"sid", resp.Sid)
	} else {
		golog.Info("Twilio response: " + string(response))
	}

	return nil
}
