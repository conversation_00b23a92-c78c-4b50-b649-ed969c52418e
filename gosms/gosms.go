package gosms

import (
	"errors"
	"fmt"

	"github.com/real-rm/golog"
)

// SendOption contains all options for sending an SMS
type SendOption struct {
	To      string // Recipient phone number
	From    string // Sender phone number
	Message string // Message content
}

// Sender is the interface for sending SMS messages
type Sender interface {
	Send(to string, message string, from string) error
}

// SMSSender is the main struct that handles SMS sending
type SMSSender struct {
	engine Sender
}

// NewSMSSender creates a new SMSSender instance with the given sender
func NewSMSSender(s Sender) (*SMSSender, error) {
	if s == nil {
		errMsg := "invalid sender"
		golog.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	return &SMSSender{engine: s}, nil
}

// Send sends an SMS message using the configured engine
func (s *SMSSender) Send(opt SendOption) error {
	if opt.To == "" || opt.From == "" || opt.Message == "" {
		errMsg := fmt.Sprintf("invalid send option, to: %s, from: %s, message: %s", opt.To, opt.From, opt.Message)
		golog.Error(errMsg)
		return errors.New(errMsg)
	}
	if s.engine == nil {
		errMsg := "no engine configured"
		golog.Error(errMsg)
		return errors.New(errMsg)
	}
	return s.engine.Send(opt.To, opt.Message, opt.From)
}
