package main

import (
	"fmt"
	"github.com/real-rm/gofile/levelStore"
)

func main() {
	mp3Hash := levelStore.MurmurToInt32("trreb/c11897846/audio")
	pdfHash := levelStore.MurmurToInt32("a3c5f9ba-0e09-4125-b773-30135ddc4fae")
	
	fmt.Printf("mp3 key: 'trreb/c11897846/audio' -> hash: %d\n", mp3Hash)
	fmt.Printf("pdf key: 'a3c5f9ba-0e09-4125-b773-30135ddc4fae' -> hash: %d\n", pdfHash)
	
	fmt.Printf("mp3 filename: %d.mp3\n", mp3Hash)
	fmt.Printf("pdf filename: %d.pdf\n", pdfHash)
	
	if mp3Hash < pdfHash {
		fmt.Println("mp3 hash is smaller, should come first")
	} else {
		fmt.Println("pdf hash is smaller, should come first")
	}
}
