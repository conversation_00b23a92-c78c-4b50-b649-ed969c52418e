package goresodownload

import (
	"context"
	"fmt"
	"time"

	golog "github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	DOWNLOAD_ALLOWED_MS       = 30 * 1000 // 30 seconds
	DEFAULT_DOWNLOAD_START_TS = "1970-01-01T00:00:00Z"
)

// QueueItem represents an item in the download queue
type QueueItem struct {
	ID           string    `bson:"_id"`
	Src          string    `bson:"src"`
	Mt           time.Time `bson:"_mt"`
	DlShallEndTs time.Time `bson:"dlShallEndTs"`
	Priority     int       `bson:"priority"`
	ChangeDoc    bson.M    `bson:"changeDoc"`
}

// ResourceDownloadQueue manages the download queue for resources
type ResourceDownloadQueue struct {
	queueCol *gomongo.MongoCollection
}

// NewResourceDownloadQueue creates a new ResourceDownloadQueue instance
func NewResourceDownloadQueue(queueCol *gomongo.MongoCollection) (*ResourceDownloadQueue, error) {
	if queueCol == nil {
		return nil, fmt.Errorf("collection cannot be nil")
	}

	queue := &ResourceDownloadQueue{
		queueCol: queueCol,
	}

	// Ensure indexes
	if err := queue.ensureIndexes(); err != nil {
		return nil, fmt.Errorf("failed to ensure indexes: %w", err)
	}

	return queue, nil
}

// ensureIndexes creates necessary indexes for the queue collection
func (q *ResourceDownloadQueue) ensureIndexes() error {
	ctx := context.Background()

	// Create compound index on src, dlShallEndTs and priority for optimal query performance
	indexModel := []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "src", Value: 1},
				{Key: "dlShallEndTs", Value: 1},
				{Key: "priority", Value: -1},
			},
		},
	}

	_, err := q.queueCol.CreateIndexes(ctx, indexModel)
	if err != nil {
		return fmt.Errorf("failed to create indexes: %w", err)
	}

	return nil
}

// AddToQueue adds a resource to the download queue
func (q *ResourceDownloadQueue) AddToQueue(changeDoc bson.M, id string, priority int, src string) error {
	// Parse default download start time
	defaultStartTime, err := time.Parse(time.RFC3339, DEFAULT_DOWNLOAD_START_TS)
	if err != nil {
		return fmt.Errorf("failed to parse default start time: %w", err)
	}

	// Use id as _id (primary key)
	ctx := context.Background()
	filter := bson.M{"_id": id}

	// Create update document directly as bson.M to avoid serialization issues
	updateDoc := bson.M{
		"_id":          id,
		"src":          src,
		"_mt":          time.Now(),
		"dlShallEndTs": defaultStartTime,
		"priority":     priority,
		"changeDoc":    changeDoc,
	}
	update := bson.M{"$set": updateDoc}
	opts := options.Update().SetUpsert(true)

	_, err = q.queueCol.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return fmt.Errorf("failed to add to queue: %w", err)
	}

	golog.Info("addToQueue: successfully added", "src", src, "id", id, "priority", priority)
	return nil
}

// GetNextBatch gets the next batch of resources to download for a specific board
func (q *ResourceDownloadQueue) GetNextBatch(boardType string, batchSize int) ([]QueueItem, error) {
	if batchSize <= 0 {
		batchSize = 100
	}

	ctx := context.Background()

	// Find documents ready for download for the specific board
	filter := bson.M{
		"src":          boardType,
		"dlShallEndTs": bson.M{"$lt": time.Now()},
	}
	opts := options.Find().
		SetSort(bson.D{{Key: "priority", Value: -1}}).
		SetLimit(int64(batchSize))

	rawResults, err := q.queueCol.FindToArray(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find documents: %w", err)
	}

	// Convert bson.M results to QueueItem structs
	var results []QueueItem
	for _, rawResult := range rawResults {
		var item QueueItem
		// Convert bson.M to QueueItem
		bsonBytes, err := bson.Marshal(rawResult)
		if err != nil {
			continue // Skip invalid items
		}
		if err := bson.Unmarshal(bsonBytes, &item); err != nil {
			continue // Skip invalid items
		}
		results = append(results, item)
	}

	if len(results) > 0 {
		// Update dlShallEndTs for all records in the batch
		var updateFilters []bson.M
		for _, record := range results {
			updateFilters = append(updateFilters, bson.M{"_id": record.ID})
		}

		updateFilter := bson.M{"$or": updateFilters}
		updateDoc := bson.M{"$set": bson.M{"dlShallEndTs": time.Now().Add(time.Duration(DOWNLOAD_ALLOWED_MS) * time.Millisecond)}}

		_, err := q.queueCol.UpdateMany(ctx, updateFilter, updateDoc)
		if err != nil {
			golog.Error("Failed to update dlShallEndTs for batch", "error", err)
			// Don't return error here, as we still want to process the batch
		}

		return results, nil
	}

	return nil, nil
}

// GetNext gets the next single resource to download for a specific board
func (q *ResourceDownloadQueue) GetNext(boardType string) (*QueueItem, error) {
	ctx := context.Background()

	filter := bson.M{
		"src":          boardType,
		"dlShallEndTs": bson.M{"$lt": time.Now()},
	}
	update := bson.M{"$set": bson.M{"dlShallEndTs": time.Now().Add(time.Duration(DOWNLOAD_ALLOWED_MS) * time.Millisecond)}}
	opts := options.FindOneAndUpdate().
		SetSort(bson.D{{Key: "priority", Value: -1}}).
		SetReturnDocument(options.After)

	var result QueueItem
	err := q.queueCol.FindOneAndUpdate(ctx, filter, update, opts).Decode(&result)
	if err != nil {
		if err.Error() == "mongo: no documents in result" {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get next item: %w", err)
	}

	return &result, nil
}

// RemoveFromQueue removes a resource from the queue
func (q *ResourceDownloadQueue) RemoveFromQueue(item *QueueItem) error {
	ctx := context.Background()

	filter := bson.M{"_id": item.ID}
	_, err := q.queueCol.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to remove from queue: %w", err)
	}

	return nil
}

// RemoveBatchFromQueue removes multiple resources from the queue
func (q *ResourceDownloadQueue) RemoveBatchFromQueue(items []QueueItem) error {
	if len(items) == 0 {
		return nil
	}

	ctx := context.Background()

	// Build filter for batch deletion using _id
	var ids []string
	for _, item := range items {
		ids = append(ids, item.ID)
	}

	filter := bson.M{"_id": bson.M{"$in": ids}}
	_, err := q.queueCol.DeleteMany(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to remove batch from queue: %w", err)
	}

	return nil
}
