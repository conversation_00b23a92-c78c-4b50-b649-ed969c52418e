[dbs]
verbose = 3

[dbs.tmp]
uri = "***************************************************************"
#uri = "mongodb://localhost:27017/test1"

[dbs.rni]
uri = "***************************************************************"

[golog]
dir = "/tmp/test_logs"
level = "info"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"

[preDefColls.emailWhitelist]
collName = "email_whitelist"
dbName = "tmp"

[preDefColls.mailLog]
collName = "mail_log"
dbName = "tmp"

  [preDefColls.mailLog.options]
  expireAfterSeconds = 2_592_000

[imageStore]
reso_treb_image_dir = ["/tmp/trb_images","/tmp/trb_images2"]
reso_car_image_dir = ["/tmp/car_images"]
reso_crea_image_dir = ["/tmp/ddf_images"]
reso_bcre_image_dir = ["/tmp/bre_images"]
reso_edm_image_dir = ["/tmp/edm_images"]

[source_l2_size]
TRB = 512
CAR = 256
DDF = 1024
BRE = 64
CLG = 64
OTW = 64
EDM = 64
USER = 64