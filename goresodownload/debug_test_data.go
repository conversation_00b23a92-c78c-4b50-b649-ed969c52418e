package main

import (
	"context"
	"fmt"
	"log"

	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func main() {
	// Connect to MongoDB
	err := gomongo.Connect("mongodb://localhost:27017", "rni")
	if err != nil {
		log.Fatal("Failed to connect to MongoDB:", err)
	}

	// Get the TRB collection
	coll := gomongo.Coll("rni", "reso_treb_evow_merged")

	// Find the test document
	var doc bson.M
	err = coll.FindOne(context.Background(), bson.M{"_id": "TRBC11897846"}).Decode(&doc)
	if err != nil {
		log.Fatal("Failed to find document:", err)
	}

	// Check the media field
	if mediaValue, exists := doc["media"]; exists {
		if phoUrls, ok := mediaValue.(primitive.A); ok {
			fmt.Printf("Found %d media items:\n", len(phoUrls))
			for i, item := range phoUrls {
				if media, ok := item.(primitive.M); ok {
					fmt.Printf("Media %d:\n", i)
					fmt.Printf("  id: %v\n", media["id"])
					fmt.Printf("  key: %v\n", media["key"])
					fmt.Printf("  url: %v\n", media["url"])
					fmt.Printf("  tp: %v\n", media["tp"])
					fmt.Printf("  cat: %v\n", media["cat"])
					fmt.Println()
				}
			}
		}
	}
}
