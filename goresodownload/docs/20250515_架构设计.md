# 图片存储系统架构设计文档（第三步）

> 本文档为 #20241101\_file\_server\_redesign.md 中整体文件服务器重构的 **第三步设计说明**，主要涉及 **图片下载与处理逻辑**、**变更监控** 以及 **本地状态与 merged 同步机制** 的详细设计与说明。
---

## 🎯 目标

构建一个健壮、高性能、可扩展的本地媒体文件下载系统，确保对媒体字段的精准监听、变化识别、并发下载、顺序写入和状态同步到 `merged` 表。

---

## 📁 涉及表与字段

### 源表（watch 监听对象）

| Board | 源表名                      | 监听字段    | Media 明细表        | logs 表  |
| ----- | ------------------------ | ------- | ----------------------- | -------- |
| car   | mls\_car\_raw\_records   | Media   | -                       |mls_car_logs|
| crea  | reso\_crea\_raw          | Media   | -                       |reso_crea_logs|
| bcre  | bridge\_bcre\_raw        | Media   | -                       |bridge_bcre_logs|
| rae   | mls\_rae\_raw\_records   | Media   | -                       |mls_rae_logs|
| treb  | reso\_treb\_evow\_merged | phoUrls | reso\_treb\_evow\_media | - |

---

## 🧩 总体结构模块
### 1. ChangeStream Watcher（每个 Board 启动一个main.go 程序[添加注释]）

#### 1.1 Media 字段监控逻辑（car/crea/bcre/rae） / media 字段（trb）

* 监听对应的源表名中 Media 字段的变更：updatedFields.Media 存在时触发

A. 获取旧列表
从 merged 表读取该 prop 之前的phoHL和docHL

区分：Photo → oldPhotosMap，Others → oldDocsMap

B. 获取新列表
从变更事件 updatedFields.Media 中取新 Media 列表。

执行 diff 比对（新增、删除、顺序变更），并构造任务队列：

* 下载新增图片，删除旧图片
* 下载成功后，更新 merged 表：添加 phoLH/docLH

  * 每个媒体任务携带：媒体类型（Photo/PDF）、MediaKey、文件 URL、原始顺序字段。
  * 使用带顺序约束的任务通道（channel + buffer + taskID），保持下载后按顺序写入本地。
  * 下载任务支持并发执行（每个 prop 限并发 3-5 个）。


### 2. 本地存储逻辑与路径规划

#### 1. 路径规则

```
[BasePath]/[L1]/[L2]/[MLS-ID]_[Hash(MediaKey)].jpg
```

* `L1`：日期（如 `202405`），源自 `propTs` 或 `onD` 字段
* `L2`：基于 `sid` 生成的 UUIDv5 前缀目录（用于目录分散）
* 文件名后缀按媒体类型自动判断：`.jpg` / `.pdf` / `.zip` / `.img`
* `Hash(MediaKey)`：媒体唯一 key（如 URL/MediaId）经 SHA1 → Hex → 前 4 位

#### 2. 字段写入（merged）

* `phoLH`: 记录所有已下载图片类文件 hash
* `docLH`: 记录其他类（PDF/ZIP）的文件 hash
* 若文件已存在、hash 一致，不重复写入

---
### 3. 内存映射与状态维护

* 内存 map：`map[string]string` 用于 sid -> L1/L2 路径映射
* 最多容纳 5000 条
* 新增或命中缓存后用于路径定位，避免重复 hash 计算

### 4. dirKeyStore 文件统计

* 每个 board 实例化一个 `dirKeyStore` 对象
* 每次下载调用：

  ```go
  dirKeyStore.AddDirStats("202405", "abc12", 1, 45) // 1 entity, 45 files
  ```
* 定时每 10 分钟批量入库

## 🚦 图片下载任务执行

### 协程设计

* 每个 prop 单独维护一个 download session：

  * 任务队列为 FIFO 保序通道
  * 可配置并发数：`MaxParallelDownloads = 3`
  * 协程通过带任务编号的 channel 接收下载任务并执行

### 下载任务结构

```go
struct MediaTask {
  TaskID string
  Sid    string
  Order  int
  MediaKey string
  URL    string
  Type   string // photo, pdf, zip
  DestPath string
}
```

### 下载逻辑

* 若目标文件已存在且哈希一致：跳过下载
* 下载失败：记录错误日志（带 sid, URL, 错误信息）并跳过该文件
* 下载成功：保存文件，更新内存缓存（dirMap），累积 hash 记录（用于后续 merged 写入）

---

## ♻️ 图片变化比对逻辑

### 对比依据

* 比较旧 media 与新 media 的 MediaKey + Order（或 URL）
* 对于新增：添加下载任务
* 对于删除：生成删除任务，移除本地文件
* 若顺序变化或大小优先级变化：认为是更新，执行替换

---

## 🔧 内存缓存与路径加速

* 使用 `map[sid]string` 维护 sid → 路径前缀缓存
* 最大容量：5000，LRU 淘汰
* 避免重复目录生成和 hash 运算

---

## 📌 错误处理与回退

* 所有删除操作失败不影响 merged 更新
* 所有下载操作失败不做merged 更新，并记录日志
* 下载失败但存在旧文件：保持 merged 中记录（避免误清除）
* merged 写入前校验 hash 列表是否为空，若为空则不写入字段
* 所有错误统一通过日志系统打点，供后续重试调度器分析

---


## ✅ 图片下载一致性与校验

### 图片哈希与碰撞概率

* 每张图片文件名为 `Hash(MediaKey)`，4位十六进制
* 50张图理论碰撞概率为 1.9%，存在一定风险

### 下载后一致性校验逻辑

* 每个 `prop` 维护一个 `phoLH` 列表：已下载图片 hash
* 下载图片后：重新生成 hash 列表，对比已存在的 `phoLH`

#### 比对结果分类

| 状态    | 描述                  |
| ----- | ------------------- |
| ✅ 未变更 | hash 存在于旧 `phoLH` 中 |
| ➕ 新增  | hash 新增，不在旧列表中      |
| ➖ 删除  | 旧 `phoLH` 中存在但未再出现  |

* 下载失败或校验失败：不更新 `phoLH`

---

## ☑️ DRY 与依赖注入

### DRY 原则

* 相同处理逻辑封装成独立模块：如图片对比函数、下载器、哈希生成器、字段写入器
* 明确拆分职责：watch、差异分析、文件操作、merged 写入分离

### Dependency Injection

* 所有核心组件（如 `dirKeyStore`, `Downloader`, `Watcher`, `MergedWriter`）注入使用，方便测试与替换
* 配置通过启动参数传入（如 board 名称、数据源、路径前缀）

---

## 🏁 最终目标：merged 更新字段

| 字段名     | 含义             | 示例值                        |
| ------- | -------------- | -------------------------- |
| `phoLH` | 图片类 hash 列表    | `[2032, 2345]`         |
| `docLH` | 文档类（非图）hash 列表 | `["9f8a.pdf", "a7b1.img"]` |
| `tnLH` | 文档类（非图）hash 列表 | `2032` |

* 确保全部下载成功且一致性验证通过，才写入上述字段
* 每个 prop 仅记录一组文件清单

---

如需继续扩展视频、音频等文件处理逻辑，可在此架构基础上进一步扩展字段与处理流程模块。



迁移【先上reso】：
1. 只有rezs有的，同样获取hash，迁移后，在merged中添加新路径【老路径最后统一删除】。迁移  读文件 有图片的 找到记录，更改【根据appweb】
2. rez+reso，reso会download，按照reso走。如果2个图片不一致，按照图片多的为准。