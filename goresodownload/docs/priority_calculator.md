# Property Priority Calculator

The Priority Calculator is a sophisticated system that calculates priority scores for property download queue items based on multiple factors. It implements the logic from the original CoffeeScript `calcPropPriority` function with board-specific field mappings.

## Overview

The priority calculator assigns scores to properties to determine their processing order in the download queue. Higher scores indicate higher priority. The system considers factors like photo availability, listing status, location, property type, and timing.

## Priority Scoring System

| Factor | Points | Description |
|--------|--------|-------------|
| No photos | +30,000 | Highest priority for properties without photos |
| Active status | +10,000 | Active, new, or 'a' status listings |
| Current year | +5,000 | Listings from the current year |
| GTA region | +500 | Toronto, Peel, York, Durham, Halton areas |
| New listing | +300 | Listed within the last 3 days |
| Residential/Condo | +200 | Residential or condo property types |
| Sold status | +50 | Sold properties |
| Leased status | +30 | Leased properties |
| Ontario province | +20 | Properties in Ontario |
| Days on market | +0 to +30 | Bonus for 0-30 days (30-DOM) |

## Board-Specific Field Mappings

Different MLS boards use different field names for the same concepts. The calculator normalizes these fields:

### TRB (Toronto Regional Real Estate Board)
- **Photos**: `media` field
- **Status**: `MlsStatus`
- **Date**: `ListingContractDate` or `OriginalEntryTimestamp`
- **Region**: `CountyOrParish`
- **Property Type**: `PropertyType`
- **Province**: `StateOrProvince`
- **Days on Market**: `DaysOnMarket`

### BRE (BC Real Estate)
- **Photos**: `Media` field
- **Date**: `ListingContractDate` or `ts`
- **Region**: `CountyOrParish`
- **Property Type**: `PropertyType`
- **Province**: `StateOrProvince`
- **Days on Market**: Calculated from `OffMarketDate` and `ListingContractDate`

### DDF (Data Distribution Facility)
- **Photos**: `Media` field
- **Status**: `MlsStatus`
- **Date**: `OriginalEntryTimestamp`
- **Region**: `CityRegion`
- **Property Type**: `PropertySubType` (requires special formatting)
- **Province**: `StateOrProvince`
- **Days on Market**: Calculated from `OriginalEntryTimestamp` and `ExpirationDate`

### CLG (Calgary)
- **Photos**: `Media` field
- **Status**: `MlsStatus`
- **Date**: `ListingContractDate` or `ts`
- **Region**: `CountyOrParish`
- **Property Type**: `PropertyType`
- **Province**: `StateOrProvince`
- **Days on Market**: `DaysOnMarket`

### EDM (Edmonton)
- **Photos**: `Media` field
- **Status**: `MlsStatus`
- **Date**: `ListingContractDate`
- **Region**: `StateRegion`
- **Property Type**: `PropertyType`
- **Province**: `StateOrProvince`
- **Days on Market**: `CumulativeDaysOnMarket`

### CAR (Canadian Association of Realtors)
- **Photos**: `Media` field
- **Status**: `MlsStatus`
- **Date**: `ListingContractDate`
- **Region**: `CountyOrParish`
- **Property Type**: `PropertyType`
- **Province**: `StateOrProvince`
- **Days on Market**: `DaysOnMarket`

## Usage

### Basic Usage

```go
import "github.com/real-rm/goresodownload"

// Calculate priority for a property
priority := goresodownload.CalculatePriority(record, boardType, existMergedProp)
```

### In Main Application

The priority calculator is integrated into the main application's `getPriority` function:

```go
func getPriority(changeDoc bson.M, prop bson.M) int {
    // Extract property ID
    propID, ok := prop["_id"].(string)
    if !ok {
        return 1000 // fallback
    }

    // Fetch existing merged property
    existMergedProp, err := goresodownload.GetExistingMergedProperty(propID, gBoardType)
    if err != nil {
        return 1000 // fallback
    }

    // Calculate priority
    return goresodownload.CalculatePriority(prop, gBoardType, existMergedProp)
}
```

### Getting Priority Breakdown

For debugging and analysis, you can get a detailed breakdown:

```go
breakdown := goresodownload.GetPriorityBreakdown(record, boardType, existMergedProp)
// Returns map[string]int with individual component scores
```

## Examples

### High Priority Property
```go
record := bson.M{
    "MlsStatus":           "Active",
    "ListingContractDate": time.Now().Format("2006-01-02"),
    "CountyOrParish":      "Toronto",
    "PropertyType":        "Residential",
    "StateOrProvince":     "ON",
    "DaysOnMarket":        5,
}

priority := CalculatePriority(record, "TRB", nil) // nil = no photos
// Result: ~46,045 points
// Breakdown: 30000 + 10000 + 5000 + 500 + 300 + 200 + 20 + 25
```

### Medium Priority Property
```go
record := bson.M{
    "MlsStatus":           "Sold",
    "ListingContractDate": "2023-06-01",
    "CountyOrParish":      "Toronto",
    "PropertyType":        "Residential",
    "StateOrProvince":     "ON",
    "DaysOnMarket":        15,
}

existingMerged := bson.M{
    "media": []interface{}{
        bson.M{"key": "photo1", "url": "http://example.com/1.jpg"},
    },
}

priority := CalculatePriority(record, "TRB", existingMerged)
// Result: 785 points
// Breakdown: 0 + 0 + 0 + 500 + 0 + 200 + 50 + 20 + 15
```

## Implementation Details

### Photo Detection
- For TRB: Checks both `phoUrls` and `media` fields
- For other boards: Checks `Media` field
- Returns `false` if field is missing, null, or empty array

### Date Parsing
Supports multiple date formats:
- `2006-01-02`
- `2006-01-02T15:04:05Z`
- `2006-01-02T15:04:05.000Z`
- `2006-01-02T15:04:05-07:00`

### Region Detection
Uses case-insensitive regex matching for GTA regions:
- Toronto, Peel, York, Durham, Halton

### Property Type Detection
Uses case-insensitive regex matching for residential properties:
- "residential", "condo"

## Testing

The priority calculator includes comprehensive tests:

```bash
cd goresodownload
go test -v -run TestCalculatePriority
go test -v priority_calculator_test.go priority_calculator.go media_diff_analyzer.go
```

## Error Handling

- Invalid board types return error
- Missing or invalid data fields are handled gracefully
- Database connection errors fall back to default priority (1000)
- Malformed dates are treated as nil

## Performance Considerations

- Database queries are minimized by fetching existing merged properties only when needed
- Field normalization is done in-memory
- Regular expressions are compiled once and reused
- No external API calls or heavy computations
