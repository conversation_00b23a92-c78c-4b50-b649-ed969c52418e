module github.com/real-rm/goresodownload

go 1.24.4

require (
	github.com/real-rm/gobase v0.0.0-20250611193001-aae5e62b1e23
	github.com/real-rm/goconfig v0.0.0-20250501202444-47bb8cfe118f
	github.com/real-rm/gofile v0.0.0-20250702151500-7c5446bd73db
	github.com/real-rm/gohelper v0.0.0-20250522214632-2a6c4ec2bb38
	github.com/real-rm/golog v0.0.0-20250508200600-5a27e4511a57
	github.com/real-rm/gomongo v0.0.0-20250501204550-1a32caf3b01b
	github.com/real-rm/goprocess v0.0.0-20250618153551-a0a2f0b0de90
	github.com/real-rm/gospeedmeter v0.0.0-20250509214336-11530e84d838
	github.com/real-rm/gostreaming v0.0.0-20250509214157-e3c0c300d016
	github.com/real-rm/gowatch v0.0.0-20250627152055-f63ef2cd24cf
	github.com/stretchr/testify v1.10.0
	go.mongodb.org/mongo-driver v1.17.3
)

require (
	github.com/HugoSmits86/nativewebp v1.1.4 // indirect
	github.com/aws/aws-sdk-go v1.55.6 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/golang-jwt/jwt/v5 v5.2.2 // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/jaytaylor/html2text v0.0.0-20230321000545-74c2419ad056 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/klauspost/compress v1.17.2 // indirect
	github.com/mattn/go-runewidth v0.0.9 // indirect
	github.com/montanaflynn/stats v0.7.1 // indirect
	github.com/olekukonko/tablewriter v0.0.5 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/real-rm/folder_sync v0.0.0-20250702024958-c5e7c4d9f304 // indirect
	github.com/real-rm/go-toml v0.0.0-20250401202034-5e664e72ac3e // indirect
	github.com/real-rm/gomail v0.0.0-20250520215336-a7d721236dde // indirect
	github.com/real-rm/gosms v0.0.0-20250522214413-3391e0a4691c // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/ssor/bom v0.0.0-20170718123548-6386211fdfcf // indirect
	github.com/twilio/twilio-go v1.26.1 // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.1.2 // indirect
	github.com/xdg-go/stringprep v1.0.4 // indirect
	github.com/youmark/pkcs8 v0.0.0-20240726163527-a2c0da244d78 // indirect
	golang.org/x/crypto v0.26.0 // indirect
	golang.org/x/image v0.24.0 // indirect
	golang.org/x/net v0.21.0 // indirect
	golang.org/x/sync v0.12.0 // indirect
	golang.org/x/text v0.23.0 // indirect
	gopkg.in/alexcesaro/quotedprintable.v3 v3.0.0-20150716171945-2caba252f4dc // indirect
	gopkg.in/gomail.v2 v2.0.0-20160411212932-81ebce5c23df // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

replace github.com/real-rm/gofile => ../gofile
