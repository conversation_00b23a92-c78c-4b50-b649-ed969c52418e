package goresodownload

import (
	"context"
	"fmt"
	"image/jpeg"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/real-rm/gofile"
	"github.com/real-rm/gofile/levelStore"
	golog "github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	THUMBNAIL_WIDTH  = 240
	THUMBNAIL_HEIGHT = 160
)

// FailedTask represents a failed download task in the database
type FailedTask struct {
	ID         primitive.ObjectID `bson:"_id,omitempty"`
	PropID     string             `bson:"prop_id"`
	ImageURL   string             `bson:"image_url"`
	ErrMsg     string             `bson:"err_msg"`
	RetryCount int                `bson:"retry_count"`
}

// DownloaderConfig represents the configuration for the downloader
type DownloaderConfig struct {
	// Concurrency settings
	DownloadConcurrency int `json:"download_concurrency"`
	DeleteConcurrency   int `json:"delete_concurrency"`

	// Retry settings
	MaxRetries int `json:"max_retries"`

	// Alert settings
	ConsecutiveFailuresThreshold int `json:"consecutive_failures_threshold"` // Default: 3
}

// NewDefaultConfig returns a new DownloaderConfig with default values
func NewDefaultConfig() *DownloaderConfig {
	return &DownloaderConfig{
		DownloadConcurrency:          5, // Restored to 5 as requested
		DeleteConcurrency:            5, // Restored to 5 for consistency
		MaxRetries:                   2, // Keep at 2 to prevent failure accumulation
		ConsecutiveFailuresThreshold: 3,
	}
}

// DownloaderOptions contains all options for creating a Downloader
type DownloaderOptions struct {
	Config       *DownloaderConfig
	StoragePaths []string
	MergedCol    *gomongo.MongoCollection
	FailedCol    *gomongo.MongoCollection
}

// WorkerPool manages a fixed number of workers for downloads and deletions
type WorkerPool struct {
	downloader      *Downloader // Reference to parent downloader
	downloadWorkers int
	deleteWorkers   int
	downloadChan    chan DownloadJob
	deleteChan      chan DeleteJob
	wg              sync.WaitGroup
	ctx             context.Context
	cancel          context.CancelFunc
}

// DownloadJob represents a download task with context
type DownloadJob struct {
	Task   MediaTask
	PropID string
	Result chan error
}

// DeleteJob represents a delete task with context
type DeleteJob struct {
	Task   DeleteTask
	Result chan error
}

// Downloader implements the media download functionality
type Downloader struct {
	*DownloaderOptions
	workerPool *WorkerPool
}

// NewWorkerPool creates a new worker pool
func NewWorkerPool(downloader *Downloader, downloadWorkers, deleteWorkers int) *WorkerPool {
	ctx, cancel := context.WithCancel(context.Background())

	wp := &WorkerPool{
		downloader:      downloader,
		downloadWorkers: downloadWorkers,
		deleteWorkers:   deleteWorkers,
		downloadChan:    make(chan DownloadJob, downloadWorkers*2), // Buffer for smooth operation
		deleteChan:      make(chan DeleteJob, deleteWorkers*2),
		ctx:             ctx,
		cancel:          cancel,
	}

	wp.start()
	return wp
}

// start initializes and starts the worker pool
func (wp *WorkerPool) start() {
	golog.Info("Starting worker pool", "downloadWorkers", wp.downloadWorkers, "deleteWorkers", wp.deleteWorkers)

	// Start download workers
	for i := 0; i < wp.downloadWorkers; i++ {
		wp.wg.Add(1)
		go wp.downloadWorker(i)
	}

	// Start delete workers
	for i := 0; i < wp.deleteWorkers; i++ {
		wp.wg.Add(1)
		go wp.deleteWorker(i)
	}
}

// downloadWorker processes download jobs
func (wp *WorkerPool) downloadWorker(workerID int) {
	defer wp.wg.Done()
	golog.Debug("Download worker started", "workerID", workerID)

	for {
		select {
		case job, ok := <-wp.downloadChan:
			if !ok {
				golog.Debug("Download worker stopping", "workerID", workerID)
				return
			}

			// Process the download job
			err := wp.processDownloadJob(job)
			job.Result <- err

		case <-wp.ctx.Done():
			golog.Debug("Download worker cancelled", "workerID", workerID)
			return
		}
	}
}

// deleteWorker processes delete jobs
func (wp *WorkerPool) deleteWorker(workerID int) {
	defer wp.wg.Done()
	golog.Debug("Delete worker started", "workerID", workerID)

	for {
		select {
		case job, ok := <-wp.deleteChan:
			if !ok {
				golog.Debug("Delete worker stopping", "workerID", workerID)
				return
			}

			// Process the delete job
			err := wp.processDeleteJob(job)
			job.Result <- err

		case <-wp.ctx.Done():
			golog.Debug("Delete worker cancelled", "workerID", workerID)
			return
		}
	}
}

// processDownloadJob processes a single download job
func (wp *WorkerPool) processDownloadJob(job DownloadJob) error {
	// Now we have access to the downloader instance
	golog.Debug("Processing download job", "propID", job.PropID, "task", job.Task.DestPath)
	return wp.downloader.downloadTask(job.Task, job.PropID)
}

// processDeleteJob processes a single delete job
func (wp *WorkerPool) processDeleteJob(job DeleteJob) error {
	// Now we have access to the downloader instance
	golog.Debug("Processing delete job", "task", job.Task.Path)
	return wp.downloader.deleteTask(job.Task)
}

// Stop gracefully shuts down the worker pool
func (wp *WorkerPool) Stop() {
	golog.Info("Stopping worker pool")
	wp.cancel()
	close(wp.downloadChan)
	close(wp.deleteChan)
	wp.wg.Wait()
	golog.Info("Worker pool stopped")
}

// NewDownloader creates a new Downloader instance with worker pool
func NewDownloader(opts *DownloaderOptions) (*Downloader, error) {
	if opts == nil {
		opts = &DownloaderOptions{
			Config: NewDefaultConfig(),
		}
	}
	if opts.Config == nil {
		opts.Config = NewDefaultConfig()
	}

	// Create downloader first
	downloader := &Downloader{
		DownloaderOptions: opts,
	}

	// Then create worker pool with reference to downloader
	downloader.workerPool = NewWorkerPool(downloader, opts.Config.DownloadConcurrency, opts.Config.DeleteConcurrency)

	return downloader, nil
}

// Stop gracefully shuts down the downloader and its worker pool
func (d *Downloader) Stop() {
	if d.workerPool != nil {
		d.workerPool.Stop()
	}
}

// ProcessAnalysisResult processes the analysis result and performs downloads/deletions
func (d *Downloader) ProcessAnalysisResult(result *AnalysisResult) (tnChangedNum int, err error) {
	var wg sync.WaitGroup
	errChan := make(chan error, 2) // Buffer size 2 for both download and deletion errors

	// Process downloads
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := d.processDownloads(result.DownloadTasks, result.ID); err != nil {
			errChan <- fmt.Errorf("download processing failed: %w", err)
		}
	}()

	// Process deletions
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := d.processDeletions(result.DeleteTasks); err != nil {
			errChan <- fmt.Errorf("deletion processing failed: %w", err)
		}
	}()

	// Wait for both operations to complete
	wg.Wait()
	close(errChan)

	// Check for errors
	var errs []error
	for err := range errChan {
		errs = append(errs, err)
	}
	if len(errs) > 0 {
		return 0, fmt.Errorf("processing failed: %v", errs)
	}

	// Generate thumbnail and update document
	var thumbnailHash int32
	if result.NewFirstPic.MediaKey != "" {
		var err error
		thumbnailHash, tnChangedNum, err = d.generateThumbnailAndUpdate(result.NewFirstPic, result.OldTnLH)
		if err != nil {
			golog.Error("failed to generate thumbnail",
				"error", err,
				"mediaKey", result.NewFirstPic.MediaKey,
				"url", result.NewFirstPic.URL,
				"oldTnLH", result.OldTnLH)
		}
	}

	// Update document with all fields
	golog.Debug("ProcessAnalysisResult", "result", result, "thumbnailHash", thumbnailHash)
	if err := d.updateMergedDoc(result, thumbnailHash); err != nil {
		golog.Error("failed to update merged document", "error", err)
	}
	golog.Info("ProcessAnalysisResult finished", "ID", result.ID)
	return tnChangedNum, nil
}

// updateMergedDoc updates the merged document with PhoLH, DocLH and optional thumbnail hash
func (d *Downloader) updateMergedDoc(result *AnalysisResult, thumbnailHash int32) error {
	if result == nil {
		golog.Error("updateMergedDoc", "result", result)
		return fmt.Errorf("result is nil")
	}

	// Check if document exists
	var doc bson.M
	err := d.MergedCol.FindOne(context.Background(), bson.M{"_id": result.ID}).Decode(&doc)
	if err != nil {
		golog.Error("updateMergedDoc", "error", err)
		return fmt.Errorf("document not found: %w", err)
	}

	update := bson.M{}
	update["$set"] = bson.M{}
	if len(result.PhoLH) == 0 && len(result.DocLH) == 0 {
		return nil
	}
	golog.Debug("updateMergedDoc", "result", result.DocLH, "thumbnailHash", thumbnailHash)
	if len(result.PhoLH) > 0 {
		update["$set"].(bson.M)["phoLH"] = result.PhoLH
	}
	if len(result.DocLH) > 0 {
		update["$set"].(bson.M)["docLH"] = result.DocLH
	}

	// Add thumbnail hash if available
	if thumbnailHash != 0 {
		update["$set"].(bson.M)["tnLH"] = thumbnailHash
	}

	// Only for test
	update["$set"].(bson.M)["DownloadTasks"] = result.DownloadTasks
	update["$set"].(bson.M)["DeleteTasks"] = result.DeleteTasks

	golog.Debug("updateMergedDoc", "update", update)

	_, err = d.MergedCol.UpdateOne(
		context.Background(),
		bson.M{"_id": result.ID},
		update,
	)
	return err
}

// processDownloads handles the download tasks using global worker pool
func (d *Downloader) processDownloads(tasks []MediaTask, id string) error {
	if len(tasks) == 0 {
		golog.Info("no tasks to download", "id", id)
		return nil
	}

	golog.Info("Submitting download tasks to global worker pool", "taskCount", len(tasks), "propID", id)

	// Create result channels for each task
	results := make([]chan error, len(tasks))
	for i := range results {
		results[i] = make(chan error, 1)
	}

	// Submit all tasks to the global worker pool
	for i, task := range tasks {
		job := DownloadJob{
			Task:   task,
			PropID: id,
			Result: results[i],
		}

		select {
		case d.workerPool.downloadChan <- job:
			// Task submitted successfully
		default:
			// Channel is full, this shouldn't happen with proper buffering
			golog.Debug("Download channel full, waiting", "propID", id)
			d.workerPool.downloadChan <- job // Block until space available
		}
	}

	// Wait for all tasks to complete and collect errors
	var firstErr error
	for i, resultChan := range results {
		err := <-resultChan
		if err != nil && firstErr == nil {
			firstErr = err
			golog.Error("Download task failed", "taskIndex", i, "propID", id, "error", err)
		}
	}

	golog.Info("All download tasks completed", "taskCount", len(tasks), "propID", id)
	return firstErr
}

// processDeletions handles the deletion tasks using global worker pool
func (d *Downloader) processDeletions(tasks []DeleteTask) error {
	if len(tasks) == 0 {
		golog.Debug("no tasks to delete")
		return nil
	}

	golog.Info("Submitting delete tasks to global worker pool", "taskCount", len(tasks))

	// Create result channels for each task
	results := make([]chan error, len(tasks))
	for i := range results {
		results[i] = make(chan error, 1)
	}

	// Submit all tasks to the global worker pool
	for i, task := range tasks {
		job := DeleteJob{
			Task:   task,
			Result: results[i],
		}

		select {
		case d.workerPool.deleteChan <- job:
			// Task submitted successfully
		default:
			// Channel is full, this shouldn't happen with proper buffering
			golog.Warn("Delete channel full, waiting")
			d.workerPool.deleteChan <- job // Block until space available
		}
	}

	// Wait for all tasks to complete and collect errors
	var firstErr error
	for i, resultChan := range results {
		err := <-resultChan
		if err != nil && firstErr == nil {
			firstErr = err
			golog.Error("Delete task failed", "taskIndex", i, "error", err)
		}
	}

	golog.Info("All delete tasks completed", "taskCount", len(tasks))
	return firstErr
}

// downloadTask handles a single download task
func (d *Downloader) downloadTask(task MediaTask, id string) error {
	var lastErr error

	for i := range make([]struct{}, d.Config.MaxRetries) {
		// Prepare full paths for all storage locations
		var fullPaths []string
		for _, basePath := range d.StoragePaths {
			fullPath := filepath.Join(basePath, task.DestPath)
			fullPaths = append(fullPaths, fullPath)
		}

		// Ensure URL has proper protocol prefix
		url := task.URL
		if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
			url = "https://" + url
		}

		golog.Debug("downloading file", "url", url, "savePaths", fullPaths)
		opts := &gofile.DownloadAndSaveFileOptions{
			URL:          url,
			SavePaths:    fullPaths,
			CompressWebP: false,
			IsPhoto:      task.IsPhoto,
			MaxRetries:   1,
		}
		// Download and save the file
		golog.Debug("downloadTask", "opts", opts)
		_, err := gofile.DownloadAndSaveFile(opts)
		if err == nil {
			golog.Debug("downloadTask", "success", opts.URL, "savePaths", fullPaths)
			return nil
		}

		lastErr = err
		golog.Debug("downloadTask", "error", err)
		golog.Warn("download failed, retrying",
			"url", task.URL,
			"attempt", i+1,
			"maxRetries", d.Config.MaxRetries,
			"error", err.Error())

		// Exponential backoff with jitter to avoid thundering herd
		// Base delay: 2^attempt seconds, with max of 30 seconds (reduced from 60)
		baseDelay := time.Duration(1<<uint(i)) * time.Second
		if baseDelay > 30*time.Second {
			baseDelay = 30 * time.Second
		}
		// Add jitter (±25% of base delay)
		jitterFactor := float64(2*time.Now().UnixNano()%2 - 1) // -1 or +1
		jitter := time.Duration(float64(baseDelay) * 0.25 * jitterFactor)
		sleepTime := baseDelay + jitter

		golog.Debug("waiting before retry with exponential backoff",
			"sleepTime", sleepTime, "attempt", i+1, "baseDelay", baseDelay)
		time.Sleep(sleepTime)
	}
	// only record failed last attempt
	if err := d.recordFailedTask(task, id, lastErr, d.Config.MaxRetries); err != nil {
		golog.Error("failed to record failed task", "error", err)
	}
	return fmt.Errorf("failed after %d attempts: %w", d.Config.MaxRetries, lastErr)
}

// deleteTask handles a single deletion task
func (d *Downloader) deleteTask(task DeleteTask) error {
	for _, basePath := range d.StoragePaths {
		fullPath := filepath.Join(basePath, task.Path)

		// Check if path exists and is a file
		fileInfo, err := os.Stat(fullPath)
		if err != nil {
			if os.IsNotExist(err) {
				golog.Info("file does not exist, skipping deletion", "path", fullPath)
				continue
			}
			golog.Error("failed to stat file", "error", err, "path", fullPath)
			return fmt.Errorf("failed to stat file %s: %w", fullPath, err)
		}

		// Skip if it's a directory
		if fileInfo.IsDir() {
			golog.Info("path is a directory, skipping deletion", "path", fullPath)
			continue
		}

		// Delete the file
		if err := os.Remove(fullPath); err != nil {
			golog.Error("failed to delete file", "error", err, "path", fullPath)
			return fmt.Errorf("failed to delete file %s: %w", fullPath, err)
		}
	}
	return nil
}

// recordFailedTask records a failed download task in the database
func (d *Downloader) recordFailedTask(task MediaTask, id string, err error, retryCount int) error {
	failedTask := FailedTask{
		PropID:     id,
		ImageURL:   task.URL,
		ErrMsg:     err.Error(),
		RetryCount: retryCount,
	}
	if d.FailedCol == nil {
		return fmt.Errorf("FailedCol is nil, cannot record failed task")
	}
	_, err = d.FailedCol.InsertOne(context.Background(), failedTask)
	return err
}

// generateThumbnailAndUpdate generates a thumbnail and returns the hash value
func (d *Downloader) generateThumbnailAndUpdate(task MediaTask, oldTnLH int32) (int32, int, error) {
	var tnChangedNum int
	// Create thumbnail key
	thumbKey := task.MediaKey + "-t"
	sid := task.Sid

	// Generate hash and filename
	hash := levelStore.MurmurToInt32(thumbKey)
	golog.Debug("generateThumbnailAndUpdate", "hash", hash, "oldTnLH", oldTnLH)
	if hash == oldTnLH { // thumbnail not changed
		golog.Info("thumbnail already exists, skipping", "hash", hash)
		return hash, 0, nil
	}
	if oldTnLH != 0 && hash == 0 {
		tnChangedNum = -1
	}
	if oldTnLH == 0 && hash != 0 {
		tnChangedNum = 1
	}

	fileName, err := levelStore.Int32ToBase62(hash)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to generate thumbnail filename: %w", err)
	}

	// Download and resize image
	newImg, err := gofile.DownloadAndResizeImage(task.URL, THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to download and resize image: %w", err)
	}
	// Save thumbnail to all storage paths
	for _, basePath := range d.StoragePaths {
		// Get the directory of the original file
		dirPath := filepath.Dir(filepath.Join(basePath, task.DestPath))

		// Create the directory if it doesn't exist
		if err := os.MkdirAll(dirPath, 0755); err != nil {
			return 0, 0, fmt.Errorf("failed to create directory: %w", err)
		}

		// delete old thumbnail
		if oldTnLH != 0 {
			fileNameOld, err := levelStore.Int32ToBase62(oldTnLH)
			if err != nil {
				golog.Error("failed to generate old thumbnail filename", "error", err, "oldTnLH", oldTnLH)
				return 0, 0, fmt.Errorf("failed to generate old thumbnail filename: %w", err)
			}
			filePathOld := filepath.Join(dirPath, sid+"_"+fileNameOld+".jpg")
			if _, err := os.Stat(filePathOld); err == nil {
				if err := os.Remove(filePathOld); err != nil {
					golog.Warn("failed to delete old thumbnail", "error", err, "path", filePathOld)
				}
			}
		}
		golog.Debug("generateThumbnailAndUpdate", "hash", hash, "fileName", fileName)
		// Save as JPEG in the same directory as the original file
		filePath := filepath.Join(dirPath, sid+"_"+fileName+".jpg")
		file, err := os.Create(filePath)
		if err != nil {
			return 0, 0, fmt.Errorf("failed to create thumbnail file: %w", err)
		}
		defer func() {
			if err := file.Close(); err != nil {
				golog.Error("failed to close thumbnail file", "error", err, "path", filePath)
			}
		}()

		if err := jpeg.Encode(file, newImg, &jpeg.Options{Quality: 100}); err != nil {
			return 0, 0, fmt.Errorf("failed to encode thumbnail: %w", err)
		}
	}
	return hash, tnChangedNum, nil
}
