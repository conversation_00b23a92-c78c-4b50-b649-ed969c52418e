package main

import (
	"fmt"
	"github.com/real-rm/gofile/levelStore"
)

func main() {
	// Test the expected keys
	key1 := "trreb/c11897846/audio"
	key2 := "a3c5f9ba-0e09-4125-b773-30135ddc4fae"
	
	hash1 := levelStore.MurmurToInt32(key1)
	hash2 := levelStore.MurmurToInt32(key2)
	
	fmt.Printf("Key1: '%s' -> Hash: %d\n", key1, hash1)
	fmt.Printf("Key2: '%s' -> Hash: %d\n", key2, hash2)
	
	// Check if either matches the failing hash
	failingHash := int32(-1491397181)
	fmt.Printf("Failing hash: %d\n", failingHash)
	
	if hash1 == failingHash {
		fmt.Printf("Hash1 matches failing hash! Key: %s\n", key1)
	}
	if hash2 == failingHash {
		fmt.Printf("Hash2 matches failing hash! Key: %s\n", key2)
	}
	
	// Test some other possible keys that might be in the test data
	testKeys := []string{
		"trreb/c11897846/audio",
		"a3c5f9ba-0e09-4125-b773-30135ddc4fae",
		"c11897846",
		"audio",
		"test",
		"document",
	}
	
	fmt.Println("\nTesting various keys:")
	for _, key := range testKeys {
		hash := levelStore.MurmurToInt32(key)
		fmt.Printf("'%s' -> %d\n", key, hash)
		if hash == failingHash {
			fmt.Printf("  *** MATCH! This key produces the failing hash ***\n")
		}
	}
}
