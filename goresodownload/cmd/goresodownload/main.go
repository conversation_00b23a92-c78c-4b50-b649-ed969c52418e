/*
This is goresodownload batch.
New config:
	./start.sh -n goresodownload -d "goresodownload" -cmd "cmd/goresodownload/main.go -board CAR -force -dryrun"
createDate:    2025-06-05
Author:        Maggie
Run frequency: always
*/

package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	gobase "github.com/real-rm/gobase"
	"github.com/real-rm/gofile/levelStore"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"github.com/real-rm/goprocess"
	"github.com/real-rm/goresodownload"
	gospeedmeter "github.com/real-rm/gospeedmeter"
	"github.com/real-rm/gowatch"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	gWatchedObject   *gowatch.WatchObject
	gUpdateSysData   gowatch.UpdateSysDataFunc
	SysdataCol       *gomongo.MongoCollection
	FailedCol        *gomongo.MongoCollection
	QueueCol         *gomongo.MongoCollection
	gProcessMonitor  *goprocess.ProcessMonitor
	dryRun           bool
	force            bool
	gBoardType       string
	analyzer         *goresodownload.MediaDiffAnalyzer
	downloader       *goresodownload.Downloader
	dirStore         *levelStore.DirKeyStore
	downloadQueue    *goresodownload.ResourceDownloadQueue
	lastTokenPrintTs = time.Now()
	gStat            = make(map[string]int)
	tokenMu          sync.Mutex
	statMu           sync.Mutex
	speedMeter       *gospeedmeter.SpeedMeter
	batchSize        int
)

const (
	HALF_DAY_IN_MS                = 12 * 3600 * 1000
	UPDATE_PROCESS_STATE_INTERVAL = 10 * 60 * 1000 // 10 minutes
	DEFAULT_BATCH_USER_ID         = "batch"
	DEFAULT_BATCH_SIZE            = 2 // Restored to 2 as requested
)

var PROCESS_STATUS_ID string

func init() {
	// CRITICAL: Replace default HTTP transport immediately to prevent connection leaks
	http.DefaultTransport = &http.Transport{
		DisableKeepAlives:     true,
		MaxIdleConns:          0,
		MaxIdleConnsPerHost:   0,
		MaxConnsPerHost:       1,
		IdleConnTimeout:       1 * time.Second,
		ResponseHeaderTimeout: 10 * time.Second,
		ExpectContinueTimeout: 3 * time.Second,
		TLSHandshakeTimeout:   8 * time.Second,
		DisableCompression:    true,
		ForceAttemptHTTP2:     false,
		DialContext: (&net.Dialer{
			Timeout:   8 * time.Second,
			KeepAlive: 0, // Completely disable keep-alive
		}).DialContext,
	}

	// Parse command line flags
	flag.StringVar(&gBoardType, "board", "", "Board type (CAR/DDF/BRE/EDM/TRB)")
	flag.BoolVar(&dryRun, "dryrun", false, "Run in dry run mode")
	flag.BoolVar(&force, "force", false, "Force start a new process")
	flag.IntVar(&batchSize, "batchSize", DEFAULT_BATCH_SIZE, "Batch size for processing queue items")
	flag.Parse()

	// Validate board type
	if gBoardType == "" {
		golog.Fatal("Board type is required")
	}
	gBoardType = strings.ToUpper(gBoardType)
	PROCESS_STATUS_ID = "watchPhotoDownload" + gBoardType
	if !isValidBoard(gBoardType) {
		golog.Fatalf("Invalid board type: %s", gBoardType)
	}

	// Initialize base[config, logging]
	if err := gobase.InitBase(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}

	// Initialize MongoDB
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Initialize collections
	SysdataCol = gomongo.Coll("rni", "sysdata")
	FailedCol = gomongo.Coll("rni", "reso_photo_download_failed")
	QueueCol = gomongo.Coll("rni", "reso_photo_download_queue")
	gUpdateSysData = gowatch.GetUpdateSysdataFunction(SysdataCol, PROCESS_STATUS_ID)

	// Initialize ProcessMonitor
	var err error
	gProcessMonitor, err = goprocess.NewProcessMonitor(goprocess.ProcessMonitorOptions{
		ProcessStatusCol: gomongo.Coll("rni", "processStatus"),
		UpdateInterval:   time.Duration(UPDATE_PROCESS_STATE_INTERVAL) * time.Millisecond,
		Multiplier:       1.5, // Allow 50% more time than the update interval
		ID:               PROCESS_STATUS_ID,
	})
	if err != nil {
		golog.Fatalf("Failed to initialize ProcessMonitor: %v", err)
	}

	// Initialize speed meter
	speedMeter = gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})
}

func isValidBoard(board string) bool {
	validBoards := map[string]bool{
		"CAR": true,
		"DDF": true,
		"BRE": true,
		"EDM": true,
		"TRB": true,
	}
	return validBoards[board]
}

// createComponents creates and returns the analyzer, downloader, dirStore, and queue components
func createComponents() error {
	golog.Info("Creating components", "boardType", gBoardType)

	// Create analyzer
	golog.Debug("Creating analyzer")
	analyzer = goresodownload.NewMediaDiffAnalyzer()

	// Create downloader
	golog.Debug("Creating downloader")
	var err error
	downloader, err = createDownloader()
	if err != nil {
		golog.Error("Failed to create downloader", "error", err)
		return fmt.Errorf("failed to create downloader: %w", err)
	}
	golog.Debug("Downloader created successfully")

	// Create dirStore
	golog.Debug("Creating dirStore")
	collection := gomongo.Coll("rni", "file_server")
	dirStore, err = levelStore.NewDirKeyStore(gBoardType, collection)
	if err != nil {
		golog.Error("Failed to create dirKeyStore", "error", err)
		return fmt.Errorf("failed to create dirKeyStore: %w", err)
	}
	if dirStore == nil {
		golog.Error("dirKeyStore is nil")
		return fmt.Errorf("failed to create dirKeyStore")
	}
	golog.Debug("DirStore created successfully")

	// Create download queue
	golog.Debug("Creating download queue")
	downloadQueue, err = goresodownload.NewResourceDownloadQueue(QueueCol)
	if err != nil {
		golog.Error("Failed to create download queue", "error", err)
		return fmt.Errorf("failed to create download queue: %w", err)
	}
	golog.Debug("Download queue created successfully")

	golog.Info("All components created successfully")
	return nil
}

// hasMediaUpdate checks if the changeDoc contains relevant media field updates
func hasMediaUpdate(changeDoc bson.M, operationType string) bool {
	if operationType == "insert" || operationType == "replace" {
		return true
	}

	updateDesc, ok := changeDoc["updateDescription"].(bson.M)
	if !ok {
		golog.Warn("hasMediaUpdate no updateDescription", "updateDesc", updateDesc)
		return false
	}

	updatedFields, ok := updateDesc["updatedFields"].(bson.M)
	if !ok {
		golog.Warn("hasMediaUpdate no updatedFields", "updatedFields", updatedFields)
		return false
	}

	// Check for Media field updates (CAR, DDF, BRE, EDM)
	if gBoardType != "TRB" {
		_, hasMedia := updatedFields["Media"]
		return hasMedia
	}

	// Check for phoUrls field updates (TRB)
	_, hasMedia := updatedFields["media"]

	return hasMedia
}

// ProcessInsert handles property insertion
func ProcessInsert(changeDoc bson.M, prop bson.M) error {
	if prop["_id"] == nil {
		golog.Error("invalid prop: no id")
		return fmt.Errorf("no id")
	}
	// Check if this change contains relevant media updates
	changeDocType, ok := changeDoc["operationType"].(string)
	if !ok {
		golog.Error("invalid changeDoc: no operationType")
		return fmt.Errorf("no operationType")
	}
	if !hasMediaUpdate(changeDoc, changeDocType) {
		golog.Info("No relevant media updates, skipping", "propId", prop["_id"])
		return nil
	}
	speedMeter.Check("needAnalyze", 1)

	// Get priority for queue
	priority := getPriority(prop)
	if priority < 0 {
		golog.Error("invalid priority value", "priority", priority, "propId", prop["_id"])
		return fmt.Errorf("invalid priority value: %d", priority)
	}

	// Add to queue for processing
	propID := prop["_id"].(string)
	if err := downloadQueue.AddToQueue(changeDoc, propID, priority, gBoardType); err != nil {
		golog.Error("Failed to add to download queue", "propId", prop["_id"], "error", err)
		return fmt.Errorf("failed to add to download queue: %w", err)
	}

	golog.Info("Successfully added to download queue", "propId", prop["_id"], "priority", priority)
	speedMeter.Check("addedToQueue", 1)
	return nil
}

// ProcessReplace handles property replacement
func ProcessReplace(changeDoc bson.M, prop bson.M) error {
	return ProcessInsert(changeDoc, prop)
}

// ProcessUpdate handles property updates
func ProcessUpdate(changeDoc bson.M, prop bson.M) error {
	// golog.Info("ProcessUpdate", "prop", prop["_id"])
	return ProcessInsert(changeDoc, prop)
}

// ProcessDelete handles property deletion
func ProcessDelete(changeDoc bson.M, id interface{}) error {
	golog.Warn("Process delete", "id", id, "changeDoc", changeDoc)
	return nil
}

// getPriority calculates the priority for a queue item
func getPriority(prop bson.M) int {
	// Extract property ID from prop
	propID, ok := prop["_id"].(string)
	if !ok {
		golog.Error("Failed to extract property ID from prop")
		return 1000 // fallback to default priority
	}

	// Fetch existing merged property
	existMergedProp, err := goresodownload.GetExistingMergedProperty(propID, gBoardType)
	if err != nil {
		golog.Error("Failed to fetch existing merged property", "propId", propID, "error", err)
		return 1000 // fallback to default priority
	}

	// Calculate priority using the new priority calculator
	priority, err := goresodownload.CalculatePriority(gBoardType, existMergedProp)
	if err != nil {
		golog.Error("Failed to calculate priority", "propId", propID, "error", err)
		return 1000 // fallback to default priority
	}

	golog.Debug("Calculated priority", "propId", propID, "priority", priority, "boardType", gBoardType)

	return priority
}

// ProcessQueueItem processes a single queue item (the actual analysis and download logic)
func ProcessQueueItem(queueItem *goresodownload.QueueItem) error {
	ctx := context.Background()
	startTime := time.Now()
	golog.Info("Processing queue item", "propId", queueItem.ID, "priority", queueItem.Priority)

	// Add panic recovery
	defer func() {
		if r := recover(); r != nil {
			golog.Error("Panic in ProcessQueueItem", "propId", queueItem.ID, "panic", r)
		}
	}()

	// Process directly without timeout - never give up on tasks
	defer func() {
		if r := recover(); r != nil {
			golog.Error("Panic in ProcessQueueItem", "propId", queueItem.ID, "panic", r)
		}
	}()

	changeDoc := queueItem.ChangeDoc

	// Log progress every 5 minutes for long-running tasks
	progressTicker := time.NewTicker(5 * time.Minute)
	defer progressTicker.Stop()

	go func() {
		for {
			select {
			case <-progressTicker.C:
				golog.Info("Still processing queue item", "propId", queueItem.ID, "duration", time.Since(startTime))
			case <-ctx.Done():
				return
			}
		}
	}()

	// Analyze changes
	result, err := analyzer.Analyze(changeDoc, gBoardType)
	if err != nil {
		speedMeter.Check("analyzeError", 1)
		return fmt.Errorf("failed to analyze changes: %w", err)
	}

	// Process analysis result
	tnChangedNum, err := downloader.ProcessAnalysisResult(&result)
	if err != nil {
		speedMeter.Check("downloadError", 1)
		return fmt.Errorf("failed to process analysis result: %w", err)
	}

	// Update dirStore stats
	if err := updateDirStoreStats(result, dirStore, tnChangedNum); err != nil {
		speedMeter.Check("updateDirStoreError", 1)
		return fmt.Errorf("failed to update dirStore stats: %w", err)
	}

	golog.Info("Successfully processed queue item",
		"propId", queueItem.ID,
		"downloadTasks", len(result.DownloadTasks),
		"deleteTasks", len(result.DeleteTasks),
		"duration", time.Since(startTime))
	speedMeter.Check("prop", 1)
	speedMeter.Check("downloadMedia", float64(len(result.DownloadTasks)))
	speedMeter.Check("deleteMedia", float64(len(result.DeleteTasks)))
	return nil
}

// ProcessQueue continuously processes items from the download queue
func ProcessQueue(ctx context.Context) {
	golog.Info("Starting queue processor")

	for {
		select {
		case <-ctx.Done():
			golog.Info("Queue processor stopping")
			return
		default:
			// Check memory pressure and goroutine count before processing
			var m runtime.MemStats
			runtime.ReadMemStats(&m)
			numGoroutines := runtime.NumGoroutine()

			// More lenient memory threshold and goroutine limit
			if m.Alloc > 800*1024*1024 || numGoroutines > 200 { // 800MB threshold and 200 goroutines limit
				golog.Warn("High resource usage detected, forcing cleanup",
					"currentAlloc", formatBytes(m.Alloc), "numGoroutines", numGoroutines)
				runtime.GC()                // Force GC
				time.Sleep(5 * time.Second) // Shorter wait time
				// Continue processing anyway to avoid deadlock
			}

			// Get next batch from queue
			batch, err := downloadQueue.GetNextBatch(gBoardType, batchSize)
			if err != nil {
				golog.Error("Failed to get next batch from queue", "error", err)
				time.Sleep(5 * time.Second) // Wait before retrying
				continue
			}

			if len(batch) == 0 {
				// No items to process, wait before checking again
				time.Sleep(5 * time.Second)
				continue
			}

			golog.Info("Processing batch from queue", "batchSize", batchSize)

			// Process items using fixed worker pool - NO MORE GOROUTINE EXPLOSION
			golog.Info("Processing batch with fixed worker pool", "batchSize", len(batch))

			// Create channels for task distribution
			taskChan := make(chan goresodownload.QueueItem, len(batch))
			var wg sync.WaitGroup

			// Start fixed number of worker goroutines (same as batchSize)
			for i := 0; i < batchSize; i++ {
				wg.Add(1)
				go func(workerID int) {
					defer wg.Done()
					golog.Debug("Queue worker started", "workerID", workerID)

					for queueItem := range taskChan {
						// Add panic recovery
						func() {
							defer func() {
								if r := recover(); r != nil {
									golog.Error("Panic in queue worker", "workerID", workerID, "propId", queueItem.ID, "panic", r)
								}
							}()

							golog.Info("Processing item from queue", "workerID", workerID, "propId", queueItem.ID, "priority", queueItem.Priority)

							// Process the item
							if err := ProcessQueueItem(&queueItem); err != nil {
								golog.Error("Failed to process queue item", "workerID", workerID, "propId", queueItem.ID, "error", err)
							} else {
								// Force GC after successful processing to free memory
								runtime.GC()
							}

							// Remove successfully processed item from queue
							if err := downloadQueue.RemoveFromQueue(&queueItem); err != nil {
								golog.Error("Failed to remove processed item from queue", "workerID", workerID, "propId", queueItem.ID, "error", err)
							} else {
								golog.Info("Successfully processed and removed item from queue", "workerID", workerID, "propId", queueItem.ID)
							}
						}()
					}
					golog.Debug("Queue worker finished", "workerID", workerID)
				}(i)
			}

			// Send all tasks to workers
			for _, item := range batch {
				taskChan <- item
			}
			close(taskChan) // Signal no more tasks

			// Wait for all workers to complete
			wg.Wait()
			golog.Info("Batch processing completed", "processedItems", len(batch))
		}
	}
}

// UpdateProcessStatus updates the process status
func UpdateProcessStatus(startTs time.Time) error {
	golog.Info("UpdateProcessStatus", "startTs", startTs)
	opts := goprocess.UpdateProcessStatusOptions{
		Status:   goprocess.RunningNormal,
		StartTs:  &startTs,
		ErrorMsg: nil,
		Stats:    nil,
	}
	return gProcessMonitor.UpdateProcessStatus(opts)
}

// OnTokenUpdate handles token updates
func OnTokenUpdate(tokenOpt gowatch.TokenUpdateOptions) error {
	// Update stats first with a separate lock
	statMu.Lock()
	golog.Info("onTokenUpdate", "token", tokenOpt.Token, "gStat", gStat, "from:", lastTokenPrintTs)
	lastTokenPrintTs = time.Now()
	gStat = make(map[string]int)
	statMu.Unlock()

	// Update token with minimal lock scope
	tokenMu.Lock()
	updateFields := gowatch.UpdateFields{
		Token:          tokenOpt.Token,
		TokenClusterTs: tokenOpt.TokenClusterTs,
		TokenChangeTs:  tokenOpt.TokenChangeTs,
		Status:         "running",
		ResumeMt:       tokenOpt.ResumeMt,
	}
	tokenMu.Unlock()

	return gUpdateSysData(updateFields)
}

// OnChange handles property changes
func OnChange(changeDoc bson.M, coll *gomongo.MongoCollection) error {
	changeType := changeDoc["operationType"].(string)
	statMu.Lock()
	if _, ok := gStat[changeType]; !ok {
		gStat[changeType] = 0
	}
	gStat[changeType] = gStat[changeType] + 1
	statMu.Unlock()

	if gWatchedObject == nil {
		golog.Error("gWatchedObject is nil")
		return fmt.Errorf("gWatchedObject is nil")
	}
	return gowatch.ProcessChangedObject(gowatch.ProcessChangedObjectOptions{
		ChangedObj:  changeDoc,
		WatchedColl: coll,
		DeleteOneFn: func(id interface{}) error {
			return ProcessDelete(changeDoc, id)
		},
		InsertOneFn: func(prop bson.M) error {
			return ProcessInsert(changeDoc, prop)
		},
		ReplaceOneFn: func(prop bson.M) error {
			return ProcessReplace(changeDoc, prop)
		},
		UpdateOneFn: func(prop bson.M) error {
			return ProcessUpdate(changeDoc, prop)
		},
		WatchedStream: gWatchedObject,
	})
}

// WatchProp sets up the watch on properties collection
func WatchProp(ctx context.Context, cancel context.CancelFunc, token *bson.M, tokenClusterTs time.Time, resumeMt ...time.Time) error {
	// Use background context for watch operation
	var startTs time.Time
	if len(resumeMt) > 0 && !resumeMt[0].IsZero() {
		startTs = resumeMt[0]
	} else {
		startTs = time.Now()
		if !tokenClusterTs.IsZero() {
			golog.Debug("tokenClusterTs is not zero", "tokenClusterTs", tokenClusterTs)
			startTs = tokenClusterTs
		}
	}

	// Get the appropriate collection based on board type
	watchedColl := gomongo.Coll("rni", goresodownload.BoardWatchTable[gBoardType])
	golog.Debug("Watching collection:", "collection", goresodownload.BoardWatchTable[gBoardType])

	query := bson.M{"mt": bson.M{"$gte": startTs}}
	golog.Debug("Watch query:", "query", query)

	// Function to create watch options
	createWatchOptions := func(token *bson.M) gowatch.WatchOptions {
		return gowatch.WatchOptions{
			WatchedColl:           watchedColl,
			OnChange:              OnChange,
			OnTokenUpdate:         OnTokenUpdate,
			QueryWhenInvalidToken: query,
			SavedToken:            token,
			HighWaterMark:         20,
			OnError: func(err error) {
				golog.Error("watchProp error", "error", err.Error())
				// Check if it's a ChangeStreamHistoryLost error
				if strings.Contains(err.Error(), "ChangeStreamHistoryLost") ||
					strings.Contains(err.Error(), "Resume of change stream was not possible") {
					golog.Warn("Change stream history lost, attempting to get new token")
					// Get a new token and retry
					if newToken, newTokenClusterTs, err := gowatch.GetToken(watchedColl); err == nil {
						tokenOpt := gowatch.TokenUpdateOptions{
							Token:          newToken,
							TokenClusterTs: newTokenClusterTs,
						}
						if err := OnTokenUpdate(tokenOpt); err == nil {
							if err := WatchProp(ctx, cancel, newToken, newTokenClusterTs); err == nil {
								return
							}
						}
					}
				}
				GracefulExit(err)
			},
			UpdateProcessStatusFn: func() error {
				return UpdateProcessStatus(time.Now())
			},
			UpdateTokenTimerS: 120,
			Context:           ctx,
			Cancel:            cancel,
		}
	}

	// Try to create watch with current token
	opt := createWatchOptions(token)
	watchObj, err := gowatch.WatchTarget(opt)
	if err != nil {
		// If error is ChangeStreamHistoryLost, try to get a new token and retry
		if strings.Contains(err.Error(), "ChangeStreamHistoryLost") ||
			strings.Contains(err.Error(), "Resume of change stream was not possible") {
			golog.Warn("Change stream history lost during creation, getting new token")
			if newToken, newTokenClusterTs, err := gowatch.GetToken(watchedColl); err == nil {
				tokenOpt := gowatch.TokenUpdateOptions{
					Token:          newToken,
					TokenClusterTs: newTokenClusterTs,
				}
				if err := OnTokenUpdate(tokenOpt); err == nil {
					// Retry with new token
					opt = createWatchOptions(newToken)
					watchObj, err = gowatch.WatchTarget(opt)
					if err == nil {
						gWatchedObject = watchObj
						golog.Debug("Watch started successfully with new token")
						return nil
					}
				}
			}
		}
		golog.Error("watchProp error", "error", err)
		GracefulExit(err)
		return err
	}

	gWatchedObject = watchObj
	golog.Debug("Watch started successfully")
	return nil
}

// GetTokenAndWatch gets a new token and starts watching
func GetTokenAndWatch(ctx context.Context, cancel context.CancelFunc) error {
	watchedColl := gomongo.Coll("rni", goresodownload.BoardWatchTable[gBoardType])

	token, tokenClusterTs, err := gowatch.GetToken(watchedColl)
	if err != nil {
		GracefulExit(err)
		return err
	}

	tokenOpt := gowatch.TokenUpdateOptions{
		Token:          token,
		TokenClusterTs: tokenClusterTs,
	}
	if err := OnTokenUpdate(tokenOpt); err != nil {
		GracefulExit(err)
		return err
	}
	golog.Debug("OnTokenUpdate", "token", token)
	return WatchProp(ctx, cancel, token, tokenClusterTs)
}

// GetSignalChan returns a channel that informs about pressing Ctrl+C
func GetSignalChan() chan os.Signal {
	signalChannel := make(chan os.Signal, 1)
	signal.Notify(signalChannel,
		syscall.SIGHUP,
		syscall.SIGINT,
		syscall.SIGTERM,
		syscall.SIGQUIT)
	return signalChannel
}

// GracefulExit handles graceful shutdown
func GracefulExit(err error) {
	if err != nil {
		golog.Error("gracefulExit error", "err", err.Error())
	}

	params := map[string]interface{}{
		"watchedObject": gWatchedObject,
		"exitFn":        gobase.Exit,
	}
	if err != nil {
		params["error"] = err.Error()
	}

	if gWatchedObject != nil {
		if err := gWatchedObject.FinishAndUpdateSysdata(gUpdateSysData); err != nil {
			golog.Error("FinishAndUpdateSysdata error", "err", err.Error())
		}
	}

	errMsg := func() string {
		if err != nil {
			return err.Error()
		}
		return ""
	}()

	gProcessMonitor.UpdateStatusWhenAllFinish(errMsg, func() {
		golog.Info("UpdateStatusWhenAllFinish", "err", errMsg)
	})
}

// handleTokenAndWatch handles token retrieval and watching logic
func handleTokenAndWatch(watchCtx context.Context, watchCancel context.CancelFunc, sysData bson.M, startTs time.Time) error {
	token := sysData["token"]

	var tokenClusterTs primitive.DateTime
	if v, ok := sysData["tokenClusterTs"].(primitive.DateTime); ok {
		tokenClusterTs = v
	}

	var resumeMt time.Time
	if v, ok := sysData["resumeMt"].(primitive.DateTime); ok {
		resumeMt = v.Time()
	}

	// Enhanced token validation
	if token == nil {
		golog.Info("No token found, starting fresh watch")
		return GetTokenAndWatch(watchCtx, watchCancel)
	}

	// Parse and validate existing token
	var parsedToken bson.M
	if err := json.Unmarshal([]byte(token.(string)), &parsedToken); err != nil {
		golog.Info("Invalid token format, starting fresh", "token", token)
		return GetTokenAndWatch(watchCtx, watchCancel)
	}

	// Check if token is too old (more than 12 hours)
	tokenAge := time.Since(tokenClusterTs.Time())
	if tokenAge > HALF_DAY_IN_MS*time.Millisecond {
		golog.Warn("Token is too old, starting fresh watch",
			"tokenAge", tokenAge,
			"tokenClusterTs", tokenClusterTs.Time(),
			"maxAge", HALF_DAY_IN_MS*time.Millisecond)
		return GetTokenAndWatch(watchCtx, watchCancel)
	}

	// If resumeMt is set, use it as the start point
	if !resumeMt.IsZero() {
		golog.Info("Using resumeMt as start point", "resumeMt", resumeMt)
		tokenOpt := gowatch.TokenUpdateOptions{
			Token:          &parsedToken,
			TokenClusterTs: tokenClusterTs.Time(),
			ResumeMt:       resumeMt,
		}
		if err := OnTokenUpdate(tokenOpt); err != nil {
			golog.Error("Failed to update token with resumeMt", "error", err)
			return GetTokenAndWatch(watchCtx, watchCancel)
		}
		return WatchProp(watchCtx, watchCancel, &parsedToken, tokenClusterTs.Time(), resumeMt)
	}

	// Use existing token with tokenClusterTs
	golog.Info("Continuing watch from existing token",
		"tokenClusterTs", tokenClusterTs.Time(),
		"tokenAge", tokenAge)
	tokenOpt := gowatch.TokenUpdateOptions{
		Token:          &parsedToken,
		TokenClusterTs: tokenClusterTs.Time(),
	}
	if err := OnTokenUpdate(tokenOpt); err != nil {
		golog.Error("Failed to update token", "error", err)
		return GetTokenAndWatch(watchCtx, watchCancel)
	}

	// Try to resume with existing token
	err := WatchProp(watchCtx, watchCancel, &parsedToken, tokenClusterTs.Time())
	if err != nil {
		if strings.Contains(err.Error(), "ChangeStreamHistoryLost") ||
			strings.Contains(err.Error(), "Resume of change stream was not possible") {
			golog.Warn("Change stream history lost, starting fresh watch", "error", err)
			return GetTokenAndWatch(watchCtx, watchCancel)
		}
		return err
	}

	return nil
}

// createDownloader creates a new downloader instance
func createDownloader() (*goresodownload.Downloader, error) {
	// Get image directories from config
	storagePaths := levelStore.GetImageDir(gBoardType)
	if len(storagePaths) == 0 {
		return nil, fmt.Errorf("no image directories configured for board %s", gBoardType)
	}

	// Create downloader options
	opts := &goresodownload.DownloaderOptions{
		Config:       goresodownload.NewDefaultConfig(),
		StoragePaths: storagePaths,
		MergedCol:    gomongo.Coll("rni", goresodownload.BoardMergedTable[gBoardType]),
		FailedCol:    FailedCol,
	}

	return goresodownload.NewDownloader(opts)
}

// updateDirStoreStats updates the directory store statistics
func updateDirStoreStats(result goresodownload.AnalysisResult, dirStore *levelStore.DirKeyStore, tnChangedNum int) error {
	l1, l2, err := levelStore.GetL1L2Separate(result.PropTs, gBoardType, result.Sid)
	if err != nil {
		return fmt.Errorf("failed to get l1 and l2: %w", err)
	}
	golog.Info("updateDirStoreStats", "l1", l1, "propTs", result.PropTs, "l2", l2)

	// Update stats
	mediaAmount := len(result.DownloadTasks) - len(result.DeleteTasks) + tnChangedNum
	if mediaAmount != 0 {
		dirStore.AddDirStats(l1, l2, 1, mediaAmount) // 1 means one property
	}
	golog.Info("updated dirStore stats", "l1", l1, "l2", l2, "mediaAmount", mediaAmount)

	return nil
}

// SetBoardType sets the board type for testing
func SetBoardType(boardType string) {
	gBoardType = boardType
}

// GetDownloader returns the downloader instance for testing
func GetDownloader() *goresodownload.Downloader {
	return downloader
}

func main() {
	golog.Info("main", "board", gBoardType, "force", force, "dryRun", dryRun)

	// Set up signal handling
	signalChannel := GetSignalChan()
	ctx, cancelCtx := context.WithCancel(context.Background())
	watchCtx, watchCancel := context.WithCancel(context.Background())
	queueCtx, queueCancel := context.WithCancel(context.Background())

	// Handle signals
	go func() {
		sig := <-signalChannel
		sigName := "SIG" + strings.ToUpper(strings.TrimPrefix(sig.String(), "SIG"))
		errMsg := fmt.Sprintf("%s received %s", PROCESS_STATUS_ID, sigName)
		golog.Error(errMsg)
		GracefulExit(fmt.Errorf("signal %s received", sigName))
		signal.Stop(signalChannel)
		close(signalChannel)
		queueCancel()
		cancelCtx()
	}()

	// Check for running process
	isRunning, err := gProcessMonitor.CheckRunningProcess()
	if err != nil {
		if !strings.Contains(err.Error(), "no documents") {
			golog.Error("Failed to check running process",
				"error", err,
				"error_type", fmt.Sprintf("%T", err),
				"error_string", err.Error(),
				"process_id", PROCESS_STATUS_ID)
			GracefulExit(fmt.Errorf("failed to check running process: %v", err))
		}
		golog.Info("No existing process found, starting fresh", "process_id", PROCESS_STATUS_ID)
	}
	if isRunning && !dryRun {
		golog.Warn("Process is already running", "process_id", PROCESS_STATUS_ID)
		GracefulExit(fmt.Errorf(goprocess.HasRunningProcess))
	}

	// Create components
	if err := createComponents(); err != nil {
		golog.Error("Failed to create components", "error", err.Error(), "process_id", PROCESS_STATUS_ID)
		GracefulExit(fmt.Errorf("failed to create components: %v", err))
	}

	// Start memory monitoring
	go monitorMemoryUsage(queueCtx)

	// Start HTTP connection cleanup
	go cleanupHTTPConnections(queueCtx)

	// Start queue processor
	go ProcessQueue(queueCtx)

	// Update process status
	startTs := time.Now()
	if err := UpdateProcessStatus(startTs); err != nil {
		golog.Error("Failed to update process status",
			"error", err,
			"error_type", fmt.Sprintf("%T", err),
			"error_string", err.Error(),
			"process_id", PROCESS_STATUS_ID)
		GracefulExit(fmt.Errorf("failed to update process status: %v", err))
	}

	// Get last resume token
	var sysData bson.M
	err = SysdataCol.FindOne(
		context.Background(),
		bson.M{"_id": PROCESS_STATUS_ID},
	).Decode(&sysData)

	if err != nil {
		if err.Error() == "mongo: no documents in result" {
			golog.Info("No existing sysdata found, starting fresh", "processId", PROCESS_STATUS_ID)
			if err := GetTokenAndWatch(watchCtx, watchCancel); err != nil {
				GracefulExit(err)
			}
		} else {
			golog.Error("Failed to get sysdata", "error", err.Error(), "processId", PROCESS_STATUS_ID)
			GracefulExit(err)
		}
	} else {
		if err := handleTokenAndWatch(watchCtx, watchCancel, sysData, startTs); err != nil {
			GracefulExit(err)
		}
	}

	// Wait for context to be cancelled
	<-ctx.Done()
	// Clean up ProcessMonitor
	if gProcessMonitor != nil {
		gProcessMonitor.StopMonitoring()
	}
}

// monitorMemoryUsage monitors memory usage and logs warnings
func monitorMemoryUsage(ctx context.Context) {
	ticker := time.NewTicker(10 * time.Second) // Check every 10 seconds
	defer ticker.Stop()

	const maxMemoryBytes = 1024 * 1024 * 1024 // 1GB absolute limit (increased to prevent deadlock)
	const maxMemoryPercent = 0.5              // 50% of system memory (increased for better performance)
	const maxGoroutines = 500                 // Alert if goroutines exceed this number

	totalSystemMemoryBytes := getSystemMemory()
	golog.Info("Memory monitoring started", "systemMemory", formatBytes(totalSystemMemoryBytes))

	for {
		select {
		case <-ticker.C:
			var m runtime.MemStats
			runtime.ReadMemStats(&m)

			currentMemoryBytes := m.Alloc // Bytes allocated and still in use
			numGoroutines := runtime.NumGoroutine()
			golog.Debug("numGoroutines", "numGoroutines", numGoroutines)

			if currentMemoryBytes > maxMemoryBytes || float64(currentMemoryBytes)/float64(totalSystemMemoryBytes) > maxMemoryPercent || numGoroutines > maxGoroutines {
				golog.Warn("High resource usage detected",
					"currentMemory", formatBytes(currentMemoryBytes),
					"systemMemory", formatBytes(totalSystemMemoryBytes),
					"percentage", fmt.Sprintf("%.2f%%", float64(currentMemoryBytes)/float64(totalSystemMemoryBytes)*100),
					"heapAlloc", formatBytes(m.HeapAlloc),
					"heapSys", formatBytes(m.HeapSys),
					"numGoroutines", numGoroutines,
					"maxGoroutines", maxGoroutines)

				// Force garbage collection
				runtime.GC()

				// If goroutines are extremely high, log stack traces for debugging
				if numGoroutines > 1000 {
					golog.Error("Extremely high goroutine count detected - possible leak",
						"numGoroutines", numGoroutines)
				}
			} else {
				golog.Debug("Resource usage normal",
					"currentMemory", formatBytes(currentMemoryBytes),
					"heapAlloc", formatBytes(m.HeapAlloc),
					"numGoroutines", numGoroutines)
			}
		case <-ctx.Done():
			golog.Debug("Memory monitor stopping")
			return
		}
	}
}

// getSystemMemory attempts to get the total system memory in bytes
func getSystemMemory() uint64 {
	// Try to read from /proc/meminfo on Linux
	if data, err := os.ReadFile("/proc/meminfo"); err == nil {
		lines := strings.Split(string(data), "\n")
		for _, line := range lines {
			if strings.HasPrefix(line, "MemTotal:") {
				fields := strings.Fields(line)
				if len(fields) >= 2 {
					if kb, err := strconv.ParseUint(fields[1], 10, 64); err == nil {
						return kb * 1024 // Convert KB to bytes
					}
				}
				break
			}
		}
	}

	// Fallback: assume 4GB if we can't detect
	return 4 * 1024 * 1024 * 1024
}

// cleanupHTTPConnections periodically forces cleanup of HTTP connections to prevent leaks
func cleanupHTTPConnections(ctx context.Context) {
	ticker := time.NewTicker(5 * time.Second) // Cleanup every 5 seconds (more aggressive)
	defer ticker.Stop()

	golog.Info("Aggressive HTTP connection cleanup started")

	for {
		select {
		case <-ticker.C:
			// Force cleanup of default HTTP transport
			if transport, ok := http.DefaultTransport.(*http.Transport); ok {
				transport.CloseIdleConnections()
			}

			// Force garbage collection to clean up any leaked connections
			runtime.GC()

			numGoroutines := runtime.NumGoroutine()
			golog.Debug("---numGoroutines", numGoroutines) // Debug output

			if numGoroutines > 200 { // Lower threshold
				golog.Error("HTTP connection leak detected",
					"numGoroutines", numGoroutines)

				// More aggressive cleanup
				runtime.GC()
				runtime.GC() // Double GC
			}

		case <-ctx.Done():
			golog.Debug("HTTP connection cleanup stopping")
			return
		}
	}
}

// formatBytes formats bytes into human readable format
func formatBytes(bytes uint64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}
