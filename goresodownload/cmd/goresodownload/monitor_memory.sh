#!/bin/bash

# Memory and Goroutine Monitor Script
# This script monitors the goresodownload process for memory usage and goroutine count

echo "Starting memory and goroutine monitoring for goresodownload..."
echo "Timestamp, Memory(MB), Goroutines, CPU%" > memory_monitor.log

while true; do
    # Find the goresodownload process
    PID=$(pgrep -f "goresodownload.*TRB" | head -1)
    
    if [ -n "$PID" ]; then
        # Get memory usage in MB
        MEMORY_KB=$(ps -p $PID -o rss= 2>/dev/null)
        if [ -n "$MEMORY_KB" ]; then
            MEMORY_MB=$((MEMORY_KB / 1024))
            
            # Get CPU usage
            CPU=$(ps -p $PID -o %cpu= 2>/dev/null)
            
            # Try to get goroutine count from logs (approximate)
            GOROUTINES=$(tail -100 /var/log/goresodownload.log 2>/dev/null | grep -o "numGoroutines=[0-9]*" | tail -1 | cut -d= -f2)
            if [ -z "$GOROUTINES" ]; then
                GOROUTINES="N/A"
            fi
            
            TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
            echo "$TIMESTAMP, ${MEMORY_MB}MB, $GOROUTINES, ${CPU}%"
            echo "$TIMESTAMP, $MEMORY_MB, $GOROUTINES, $CPU" >> memory_monitor.log
            
            # Alert if memory usage is high
            if [ "$MEMORY_MB" -gt 800 ]; then
                echo "⚠️  HIGH MEMORY USAGE: ${MEMORY_MB}MB"
            fi
            
            # Alert if goroutines are high
            if [ "$GOROUTINES" != "N/A" ] && [ "$GOROUTINES" -gt 100 ]; then
                echo "⚠️  HIGH GOROUTINE COUNT: $GOROUTINES"
            fi
        else
            echo "$(date '+%Y-%m-%d %H:%M:%S') - Process not found or no memory info"
        fi
    else
        echo "$(date '+%Y-%m-%d %H:%M:%S') - goresodownload process not running"
    fi
    
    sleep 30  # Check every 30 seconds
done
