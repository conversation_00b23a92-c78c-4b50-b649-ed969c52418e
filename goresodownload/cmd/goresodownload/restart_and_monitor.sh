#!/bin/bash

echo "🔧 Restarting goresodownload with improved goroutine management (NEVER SKIP TASKS)..."

# Kill any existing goresodownload processes
echo "Stopping existing processes..."
pkill -f "goresodownload.*TRB" || true
sleep 3

# Check if processes are still running
if pgrep -f "goresodownload.*TRB" > /dev/null; then
    echo "Force killing remaining processes..."
    pkill -9 -f "goresodownload.*TRB" || true
    sleep 2
fi

echo "✅ All existing processes stopped"

# Build the latest version
echo "Building latest version..."
cd /home/<USER>/github/go/goresodownload/cmd/goresodownload
go build -o goresodownload main.go

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build successful"

# Start the new process
echo "Starting goresodownload with TRB board..."
nohup ./goresodownload -board TRB > goresodownload.log 2>&1 &
NEW_PID=$!

echo "✅ Started new process with PID: $NEW_PID"

# Monitor for the first few minutes
echo "📊 Monitoring for 2 minutes..."
for i in {1..24}; do  # 24 * 5 seconds = 2 minutes
    sleep 5
    
    if ! kill -0 $NEW_PID 2>/dev/null; then
        echo "❌ Process died! Check logs:"
        tail -20 goresodownload.log
        exit 1
    fi
    
    # Get memory and goroutine info
    MEMORY_KB=$(ps -p $NEW_PID -o rss= 2>/dev/null)
    if [ -n "$MEMORY_KB" ]; then
        MEMORY_MB=$((MEMORY_KB / 1024))
        
        # Try to get goroutine count from recent logs
        GOROUTINES=$(tail -50 goresodownload.log 2>/dev/null | grep -o "numGoroutines=[0-9]*" | tail -1 | cut -d= -f2)
        if [ -z "$GOROUTINES" ]; then
            GOROUTINES="N/A"
        fi
        
        echo "$(date '+%H:%M:%S') - Memory: ${MEMORY_MB}MB, Goroutines: $GOROUTINES"
        
        # Alert if memory is high
        if [ "$MEMORY_MB" -gt 500 ]; then
            echo "⚠️  Memory usage: ${MEMORY_MB}MB"
        fi
        
        # Alert if goroutines are high
        if [ "$GOROUTINES" != "N/A" ] && [ "$GOROUTINES" -gt 100 ]; then
            echo "⚠️  Goroutine count: $GOROUTINES"
        fi
    fi
done

echo "✅ Process appears stable after 2 minutes"
echo "📋 Process PID: $NEW_PID"
echo "📄 Log file: $(pwd)/goresodownload.log"
echo ""
echo "To continue monitoring, run:"
echo "  tail -f goresodownload.log"
echo ""
echo "To check goroutine stack traces if needed:"
echo "  kill -SIGQUIT $NEW_PID"
