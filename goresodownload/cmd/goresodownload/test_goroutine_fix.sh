#!/bin/bash

echo "🔧 Testing Goroutine Fix - Worker Pool Pattern"
echo "Current time: $(date)"

# Kill current process
echo "Stopping current process..."
pkill -f "goresodownload.*TRB" || true
sleep 3

# Build with worker pool fix
echo "🔨 Building with worker pool fix..."
cd /home/<USER>/github/go/goresodownload/cmd/goresodownload
go build -o goresodownload main.go

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build successful with worker pool pattern"

# Start new process
echo "🚀 Starting with worker pool pattern..."
nohup ./goresodownload -board TRB > goresodownload.log 2>&1 &
NEW_PID=$!

echo "✅ Started process with PID: $NEW_PID"

# Monitor for goroutine behavior during image downloads
echo "📊 Monitoring goroutine behavior during image downloads..."
echo "Looking for download tasks to test the fix..."

BASELINE_GOROUTINES=0
MAX_GOROUTINES=0
DOWNLOAD_DETECTED=false

for i in {1..120}; do  # Monitor for 10 minutes
    sleep 5
    
    if ! kill -0 $NEW_PID 2>/dev/null; then
        echo "❌ Process died! Last logs:"
        tail -20 goresodownload.log
        exit 1
    fi
    
    # Get goroutine count
    GOROUTINES=$(tail -20 goresodownload.log 2>/dev/null | grep "---numGoroutines" | tail -1 | awk '{print $2}')
    if [ -z "$GOROUTINES" ]; then
        continue
    fi
    
    # Set baseline if not set
    if [ "$BASELINE_GOROUTINES" -eq 0 ]; then
        BASELINE_GOROUTINES=$GOROUTINES
        echo "📊 Baseline goroutines: $BASELINE_GOROUTINES"
    fi
    
    # Track maximum
    if [ "$GOROUTINES" -gt "$MAX_GOROUTINES" ]; then
        MAX_GOROUTINES=$GOROUTINES
    fi
    
    # Check for download activity
    if tail -10 goresodownload.log 2>/dev/null | grep -q "downloadTasks=[1-9]"; then
        if [ "$DOWNLOAD_DETECTED" = false ]; then
            echo "🔍 Download activity detected! Monitoring goroutine behavior..."
            DOWNLOAD_DETECTED=true
        fi
        
        INCREASE=$((GOROUTINES - BASELINE_GOROUTINES))
        echo "$(date '+%H:%M:%S') - Goroutines: $GOROUTINES (baseline: $BASELINE_GOROUTINES, +$INCREASE)"
        
        # Test the fix
        if [ "$GOROUTINES" -gt 200 ]; then
            echo "⚠️  High goroutines during download: $GOROUTINES"
        fi
        
        if [ "$GOROUTINES" -gt 1000 ]; then
            echo "❌ WORKER POOL FIX FAILED: Still creating too many goroutines!"
            echo "Expected: ~$((BASELINE_GOROUTINES + 10)) goroutines"
            echo "Actual: $GOROUTINES goroutines"
            exit 1
        fi
    else
        # No download activity, just show periodic updates
        if [ $((i % 12)) -eq 0 ]; then
            MINUTES=$((i / 12))
            echo "$(date '+%H:%M:%S') - Waiting for downloads... Goroutines: $GOROUTINES (${MINUTES}min)"
        fi
    fi
    
    # Success criteria check
    if [ "$DOWNLOAD_DETECTED" = true ] && [ "$GOROUTINES" -lt 100 ]; then
        echo "✅ WORKER POOL SUCCESS: Goroutines stayed below 100 during downloads!"
        break
    fi
done

echo ""
echo "📊 Final Results:"
echo "  🔹 Baseline goroutines: $BASELINE_GOROUTINES"
echo "  🔹 Maximum goroutines: $MAX_GOROUTINES"
echo "  🔹 Download activity detected: $DOWNLOAD_DETECTED"
echo "  🔹 Process PID: $NEW_PID"

if [ "$DOWNLOAD_DETECTED" = true ]; then
    INCREASE=$((MAX_GOROUTINES - BASELINE_GOROUTINES))
    echo "  🔹 Maximum increase: +$INCREASE goroutines"
    
    if [ "$MAX_GOROUTINES" -lt 100 ]; then
        echo ""
        echo "🎉 SUCCESS: Worker pool pattern working perfectly!"
        echo "   Goroutine count stayed below 100 even during downloads"
    elif [ "$MAX_GOROUTINES" -lt 500 ]; then
        echo ""
        echo "✅ GOOD: Significant improvement with worker pool"
        echo "   Much better than the previous 3000+ goroutines"
    else
        echo ""
        echo "⚠️  PARTIAL: Some improvement but still high goroutine count"
    fi
else
    echo ""
    echo "ℹ️  No download activity detected during monitoring period"
    echo "   Worker pool fix will be tested when downloads occur"
fi

echo ""
echo "Continue monitoring: tail -f goresodownload.log | grep -E '(numGoroutines|downloadTasks|Download worker)'"
