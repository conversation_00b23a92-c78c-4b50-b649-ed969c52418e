#!/bin/bash

echo "🔧 Testing Queue Worker Pool Fix"
echo "Current time: $(date)"

# Kill current process
echo "Stopping current process..."
pkill -f "goresodownload.*TRB" || true
sleep 3

# Build with queue worker pool fix
echo "🔨 Building with queue worker pool fix..."
cd /home/<USER>/github/go/goresodownload/cmd/goresodownload
go build -o goresodownload main.go

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build successful with queue worker pool fix"
echo "  🔹 Fixed number of queue workers: 2 (batchSize)"
echo "  🔹 No more goroutine per queue item"
echo "  🔹 Tasks distributed via channel to workers"

# Start new process
echo "🚀 Starting with queue worker pool..."
nohup ./goresodownload -board TRB > goresodownload.log 2>&1 &
NEW_PID=$!

echo "✅ Started process with PID: $NEW_PID"

# Monitor for goroutine behavior during queue processing
echo "📊 Monitoring queue worker pool behavior..."
echo "Expected: Baseline + 2 queue workers + 10 download/delete workers = ~60 goroutines max"

BASELINE_GOROUTINES=0
MAX_GOROUTINES=0
QUEUE_ACTIVITY_DETECTED=false
LARGE_BATCH_DETECTED=false

for i in {1..240}; do  # Monitor for 20 minutes
    sleep 5
    
    if ! kill -0 $NEW_PID 2>/dev/null; then
        echo "❌ Process died! Last logs:"
        tail -30 goresodownload.log
        exit 1
    fi
    
    # Get goroutine count
    GOROUTINES=$(tail -20 goresodownload.log 2>/dev/null | grep "---numGoroutines" | tail -1 | awk '{print $2}')
    if [ -z "$GOROUTINES" ]; then
        continue
    fi
    
    # Set baseline if not set
    if [ "$BASELINE_GOROUTINES" -eq 0 ]; then
        BASELINE_GOROUTINES=$GOROUTINES
        echo "📊 Baseline goroutines: $BASELINE_GOROUTINES"
    fi
    
    # Track maximum
    if [ "$GOROUTINES" -gt "$MAX_GOROUTINES" ]; then
        MAX_GOROUTINES=$GOROUTINES
    fi
    
    # Check for queue processing activity
    RECENT_QUEUE_ACTIVITY=$(tail -20 goresodownload.log 2>/dev/null | grep -c "Processing batch from queue")
    LARGE_BATCHES=$(tail -20 goresodownload.log 2>/dev/null | grep "Processing batch from queue" | grep -E "batchSize=[5-9]|batchSize=[1-9][0-9]")
    
    if [ "$RECENT_QUEUE_ACTIVITY" -gt 0 ]; then
        if [ "$QUEUE_ACTIVITY_DETECTED" = false ]; then
            echo "🔍 Queue processing activity detected! Testing worker pool..."
            QUEUE_ACTIVITY_DETECTED=true
        fi
        
        if [ -n "$LARGE_BATCHES" ] && [ "$LARGE_BATCH_DETECTED" = false ]; then
            echo "🎯 Large batch detected! This is the real test..."
            LARGE_BATCH_DETECTED=true
        fi
        
        INCREASE=$((GOROUTINES - BASELINE_GOROUTINES))
        echo "$(date '+%H:%M:%S') - Goroutines: $GOROUTINES (baseline: $BASELINE_GOROUTINES, +$INCREASE)"
        
        # Test the queue worker pool fix
        if [ "$GOROUTINES" -gt 100 ]; then
            echo "⚠️  High goroutines during queue processing: $GOROUTINES"
            
            if [ "$GOROUTINES" -gt 1000 ]; then
                echo "❌ QUEUE WORKER POOL FIX FAILED!"
                echo "Expected: ~$((BASELINE_GOROUTINES + 12)) goroutines (baseline + 2 queue workers + 10 download workers)"
                echo "Actual: $GOROUTINES goroutines"
                echo ""
                echo "Recent logs:"
                tail -50 goresodownload.log | grep -E "(Processing batch|worker|goroutine)"
                exit 1
            fi
        fi
        
        # Success criteria for queue processing
        if [ "$QUEUE_ACTIVITY_DETECTED" = true ] && [ "$GOROUTINES" -lt 100 ]; then
            echo "🎉 QUEUE WORKER POOL SUCCESS!"
            echo "   Queue processing with <100 goroutines"
        fi
    else
        # No queue activity, show periodic updates
        if [ $((i % 24)) -eq 0 ]; then
            MINUTES=$((i / 12))
            echo "$(date '+%H:%M:%S') - Waiting for queue activity... Goroutines: $GOROUTINES (${MINUTES}min)"
        fi
    fi
    
    # Early success detection
    if [ "$QUEUE_ACTIVITY_DETECTED" = true ] && [ "$GOROUTINES" -lt 100 ] && [ "$i" -gt 60 ]; then
        echo "✅ Early success detected - queue worker pool is working!"
        break
    fi
done

echo ""
echo "📊 Queue Worker Pool Test Results:"
echo "  🔹 Baseline goroutines: $BASELINE_GOROUTINES"
echo "  🔹 Maximum goroutines: $MAX_GOROUTINES"
echo "  🔹 Queue activity detected: $QUEUE_ACTIVITY_DETECTED"
echo "  🔹 Large batch detected: $LARGE_BATCH_DETECTED"
echo "  🔹 Process PID: $NEW_PID"

if [ "$QUEUE_ACTIVITY_DETECTED" = true ]; then
    INCREASE=$((MAX_GOROUTINES - BASELINE_GOROUTINES))
    echo "  🔹 Maximum increase: +$INCREASE goroutines"
    
    if [ "$MAX_GOROUTINES" -lt 100 ]; then
        echo ""
        echo "🎉 EXCELLENT: Queue worker pool working perfectly!"
        echo "   Goroutine count stayed below 100 during queue processing"
        echo "   Fixed worker pool prevents goroutine explosion"
    elif [ "$MAX_GOROUTINES" -lt 500 ]; then
        echo ""
        echo "✅ GOOD: Significant improvement with queue worker pool"
        echo "   Much better than the previous 3000+ goroutines"
        echo "   Some room for improvement but major progress"
    else
        echo ""
        echo "⚠️  PARTIAL: Some improvement but still high goroutine count"
        echo "   Queue worker pool may need additional tuning"
    fi
    
    if [ "$LARGE_BATCH_DETECTED" = true ]; then
        echo "  🎯 Large batch test: PASSED"
    else
        echo "  ℹ️  Large batch test: Not encountered during monitoring"
    fi
else
    echo ""
    echo "ℹ️  No queue activity detected during monitoring period"
    echo "   Queue worker pool fix will be tested when queue processing occurs"
fi

echo ""
echo "Continue monitoring: tail -f goresodownload.log | grep -E '(numGoroutines|Processing batch|worker)'"
