#!/bin/bash

echo "🔍 Debug Goroutine Issue"
echo "Current time: $(date)"

# Find current process
CURRENT_PID=$(pgrep -f "goresodownload.*TRB" | head -1)

if [ -n "$CURRENT_PID" ]; then
    echo "Found process PID: $CURRENT_PID"
    
    # Get current goroutine count from logs
    echo "📊 Recent goroutine counts from logs:"
    tail -100 goresodownload.log 2>/dev/null | grep "---numGoroutines" | tail -10
    
    echo ""
    echo "📋 Recent download activity:"
    tail -100 goresodownload.log 2>/dev/null | grep -E "(downloadTasks=[1-9]|Processing.*download)" | tail -5
    
    echo ""
    echo "🔍 Looking for worker pool activity:"
    tail -100 goresodownload.log 2>/dev/null | grep -i "worker" | tail -5
    
    echo ""
    echo "📈 Memory usage:"
    MEMORY_KB=$(ps -p $CURRENT_PID -o rss= 2>/dev/null)
    if [ -n "$MEMORY_KB" ]; then
        MEMORY_MB=$((MEMORY_KB / 1024))
        echo "Memory: ${MEMORY_MB}MB"
    fi
    
    # Send SIGQUIT to get goroutine dump
    echo ""
    echo "🔬 Getting goroutine stack trace..."
    echo "Sending SIGQUIT to process $CURRENT_PID..."
    kill -QUIT $CURRENT_PID 2>/dev/null
    
    echo "Waiting for stack trace to be written to log..."
    sleep 3
    
    echo ""
    echo "📊 Analyzing goroutine stack trace:"
    echo "Looking for goroutine patterns in recent logs..."
    
    # Count different types of goroutines
    tail -1000 goresodownload.log 2>/dev/null | grep "goroutine [0-9]" | head -20 | while read line; do
        echo "  $line"
    done
    
    echo ""
    echo "🔍 Counting goroutine types:"
    tail -1000 goresodownload.log 2>/dev/null | grep -E "(net/http|chan send|chan receive|select)" | head -10 | while read line; do
        echo "  $line"
    done
    
else
    echo "❌ No goresodownload process found"
    echo ""
    echo "📋 Recent logs:"
    tail -20 goresodownload.log 2>/dev/null
fi

echo ""
echo "💡 To continue monitoring:"
echo "  tail -f goresodownload.log | grep -E '(numGoroutines|downloadTasks|worker|goroutine)'"
