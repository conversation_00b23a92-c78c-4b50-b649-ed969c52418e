#!/bin/bash

echo "🔧 Testing Global Worker Pool Fix"
echo "Current time: $(date)"

# Kill current process
echo "Stopping current process..."
pkill -f "goresodownload.*TRB" || true
sleep 3

# Build with global worker pool fix
echo "🔨 Building with global worker pool fix..."
cd /home/<USER>/github/go/goresodownload/cmd/goresodownload
go build -o goresodownload main.go

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build successful with global worker pool pattern"
echo "  🔹 Fixed number of download workers: 5"
echo "  🔹 Fixed number of delete workers: 5"
echo "  🔹 No more goroutine explosion per task"

# Start new process
echo "🚀 Starting with global worker pool..."
nohup ./goresodownload -board TRB > goresodownload.log 2>&1 &
NEW_PID=$!

echo "✅ Started process with PID: $NEW_PID"

# Monitor for goroutine behavior during image downloads
echo "📊 Monitoring global worker pool behavior..."
echo "Expected: Baseline + 5 download workers + 5 delete workers = ~50 goroutines max"

BASELINE_GOROUTINES=0
MAX_GOROUTINES=0
DOWNLOAD_DETECTED=false
LARGE_DOWNLOAD_DETECTED=false

for i in {1..180}; do  # Monitor for 15 minutes
    sleep 5
    
    if ! kill -0 $NEW_PID 2>/dev/null; then
        echo "❌ Process died! Last logs:"
        tail -30 goresodownload.log
        exit 1
    fi
    
    # Get goroutine count
    GOROUTINES=$(tail -20 goresodownload.log 2>/dev/null | grep "---numGoroutines" | tail -1 | awk '{print $2}')
    if [ -z "$GOROUTINES" ]; then
        continue
    fi
    
    # Set baseline if not set
    if [ "$BASELINE_GOROUTINES" -eq 0 ]; then
        BASELINE_GOROUTINES=$GOROUTINES
        echo "📊 Baseline goroutines: $BASELINE_GOROUTINES"
    fi
    
    # Track maximum
    if [ "$GOROUTINES" -gt "$MAX_GOROUTINES" ]; then
        MAX_GOROUTINES=$GOROUTINES
    fi
    
    # Check for download activity
    RECENT_DOWNLOADS=$(tail -20 goresodownload.log 2>/dev/null | grep -c "downloadTasks=[1-9]")
    LARGE_DOWNLOADS=$(tail -20 goresodownload.log 2>/dev/null | grep "downloadTasks=[2-9][0-9]")
    
    if [ "$RECENT_DOWNLOADS" -gt 0 ]; then
        if [ "$DOWNLOAD_DETECTED" = false ]; then
            echo "🔍 Download activity detected! Testing global worker pool..."
            DOWNLOAD_DETECTED=true
        fi
        
        if [ -n "$LARGE_DOWNLOADS" ] && [ "$LARGE_DOWNLOAD_DETECTED" = false ]; then
            echo "🎯 Large download batch detected! This is the real test..."
            LARGE_DOWNLOAD_DETECTED=true
        fi
        
        INCREASE=$((GOROUTINES - BASELINE_GOROUTINES))
        echo "$(date '+%H:%M:%S') - Goroutines: $GOROUTINES (baseline: $BASELINE_GOROUTINES, +$INCREASE)"
        
        # Test the global worker pool fix
        if [ "$GOROUTINES" -gt 100 ]; then
            echo "⚠️  High goroutines during download: $GOROUTINES"
            
            if [ "$GOROUTINES" -gt 500 ]; then
                echo "❌ GLOBAL WORKER POOL FIX FAILED!"
                echo "Expected: ~$((BASELINE_GOROUTINES + 10)) goroutines (baseline + workers)"
                echo "Actual: $GOROUTINES goroutines"
                echo ""
                echo "Recent logs:"
                tail -50 goresodownload.log | grep -E "(downloadTasks|deleteTask|worker|goroutine)"
                exit 1
            fi
        fi
        
        # Success criteria for large downloads
        if [ "$LARGE_DOWNLOAD_DETECTED" = true ] && [ "$GOROUTINES" -lt 100 ]; then
            echo "🎉 GLOBAL WORKER POOL SUCCESS!"
            echo "   Large downloads processed with <100 goroutines"
            break
        fi
    else
        # No download activity, show periodic updates
        if [ $((i % 24)) -eq 0 ]; then
            MINUTES=$((i / 12))
            echo "$(date '+%H:%M:%S') - Waiting for downloads... Goroutines: $GOROUTINES (${MINUTES}min)"
        fi
    fi
done

echo ""
echo "📊 Global Worker Pool Test Results:"
echo "  🔹 Baseline goroutines: $BASELINE_GOROUTINES"
echo "  🔹 Maximum goroutines: $MAX_GOROUTINES"
echo "  🔹 Download activity detected: $DOWNLOAD_DETECTED"
echo "  🔹 Large download detected: $LARGE_DOWNLOAD_DETECTED"
echo "  🔹 Process PID: $NEW_PID"

if [ "$DOWNLOAD_DETECTED" = true ]; then
    INCREASE=$((MAX_GOROUTINES - BASELINE_GOROUTINES))
    echo "  🔹 Maximum increase: +$INCREASE goroutines"
    
    if [ "$MAX_GOROUTINES" -lt 100 ]; then
        echo ""
        echo "🎉 EXCELLENT: Global worker pool working perfectly!"
        echo "   Goroutine count stayed below 100 even during downloads"
        echo "   Expected behavior: Fixed workers handle all tasks"
    elif [ "$MAX_GOROUTINES" -lt 500 ]; then
        echo ""
        echo "✅ GOOD: Significant improvement with global worker pool"
        echo "   Much better than the previous 3000+ goroutines"
        echo "   Some room for improvement but major progress"
    else
        echo ""
        echo "⚠️  PARTIAL: Some improvement but still high goroutine count"
        echo "   Global worker pool may need additional tuning"
    fi
    
    if [ "$LARGE_DOWNLOAD_DETECTED" = true ]; then
        echo "  🎯 Large download test: PASSED"
    else
        echo "  ℹ️  Large download test: Not encountered during monitoring"
    fi
else
    echo ""
    echo "ℹ️  No download activity detected during monitoring period"
    echo "   Global worker pool fix will be tested when downloads occur"
fi

echo ""
echo "Continue monitoring: tail -f goresodownload.log | grep -E '(numGoroutines|downloadTasks|worker)'"
