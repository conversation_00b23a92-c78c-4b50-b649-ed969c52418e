# **Folder Sync Program Specification for Coding AI (TDD Approach)**

## **1\. Introduction**

This document provides a detailed specification for folder-sync, a Go program designed to synchronize folders between multiple servers. The development process for this program **must strictly adhere to a Test-Driven Development (TDD) methodology**. For every functional requirement, unit tests, integration tests, or end-to-end tests (as appropriate) shall be designed and implemented *prior* to writing the production code for that feature.  
The goal is to create a highly reliable, performant, and resilient synchronization tool that can run as a systemd service, handle network disruptions gracefully, manage file conflicts, and support specialized modes like writethrough.

## **2\. Overall Architecture**

The folder-sync program will follow a modular architecture, leveraging Go's concurrency primitives (goroutines and channels). Key components include:

* **Configuration & CLI:** Handles parsing command-line arguments and the YAML configuration file.  
* **Logging:** Manages structured logging to files and systemd journal.  
* **Meta Data Management:** Persists and retrieves pairing status, last synced timestamps, and queues.  
* **Network Communication:** Establishes secure (token-based) and reliable connections, handles retries, version exchange, and binary protocol.  
* **File System Operations:** Provides atomic file writes, checksumming, symlink handling, and permission/ownership management.  
* **Change Monitoring (Ingress):** Detects file system changes via inotify or accepts changes via a local proxy socket.  
* **Synchronization Core:** Orchestrates initial sync, ongoing sync, conflict resolution, queue processing, and the writethrough mode.  
* **Main Application:** Initializes components, orchestrates goroutines, and manages graceful shutdown.  
* **Go Client Package:** Provides an API for external Go programs to interact with the proxy socket.

## **3\. Core Concepts and Data Structures**

The coding AI must define and use the following core data structures and types throughout the codebase.  
**Test-Driven Development Note:** For each of these core concepts, consider writing a simple test to ensure its definition (e.g., fmt.Stringer implementation, basic constructor, zero-value behavior) is correct before using it extensively.

* **FileSyncEvent:** Represents a change event in the sync folder.  
  * Fields: Type (enum: CREATE\_FILE, WRITE\_FILE, DELETE\_FILE, RENAME\_FILE, CHMOD\_FILE, CREATE\_DIR, DELETE\_DIR, RENAME\_DIR, CREATE\_SYMLINK), Path (relative to sync folder), Timestamp (mtime, time.Time), Size (for files), Checksum (based on compares strategy).  
* **PairConfig:** Represents configuration for a single paired server.  
  * Fields: HostnameOrIP (string), Direction (enum: ONE\_WAY\_TO\_PEER, ONE\_WAY\_FROM\_PEER, BIDIRECTIONAL\_NEWEST\_WIN), InitialSyncStrategy (enum: SYNC, NO\_INITIAL\_SYNC).  
* **PairStatus:** Persisted status for a paired server.  
  * Fields: LastSyncedTimestamp (time.Time), InitialSyncStatus (enum: PENDING, IN\_PROGRESS, COMPLETED), CurrentQueueDBFile (path to SQLite DB), ConnectionRetries (int. Connection retry counter. Cleared to 0 when connected.),TotalSent (SyncOpCounts), TotalReceived (SyncOpCounts), .  
* **SyncOpCounts:** A struct to track counts for different sync operations and bytes.  
  * Fields: Creates (int), Writes (int), Deletes (int), Renames (int), Chmods (int), CreateDirs (int), DeleteDirs (int), RenameDirs (int),SentFiles (int),ReceivedFiles (int),RemovedBeforeSent (int, FILE-REMOVED event), Bytes (uint64).  
* **ProtocolVersion:** Semantic version string. (e.g., 1.2.3)  
* **CompareStrategy:** Enum for size, sample, full.  
* **FileCompressionType:** Enum for none, zstd.  
* **IgnoredPathMatcher interface:** Defines behavior for checking if a path should be ignored (e.g., IsIgnored(path string) bool). Implementations for regexp, exact match, extension match.

## **4\. Module Breakdown and TDD Instructions**

Each section below details the requirements for a specific module/component and explicitly outlines the TDD approach.

### **4.1. Configuration and CLI (config, cli packages)**

**Requirements:**

* Read configuration from a YAML file (/etc/folder\_sync.ini by default, or specified by \-c).  
* Parse server, folder, and monitor sections.  
* Validate pairs syntax (e.g., "\>:host", \<:host).  
* Implement command-line flags:  
  * \-c \<config-file\>: Specify configuration file path.  
  * \-t: Test configuration file for conflicting parameters or unrecognized ones; program exits after validation.  
  * \-d: Daemonize the program (will be handled by systemd integration, but the flag should be recognized).  
  * \-h: Display help message.  
  * \-v: Enable debug mode (overrides debug setting in config).  
* Handle default values for optional configurations (e.g., writethrough, compares, concurrent, debug, compress).  
* Validate concurrent value (1-1024).  
* Validate readonly or writethrough and pairs conflicts: If readonly or writethrough is true, but a pairs entry attempts to make this server a *destination* for sync (e.g., :host or \<:host), the program must output an error and exit.  
* Validate writethrough must accompany monitor.socket.

**Test-Driven Development Steps:**

1. **Unit Tests (config\_test.go, cli\_test.go):**  
   * Write tests for parsing a basic YAML config file.  
   * Write tests for ignores parsing and matching different patterns (/regexp/, \*.ext, exact).  
   * Write tests for default values.  
   * Write tests for validation of concurrent range.  
   * Write tests specifically for the readonly , writethrough and pairs conflict detection.  
   * Write tests for validation of writethrough and monitor.socketrequirement.  
   * Write tests for command-line argument parsing and how they override config values.  
2. **Implementation (config.go, cli.go):** Implement the parsing and validation logic, ensuring all tests pass.

### **4.2. Logging (logger package)**

**Requirements:**

* Provide structured logging (e.g., using log/slog or a simple custom struct).  
* Support different log levels (DEBUG, INFO, WARN, ERROR, FATAL).  
* Output logs to files named after paired server IP (e.g., 192\_168\_0\_111.log) within the logs folder.  
* Output critical errors to systemd logs (e.g., os.Stderr for systemd to capture).  
* Debug mode (-v or debug=true in config) should enable verbose logging for all operations.

**Test-Driven Development Steps:**

1. **Unit Tests (logger\_test.go):**  
   * Write tests to verify logs are written to the correct files based on IP.  
   * Test different log levels and ensure messages appear/disappear based on enabled debug mode.  
   * Test capturing of critical errors to os.Stderr.  
2. **Implementation (logger.go):** Implement the logging functions, ensuring tests pass.

### **4.3. Meta Data Management (meta package)**

**Requirements:**

* Persist PairStatus for each paired server in the meta folder.  
* Meta files named after pairing server IP (e.g., 192\_168\_0\_111\_status).  
* Use SQLite for change information queues for each pairing server (CurrentQueue field in PairStatus points to the SQLite DB file). Each pair gets its own DB file for easy removal. DB files named after pairing server IP too.  
* Implement atomic writes for meta files to prevent corruption.  
* TotalSent and TotalReceived metrics (SyncOpCounts) for each peer must be updated and persisted.  
* Information for a pairing must survive restarts.  
* Handle graceful flushing of queue data to disk during shutdown.

**Test-Driven Development Steps:**

1. **Unit Tests (meta\_test.go):**  
   * Write tests for saving and loading PairStatus objects.  
   * Test atomic updates to meta files (e.g., by simulating crashes mid-write).  
   * Write tests for basic SQLite queue operations (add, retrieve, remove items).  
   * Test persistence of queue data across mock restarts.  
   * Test updates and persistence of TotalSent/TotalReceived counters.  
2. **Implementation (meta.go, queue.go):** Implement the meta data and queue management logic, ensuring tests pass.

### **4.4. Network Communication (network package)**

**Requirements:**

* Listen on specified listen addresses and port for incoming connections.  
* Initiate connections to pairs configured servers.  
* Use a shared token for authentication (no TLS in this version).  
* Implement a custom, **efficient binary protocol** for all communications.  
  * The protocol must support:  
    * Initial handshake for authentication (token exchange).  
    * Protocol version exchange.  
    * Sending FileSyncEvent metadata (filename, timestamp, type, size, checksum).  
    * Requesting file bodies (PULL-FILE).  
    * Sending file bodies.  
    * Sending file removed flag (FILE-REMOVED).  
    * Acknowledging sync completion (SYNCED).  
    * Error messages.  
  * **Test-Driven Development Note:** Start with simple handshake messages, then FileSyncEvent metadata, then file transfers.  
* Connection retry logic: When a paired server is down, keep retrying connection every 5 seconds until established, a fatal error (version/token mismatch) occurs, or program shuts down. If a fatal error closes the connection, restart retries after 5 seconds.  
* Support concurrent file pulls, limited by the concurrent configuration (or CPU cores default).  
* Implement optional compression (zstd) for text-based file transfers over the network. Binary files (e.g., images, archives based on MIME type or heuristic) must *not* be compressed.

**Test-Driven Development Steps:**

1. **Unit Tests (network\_test.go, protocol\_test.go):**  
   * Write tests for client/server connection establishment and basic data exchange.  
   * Test token-based authentication.  
   * Test protocol version exchange and compatibility checks.  
   * Write tests for serializing/deserializing FileSyncEvent and other protocol messages.  
   * Test the retry logic for down peers, fatal errors, and re-retries after fatal errors.  
   * Test concurrent file transfers (mocking network delays).  
   * Write tests for zstd compression logic on text vs. binary data.  
2. **Integration Tests:** Set up two mock servers and test a full handshake and simple file transfer.  
3. **Implementation (network.go, protocol.go):** Implement the network client/server, connection management, and binary protocol, ensuring tests pass.

### **4.5. File System Operations (fsutil package)**

**Requirements:**

* Perform atomic file writes: write to a temporary file in the same directory, then rename to the final destination.  
* Calculate xxHash checksums for size, sample, and full comparing strategies.  
  * size strategy: No checksum hash is calculated. Only compare file size.  
  * sample strategy: Read disk block size from OS at startup (syscall.Statfs). Hash first block, then last block if file \> 1 block.  
  * full strategy: Hash the full file content.  
* Handle symlinks:  
  * Sync symlinks by copying their target path.  
  * Ignore and discard symlinks pointing outside the sync folder.  
* Preserve and apply file permissions (chmod) and ownership (chown) during sync operations where applicable (indicated by SYNCED response for metadata changes).  
* Ensure all path operations are relative to the sync folder.

**Test-Driven Development Steps:**

1. **Unit Tests (fsutil\_test.go):**  
   * Write tests for atomic file writes, verifying temporary file creation and successful rename.  
   * Write tests for xxHash calculation for size, sample (using mock block data), and full strategies.  
   * Test symlink creation, resolution, and detection of outside-of-sync-folder targets.  
   * Test setting/preserving file permissions and ownership.  
   * Test handling of relative paths.  
2. **Implementation (fsutil.go):** Implement file system utility functions, ensuring tests pass.

### **4.6. Change Monitoring (Ingress) (ingress package)**

**Requirements:**

* **Inotify Watch:** If monitor.socket is *not* specified, use inotify to watch the sync folder for all relevant changes (create, write, delete, rename, chmod, dir creation/deletion/rename, symlink creation).  
* **Proxy Socket:** If monitor.socket *is* specified, listen on the Unix domain socket for incoming change events. This socket will be the sole source of changes for the sync folder in this mode.  
* Translate detected changes (from inotify or proxy socket) into FileSyncEvent objects.  
* Feed FileSyncEvents into a Go channel to be consumed by the synchronization core.  
* Gracefully handle inotify events (e.g., IN\_Q\_OVERFLOW).  
* In writethrough mode, this module *only* listens on the proxy socket and does *not* use inotify.

**Test-Driven Development Steps:**

1. **Unit Tests (ingress\_test.go):**  
   * Write tests for inotify event translation to FileSyncEvent.  
   * Write tests for the proxy socket listener, verifying correct parsing of incoming binary protocol messages into FileSyncEvents.  
   * Test that events are correctly sent to the output channel.  
2. **Integration Tests:**  
   * For inotify: Create/modify/delete files in a temporary directory and verify FileSyncEvents are generated.  
   * For proxy-socket: Write a simple client that sends a mock binary event to the socket and verify the FileSyncEvent is received by the ingress module.  
3. **Implementation (inotify.go, proxysocket.go, ingress.go):** Implement change detection and event generation, ensuring tests pass.

### **4.7. Synchronization Core (sync package)**

This is the central orchestration logic.  
**Requirements:**

* **Initial Sync Logic:**  
  * Perform a whole folder scan based on the InitialSyncStrategy (one-way, newest-win, no-initial-sync).  
  * newest-win: Compare timestamps, copy if newer or missing on one side.  
  * one-way-to-peer: Copies all source files to the peer.  
  * Log progress every minute if initial sync takes longer than 20 seconds.  
  * Queue any changes occurring *during* initial sync to prevent data loss.  
* **Ongoing Sync Logic:**  
  * Continuously process FileSyncEvents from the ingress channel.  
  * Upon receiving an event via the proxy socket, the local file/directory is created/modified.  
  * For each event, add it to the SQLite queue for *every* active paired server.  
  * Each paired server connection (handled by network package) will pull changes from its dedicated queue.  
  * Process responses from receiving servers:  
    * SYNCED: Mark the file/operation as synced for that peer, update LastSyncedTimestamp for the peer, update TotalSent stats. If in writethrough mode, trigger cleanup if all peers have acknowledged.  
    * PULL-FILE: Initiate sending the file body to the requesting peer. If the file was removed before sending the file body, send a FILE-REMOVED flag to the peer.  
* **Conflict Resolution (newest-win):**  
  * If mtime is identical, do *not* overwrite. Log an error on both sides.  
  * If a file exists on only one server, copy it to the other.  
* **Writethrough Mode:**  
  * If writethrough=true, the sync folder acts as a *buffer*, and no initial sync shall be performed.  
  * The server *will not monitor* the sync folder via inotify. It *only* receives events via the proxy socket.  
  * The change is then propagated to all active paired servers.  
  * Once a file/directory modification (originating from the proxy socket) has been **successfully applied and acknowledged by *all* currently active paired servers**, the corresponding local buffer file or empty directory within the sync folder will be **automatically removed**. This requires tracking per-file/directory sync completion across all peers.  
  * If a new file is created or written into a non-existing folder, the folder must be created on-the-fly.  
* **Concurrency:**  
  * Ingress (inotify or proxy-socket) feeds events through channels to the sync core.  
  * Pair-syncing operations (sending events, receiving responses, handling file pulls) for each peer must be managed in separate goroutines, utilizing the concurrent limit for file pulls.  
  * All access to shared resources (meta data, queues, fsutil) must be goroutine-safe (e.g., using mutexes).

**Test-Driven Development Steps:**

1. **Unit Tests (sync\_test.go):**  
   * Write tests for initial sync strategies (mock file system, mock network).  
   * Test conflict resolution logic (same mtime, one-sided files).  
   * Test queueing of events during and after initial sync.  
   * Test processing of SYNCED and PULL-FILE responses.  
   * Test target file removed before responding PULL-FILE request .  
   * Write comprehensive tests for writethrough mode:  
     * Creation/modification events received via proxy.  
     * Propagation to multiple mock peers.  
     * Correct cleanup of local buffer file/dir only *after all* acknowledgments.  
     * Handling of partial acknowledgments (file remains until all peers acknowledge).  
   * Test goroutine safety of shared data.  
2. **Integration Tests:** Set up two folder-sync instances (one "first server", one "second server") and:  
   * Test initial sync scenarios (one-way, newest-win).  
   * Test ongoing sync for file creates, writes, deletes, renames, chmod.  
   * Test network disconnections and reconnections, verifying resumed sync from LastSyncedTimestamp.  
   * Test a full writethrough scenario with multiple peers.  
3. **Implementation (sync.go, initialsync.go, ongoing.go, writethrough.go):** Implement the core synchronization logic, ensuring all tests pass. This will be the largest and most complex module.

### **4.8. Main Application (main package)**

**Requirements:**

* Initialize all components (config, logger, meta, network, ingress, sync core).  
* Parse command-line arguments.  
* Handle \-t (test config): perform validation and exit.  
* Orchestrate the main goroutines: ingress, network listeners, per-peer sync routines.  
* Implement graceful shutdown:  
  * Listen for OS signals (e.g., SIGTERM, SIGINT).  
  * Stop network listeners.  
  * Drain and flush any pending queues to meta files.  
  * Ensure all goroutines exit cleanly.  
  * Perform any necessary cleanup (e.g., closing SQLite connections).  
* Monitor memory usage: Output a warning to systemd log if memory usage exceeds 500MB or 50% of total memory (whichever is less). Use Go's runtime.MemStats or debug.SetGCPercent as a starting point.

**Test-Driven Development Steps:**

1. **End-to-End Tests (e2e\_test.go):**  
   * Use podman or Docker Compose or similar to spin up multiple mock folder-sync instances.  
   * Test the entire sync pipeline from a file change on one server to its propagation to another.  
   * Test graceful shutdown scenarios and verify meta data integrity after restart.  
   * Test command-line parameters (-c, \-t, \-v).  
   * Test memory usage warnings (can be challenging to precisely test in E2E but aim for warnings if large file transfers occur).  
2. **Implementation (main.go):** Implement the application's entry point and orchestration, ensuring all tests pass.

### **4.9. Go Client Package (client package)**

**Requirements:**

* Generate a separate Go client package.  
* This package will be used by other Go programs to make changes to the sync folder via the proxy socket.  
* The client API should resemble the native Go os package interface for file and folder change operations (e.g., client.Create(path string), client.WriteFile(path string, data \[\]byte), client.Remove(path string)).  
* For read and inquiry operations (os.ReadFile, os.Stat, os.ReadDir), this package can proxy directly to the OS (i.e., it doesn't need to go through the socket for reads).  
* For write and change operations, which work within the specified folder prefix, shall performed on the corresponding proxy socket. If the target files or folders do not start with the specified folder prefix, the operations shall be proxied directly to the OS.  
* The client API shall provide an initialization function to add pairs of folder prefix and corresponding proxy socket path. The proxy socket and the client package shall support a test connection operation, so the client knows the socket is working when initiating the folder and socket pair.  
* The client must communicate with the folder-sync daemon through the proxy-socket using the defined **efficient binary custom protocol**.  
* A separate, clear document detailing the **custom binary protocol** used by the proxy socket must be generated. This document should specify message types, field formats, and sequencing.

**Test-Driven Development Steps:**

1. **Unit Tests (client\_test.go):**  
   * Write tests for each client API function (e.g., client.Create, client.WriteFile), mocking the proxy socket connection to verify correct binary protocol message generation.  
   * Write tests for operations not on the targeting folders.  
   * Write tests for reading operations.  
   * Test error handling for socket communication.  
2. **Integration Tests:** Spin up a folder-sync instance in proxy-socket mode, and use the client package to perform operations, verifying FileSyncEvents are received by the daemon.  
3. **Implementation (client.go):** Implement the client API and binary protocol encoding/decoding.  
4. **Protocol Document:** Create a markdown document (FOLDER-SYNC-SOCKET-PROTOCOL.md) detailing the binary protocol.

### **4.10. Installation Script**

**Requirements:**

* A shell script (e.g., install.sh) to automate deployment.  
* Copy the compiled folder-sync binary to /usr/local/bin/.  
* Create a default configuration file at /etc/folder\_sync.ini.  
* Create a systemd service unit file for folder-sync and enable it.  
* Show user the action steps when performing them.

**Test-Driven Development Steps:**

1. **Manual Verification (or Scripted Test):** This is typically tested manually or with a simple shell script that calls the install script in a clean VM and verifies paths, permissions, and systemd service status.  
2. **Implementation (install.sh):** Write the shell script.

## **5\. Non-Functional Requirements Summary**

These requirements apply across the entire program.

* **Platform:** Linux-like systems only.  
* **Resource Usage:**  
  * Memory: Max 500MB or 50% of total system memory (whichever is less). Log a warning if this limit is approached.  
  * CPU: Efficient use of CPU, leveraging Go's concurrency.  
* **Logging:** Structured, file-based per-peer logs, critical errors to systemd journal. Debug mode for verbose logging.  
* **Error Handling:** Robust error handling throughout, clear error messages, graceful recovery where possible, fatal errors for irrecoverable states.  
* **Concurrency:** Extensive use of goroutines and channels for ingress, network operations, and per-peer sync routines.  
* **Security:** Token-based authentication for server-to-server communication. **TLS is explicitly *not* supported in this version.**  
* **Resilience:** Connection retry logic, queue persistence, atomic file operations, graceful shutdown.  
* **Usability:** Clear CLI parameters, comprehensive configuration options, progress logging for long initial syncs.

## **6\. Glossary / Key Definitions**

* **sync folder:** The directory whose contents are to be synchronized.  
* **meta folder:** Directory for storing program status, pairing information, and SQLite queues.  
* **logs folder:** Directory for storing log files.  
* **Paired Server:** Another folder-sync instance participating in synchronization.  
* **mtime:** Modification timestamp of a file or directory.  
* **Atomic Operation:** An operation that completes entirely or not at all, preventing partial states (e.g., writing to temp file then renaming).  
* **inotify:** Linux kernel subsystem for real-time monitoring of file system events.  
* **Proxy Socket:** A local Unix domain socket used as an ingress point for file system change events originating from other local applications.  
* **writethrough mode:** A special mode where folder-sync *only* acts as a conduit for changes from its local proxy socket to remote peers, and deletes local buffer files/dirs after all peers acknowledge.  
* **Binary Protocol:** A custom network protocol where data is encoded in a compact, efficient binary format, rather than text-based formats like JSON or XML.  
* **TDD (Test-Driven Development):** A software development process where tests are written *before* the code they test.