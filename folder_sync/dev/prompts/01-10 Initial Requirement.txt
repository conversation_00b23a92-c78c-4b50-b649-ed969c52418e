I'd like to write a program to sync folders between servers. It meets the following requirements:
- Run as a systemd service, read configuration from a file, and maintain a meta folder to save status, graceful shutdown.
- Watch or proxy (through a linux socket as local ingress) all changes to a folder
- Sync all changes to paired servers (by specify IP or hostname). The very first server has no 'pairs' configuration and waits for other servers to pair with it. The 2nd and later servers will have 'pairs' configuration so they initiate the paring request. Once the pairing established, the other server's information shall be saved in the meta folder on both sides. The pairing information survives restarts.
- To dispairing servers, admin needs to remove meta files for the pair on both sides and remove 'pairs' from the configuration of the initiating server, then restarts both servers.
- Pairing connection shall exchange protocal version for future downwards compatability.
- Pairing servers will keep retry connect to the other server every 5 seconds when the other party is down, until the connection established, fatal error(version incompatible, token wrong, i.e.), or program shutdown. When the fatal error connection is closed, the server will start new retries as the other party may be updated.
- Comparing strategy([server]compares): size, which comparing the size only. sample, use xxHash algorism on the first and last blocks of the file content(if it's bigger than 1 block. Disk block size shall be read from OS when program starts to optimize the performance). full, use xxHash algorism on the full content of the file. This program will only support Linux-like systems.
- Conflict resolution (newest-win): When two timestamps(mtime) are exactly the same, do not overwrite. Just output an error log on both sides. If a file exists on only one server, it will be copied to the other server. An initial server time comparison shall be performed. If the time difference is larger than 1s, both servers shall write out errors in logs and mark the connection as fatal error, and do not sync files.
- Atomic file operations: When the receiving server writes a file, it should do so atomically (e.g., write to a temporary file, then rename to the final destination). This prevents partial files if the process crashes during a write.
- Symlink: should be synced as symlinks, copying the target path. If a symlink points outside the sync folder, it should be ignored and discarded.
- Keep tracking (save in meta folder) last synced file/folder timestamp for each paired server. When certain paired server downs and backs up again, changes shall be synced to that server from the last timestamp
- During initial paring, a whole folder sync shall be performed. one-way or newest-win(default) or no-initial-sync strategy will be chosen by the sync initiation server. When initial sync takes longer than 20s, server shall log progress every one minutes about how many syncs operations have been done.
- After paring initial sync, the connection state between those two servers becomes on-going mode. Between starting initial sync and switching to on-going mode, all changes(change type, relative file name, and timestamp) shall be queued and saved to meta folder if needed. On-going change shall be queued too.
- On-going watch or proxy sends all changes to every paired server queue. The sending server sends file name, change timestamp, type(folder/file/symbol link), file size, and checksum(depending on comparing strategy) to the other server. The receiving server compares those information with its local file then send back a SYNCED(when file pull is not necessory, but file permission and ownership change may be performed) or PULL-FILE response to the sending server. When the sending server received the pull file request, it'll send the file body as response. When the file body sending is done and a SYNCED flag for this file is received, the sending server marks the new timestamp for that paired server. Concurrent file pulls shall be supported but limited to a configurable threads, default is the CPU cores of the pulling side.
- To dispair two servers, change meta file and service restart is needed.
- The connection between servers are secured by shared token and all pairing servers listen on the same port in their respective servers. TLS will not be supported in this version.
- When observation/monitor mode is proxy-socket, an efficient binary custom protocal shall be used, so all and only write/modification operations can be performed through the socket on the sync-folder as relative path, and notify paired servers through their respective queues.
- A separate go client package shall be generated to be imported by other go programs to make changes on the sync-folder through the proxy-socket. A document shall also be generated for the custom protocal if needed. This go package shall resemble the native go os pacakge interface on file and folder change operations. And for read and inquiery operations, this pacakge can proxy it to the os directly.
- Support a special writethrough mode. In this mode, the server will not monitor the sync-folder, but listen on the proxy-socket only. In writethrough mode, once a specific file or directory modification event (originating from the proxy socket) has been successfully applied and acknowledged by all currently active paired servers, the corresponding local buffer file or empty directory within the sync folder will be automatically removed. This also implys when a new file created or write on an non-existing folder, the folder shall be created on-the-fly. This requires per-file/directory tracking of sync completion across all peers. This mode is to allow local program to write folders and files on remote paired servers without using too much disk spaces on local file system. In this mode, the sync-folder is used as a buffer folder.
- Meta data shall be saved to meta folder and named staring with pairing server ip, like: 192_168_0_111_status, and so on.
- The changing-information queue for each pairing server can be saved in files or use sqlite like package.
- Log files shall be named after each pairing server ip, like: 192_168_0_111.log
- This program shall not use more than 500MB memory or 50% of total memory, whichever is less. Use structure and techniques to keep the footprint low. When program uses more than this limit, output warning in systemd log.
- Ciritcal errors shall be output to systemd logs
- ingress and pair-syncing both can be multiple threaded and in separate goroutines. ingress(inotify or proxy-socket) will feed the pair-syncing routine through channels.
- The program shall accept following parameters: -c config-file, -t test config file for conflicting parameters or unrecognized ones, -d daemonize program, -h help, -v debug mode(override config).
- A installation script is needed to copy binaries to /usr/local/bin and create /etc/folder_sync.ini and systemd service.

Suggested configuration file format yaml:
```
[server]
token = "xxx"   # shared token
listen = ["*************", "**********"]
port = 12345
# paring servers. optional. empty for the very first server. remove with meta files when dispairing
# <, >, ! can be combined to specify direction and initial sync requirements. "<>" can be omitted as default.
pairs = [">:one-way-to-paired-host1","<:one-way-from-paired-host2",":newest-win-paired-host3","!:no-initial-sync-host4"]
# writethrough mode, optional. default false.
writethrough = false
# comparing strategy: size, sample(default), full
compares = "size"
# concurrent pulls, syncs. optional. between 1~1024. default: CPU cores.
concurrent = 10
# debug. When true, all operations shall be logged.
debug = false
# compress: none, zstd(default). Only apply to MIME text based files over network. Binary files will not be compressed.
compress = zstd

[folder]
sync = "/path/to/be/synced"
meta = "/path/for/saving/metas"
logs = "/path/for/sync/logs"
# ignored file, folder, symbol links names
# string starting with "/" is regexp. "*.XXX" format is for extention. Others for exactly matchs
ignores = ['/^\.reg/i','fullname','*.ext'] 
# readonly default false. To prevent accidental write from other paired servers. Do not prevent self writing.
# When pairs option conflicts with this flag, output error and exits
readonly = false

[monitor]
# proxy socket. optional. when not specified, a inotify watch strategy will be used.
socket = "/var/run/socket-name"

```

Meta file for each pairing server shall includes:
LastSyncedTimestamp: The time.Time of the last successfully synced change for that specific paired server. This is crucial for recovery.
InitialSyncStatus: Enum/string indicating PENDING, IN_PROGRESS, COMPLETED.
CurrentQueue: the SQLite database file specifically for that paired server's queue. Each pairing connection shall have its own db file, in case the connection to be removed, admin can simply remove corresponding files.
TotalSent and TotalReceived: CREATE, WRITE, DELETE, RENAME, CHMOD, CREATEDIR, DELETEDIR, RENAMEDIR, bytes(K,M,G,T,P)


Please don't to write the program but review my requirements and let me know if everything is covered and if anything to be clarified. 

If everything looks fine, please rewrite my requirements into a detailed document to instruct a coding AI to write the go program in a test-driven development(TDD) approach at next step.