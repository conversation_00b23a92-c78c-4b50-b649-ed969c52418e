Round one:
Please generate the network communication protocol documents first. (from 01-13)

Round two:
1. This protocol is for both inter-server communication and the proxy socket communication with the Go Client Package. Each message type shall be flagged for the communication type(s).
2. File/dir permission and ownership need to be considered.
3. When sender find the receiver have the same file contents as the receiver, the sender shall be able to send a SAME_CONTENT message type.
4. Inter-server communication may use an message id, which can be the id in queue table, to simplify the file-pull, file-pull response, sync-acknowledge, file-removed operations on both side.
5. A simple welcome message exchange is needed for proxy socket communication with the Go Client Package.
6. A separate proxy socket communication with the Go Client Package sequencing and flow shall be generated.

Please make change to resolve my review comments above.


Round three:
Further review:
1. When a file is big, the sender shall not send the body accompanying initial message. To respond a pull request, the sender shall be able to send chunks instead of the whole file in one message. The receiver shall be able to rebuild the whole file from the chunks.
2. Chunk writing may be available for socket client communication or required by os package API too.
3. 5.7.3 3 acknowledgement from daemon to client may be required by os package API. Please check its golang documents.

Overall this protocol document is very precise. Please make the above modifications.

Round four:
We are almost done.
Sometimes, the file path is deep and long. With the using of MessageID, is it a good idea to remove the Path field when it's clear on both side, especially in pull request, sync acknowledge, removed, same contents, chunk, chunk acknowledge, or proxy event acknowledge?

Got: 02 Sync Protocol.md
Copy 01-13 Gemini revised Specification.md as 01 Specification.md

