#!/bin/bash

# Script to debug the handshake issue

echo "=== Debugging Handshake Issue ==="
echo ""

# Function to test what's actually being sent to a port
test_connection() {
    local host="$1"
    local port="$2"
    local description="$3"
    
    echo "Testing connection to $description ($host:$port)..."
    
    # Test with netcat to see what the server responds with
    echo "Sending test byte 0x01 (MSG_HANDSHAKE_REQUEST):"
    printf '\x01' | nc -w 2 "$host" "$port" 2>/dev/null | hexdump -C || echo "  Connection failed or no response"
    
    echo "Sending test byte 0xFF:"
    printf '\xFF' | nc -w 2 "$host" "$port" 2>/dev/null | hexdump -C || echo "  Connection failed or no response"
    
    echo ""
}

# Function to check what's listening on a port
check_listener() {
    local host="$1"
    local port="$2"
    local description="$3"
    
    echo "Checking what's listening on $description ($host:$port)..."
    
    # Check if port is open
    if nc -z "$host" "$port" 2>/dev/null; then
        echo "  ✓ Port is open and accepting connections"
        
        # Try to get banner or initial response
        echo "Getting initial response:"
        timeout 2 nc "$host" "$port" </dev/null 2>/dev/null | hexdump -C || echo "  No initial response"
        
    else
        echo "  ✗ Port is not accessible"
    fi
    echo ""
}

# Function to show current configuration
show_config() {
    local server="$1"
    echo "=== Current Configuration for $server ==="
    
    if [ -f /etc/folder-sync.ini ]; then
        echo "Configuration file exists:"
        echo "Auth token:"
        grep "auth_token:" /etc/folder-sync.ini || echo "  Not found"
        echo ""
        echo "Server listen address:"
        grep "listen_address:" /etc/folder-sync.ini || echo "  Not found"
        echo ""
        echo "Pairs configuration:"
        sed -n '/^pairs:/,/^[a-zA-Z]/p' /etc/folder-sync.ini | head -20
    else
        echo "Configuration file not found!"
    fi
    echo ""
}

# Determine which server this is
if [[ $(hostname) == "ca2" ]] || [[ $(ip addr | grep -q "*************") ]]; then
    SERVER="ca2"
    LOCAL_IP="*************"
    PEER_IP="*************"
elif [[ $(hostname) == "ca3" ]] || [[ $(ip addr | grep -q "*************") ]]; then
    SERVER="ca3"
    LOCAL_IP="*************"
    PEER_IP="*************"
else
    echo "Cannot determine server identity"
    LOCAL_IP=$(ip route get ******* | awk '{print $7; exit}')
    echo "Detected local IP: $LOCAL_IP"
    echo "Please specify peer IP manually"
    exit 1
fi

echo "Detected server: $SERVER (Local: $LOCAL_IP, Peer: $PEER_IP)"
echo ""

# Show current configuration
show_config "$SERVER"

# Check local service
echo "=== Local Service Status ==="
echo "Service status:"
systemctl is-active folder-sync 2>/dev/null || echo "  Service not running"

echo "Listening ports:"
ss -tupln | grep ":7890" || echo "  No service listening on port 7890"

echo "Recent service logs:"
journalctl -u folder-sync --no-pager -n 5 2>/dev/null || echo "  No recent logs"
echo ""

# Test local listener
check_listener "$LOCAL_IP" "7890" "local server"
check_listener "127.0.0.1" "7890" "local server (localhost)"

# Test peer connection
check_listener "$PEER_IP" "7890" "peer server"

# Test what happens when we connect
echo "=== Connection Tests ==="
test_connection "$LOCAL_IP" "7890" "local server"
test_connection "$PEER_IP" "7890" "peer server"

# Check for any other processes that might be interfering
echo "=== Process Check ==="
echo "Processes listening on port 7890:"
lsof -i :7890 2>/dev/null || echo "  No processes found"
echo ""

echo "=== Recommendations ==="
echo "1. Check if both servers are using the correct configuration"
echo "2. Verify that auth tokens match between servers"
echo "3. Check if there are any firewall rules blocking connections"
echo "4. Verify that the services are actually running the updated code"
echo "5. Check if there are any other services interfering with port 7890"
