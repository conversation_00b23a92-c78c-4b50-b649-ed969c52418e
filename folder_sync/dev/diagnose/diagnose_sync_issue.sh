#!/bin/bash

# Diagnose sync issues between folder-sync servers
# Usage: ./diagnose_sync_issue.sh [log_file_1] [log_file_2]

LOG_FILE_1=${1:-"192_168_0_103_45158.log"}
LOG_FILE_2=${2:-"192_168_0_102_7890.log"}

echo "=== Folder-Sync Sync Issue Diagnostic Tool ==="
echo "Analyzing logs: $LOG_FILE_1 and $LOG_FILE_2"
echo

# Function to analyze a log file
analyze_log() {
    local log_file=$1
    local server_name=$2
    
    echo "=== Analysis for $server_name ($log_file) ==="
    
    if [ ! -f "$log_file" ]; then
        echo "✗ Log file $log_file not found"
        return 1
    fi
    
    echo "1. Message types being exchanged:"
    grep -o "Type=[A-Z_]*" "$log_file" | sort | uniq -c | sort -nr
    echo
    
    echo "2. Looking for conflict resolution messages:"
    grep -i "conflict\|same.*content\|newer\|identical" "$log_file" || echo "  No conflict resolution messages found"
    echo
    
    echo "3. Looking for pull requests:"
    grep -i "pull.*request\|pull.*response" "$log_file" || echo "  No pull request messages found"
    echo
    
    echo "4. Looking for error messages:"
    grep -i "error\|failed\|warn" "$log_file" | head -5 || echo "  No error messages found"
    echo
    
    echo "5. File operations:"
    grep -i "created\|deleted\|modified\|applied.*change" "$log_file" | head -5 || echo "  No file operations found"
    echo
    
    echo "6. Recent activity (last 10 lines):"
    tail -10 "$log_file"
    echo
}

# Analyze both log files
analyze_log "$LOG_FILE_1" "Server 1"
analyze_log "$LOG_FILE_2" "Server 2"

echo "=== Summary and Recommendations ==="
echo

# Check for common issues
echo "Checking for common sync issues..."

# Check if both servers are only sending, not receiving
send_count_1=$(grep -c "Queued message to send" "$LOG_FILE_1" 2>/dev/null || echo "0")
recv_count_1=$(grep -c "Received message" "$LOG_FILE_1" 2>/dev/null || echo "0")
send_count_2=$(grep -c "Queued message to send" "$LOG_FILE_2" 2>/dev/null || echo "0")
recv_count_2=$(grep -c "Received message" "$LOG_FILE_2" 2>/dev/null || echo "0")

echo "Server 1: Sent $send_count_1, Received $recv_count_1"
echo "Server 2: Sent $send_count_2, Received $recv_count_2"

if [ "$send_count_1" -gt 0 ] && [ "$recv_count_1" -gt 0 ] && [ "$send_count_2" -gt 0 ] && [ "$recv_count_2" -gt 0 ]; then
    echo "✓ Both servers are sending and receiving messages"
else
    echo "⚠ Message flow issue detected"
fi

echo
echo "Possible causes for no file transfers:"
echo "1. Files already exist and are identical (check for MSG_SAME_CONTENT)"
echo "2. Conflict resolution preventing transfers (local files newer)"
echo "3. Configuration issues (readonly mode, wrong direction)"
echo "4. Pull request mechanism failing for large files"
echo "5. Writethrough mode ignoring local events"
echo
echo "Next steps:"
echo "1. Check configuration files for both servers"
echo "2. Verify sync directories have different content"
echo "3. Look for MSG_SAME_CONTENT or conflict messages in full logs"
echo "4. Test with a new file created on one server"

echo
echo "=== Diagnostic Complete ==="
