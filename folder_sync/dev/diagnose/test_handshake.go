package main

import (
	"log"
	"net"
	"time"

	"github.com/real-rm/folder_sync/pkg/network"
	"github.com/real-rm/folder_sync/pkg/types"
)

// Simple logger for testing
type TestLogger struct{}

func (l *TestLogger) Error(format string, args ...interface{}) {
	log.Printf("[ERROR] "+format, args...)
}
func (l *TestLogger) Info(format string, args ...interface{}) {
	log.Printf("[INFO] "+format, args...)
}
func (l *TestLogger) Debug(format string, args ...interface{}) {
	log.Printf("[DEBUG] "+format, args...)
}
func (l *TestLogger) Warn(format string, args ...interface{}) {
	log.Printf("[WARN] "+format, args...)
}
func (l *TestLogger) ErrorP(peerID string, format string, args ...interface{}) {
	log.Printf("[ERROR][%s] "+format, append([]interface{}{peerID}, args...)...)
}
func (l *TestLogger) InfoP(peerID string, format string, args ...interface{}) {
	log.Printf("[INFO][%s] "+format, append([]interface{}{peerID}, args...)...)
}
func (l *TestLogger) DebugP(peerID string, format string, args ...interface{}) {
	log.Printf("[DEBUG][%s] "+format, append([]interface{}{peerID}, args...)...)
}
func (l *TestLogger) WarnP(peerID string, format string, args ...interface{}) {
	log.Printf("[WARN][%s] "+format, append([]interface{}{peerID}, args...)...)
}

// Simple message handler for testing
type TestHandler struct {
	logger *TestLogger
}

func (h *TestHandler) HandleMessage(conn *network.Connection, msgType types.MessageType, payload []byte) {
	h.logger.InfoP(conn.RemoteAddr().String(), "Received message: Type=%s, Length=%d", msgType.String(), len(payload))
}

func main() {
	logger := &TestLogger{}

	// Test the handshake protocol
	authToken := "cd16c30fab2ff614e5628feba886fc42c1fabb1e6fdeb2f01f881d40bbc160f0"

	// Start a test server
	handler := &TestHandler{logger: logger}
	server := network.NewServer("127.0.0.1:0", authToken, handler, logger)

	err := server.Start()
	if err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
	defer server.Stop()

	serverAddr := server.GetListenAddr()
	logger.Info("Test server started on %s", serverAddr)

	// Give server time to start
	time.Sleep(100 * time.Millisecond)

	// Test 1: Valid handshake
	logger.Info("=== Test 1: Valid Handshake ===")
	client := network.NewClient(serverAddr, authToken, "test-client", logger)
	conn, err := client.Connect()
	if err != nil {
		logger.Error("Valid handshake failed: %v", err)
	} else {
		logger.Info("Valid handshake succeeded")
		conn.Close()
	}

	// Test 2: Invalid auth token
	logger.Info("=== Test 2: Invalid Auth Token ===")
	badClient := network.NewClient(serverAddr, "wrong-token", "test-client", logger)
	conn, err = badClient.Connect()
	if err != nil {
		logger.Info("Invalid auth token correctly rejected: %v", err)
	} else {
		logger.Error("Invalid auth token was accepted!")
		conn.Close()
	}

	// Test 3: Send raw bytes (simulate the 0xFF issue)
	logger.Info("=== Test 3: Raw Bytes (0xFF) ===")
	rawConn, err := net.Dial("tcp", serverAddr)
	if err != nil {
		logger.Error("Failed to connect for raw test: %v", err)
	} else {
		// Send 0xFF byte
		rawConn.Write([]byte{0xFF})
		time.Sleep(100 * time.Millisecond)
		rawConn.Close()
		logger.Info("Sent 0xFF byte to server")
	}

	// Test 4: Send valid handshake request manually
	logger.Info("=== Test 4: Manual Handshake ===")
	rawConn, err = net.Dial("tcp", serverAddr)
	if err != nil {
		logger.Error("Failed to connect for manual test: %v", err)
	} else {
		// Send MSG_HANDSHAKE_REQUEST (0x01)
		rawConn.Write([]byte{0x01})
		// Send auth token length (as uint16 big-endian) + auth token
		tokenBytes := []byte(authToken)
		tokenLen := len(tokenBytes)
		rawConn.Write([]byte{byte(tokenLen >> 8), byte(tokenLen & 0xFF)}) // Length as uint16
		rawConn.Write(tokenBytes)
		// Send protocol version (3 bytes: 1.0.0)
		rawConn.Write([]byte{1, 0, 0})

		// Read response
		response := make([]byte, 1024)
		n, err := rawConn.Read(response)
		if err != nil {
			logger.Error("Failed to read handshake response: %v", err)
		} else {
			logger.Info("Handshake response: %x", response[:n])
		}
		rawConn.Close()
	}

	logger.Info("=== Test Complete ===")
}
