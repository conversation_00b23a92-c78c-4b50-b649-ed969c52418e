# Development Directory

This directory contains development-related files and tools that are not part of the main application.

## Contents

### Test Coverage
- **coverage.out** - Go test coverage data (generated by `make test`)
- **coverage.html** - HTML coverage report (generated by `make coverage`)

### Diagnostic Tools
- **diagnose/** - Directory containing debugging and diagnostic utilities
  - Debug scripts for handshake issues
  - Diagnostic tools for sync problems
  - Test utilities for development

### Development Prompts
- **prompts/** - Directory containing development prompts and specifications
  - Initial requirements and specifications
  - Implementation commands and guidelines
  - Historical development documentation

## Usage

### Generating Coverage Reports
```bash
# Run tests with coverage
make test

# Generate HTML coverage report
make coverage

# View coverage report
open dev/coverage.html
```

### Using Diagnostic Tools
```bash
# Run diagnostic scripts
cd dev/diagnose
./debug_handshake.sh
./diagnose_sync_issue.sh
```

### Development Workflow
```bash
# Clean development artifacts
make clean

# Run all quality checks
make check

# Watch for changes (requires entr)
make watch
```

## Notes

- Files in this directory are typically excluded from production builds
- Coverage files are regenerated on each test run
- Diagnostic tools are for development and debugging only
- Prompts directory contains historical development context

## Gitignore

The following patterns should be in .gitignore for this directory:
```
dev/coverage.out
dev/coverage.html
dev/*.log
```
