package sync

import (
	"fmt"
	"testing"
	"time"

	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/logger"
	"github.com/real-rm/folder_sync/pkg/types"
)

// TestEventDeduplication tests that duplicate events for the same file are properly deduplicated
func TestEventDeduplication(t *testing.T) {
	// Create a minimal config
	cfg := config.NewDefaultConfig()

	// Create a memory logger
	log := logger.NewMemoryLogger()

	// Create event queue
	eventQueue := make(chan types.FileSyncEvent, 10)

	// Create sync core
	syncCore := NewSyncCore(cfg, nil, nil, eventQueue)
	syncCore.log = log

	testFile := "test.txt"

	// First event should NOT be deduplicated
	if syncCore.ShouldDeduplicateEvent(testFile) {
		t.Error("First event for file should not be deduplicated")
	}

	// Immediate second event should be deduplicated (within 500ms window)
	if !syncCore.ShouldDeduplicateEvent(testFile) {
		t.Error("Second immediate event for same file should be deduplicated")
	}

	// Third immediate event should also be deduplicated
	if !syncCore.ShouldDeduplicateEvent(testFile) {
		t.Error("Third immediate event for same file should be deduplicated")
	}

	// Wait for deduplication window to expire
	time.Sleep(600 * time.Millisecond)

	// Event after window should NOT be deduplicated
	if syncCore.ShouldDeduplicateEvent(testFile) {
		t.Error("Event after deduplication window should not be deduplicated")
	}
}

// TestEventDeduplicationDifferentFiles tests that events for different files are not deduplicated
func TestEventDeduplicationDifferentFiles(t *testing.T) {
	// Create a minimal config
	cfg := config.NewDefaultConfig()

	// Create a memory logger
	log := logger.NewMemoryLogger()

	// Create event queue
	eventQueue := make(chan types.FileSyncEvent, 10)

	// Create sync core
	syncCore := NewSyncCore(cfg, nil, nil, eventQueue)
	syncCore.log = log

	// Events for different files should not be deduplicated
	if syncCore.ShouldDeduplicateEvent("file1.txt") {
		t.Error("First event for file1.txt should not be deduplicated")
	}

	if syncCore.ShouldDeduplicateEvent("file2.txt") {
		t.Error("First event for file2.txt should not be deduplicated")
	}

	if syncCore.ShouldDeduplicateEvent("file3.txt") {
		t.Error("First event for file3.txt should not be deduplicated")
	}

	// Second events for same files should be deduplicated
	if !syncCore.ShouldDeduplicateEvent("file1.txt") {
		t.Error("Second event for file1.txt should be deduplicated")
	}

	if !syncCore.ShouldDeduplicateEvent("file2.txt") {
		t.Error("Second event for file2.txt should be deduplicated")
	}
}

// TestEventDeduplicationCleanup tests that old entries are cleaned up
func TestEventDeduplicationCleanup(t *testing.T) {
	// Create a minimal config
	cfg := config.NewDefaultConfig()

	// Create a memory logger
	log := logger.NewMemoryLogger()

	// Create event queue
	eventQueue := make(chan types.FileSyncEvent, 10)

	// Create sync core
	syncCore := NewSyncCore(cfg, nil, nil, eventQueue)
	syncCore.log = log

	// Add many files to trigger cleanup
	for i := 0; i < 100; i++ {
		filename := fmt.Sprintf("file%d.txt", i)
		syncCore.ShouldDeduplicateEvent(filename)
	}

	// Check that we have entries
	syncCore.recentEventsMu.RLock()
	initialCount := len(syncCore.recentEvents)
	syncCore.recentEventsMu.RUnlock()

	if initialCount == 0 {
		t.Error("Expected some entries in recentEvents map")
	}

	// Wait for cleanup threshold (5 seconds + buffer)
	time.Sleep(100 * time.Millisecond) // Small wait to ensure cleanup happens

	// Add one more event to trigger cleanup
	syncCore.ShouldDeduplicateEvent("trigger_cleanup.txt")

	// Check that cleanup happened (entries should still be there since we didn't wait 5 seconds)
	syncCore.recentEventsMu.RLock()
	finalCount := len(syncCore.recentEvents)
	syncCore.recentEventsMu.RUnlock()

	// Since we didn't wait 5 seconds, entries should still be there
	if finalCount == 0 {
		t.Error("Expected entries to still be present (cleanup threshold not reached)")
	}

	t.Logf("Initial count: %d, Final count: %d", initialCount, finalCount)
}

// TestEventDeduplicationConcurrency tests that deduplication is thread-safe
func TestEventDeduplicationConcurrency(t *testing.T) {
	// Create a minimal config
	cfg := config.NewDefaultConfig()

	// Create a memory logger
	log := logger.NewMemoryLogger()

	// Create event queue
	eventQueue := make(chan types.FileSyncEvent, 10)

	// Create sync core
	syncCore := NewSyncCore(cfg, nil, nil, eventQueue)
	syncCore.log = log

	// Run concurrent deduplication checks
	done := make(chan bool, 10)

	for i := 0; i < 10; i++ {
		go func(id int) {
			defer func() { done <- true }()
			filename := fmt.Sprintf("concurrent_file_%d.txt", id)

			// Each goroutine checks the same file multiple times
			for j := 0; j < 100; j++ {
				syncCore.ShouldDeduplicateEvent(filename)
				time.Sleep(1 * time.Millisecond)
			}
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < 10; i++ {
		<-done
	}

	// If we get here without panicking, the test passed
	t.Log("Concurrent deduplication test completed successfully")
}
