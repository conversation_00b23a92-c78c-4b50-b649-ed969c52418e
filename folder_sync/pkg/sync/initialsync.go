package sync

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/logger"
	"github.com/real-rm/folder_sync/pkg/types"
)

// InitialSyncHandlerImpl implements the InitialSyncHandler interface.
type InitialSyncHandlerImpl struct {
	cfg         *config.Config
	metaManager MetaManager
	fsUtil      FSUtil
	log         *logger.Logger
	syncCore    *SyncCore // Reference back to the SyncCore to queue events
}

// NewInitialSyncHandler creates a new InitialSyncHandlerImpl.
func NewInitialSyncHandler(
	cfg *config.Config,
	metaManager MetaManager,
	fsUtil FSUtil,
	log *logger.Logger,
	syncCore *SyncCore,
) InitialSyncHandler {
	return &InitialSyncHandlerImpl{
		cfg:         cfg,
		metaManager: metaManager,
		fsUtil:      fsUtil,
		log:         log,
		syncCore:    syncCore,
	}
}

// PerformInitialSync executes the initial synchronization strategy for a given peer.
func (h *InitialSyncHandlerImpl) PerformInitialSync(
	peerName string,
	pairConfig *types.PairConfig,
	pairStatus *types.PairStatus,
	conn NetworkConnection,
) error {
	h.log.Info("Performing initial sync for peer %q with strategy %q...",
		peerName, pairConfig.InitialSyncStrategy)

	if pairConfig.InitialSyncStrategy == types.NO_INITIAL_SYNC {
		h.log.Info("Initial sync explicitly skipped for peer %q.", peerName)
		return nil
	}

	// For SYNC strategy:
	// 1. Scan local sync folder
	// 2. Send events for local files/dirs to the peer.
	// 3. (Bidirectional only) Request metadata for remote files, then pull if needed.

	// Mark initial sync as in progress
	if peerHandler, ok := h.syncCore.peerHandlers[peerName]; ok {
		peerHandler.SetInitialSyncStatus(types.IN_PROGRESS)
		if err := h.metaManager.SaveImmediately(map[string]*types.PairStatus{peerName: peerHandler.GetPairStatus()}); err != nil {
			h.log.Error("Failed to save pair status for %q during initial sync: %v", peerName, err)
		}
	}

	// Clear any pending events in the queue before starting, as initial sync will re-evaluate.
	// This ensures we don't re-send old events that might be handled by the initial scan.
	if err := h.metaManager.ClearQueue(peerName); err != nil {
		h.log.Warn("Failed to clear queue for peer %q before initial sync: %v", peerName, err)
		// Continue anyway, it's not fatal
	}

	// Track progress for logging
	startTime := time.Now()
	lastLogTime := time.Now()
	totalEventsSent := 0
	totalBytesSent := uint64(0)
	totalEventsReceived := 0
	totalBytesReceived := uint64(0)
	_ = totalEventsReceived // Unused for now
	_ = totalBytesReceived  // Unused for now

	// Scan local folder and send events
	h.log.Info("Scanning local sync folder %q for initial sync with peer %q...", h.cfg.Folder.SyncPath, peerName)
	err := filepath.Walk(h.cfg.Folder.SyncPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			h.log.Warn("Error walking path %q: %v", path, err)
			return nil // Continue walking
		}

		relPath, err := filepath.Rel(h.cfg.Folder.SyncPath, path)
		if err != nil {
			h.log.Error("Failed to get relative path for %q: %v", path, err)
			return fmt.Errorf("initial sync: failed to get relative path: %w", err)
		}
		if relPath == "." { // Skip the sync folder root itself as an event
			return nil
		}

		// Check ignore list
		ignoreMatcher, err := h.cfg.NewPatternMatcher()
		if err != nil {
			h.log.Warn("Failed to create pattern matcher: %v", err)
		} else if ignoreMatcher != nil && ignoreMatcher.IsIgnored(relPath) {
			h.log.Debug("Initial sync: Ignoring path %q", relPath)
			if info.IsDir() {
				return filepath.SkipDir // Skip ignored directories
			}
			return nil
		}

		// Create a FileSyncEvent for the local file/directory
		event := types.FileSyncEvent{
			Path:      relPath,
			Timestamp: info.ModTime(),
			Origin:    types.LOCAL_INOTIFY, // Origin is local scan
			MessageID: 0,                   // Will be assigned by queueing/sending
		}

		if info.IsDir() {
			event.Type = types.CREATE_DIR
			event.Size = 0
			event.FileMode = uint32(info.Mode().Perm())
		} else if (info.Mode() & os.ModeSymlink) != 0 {
			symlinkTarget, err := h.fsUtil.ExtractSymlinkTarget(path)
			if err != nil {
				h.log.Error("Initial sync: Failed to read symlink target for %q: %v", path, err)
				return nil // Skip this symlink
			}
			// Only sync symlinks pointing within the sync folder
			// Use the proper symlink resolution logic to handle both absolute and relative symlinks
			if !h.fsUtil.IsSymlinkWithinSyncRoot(path, symlinkTarget, h.cfg.Folder.SyncPath) {
				h.log.Info("Initial sync: Ignoring symlink %q pointing outside sync folder: %q", relPath, symlinkTarget)
				return nil
			}
			event.Type = types.CREATE_SYMLINK
			event.Size = 0
			event.FileMode = uint32(info.Mode().Perm())
			event.FileBody = []byte(symlinkTarget) // Store target path in FileBody
		} else {
			event.Type = types.CREATE_FILE
			event.Size = uint64(info.Size())
			event.FileMode = uint32(info.Mode().Perm())

			// Calculate checksum if needed
			if h.cfg.Folder.Compares != types.SIZE_ONLY {
				checksum, err := h.fsUtil.CalculateXXHash(path, h.cfg.Folder.Compares)
				if err != nil {
					h.log.Error("Initial sync: Failed to calculate checksum for %q: %v", path, err)
					return nil // Skip this file
				}
				event.Checksum = checksum
			}

			// Read file body for small files during initial sync
			if event.Size <= uint64(h.cfg.Folder.BlockSize) { // Heuristic for "small" files
				fileBody, err := h.fsUtil.ReadFile(path)
				if err != nil {
					h.log.Error("Initial sync: Failed to read file body for %q: %v", path, err)
					return nil // Skip this file
				}
				event.FileBody = fileBody
				event.SendsChunks = false
			} else {
				event.SendsChunks = true // Larger files will be pulled in chunks
			}
		}

		uid, gid, err := h.fsUtil.GetFileOwnership(path)
		if err != nil {
			h.log.Warn("Initial sync: Failed to get ownership for %q: %v", path, err)
		} else {
			event.OwnerUID = uid
			event.OwnerGID = gid
		}

		// Send the event directly to the peer during initial sync
		if pairConfig.Direction == types.ONE_WAY_TO_PEER || pairConfig.Direction == types.BIDIRECTIONAL_NEWEST_WIN {
			// Assign a unique MessageID for this event
			event.MessageID = h.syncCore.generateMessageID()

			// Send the event directly via the connection (bypass queue during initial sync)
			conn := h.syncCore.peerHandlers[peerName].GetConnection()
			if conn != nil && conn.IsConnected() {
				if err := conn.SendFileSyncEvent(event); err != nil {
					h.log.Error("Initial sync: Failed to send event %s to peer %q: %v", event.String(), peerName, err)
				} else {
					totalEventsSent++
					if event.FileBody != nil {
						totalBytesSent += uint64(len(event.FileBody))
					} else if event.SendsChunks {
						totalBytesSent += event.Size // Add full size if sends chunks
					}
				}
			} else {
				h.log.Error("Initial sync: No active connection to peer %q", peerName)
			}
		}

		// Log progress every minute if initial sync takes longer than 20 seconds (per specification)
		if time.Since(startTime) >= 20*time.Second && time.Since(lastLogTime) >= time.Minute {
			h.log.Info("Initial sync progress for %q: Scanned %d local items, sent %d events. Total Bytes: %d",
				peerName, totalEventsSent, totalEventsSent, totalBytesSent)
			lastLogTime = time.Now()
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("initial sync: failed during local folder scan: %w", err)
	}
	h.log.Info("Finished scanning local folder for initial sync with peer %q. Queued %d events.", peerName, totalEventsSent)

	// If bidirectional, also request remote state (this is simplified, actual initial sync compares local vs remote)
	if pairConfig.Direction == types.BIDIRECTIONAL_NEWEST_WIN {
		h.log.Info("Requesting remote state for initial sync (BIDIRECTIONAL) with peer %q...", peerName)
		// A full initial sync for BIDIRECTIONAL_NEWEST_WIN requires:
		// 1. Local server sends its file events to remote.
		// 2. Remote server sends its file events to local.
		// 3. Both sides reconcile based on mtime/checksum.
		// For simplicity now, we assume local has sent its state.
		// The `handleIncomingMessages` in PeerHandler will process remote events as they arrive.
		// During initial sync, `handleRemoteEvent` will handle conflicts/pulls.

		// Request the remote peer's complete file list for bidirectional sync
		scanRequest := types.InitialScanRequest{
			MessageID: h.syncCore.generateMessageID(),
		}

		if err := conn.SendMessage(types.MSG_INITIAL_SCAN_REQUEST, scanRequest); err != nil {
			h.log.Error("Failed to send initial scan request to peer %q: %v", peerName, err)
			return err
		}

		h.log.Info("Sent initial scan request to peer %q for bidirectional sync", peerName)
	}

	h.log.Info("Initial sync for peer %q phase 1 complete. Total events sent: %d, Total bytes sent: %d.",
		peerName, totalEventsSent, totalBytesSent)

	// Since we're sending events directly during initial sync (not queueing them),
	// we don't need to wait for queue draining. The events have already been sent.
	// However, we should wait a bit for acknowledgments to ensure the peer received them.

	if totalEventsSent > 0 {
		h.log.Info("Initial sync for peer %q: Waiting for acknowledgments of %d events...", peerName, totalEventsSent)
		// Wait a reasonable time for acknowledgments
		waitTime := time.Duration(totalEventsSent) * 50 * time.Millisecond
		if waitTime > 5*time.Second {
			waitTime = 5 * time.Second
		}
		if waitTime < 500*time.Millisecond {
			waitTime = 500 * time.Millisecond
		}
		time.Sleep(waitTime)
		h.log.Info("Initial sync for peer %q: Acknowledgment wait period completed", peerName)
	}

	h.log.Info("Initial sync for peer %q completed. Duration: %s", peerName, time.Since(startTime))

	// Update the initial sync status to COMPLETED
	if peerHandler, ok := h.syncCore.peerHandlers[peerName]; ok {
		peerHandler.SetInitialSyncStatus(types.COMPLETED)
		if err := h.syncCore.metaManager.Save(map[string]*types.PairStatus{peerName: peerHandler.GetPairStatus()}); err != nil {
			h.log.Error("Failed to save completed initial sync status for peer %q: %v", peerName, err)
		} else {
			h.log.Info("Marked initial sync as COMPLETED for peer %q", peerName)
		}
	}

	return nil
}

// QueueDuringInitialSync is a placeholder. In a more complex initial sync,
// events detected during the initial full scan might need to be temporarily
// queued differently to avoid conflicts with the scan itself.
// For now, it just adds to the main queue, assuming the sync core handles it.
func (h *InitialSyncHandlerImpl) QueueDuringInitialSync(event types.FileSyncEvent, peerName string) {
	// For now, this is handled by `QueueOutgoingEvent` in PeerHandler, which always adds to the main queue.
	// A more sophisticated initial sync would have a dedicated temporary queue here.
	h.log.Debug("Queueing event %s during initial sync for peer %q (via main queue)", event.String(), peerName)
}
