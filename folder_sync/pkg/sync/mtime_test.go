package sync

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/fsutil"
	"github.com/real-rm/folder_sync/pkg/logger"
	"github.com/real-rm/folder_sync/pkg/meta"
	"github.com/real-rm/folder_sync/pkg/types"
)

// TestMtimePreservation tests that modification times are preserved during file synchronization
func TestMtimePreservation(t *testing.T) {
	// Create temporary directories
	tempDir := t.TempDir()
	syncPath := filepath.Join(tempDir, "sync")
	metaPath := filepath.Join(tempDir, "meta")
	logsPath := filepath.Join(tempDir, "logs")

	if err := os.MkdirAll(syncPath, 0755); err != nil {
		t.Fatalf("Failed to create sync directory: %v", err)
	}

	// Create configuration
	cfg := &config.Config{}
	cfg.Folder.SyncPath = syncPath
	cfg.Folder.MetaPath = metaPath
	cfg.Folder.LogsPath = logsPath
	cfg.Folder.Compares = types.FULL_FILE
	cfg.Folder.BlockSize = 4096
	cfg.Server.Name = "test-server"
	cfg.Debug = true

	// Create logger
	log, err := logger.NewLogger(logsPath, true)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// Create FSUtil
	fsUtil := fsutil.NewFSUtil(syncPath)

	// Create meta manager
	metaManager, err := meta.NewMetaManager(metaPath, log)
	if err != nil {
		t.Fatalf("Failed to create meta manager: %v", err)
	}
	defer metaManager.Close()

	// Create event queue channel
	eventQueue := make(chan types.FileSyncEvent, 10)

	// Create sync core
	syncCore := NewSyncCore(cfg, metaManager, fsUtil, eventQueue)

	// Create a proper peer configuration and status for testing
	peerConfig := types.PairConfig{
		Name:         "test-peer",
		HostnameOrIP: "127.0.0.1",
		RemotePort:   8080,
		Direction:    types.BIDIRECTIONAL_NEWEST_WIN,
	}

	queueDBFile := filepath.Join(metaPath, "test-peer.queue.db")
	status := types.NewPairStatus("test-peer", queueDBFile)

	// Create a proper peer handler for testing
	peerHandler := NewPeerHandler(peerConfig, status, syncCore, log)
	syncCore.peerHandlers = map[string]*PeerHandler{
		"test-peer": peerHandler,
	}

	// Close the initialSyncDone channel to indicate initial sync is complete
	close(peerHandler.initialSyncDone)

	// Create ongoing sync handler
	handler := NewOngoingSyncHandler(cfg, metaManager, fsUtil, log, syncCore)

	// Test cases for different event types
	testCases := []struct {
		name      string
		eventType types.FileSyncEventType
		setupFile func(string) error
		checkFile func(string, time.Time) error
	}{
		{
			name:      "CREATE_FILE preserves mtime",
			eventType: types.CREATE_FILE,
			setupFile: func(path string) error {
				// File doesn't exist yet for CREATE_FILE
				return nil
			},
			checkFile: func(path string, expectedTime time.Time) error {
				info, err := os.Stat(path)
				if err != nil {
					return err
				}
				actualTime := info.ModTime().Truncate(time.Second)
				expectedTime = expectedTime.Truncate(time.Second)
				if !actualTime.Equal(expectedTime) {
					t.Errorf("File mtime mismatch: expected %s, got %s", expectedTime.Format(time.RFC3339), actualTime.Format(time.RFC3339))
				}
				return nil
			},
		},
		{
			name:      "WRITE_FILE preserves mtime",
			eventType: types.WRITE_FILE,
			setupFile: func(path string) error {
				// Create existing file with different content and older timestamp
				if err := os.WriteFile(path, []byte("old content"), 0644); err != nil {
					return err
				}
				// Set the local file to be older than the remote event (2 hours ago)
				oldTime := time.Now().Add(-2 * time.Hour)
				return os.Chtimes(path, oldTime, oldTime)
			},
			checkFile: func(path string, expectedTime time.Time) error {
				info, err := os.Stat(path)
				if err != nil {
					return err
				}
				actualTime := info.ModTime().Truncate(time.Second)
				expectedTime = expectedTime.Truncate(time.Second)
				if !actualTime.Equal(expectedTime) {
					t.Errorf("File mtime mismatch: expected %s, got %s", expectedTime.Format(time.RFC3339), actualTime.Format(time.RFC3339))
				}
				return nil
			},
		},
		{
			name:      "CREATE_DIR preserves mtime",
			eventType: types.CREATE_DIR,
			setupFile: func(path string) error {
				// Directory doesn't exist yet for CREATE_DIR
				return nil
			},
			checkFile: func(path string, expectedTime time.Time) error {
				info, err := os.Stat(path)
				if err != nil {
					return err
				}
				actualTime := info.ModTime().Truncate(time.Second)
				expectedTime = expectedTime.Truncate(time.Second)
				if !actualTime.Equal(expectedTime) {
					t.Errorf("Directory mtime mismatch: expected %s, got %s", expectedTime.Format(time.RFC3339), actualTime.Format(time.RFC3339))
				}
				return nil
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create test file/directory path
			testPath := "test_mtime_" + tc.name
			fullPath := filepath.Join(syncPath, testPath)

			// Setup existing file if needed
			if err := tc.setupFile(fullPath); err != nil {
				t.Fatalf("Failed to setup test file: %v", err)
			}

			// Create a specific timestamp (1 hour ago)
			originalTime := time.Now().Add(-1 * time.Hour).Truncate(time.Second)

			// Create file sync event with specific timestamp
			event := types.FileSyncEvent{
				Type:      tc.eventType,
				Path:      testPath,
				Timestamp: originalTime,
				Size:      uint64(len("test content")),
				Origin:    types.REMOTE_PEER,
				MessageID: 1,
				FileMode:  0644,
				OwnerUID:  0,
				OwnerGID:  0,
			}

			// Add file body for file events
			if tc.eventType == types.CREATE_FILE || tc.eventType == types.WRITE_FILE {
				event.FileBody = []byte("test content")
			}

			// Handle the remote event
			err := handler.HandleRemoteEvent("test-peer", event)
			if err != nil {
				t.Fatalf("Failed to handle remote event: %v", err)
			}

			// Check that the mtime was preserved
			if err := tc.checkFile(fullPath, originalTime); err != nil {
				t.Fatalf("Failed to check file mtime: %v", err)
			}

			// Clean up
			os.RemoveAll(fullPath)
		})
	}
}

// TestMtimeZeroTimestamp tests that zero timestamps are handled correctly (no mtime change)
func TestMtimeZeroTimestamp(t *testing.T) {
	// Create temporary directories
	tempDir := t.TempDir()
	syncPath := filepath.Join(tempDir, "sync")
	metaPath := filepath.Join(tempDir, "meta")
	logsPath := filepath.Join(tempDir, "logs")

	if err := os.MkdirAll(syncPath, 0755); err != nil {
		t.Fatalf("Failed to create sync directory: %v", err)
	}

	// Create configuration
	cfg := &config.Config{}
	cfg.Folder.SyncPath = syncPath
	cfg.Folder.MetaPath = metaPath
	cfg.Folder.LogsPath = logsPath
	cfg.Folder.Compares = types.FULL_FILE
	cfg.Folder.BlockSize = 4096
	cfg.Server.Name = "test-server"
	cfg.Debug = true

	// Create logger
	log, err := logger.NewLogger(logsPath, true)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// Create FSUtil
	fsUtil := fsutil.NewFSUtil(syncPath)

	// Create meta manager
	metaManager, err := meta.NewMetaManager(metaPath, log)
	if err != nil {
		t.Fatalf("Failed to create meta manager: %v", err)
	}
	defer metaManager.Close()

	// Create event queue channel
	eventQueue := make(chan types.FileSyncEvent, 10)

	// Create sync core
	syncCore := NewSyncCore(cfg, metaManager, fsUtil, eventQueue)

	// Create a proper peer configuration and status for testing
	peerConfig := types.PairConfig{
		Name:         "test-peer",
		HostnameOrIP: "127.0.0.1",
		RemotePort:   8080,
		Direction:    types.BIDIRECTIONAL_NEWEST_WIN,
	}

	queueDBFile := filepath.Join(metaPath, "test-peer.queue.db")
	status := types.NewPairStatus("test-peer", queueDBFile)

	// Create a proper peer handler for testing
	peerHandler := NewPeerHandler(peerConfig, status, syncCore, log)
	syncCore.peerHandlers = map[string]*PeerHandler{
		"test-peer": peerHandler,
	}

	// Close the initialSyncDone channel to indicate initial sync is complete
	close(peerHandler.initialSyncDone)

	// Create ongoing sync handler
	handler := NewOngoingSyncHandler(cfg, metaManager, fsUtil, log, syncCore)

	// Test file path
	testPath := "zero_timestamp_test.txt"
	fullPath := filepath.Join(syncPath, testPath)

	// Create file sync event with zero timestamp
	event := types.FileSyncEvent{
		Type:      types.CREATE_FILE,
		Path:      testPath,
		Timestamp: time.Time{}, // Zero timestamp
		Size:      uint64(len("test content")),
		Origin:    types.REMOTE_PEER,
		MessageID: 1,
		FileMode:  0644,
		OwnerUID:  0,
		OwnerGID:  0,
		FileBody:  []byte("test content"),
	}

	// Record time before handling event
	beforeTime := time.Now()

	// Handle the remote event
	err = handler.HandleRemoteEvent("test-peer", event)
	if err != nil {
		t.Fatalf("Failed to handle remote event: %v", err)
	}

	// Record time after handling event
	afterTime := time.Now()

	// Check that file was created
	info, err := os.Stat(fullPath)
	if err != nil {
		t.Fatalf("Failed to stat file: %v", err)
	}

	// Check that mtime is current time (not zero) since zero timestamp should be ignored
	actualTime := info.ModTime()
	if actualTime.Before(beforeTime) || actualTime.After(afterTime) {
		t.Errorf("Expected mtime to be current time (between %s and %s), got %s",
			beforeTime.Format(time.RFC3339), afterTime.Format(time.RFC3339), actualTime.Format(time.RFC3339))
	}
}
