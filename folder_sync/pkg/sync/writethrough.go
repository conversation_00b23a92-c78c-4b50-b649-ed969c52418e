package sync

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"

	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/logger"
	"github.com/real-rm/folder_sync/pkg/types"
)

// WritethroughHandlerImpl implements the WritethroughHandler interface.
type WritethroughHandlerImpl struct {
	cfg             *config.Config
	fsUtil          FSUtil
	log             *logger.Logger
	pendingEvents   map[uint64]*pendingEvent // Shared map from SyncCore
	pendingEventsMu *sync.RWMutex            // Shared mutex for pendingEvents
}

// NewWritethroughHandler creates a new WritethroughHandlerImpl.
func NewWritethroughHandler(
	cfg *config.Config,
	fsUtil FSUtil,
	log *logger.Logger,
	pendingEvents map[uint64]*pendingEvent,
	pendingEventsMu *sync.RWMutex,
) WritethroughHandler {
	return &WritethroughHandlerImpl{
		cfg:             cfg,
		fsUtil:          fsUtil,
		log:             log,
		pendingEvents:   pendingEvents,
		pendingEventsMu: pendingEventsMu,
	}
}

// ProcessProxyEvent handles a FileSyncEvent originating from the proxy socket.
// In writethrough mode, this means applying the change locally as a buffer,
// and preparing to track its propagation to all peers.
func (h *WritethroughHandlerImpl) ProcessProxyEvent(event types.FileSyncEvent) error {
	h.log.Debug("Processing proxy event in writethrough mode: %s", event.String())

	// Sanity check: ensure event is from PROXY_CLIENT_WRITE
	if event.Origin != types.PROXY_CLIENT_WRITE {
		return fmt.Errorf("writethrough mode received non-proxy event origin: %s", event.Origin.String())
	}

	fullPath := filepath.Join(h.cfg.Folder.SyncPath, event.Path)

	// Apply the change locally (create buffer file/directory)
	switch event.Type {
	case types.CREATE_DIR:
		if err := h.fsUtil.CreateDir(fullPath, os.FileMode(event.FileMode)); err != nil && !os.IsExist(err) {
			h.log.Error("Writethrough: Failed to create buffer directory %q: %v", fullPath, err)
			return err
		}
		// Apply mtime to preserve original modification time
		if !event.Timestamp.IsZero() {
			if err := h.fsUtil.Chtimes(fullPath, event.Timestamp, event.Timestamp); err != nil {
				h.log.Warn("Writethrough: Failed to set mtime for buffer directory %q: %v", fullPath, err)
			}
		}
		h.log.Info("Writethrough: Created buffer directory: %q", event.Path)
	case types.CREATE_FILE, types.WRITE_FILE:
		// Ensure parent directories exist
		parentDir := filepath.Dir(fullPath)
		if err := h.fsUtil.CreateDir(parentDir, 0755); err != nil && !os.IsExist(err) {
			h.log.Error("Writethrough: Failed to create parent directories for %q: %v", fullPath, err)
			return err
		}
		if err := h.fsUtil.AtomicWrite(fullPath, event.FileBody, os.FileMode(event.FileMode)); err != nil {
			h.log.Error("Writethrough: Failed to write buffer file %q: %v", fullPath, err)
			return err
		}
		// Apply ownership (optional for buffer files, but good for consistency)
		if err := h.fsUtil.Chown(fullPath, event.OwnerUID, event.OwnerGID); err != nil {
			h.log.Warn("Writethrough: Failed to chown buffer file %q to %d:%d: %v", fullPath, event.OwnerUID, event.OwnerGID, err)
		}
		// Apply mtime to preserve original modification time
		if !event.Timestamp.IsZero() {
			if err := h.fsUtil.Chtimes(fullPath, event.Timestamp, event.Timestamp); err != nil {
				h.log.Warn("Writethrough: Failed to set mtime for buffer file %q: %v", fullPath, err)
			}
		}
		h.log.Info("Writethrough: Created/updated buffer file: %q", event.Path)
	case types.DELETE_FILE, types.DELETE_DIR:
		// For deletes, we should still record it as pending deletion for peers,
		// but the local deletion typically happens when the event is received.
		// So we'll remove it from the local buffer here immediately.
		if err := h.fsUtil.Remove(fullPath); err != nil && !os.IsNotExist(err) {
			h.log.Warn("Writethrough: Failed to remove buffer item %q during proxy delete event: %v", fullPath, err)
			// Continue, as the primary goal is to propagate the delete
		}
		h.log.Info("Writethrough: Initiated deletion of buffer item: %q", event.Path)
	case types.RENAME_FILE, types.RENAME_DIR:
		// For renames, the proxy event provides the *new* path.
		// The old path should have been deleted or moved by the client side.
		// So we apply the new path locally.
		if event.FileBody != nil { // RENAME_FILE
			// Ensure parent directories exist
			parentDir := filepath.Dir(fullPath)
			if err := h.fsUtil.CreateDir(parentDir, 0755); err != nil && !os.IsExist(err) {
				h.log.Error("Writethrough: Failed to create parent dirs for renamed file %q: %v", fullPath, err)
				return err
			}
			if err := h.fsUtil.AtomicWrite(fullPath, event.FileBody, os.FileMode(event.FileMode)); err != nil {
				h.log.Error("Writethrough: Failed to write renamed buffer file %q: %v", fullPath, err)
				return err
			}
			if err := h.fsUtil.Chown(fullPath, event.OwnerUID, event.OwnerGID); err != nil {
				h.log.Warn("Writethrough: Failed to chown renamed buffer file %q: %v", fullPath, err)
			}
			// Apply mtime to preserve original modification time for renamed file
			if !event.Timestamp.IsZero() {
				if err := h.fsUtil.Chtimes(fullPath, event.Timestamp, event.Timestamp); err != nil {
					h.log.Warn("Writethrough: Failed to set mtime for renamed buffer file %q: %v", fullPath, err)
				}
			}
		} else { // RENAME_DIR
			if err := h.fsUtil.CreateDir(fullPath, os.FileMode(event.FileMode)); err != nil && !os.IsExist(err) {
				h.log.Error("Writethrough: Failed to create renamed buffer directory %q: %v", fullPath, err)
				return err
			}
			// Apply mtime to preserve original modification time for renamed directory
			if !event.Timestamp.IsZero() {
				if err := h.fsUtil.Chtimes(fullPath, event.Timestamp, event.Timestamp); err != nil {
					h.log.Warn("Writethrough: Failed to set mtime for renamed buffer directory %q: %v", fullPath, err)
				}
			}
		}
		h.log.Info("Writethrough: Applied renamed buffer item: %q", event.Path)
	case types.CHMOD_FILE:
		if err := h.fsUtil.Chmod(fullPath, os.FileMode(event.FileMode)); err != nil {
			h.log.Error("Writethrough: Failed to chmod buffer file %q: %v", fullPath, err)
			return err
		}
		if err := h.fsUtil.Chown(fullPath, event.OwnerUID, event.OwnerGID); err != nil {
			h.log.Warn("Writethrough: Failed to chown buffer file %q: %v", fullPath, err)
		}
		// Apply mtime if provided (for CHMOD events that also want to preserve timestamps)
		if !event.Timestamp.IsZero() {
			if err := h.fsUtil.Chtimes(fullPath, event.Timestamp, event.Timestamp); err != nil {
				h.log.Warn("Writethrough: Failed to set mtime for buffer file %q during chmod: %v", fullPath, err)
			}
		}
		h.log.Info("Writethrough: Applied chmod to buffer file: %q", event.Path)
	case types.CREATE_SYMLINK:
		targetPath := string(event.FileBody)
		// Symlink creation via fsutil would be needed here. For now, using os.Symlink
		if err := os.Symlink(targetPath, fullPath); err != nil && !os.IsExist(err) { // If it's not already there
			h.log.Error("Writethrough: Failed to create symlink %q -> %q: %v", fullPath, targetPath, err)
			return err
		}
		h.log.Info("Writethrough: Created symlink buffer: %q -> %q", event.Path, targetPath)

	default:
		h.log.Warn("Writethrough: Unhandled proxy event type: %s for %q", event.Type.String(), event.Path)
		return fmt.Errorf("unhandled proxy event type in writethrough: %s", event.Type.String())
	}

	// Record the event as pending acknowledgment from all active peers
	h.pendingEventsMu.Lock()
	defer h.pendingEventsMu.Unlock()

	ackMap := make(map[string]bool)
	for _, pairCfg := range h.cfg.Pairs {
		// Only track peers that this server sends *to*
		if pairCfg.Direction == types.ONE_WAY_TO_PEER || pairCfg.Direction == types.BIDIRECTIONAL_NEWEST_WIN {
			ackMap[pairCfg.Name] = false // Not yet acknowledged by this peer
		}
	}
	h.pendingEvents[event.MessageID] = &pendingEvent{
		Event: event,
		Acks:  ackMap,
	}
	h.log.Debug("Writethrough: Event %d added to pending list, awaiting %d acknowledgments.", event.MessageID, len(ackMap))

	return nil
}

// RecordPeerAcknowledgement records an acknowledgment from a specific peer for a given message ID.
// If all peers acknowledge, the local buffer file/directory is removed.
func (h *WritethroughHandlerImpl) RecordPeerAcknowledgement(messageID uint64, peerName string) {
	h.pendingEventsMu.Lock()
	defer h.pendingEventsMu.Unlock()

	pe, ok := h.pendingEvents[messageID]
	if !ok {
		h.log.Warn("Writethrough: Received ACK for unknown or already processed message ID %d from peer %q. Skipping.", messageID, peerName)
		return
	}

	if _, exists := pe.Acks[peerName]; !exists {
		h.log.Warn("Writethrough: Peer %q is not configured to receive events for message ID %d. Skipping ACK.", peerName, messageID)
		return
	}

	pe.Acks[peerName] = true // Mark as acknowledged by this peer
	h.log.Debug("Writethrough: Peer %q acknowledged MessageID %d for path %q.", peerName, messageID, pe.Event.Path)

	// Check if all configured peers have acknowledged
	allAcknowledged := true
	for _, acked := range pe.Acks {
		if !acked {
			allAcknowledged = false
			break
		}
	}

	if allAcknowledged {
		h.log.Info("Writethrough: All peers acknowledged MessageID %d for path %q. Removing local buffer item.", messageID, pe.Event.Path)
		fullPath := filepath.Join(h.cfg.Folder.SyncPath, pe.Event.Path)
		if err := h.fsUtil.Remove(fullPath); err != nil && !os.IsNotExist(err) {
			h.log.Error("Writethrough: Failed to remove local buffer item %q after all acknowledgments: %v", fullPath, err)
			// Don't return error, still remove from map, but log issue
		} else if os.IsNotExist(err) {
			h.log.Warn("Writethrough: Buffer item %q already removed for MessageID %d. No action needed.", fullPath, messageID)
		} else {
			h.log.Info("Writethrough: Successfully removed local buffer item %q for MessageID %d.", fullPath, messageID)
		}
		delete(h.pendingEvents, messageID) // Remove from tracking map
	} else {
		h.log.Debug("Writethrough: MessageID %d for path %q still awaiting acknowledgments from peers: %v", messageID, pe.Event.Path, pe.Acks)
	}
}
