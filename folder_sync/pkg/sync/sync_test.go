package sync

import (
	"bytes"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"syscall"
	"testing"
	"time"

	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/logger"
	"github.com/real-rm/folder_sync/pkg/types"
)

// --- Mocks for Dependencies ---

// MockMetaManager implements meta.Manager for testing.
type MockMetaManager struct {
	mu           sync.Mutex
	pairStatuses map[string]*types.PairStatus
	queues       map[string]*MockQueue // Map peer name to its mock queue
	loaded       bool
}

// NewMockMetaManager creates a new MockMetaManager.
func NewMockMetaManager() *MockMetaManager {
	return &MockMetaManager{
		pairStatuses: make(map[string]*types.PairStatus),
		queues:       make(map[string]*MockQueue),
	}
}

// Load implements meta.Manager.Load.
func (m *MockMetaManager) Load() (map[string]*types.PairStatus, error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.loaded = true
	// Return a deep copy to prevent external modification
	copiedStatuses := make(map[string]*types.PairStatus)
	for name, status := range m.pairStatuses {
		copyStatus := types.PairStatus{
			Name:                status.Name,
			LastSyncedTimestamp: status.LastSyncedTimestamp,
			InitialSyncStatus:   status.InitialSyncStatus,
			ConnectionRetries:   status.ConnectionRetries,
			TotalSent:           status.TotalSent.Copy(),     // Use thread-safe copy
			TotalReceived:       status.TotalReceived.Copy(), // Use thread-safe copy
		}
		copiedStatuses[name] = &copyStatus
	}
	return copiedStatuses, nil
}

// Save implements meta.Manager.Save.
func (m *MockMetaManager) Save(statuses map[string]*types.PairStatus) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	// Deep copy to simulate persistence
	for name, status := range statuses {
		copyStatus := types.PairStatus{
			Name:                status.Name,
			LastSyncedTimestamp: status.LastSyncedTimestamp,
			InitialSyncStatus:   status.InitialSyncStatus,
			ConnectionRetries:   status.ConnectionRetries,
			TotalSent:           status.TotalSent.Copy(),     // Use thread-safe copy
			TotalReceived:       status.TotalReceived.Copy(), // Use thread-safe copy
		}
		m.pairStatuses[name] = &copyStatus
	}
	return nil
}

// SaveImmediately implements meta.Manager.SaveImmediately.
func (m *MockMetaManager) SaveImmediately(statuses map[string]*types.PairStatus) error {
	// For mock, SaveImmediately is the same as Save
	return m.Save(statuses)
}

// GetQueue implements meta.Manager.GetQueue.
func (m *MockMetaManager) GetQueue(peerName string) (interface{}, error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	if q, ok := m.queues[peerName]; ok {
		return q, nil
	}
	newQueue := NewMockQueue()
	m.queues[peerName] = newQueue
	return newQueue, nil
}

// GetPairStatus gets the pair status for a peer (test helper method).
func (m *MockMetaManager) GetPairStatus(peerName string) (*types.PairStatus, error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	if status, ok := m.pairStatuses[peerName]; ok {
		// Return a copy to prevent external modification
		copyStatus := *status
		copyStatus.TotalSent = status.TotalSent
		copyStatus.TotalReceived = status.TotalReceived
		return &copyStatus, nil
	}
	// Return a default status if not found
	return &types.PairStatus{
		Name:              peerName,
		TotalSent:         types.SyncOpCounts{},
		TotalReceived:     types.SyncOpCounts{},
		InitialSyncStatus: types.COMPLETED,
	}, nil
}

// Close implements meta.Manager.Close.
func (m *MockMetaManager) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()
	for _, q := range m.queues {
		q.Close()
	}
	return nil
}

// ClearQueue implements meta.Manager.ClearQueue.
func (m *MockMetaManager) ClearQueue(peerName string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	if q, ok := m.queues[peerName]; ok {
		q.Clear()
	}
	return nil
}

// MockQueue implements the MetaQueue interface for testing.
type MockQueue struct {
	mu       sync.Mutex
	events   []types.FileSyncEvent
	closed   bool
	autoAck  bool // If true, automatically acknowledge events after a delay
	ackDelay time.Duration
}

// NewMockQueue creates a new MockQueue.
func NewMockQueue() *MockQueue {
	return &MockQueue{
		events:   []types.FileSyncEvent{},
		autoAck:  true,                  // Enable auto-acknowledgment for testing
		ackDelay: 50 * time.Millisecond, // Short delay to simulate network round-trip
	}
}

// DisableAutoAck disables automatic acknowledgment for this queue.
func (q *MockQueue) DisableAutoAck() {
	q.mu.Lock()
	defer q.mu.Unlock()
	q.autoAck = false
}

// AddEvent implements MetaQueue.AddEvent.
func (q *MockQueue) AddEvent(event types.FileSyncEvent) error {
	q.mu.Lock()
	defer q.mu.Unlock()
	if q.closed {
		return fmt.Errorf("queue is closed")
	}
	q.events = append(q.events, event)

	// Auto-acknowledge the event after a delay to simulate peer acknowledgment
	if q.autoAck {
		go func(messageID uint64) {
			time.Sleep(q.ackDelay)
			q.RemoveEvent(messageID)
		}(event.MessageID)
	}

	return nil
}

// GetNextEvent implements MetaQueue.GetNextEvent.
func (q *MockQueue) GetNextEvent() (*types.FileSyncEvent, error) {
	q.mu.Lock()
	defer q.mu.Unlock()
	if q.closed {
		return nil, fmt.Errorf("queue is closed")
	}
	if len(q.events) == 0 {
		return nil, nil // No events
	}
	event := q.events[0]
	return &event, nil
}

// RemoveEvent implements MetaQueue.RemoveEvent.
func (q *MockQueue) RemoveEvent(messageID uint64) error {
	q.mu.Lock()
	defer q.mu.Unlock()
	if q.closed {
		return fmt.Errorf("queue is closed")
	}
	for i, event := range q.events {
		if event.MessageID == messageID {
			q.events = append(q.events[:i], q.events[i+1:]...)
			return nil
		}
	}
	return fmt.Errorf("event with MessageID %d not found", messageID)
}

// GetEventByMessageID implements MetaQueue.GetEventByMessageID.
func (q *MockQueue) GetEventByMessageID(messageID uint64) (*types.FileSyncEvent, error) {
	q.mu.Lock()
	defer q.mu.Unlock()
	if q.closed {
		return nil, fmt.Errorf("queue is closed")
	}
	for _, event := range q.events {
		if event.MessageID == messageID {
			return &event, nil
		}
	}
	return nil, fmt.Errorf("event with MessageID %d not found", messageID)
}

// GetEvents implements MetaQueue.GetEvents.
func (q *MockQueue) GetEvents() ([]types.FileSyncEvent, error) {
	q.mu.Lock()
	defer q.mu.Unlock()
	if q.closed {
		return nil, fmt.Errorf("queue is closed")
	}
	// Return a copy to prevent external modification
	copiedEvents := make([]types.FileSyncEvent, len(q.events))
	copy(copiedEvents, q.events)
	return copiedEvents, nil
}

// Count implements MetaQueue.Count.
func (q *MockQueue) Count() (int, error) {
	q.mu.Lock()
	defer q.mu.Unlock()
	if q.closed {
		return 0, fmt.Errorf("queue is closed")
	}
	return len(q.events), nil
}

// Close implements MetaQueue.Close.
func (q *MockQueue) Close() error {
	q.mu.Lock()
	defer q.mu.Unlock()
	q.closed = true
	return nil
}

// Clear implements MetaQueue.Clear.
func (q *MockQueue) Clear() error {
	q.mu.Lock()
	defer q.mu.Unlock()
	q.events = []types.FileSyncEvent{}
	return nil
}

// MockNetworkConnection implements network.Connection for testing.
type MockNetworkConnection struct {
	mu                sync.Mutex
	peerName          string
	sentEvents        []types.FileSyncEvent
	sentMessages      []MockSentMessage // Track messages sent via SendMessage
	receivedResponses []interface{}     // Can be ACK, PULL, etc.
	isConnected       bool
	sendEventFunc     func(event types.FileSyncEvent) error
	sendResponseFunc  func(msgType types.MessageType, payload interface{}) error
	pullFileFunc      func(request types.FileSyncEvent) ([]byte, error)
	closeChan         chan struct{} // Channel to signal closure from test
	closed            bool
}

// MockSentMessage tracks messages sent via SendMessage
type MockSentMessage struct {
	MessageType types.MessageType
	Payload     interface{}
}

// NewMockNetworkConnection creates a new MockNetworkConnection.
func NewMockNetworkConnection(peerName string) *MockNetworkConnection {
	return &MockNetworkConnection{
		peerName:    peerName,
		isConnected: true,
		closeChan:   make(chan struct{}),
	}
}

// SetupAutoACK configures the mock connection to automatically send ACKs when events are sent.
func (m *MockNetworkConnection) SetupAutoACK(core *SyncCore, peerName string) {
	m.SetupAutoACKWithDelay(core, peerName, 10*time.Millisecond)
}

// SetupAutoACKWithDelay configures the mock connection to automatically send ACKs with a custom delay.
func (m *MockNetworkConnection) SetupAutoACKWithDelay(core *SyncCore, peerName string, delay time.Duration) {
	m.SetSendEventFunc(func(sentEvent types.FileSyncEvent) error {
		// Automatically send ACK back to the ongoing sync handler
		ack := types.SyncAcknowledge{
			MessageID: sentEvent.MessageID,
			Path:      sentEvent.Path,
			EventType: sentEvent.Type,
			Success:   true,
		}

		// Send the ACK back to the ongoing sync handler
		go func() {
			time.Sleep(delay) // Configurable delay to simulate network
			if core.ongoingSyncHandler != nil {
				core.ongoingSyncHandler.HandlePeerResponse(peerName, types.MSG_SYNC_ACKNOWLEDGE, ack)
			}
		}()
		return nil
	})
}

// SimulateIncomingEvent simulates receiving an event from a remote peer.
func (m *MockNetworkConnection) SimulateIncomingEvent(event types.FileSyncEvent) {
	m.mu.Lock()
	m.receivedResponses = append(m.receivedResponses, event)
	m.mu.Unlock()
}

// SendIncomingEventToCore sends a simulated incoming event directly to the sync core
func (m *MockNetworkConnection) SendIncomingEventToCore(core *SyncCore, event types.FileSyncEvent) {
	if core.ongoingSyncHandler != nil {
		go func() {
			time.Sleep(10 * time.Millisecond) // Small delay to simulate network
			core.ongoingSyncHandler.HandlePeerResponse(m.peerName, types.MSG_FILE_SYNC_EVENT, event)
		}()
	}
}

// PeerName implements network.Connection.PeerName.
func (m *MockNetworkConnection) PeerName() string {
	return m.peerName
}

// IsConnected implements network.Connection.IsConnected.
func (m *MockNetworkConnection) IsConnected() bool {
	m.mu.Lock()
	defer m.mu.Unlock()
	return m.isConnected && !m.closed
}

// SendFileSyncEvent implements network.Connection.SendFileSyncEvent.
func (m *MockNetworkConnection) SendFileSyncEvent(event types.FileSyncEvent) error {
	m.mu.Lock()
	if m.closed {
		m.mu.Unlock()
		return fmt.Errorf("connection closed")
	}
	m.sentEvents = append(m.sentEvents, event)
	eventFunc := m.sendEventFunc
	m.mu.Unlock()

	if eventFunc != nil {
		return eventFunc(event)
	}
	return nil
}

// SendMessage implements network.Connection.SendMessage.
func (m *MockNetworkConnection) SendMessage(msgType types.MessageType, payload interface{}) error {
	m.mu.Lock()
	if m.closed {
		m.mu.Unlock()
		return fmt.Errorf("connection closed")
	}
	// Track the sent message
	m.sentMessages = append(m.sentMessages, MockSentMessage{
		MessageType: msgType,
		Payload:     payload,
	})
	responseFunc := m.sendResponseFunc
	m.mu.Unlock()

	if responseFunc != nil {
		return responseFunc(msgType, payload)
	}
	return nil
}

// PullFile implements network.Connection.PullFile.
func (m *MockNetworkConnection) PullFile(request types.FileSyncEvent) ([]byte, error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	if m.closed {
		return nil, fmt.Errorf("connection closed")
	}
	if m.pullFileFunc != nil {
		return m.pullFileFunc(request)
	}
	return []byte("mock file content for " + request.Path), nil
}

// ReadResponse simulates reading a response from the network.
func (m *MockNetworkConnection) ReadResponse() (types.MessageType, interface{}, error) {
	// Use a timeout to prevent hanging in tests
	timeout := time.NewTimer(100 * time.Millisecond)
	defer timeout.Stop()

	for {
		select {
		case <-timeout.C:
			return 0, nil, fmt.Errorf("timeout waiting for response")
		case <-m.closeChan:
			return 0, nil, fmt.Errorf("connection closed")
		default:
			m.mu.Lock()
			if len(m.receivedResponses) == 0 {
				m.mu.Unlock()
				time.Sleep(10 * time.Millisecond) // Small delay before checking again
				continue
			}
			resp := m.receivedResponses[0]
			m.receivedResponses = m.receivedResponses[1:]
			m.mu.Unlock()

			// Determine MessageType based on the type of response
			switch resp.(type) {
			case types.FileSyncEvent:
				return types.MSG_FILE_SYNC_EVENT, resp, nil
			case types.SyncAcknowledge:
				return types.MSG_SYNC_ACKNOWLEDGE, resp, nil
			case types.PullFileRequest:
				return types.MSG_PULL_FILE_REQUEST, resp, nil // This shouldn't happen, daemon doesn't pull
			case types.FileRemoved:
				return types.MSG_FILE_REMOVED, resp, nil
			case types.SameContent:
				return types.MSG_SAME_CONTENT, resp, nil
			case types.PullFileResponse:
				return types.MSG_PULL_FILE_RESPONSE, resp, nil
			default:
				return types.MSG_ERROR, nil, fmt.Errorf("unknown response type for ReadResponse: %T", resp)
			}
		}
	}
}

// Close simulates closing the connection.
func (m *MockNetworkConnection) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()
	if m.closed {
		return nil
	}
	m.isConnected = false
	m.closed = true
	// Close the channel to unblock any goroutines waiting on it
	close(m.closeChan)
	return nil
}

// SetSendResponseFunc safely sets the sendResponseFunc with proper synchronization.
func (m *MockNetworkConnection) SetSendResponseFunc(fn func(msgType types.MessageType, payload interface{}) error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.sendResponseFunc = fn
}

// SetSendEventFunc safely sets the sendEventFunc with proper synchronization.
func (m *MockNetworkConnection) SetSendEventFunc(fn func(event types.FileSyncEvent) error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.sendEventFunc = fn
}

// MockFSUtil implements fsutil.FSUtil for testing.
type MockFSUtil struct {
	mu                       sync.Mutex
	fileContents             map[string][]byte
	fileStats                map[string]os.FileInfo
	atomicWriteFunc          func(filePath string, data []byte, perm os.FileMode) error
	calculateXXHashFunc      func(filePath string, strategy types.CompareStrategy) ([]byte, error)
	getFileStatFunc          func(path string) (os.FileInfo, error)
	getFileOwnershipFunc     func(path string) (uint32, uint32, error)
	extractSymlinkTargetFunc func(path string) (string, error)
	createDirFunc            func(path string, perm os.FileMode) error
	removeFunc               func(path string) error
	chmodFunc                func(path string, perm os.FileMode) error
	chownFunc                func(path string, uid, gid uint32) error
	chtimesFunc              func(path string, atime, mtime time.Time) error
	readFileFunc             func(path string) ([]byte, error)
	renameFunc               func(oldPath, newPath string) error
}

// NewMockFSUtil creates a new MockFSUtil.
func NewMockFSUtil() *MockFSUtil {
	return &MockFSUtil{
		fileContents: make(map[string][]byte),
		fileStats:    make(map[string]os.FileInfo),
	}
}

// SetFileContent sets mock content for a file.
func (m *MockFSUtil) SetFileContent(path string, content []byte, info os.FileInfo) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.fileContents[path] = content
	m.fileStats[path] = info
}

// DeleteFile simulates file deletion.
func (m *MockFSUtil) DeleteFile(path string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	delete(m.fileContents, path)
	delete(m.fileStats, path)
}

// AtomicWrite implements fsutil.FSUtil.AtomicWrite.
func (m *MockFSUtil) AtomicWrite(filePath string, data []byte, perm os.FileMode) error {
	if m.atomicWriteFunc != nil {
		return m.atomicWriteFunc(filePath, data, perm)
	}
	m.SetFileContent(filePath, data, &mockFileInfo{
		name:    filepath.Base(filePath),
		size:    int64(len(data)),
		mode:    perm,
		modTime: time.Now(),
		isDir:   false,
	})
	return nil
}

// CalculateXXHash implements fsutil.FSUtil.CalculateXXHash.
func (m *MockFSUtil) CalculateXXHash(filePath string, strategy types.CompareStrategy) ([]byte, error) {
	if m.calculateXXHashFunc != nil {
		return m.calculateXXHashFunc(filePath, strategy)
	}
	m.mu.Lock()
	defer m.mu.Unlock()
	if content, ok := m.fileContents[filePath]; ok {
		return []byte(fmt.Sprintf("mock-hash-%s-%x", filePath, content)), nil
	}
	return nil, os.ErrNotExist
}

// GetFileStat implements fsutil.FSUtil.GetFileStat.
func (m *MockFSUtil) GetFileStat(path string) (os.FileInfo, error) {
	if m.getFileStatFunc != nil {
		return m.getFileStatFunc(path)
	}
	m.mu.Lock()
	defer m.mu.Unlock()
	if info, ok := m.fileStats[path]; ok {
		return info, nil
	}
	return nil, os.ErrNotExist
}

// GetFileOwnership implements fsutil.FSUtil.GetFileOwnership.
func (m *MockFSUtil) GetFileOwnership(path string) (uint32, uint32, error) {
	if m.getFileOwnershipFunc != nil {
		return m.getFileOwnershipFunc(path)
	}
	return 1000, 1000, nil // Default mock UID/GID
}

// ExtractSymlinkTarget implements fsutil.FSUtil.ExtractSymlinkTarget.
func (m *MockFSUtil) ExtractSymlinkTarget(path string) (string, error) {
	if m.extractSymlinkTargetFunc != nil {
		return m.extractSymlinkTargetFunc(path)
	}
	return "", fmt.Errorf("not implemented for mock")
}

// IsSymlinkWithinSyncRoot implements fsutil.FSUtil.IsSymlinkWithinSyncRoot.
func (m *MockFSUtil) IsSymlinkWithinSyncRoot(symlinkPath, symlinkTarget, syncRoot string) bool {
	// For mock, use the same logic as the real implementation
	if filepath.IsAbs(symlinkTarget) {
		return strings.HasPrefix(filepath.Clean(symlinkTarget), filepath.Clean(syncRoot))
	}
	absTargetPath := filepath.Clean(filepath.Join(filepath.Dir(symlinkPath), symlinkTarget))
	return strings.HasPrefix(absTargetPath, filepath.Clean(syncRoot))
}

// CreateDir implements fsutil.FSUtil.CreateDir.
func (m *MockFSUtil) CreateDir(path string, perm os.FileMode) error {
	if m.createDirFunc != nil {
		return m.createDirFunc(path, perm)
	}
	m.mu.Lock()
	defer m.mu.Unlock()

	// Simulate os.MkdirAll behavior - create all parent directories
	currentPath := path
	var pathsToCreate []string

	// Collect all paths that need to be created (from deepest to shallowest)
	for currentPath != "." && currentPath != "/" {
		if _, exists := m.fileStats[currentPath]; !exists {
			pathsToCreate = append([]string{currentPath}, pathsToCreate...) // Prepend to create parents first
		}
		currentPath = filepath.Dir(currentPath)
	}

	// Create all directories from parent to child
	for _, dirPath := range pathsToCreate {
		m.fileStats[dirPath] = &mockFileInfo{
			name:    filepath.Base(dirPath),
			size:    0,
			mode:    perm,
			modTime: time.Now(),
			isDir:   true,
		}
	}
	return nil
}

// Remove implements fsutil.FSUtil.Remove.
func (m *MockFSUtil) Remove(path string) error {
	if m.removeFunc != nil {
		return m.removeFunc(path)
	}
	m.DeleteFile(path)
	return nil
}

// Chmod implements fsutil.FSUtil.Chmod.
func (m *MockFSUtil) Chmod(path string, perm os.FileMode) error {
	if m.chmodFunc != nil {
		return m.chmodFunc(path, perm)
	}
	m.mu.Lock()
	defer m.mu.Unlock()
	if info, ok := m.fileStats[path]; ok {
		// Create a new mockFileInfo with updated mode
		m.fileStats[path] = &mockFileInfo{
			name:    info.Name(),
			size:    info.Size(),
			mode:    perm,
			modTime: time.Now(), // ModTime also changes on chmod
			isDir:   info.IsDir(),
		}
		return nil
	}
	return os.ErrNotExist
}

// Chown implements fsutil.FSUtil.Chown.
func (m *MockFSUtil) Chown(path string, uid, gid uint32) error {
	if m.chownFunc != nil {
		return m.chownFunc(path, uid, gid)
	}
	// For mock, just return nil. Actual chown requires privileges.
	return nil
}

// Chtimes implements fsutil.FSUtil.Chtimes.
func (m *MockFSUtil) Chtimes(path string, atime, mtime time.Time) error {
	if m.chtimesFunc != nil {
		return m.chtimesFunc(path, atime, mtime)
	}
	// For mock, just return nil. Actual chtimes would change file timestamps.
	return nil
}

// ReadFile implements fsutil.FSUtil.ReadFile.
func (m *MockFSUtil) ReadFile(path string) ([]byte, error) {
	if m.readFileFunc != nil {
		return m.readFileFunc(path)
	}
	m.mu.Lock()
	defer m.mu.Unlock()
	if content, ok := m.fileContents[path]; ok {
		return content, nil
	}
	return nil, os.ErrNotExist
}

// Rename implements fsutil.FSUtil.Rename.
func (m *MockFSUtil) Rename(oldPath, newPath string) error {
	if m.renameFunc != nil {
		return m.renameFunc(oldPath, newPath)
	}
	m.mu.Lock()
	defer m.mu.Unlock()

	// Handle file contents
	if content, ok := m.fileContents[oldPath]; ok {
		m.fileContents[newPath] = content
		delete(m.fileContents, oldPath)
	}

	// Handle file stats
	if info, ok := m.fileStats[oldPath]; ok {
		m.fileStats[newPath] = &mockFileInfo{
			name:    filepath.Base(newPath),
			size:    info.Size(),
			mode:    info.Mode(),
			modTime: time.Now(), // Rename changes mtime
			isDir:   info.IsDir(),
		}
		delete(m.fileStats, oldPath)
	} else {
		return os.ErrNotExist // Old path doesn't exist
	}
	return nil
}

// MockIgnoredPathMatcher is a mock implementation of types.IgnoredPathMatcher.
type MockIgnoredPathMatcher struct {
	ignoredPaths map[string]bool
	mu           sync.RWMutex
}

// NewMockIgnoredPathMatcher creates a new mock matcher.
func NewMockIgnoredPathMatcher() *MockIgnoredPathMatcher {
	return &MockIgnoredPathMatcher{
		ignoredPaths: make(map[string]bool),
	}
}

// AddIgnoredPath adds a path to be considered ignored.
func (m *MockIgnoredPathMatcher) AddIgnoredPath(path string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.ignoredPaths[path] = true
}

// IsIgnored checks if the given path should be ignored.
func (m *MockIgnoredPathMatcher) IsIgnored(path string) bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.ignoredPaths[path]
}

// mockFileInfo implements os.FileInfo for testing purposes.
type mockFileInfo struct {
	name    string
	size    int64
	mode    os.FileMode
	modTime time.Time
	isDir   bool
}

func (m *mockFileInfo) Name() string       { return m.name }
func (m *mockFileInfo) Size() int64        { return m.size }
func (m *mockFileInfo) Mode() os.FileMode  { return m.mode }
func (m *mockFileInfo) ModTime() time.Time { return m.modTime }
func (m *mockFileInfo) IsDir() bool        { return m.isDir }
func (m *mockFileInfo) Sys() interface{} {
	// Provide a mock for Sys() if it's accessed, e.g., for syscall.Stat_t
	return &syscall.Stat_t{
		Uid: 1000,
		Gid: 1000,
	}
}

// --- Test Setup and Helpers ---

// setupTestSync initializes a SyncCore with mock dependencies.
func setupTestSync(t *testing.T, cfg *config.Config, isWritethrough bool) (*SyncCore, *MockMetaManager, *MockFSUtil, chan types.FileSyncEvent) {
	mockMeta := NewMockMetaManager()
	mockFS := NewMockFSUtil()
	eventChan := make(chan types.FileSyncEvent, 100) // Buffered channel

	// Use NewSyncCore to properly initialize all handlers
	core := NewSyncCore(cfg, mockMeta, mockFS, eventChan)

	// Override the writethrough setting if needed for tests
	core.isWritethrough = isWritethrough

	return core, mockMeta, mockFS, eventChan
}

// createMockConfig creates a basic mock configuration for testing.
func createMockConfig(syncPath string, pairs []types.PairConfig, compare types.CompareStrategy) *config.Config {
	return &config.Config{
		Folder: struct {
			SyncPath string `yaml:"sync_path"`
			MetaPath string `yaml:"meta_path"`
			LogsPath string `yaml:"logs_path"`
			Ignores  []struct {
				Pattern string "yaml:\"pattern\""
				Type    string "yaml:\"type\""
			} `yaml:"ignores"`
			Compares                  types.CompareStrategy     `yaml:"compares"`
			Compression               types.FileCompressionType `yaml:"compression"`
			Readonly                  bool                      `yaml:"readonly"`
			Writethrough              bool                      `yaml:"writethrough"`
			Concurrent                int                       `yaml:"concurrent"`
			BlockSize                 int                       `yaml:"block_size"`
			InitialSyncTimeoutMinutes int                       `yaml:"initial_sync_timeout_minutes"`
		}{
			SyncPath:    syncPath,
			MetaPath:    filepath.Join(syncPath, ".meta"),
			LogsPath:    filepath.Join(os.TempDir(), "sync_test_logs"), // Use separate temp dir for logs
			Compares:    compare,
			Compression: types.COMPRESSION_NONE,
			Concurrent:  10,
			BlockSize:   4096,
		},
		Server: struct {
			Name          string `yaml:"name"`
			ListenAddress string `yaml:"listen_address"`
			AuthToken     string `yaml:"auth_token"`
		}{
			Name:          "test-server",
			ListenAddress: "127.0.0.1:8080",
			AuthToken:     "test-token",
		},
		Monitor: struct {
			SocketPath string `yaml:"socket_path"`
		}{},
		Pairs: pairs,
		Debug: true,
	}
}

// --- Initial Sync Tests ---

func TestInitialSync_OneWayToPeer(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "sync_test")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	// Create config with NO pairs (listener mode) to simulate server-side behavior
	cfg := createMockConfig(tmpDir, []types.PairConfig{}, types.FULL_FILE)

	core, _, mockFS, _ := setupTestSync(t, cfg, false)
	defer core.Close()

	// Create some initial files on local side (both in mock and real filesystem for initial sync to find them)
	os.WriteFile(filepath.Join(tmpDir, "file1.txt"), []byte("content1"), 0644)
	os.MkdirAll(filepath.Join(tmpDir, "dir1"), 0755)
	os.WriteFile(filepath.Join(tmpDir, "dir1/file2.txt"), []byte("content2"), 0644)
	os.MkdirAll(filepath.Join(tmpDir, "empty_dir"), 0755)

	// Also update the mock for consistency
	mockFS.AtomicWrite(filepath.Join(tmpDir, "file1.txt"), []byte("content1"), 0644)
	mockFS.CreateDir(filepath.Join(tmpDir, "dir1"), 0755)
	mockFS.AtomicWrite(filepath.Join(tmpDir, "dir1/file2.txt"), []byte("content2"), 0644)
	mockFS.CreateDir(filepath.Join(tmpDir, "empty_dir"), 0755)

	// Start sync core
	go core.Start()

	// Give the sync core a moment to start monitoring peer readiness
	time.Sleep(50 * time.Millisecond)

	// Simulate an incoming connection from "peerA" (server-side) which should perform initial sync
	peerAConn := NewMockNetworkConnection("peerA")
	core.AddPeerConnection(peerAConn)

	// Get the peer handler that was created for the incoming connection
	handler := core.GetPeerHandler("peerA")
	if handler == nil {
		t.Fatal("Expected PeerHandler to be created for incoming peer")
	}

	// Set the pair config to match what we want to test (ONE_WAY_TO_PEER with SYNC)
	handler.pairConfig.Direction = types.ONE_WAY_TO_PEER
	handler.pairConfig.InitialSyncStrategy = types.SYNC

	// Manually trigger sync processes as this is an incoming connection
	go handler.PerformSyncProcesses()

	// Allow time for initial sync to complete. This is non-deterministic.
	// In a real test, you'd check PairStatus or wait for specific events.
	time.Sleep(1500 * time.Millisecond) // Increased timeout to allow initial sync to complete

	// Check status from the handler since this is an incoming connection
	status := handler.GetPairStatus()
	if status.InitialSyncStatus != types.COMPLETED {
		t.Errorf("Expected InitialSyncStatus to be COMPLETED, got %s", status.InitialSyncStatus)
	}

	// Verify events were sent to peerA's mock connection
	peerAConn.mu.Lock() // Lock to safely access sentEvents
	sentEvents := peerAConn.sentEvents
	peerAConn.mu.Unlock()

	expectedPaths := map[string]types.FileSyncEventType{
		"file1.txt":      types.CREATE_FILE,
		"dir1":           types.CREATE_DIR,
		"dir1/file2.txt": types.CREATE_FILE,
		"empty_dir":      types.CREATE_DIR,
	}

	if len(sentEvents) != len(expectedPaths) {
		t.Errorf("Expected %d events sent, got %d", len(expectedPaths), len(sentEvents))
	}

	for _, event := range sentEvents {
		if expectedType, ok := expectedPaths[event.Path]; ok {
			if event.Type != expectedType {
				t.Errorf("Event for %q: Expected type %s, got %s", event.Path, expectedType, event.Type)
			}
			delete(expectedPaths, event.Path)
		} else {
			t.Errorf("Unexpected event sent: %s", event.Path)
		}
	}
	if len(expectedPaths) > 0 {
		t.Errorf("Missing expected events: %v", expectedPaths)
	}
}

func TestInitialSync_NewestWin(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "sync_test_newest")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	localPath := filepath.Join(tmpDir, "local")
	remotePath := filepath.Join(tmpDir, "remote")
	os.Mkdir(localPath, 0755)
	os.Mkdir(remotePath, 0755)

	// Mock peer connection and local FS for two-way sync
	cfg := createMockConfig(localPath, []types.PairConfig{
		{Name: "peerB", HostnameOrIP: "127.0.0.1", RemotePort: 8082, Direction: types.BIDIRECTIONAL_NEWEST_WIN, InitialSyncStrategy: types.SYNC},
	}, types.FULL_FILE)

	core, mockMeta, mockFS, _ := setupTestSync(t, cfg, false)
	defer core.Close()

	// Start sync core first
	go core.Start()
	time.Sleep(50 * time.Millisecond) // Let SyncCore start and create PeerHandlers

	peerBConn := NewMockNetworkConnection("peerB")
	core.AddPeerConnection(peerBConn)
	time.Sleep(50 * time.Millisecond)

	// Case 1: File only on local, should be sent to peer.
	mockFS.AtomicWrite(filepath.Join(localPath, "local_only.txt"), []byte("local_content"), 0644)
	mockFS.CreateDir(filepath.Join(localPath, "local_dir"), 0755)

	// Case 2: File only on remote (simulated by peer response for pull request), should be pulled.
	// This will be tested by the core requesting a pull.
	remoteOnlyContent := []byte("remote content for remote_only.txt")
	remoteOnlyFileEvent := types.FileSyncEvent{
		Type:      types.CREATE_FILE,
		Path:      "remote_only.txt",
		Timestamp: time.Now().Add(-2 * time.Hour), // Older than local mtime
		Size:      uint64(len(remoteOnlyContent)),
		FileMode:  0644,
		Checksum:  []byte("remote-checksum"),
		Origin:    types.REMOTE_PEER,
		MessageID: 100, // Important for tracking
		FileBody:  remoteOnlyContent,
	}

	// Case 3: Both have file, local is newer.
	localNewerTime := time.Now().Add(time.Hour)
	mockFS.AtomicWrite(filepath.Join(localPath, "newer_local.txt"), []byte("newer_local_content"), 0644)
	mockFS.fileStats[filepath.Join(localPath, "newer_local.txt")].(*mockFileInfo).modTime = localNewerTime

	remoteOlderContent := []byte("older remote content")
	remoteOlderFileEvent := types.FileSyncEvent{
		Type:      types.WRITE_FILE,
		Path:      "newer_local.txt",
		Timestamp: time.Now().Add(-5 * time.Hour),
		Size:      uint64(len(remoteOlderContent)),
		FileMode:  0644,
		Checksum:  []byte("older-remote-checksum"),
		Origin:    types.REMOTE_PEER,
		MessageID: 101,
		FileBody:  remoteOlderContent,
	}

	// Case 4: Both have file, remote is newer (simulated).
	remoteNewerTime := time.Now().Add(time.Hour)
	mockFS.AtomicWrite(filepath.Join(localPath, "newer_remote.txt"), []byte("older_local_content"), 0644)
	mockFS.fileStats[filepath.Join(localPath, "newer_remote.txt")].(*mockFileInfo).modTime = time.Now().Add(-10 * time.Minute)

	remoteNewerContent := []byte("newer remote content")
	remoteNewerFileEvent := types.FileSyncEvent{
		Type:      types.WRITE_FILE,
		Path:      "newer_remote.txt",
		Timestamp: remoteNewerTime,
		Size:      uint64(len(remoteNewerContent)),
		FileMode:  0644,
		Checksum:  []byte("newer-remote-checksum"),
		Origin:    types.REMOTE_PEER,
		MessageID: 102,
		FileBody:  remoteNewerContent,
	}

	// Case 5: Both have file, identical mtime, different checksums (conflict)
	conflictTime := time.Now().Truncate(time.Second) // Truncate to ensure exact match
	mockFS.AtomicWrite(filepath.Join(localPath, "conflict.txt"), []byte("local_conflict_content"), 0644)
	mockFS.fileStats[filepath.Join(localPath, "conflict.txt")].(*mockFileInfo).modTime = conflictTime
	_, _ = mockFS.CalculateXXHash(filepath.Join(localPath, "conflict.txt"), types.FULL_FILE)

	remoteConflictContent := []byte("remote conflict content")
	remoteConflictFileEvent := types.FileSyncEvent{
		Type:      types.WRITE_FILE,
		Path:      "conflict.txt",
		Timestamp: conflictTime,
		Size:      uint64(len(remoteConflictContent)),
		FileMode:  0644,
		Checksum:  []byte("remote_conflict_checksum_differs"),
		Origin:    types.REMOTE_PEER,
		MessageID: 103,
		FileBody:  remoteConflictContent,
	}

	// Simulate remote events coming through the network connection (correct approach)
	peerBConn.SendIncomingEventToCore(core, remoteOnlyFileEvent)
	peerBConn.SendIncomingEventToCore(core, remoteOlderFileEvent)
	peerBConn.SendIncomingEventToCore(core, remoteNewerFileEvent)
	peerBConn.SendIncomingEventToCore(core, remoteConflictFileEvent)

	// Give time for events to be processed
	time.Sleep(500 * time.Millisecond)

	// Simulate responses from peerB for pull requests
	peerBConn.SetSendResponseFunc(func(msgType types.MessageType, payload interface{}) error {
		t.Logf("MockNetworkConnection received msgType %s, payload %T", msgType.String(), payload)
		if msgType == types.MSG_PULL_FILE_REQUEST {
			req := payload.(types.PullFileRequest)
			// Respond with the content for 'remote_only.txt' and 'newer_remote.txt'
			if req.Path == "remote_only.txt" {
				pullResp := types.PullFileResponse{
					MessageID:    req.MessageID,
					Path:         req.Path,
					Timestamp:    remoteOnlyFileEvent.Timestamp,
					Size:         remoteOnlyFileEvent.Size,
					FileMode:     remoteOnlyFileEvent.FileMode,
					OwnerUID:     remoteOnlyFileEvent.OwnerUID,
					OwnerGID:     remoteOnlyFileEvent.OwnerGID,
					SendsChunks:  false,
					IsCompressed: false,
					FileBody:     []byte("remote content for remote_only.txt"),
				}
				// Send the response back to the sync core asynchronously
				go func() {
					time.Sleep(10 * time.Millisecond) // Small delay to simulate network
					if core.ongoingSyncHandler != nil {
						core.ongoingSyncHandler.HandlePeerResponse("peerB", types.MSG_PULL_FILE_RESPONSE, pullResp)
					}
				}()
			} else if req.Path == "newer_remote.txt" {
				pullResp := types.PullFileResponse{
					MessageID:    req.MessageID,
					Path:         req.Path,
					Timestamp:    remoteNewerFileEvent.Timestamp,
					Size:         remoteNewerFileEvent.Size,
					FileMode:     remoteNewerFileEvent.FileMode,
					OwnerUID:     remoteNewerFileEvent.OwnerUID,
					OwnerGID:     remoteNewerFileEvent.OwnerGID,
					SendsChunks:  false,
					IsCompressed: false,
					FileBody:     []byte("newer remote content"),
				}
				// Send the response back to the sync core asynchronously
				go func() {
					time.Sleep(10 * time.Millisecond) // Small delay to simulate network
					if core.ongoingSyncHandler != nil {
						core.ongoingSyncHandler.HandlePeerResponse("peerB", types.MSG_PULL_FILE_RESPONSE, pullResp)
					}
				}()
			}
		} else if msgType == types.MSG_SYNC_ACKNOWLEDGE {
			ack := payload.(types.SyncAcknowledge)
			if ack.Path == "local_only.txt" || ack.Path == "local_dir" || ack.Path == "newer_local.txt" {
				// Simulating peer acknowledging receiving local changes
				peerBConn.mu.Lock()
				peerBConn.receivedResponses = append(peerBConn.receivedResponses, ack)
				peerBConn.mu.Unlock()
			}
		}
		return nil
	})

	time.Sleep(1 * time.Second) // Give time for initial sync to process

	status, err := mockMeta.GetPairStatus("peerB")
	if err != nil {
		t.Fatalf("Failed to get peerB status: %v", err)
	}
	if status.InitialSyncStatus != types.COMPLETED {
		t.Errorf("Expected InitialSyncStatus to be COMPLETED, got %s", status.InitialSyncStatus)
	}

	// Wait for all processing to complete
	time.Sleep(1 * time.Second)

	// Verify local FS state after sync
	content, err := mockFS.ReadFile(filepath.Join(localPath, "remote_only.txt"))
	if err != nil {
		t.Errorf("Expected 'remote_only.txt' to be synced locally, but got error: %v", err)
	}
	if string(content) != "remote content for remote_only.txt" {
		t.Errorf("Content of 'remote_only.txt' mismatch: %q", string(content))
	}

	content, err = mockFS.ReadFile(filepath.Join(localPath, "newer_local.txt"))
	if err != nil {
		t.Errorf("Expected 'newer_local.txt' to remain local version, but got error: %v", err)
	}
	if string(content) != "newer_local_content" {
		t.Errorf("Content of 'newer_local.txt' mismatch: %q", string(content))
	}

	content, err = mockFS.ReadFile(filepath.Join(localPath, "newer_remote.txt"))
	if err != nil {
		t.Errorf("Expected 'newer_remote.txt' to be updated from remote, but got error: %v", err)
	}
	if string(content) != "newer remote content" {
		t.Errorf("Content of 'newer_remote.txt' mismatch: %q", string(content))
	}

	// Verify conflict.txt was not overwritten and checksums logged
	conflictFileContent, err := mockFS.ReadFile(filepath.Join(localPath, "conflict.txt"))
	if err != nil {
		t.Fatalf("Failed to read conflict.txt: %v", err)
	}
	if string(conflictFileContent) != "local_conflict_content" {
		t.Errorf("Conflict file content changed, expected 'local_conflict_content', got %q", string(conflictFileContent))
	}
	// TODO: Add a mock logger to capture and verify log messages for conflict.
}

func TestInitialSync_NoInitialSync(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "sync_test")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	cfg := createMockConfig(tmpDir, []types.PairConfig{
		{Name: "peerC", HostnameOrIP: "127.0.0.1", RemotePort: 8083, Direction: types.ONE_WAY_TO_PEER, InitialSyncStrategy: types.NO_INITIAL_SYNC},
	}, types.FULL_FILE)

	core, mockMeta, mockFS, eventChan := setupTestSync(t, cfg, false)
	defer core.Close()

	// Create a file *before* starting sync core
	mockFS.AtomicWrite(filepath.Join(tmpDir, "pre_sync_file.txt"), []byte("pre_sync_content"), 0644)

	go core.Start()
	time.Sleep(50 * time.Millisecond) // Let SyncCore start and create PeerHandlers

	peerCConn := NewMockNetworkConnection("peerC")
	peerCConn.SetupAutoACK(core, "peerC")

	// Disable auto-acknowledgment for the queue to allow proper queue processing
	if queue, err := mockMeta.GetQueue("peerC"); err == nil {
		if mockQueue, ok := queue.(*MockQueue); ok {
			mockQueue.DisableAutoAck()
		}
	}

	core.AddPeerConnection(peerCConn)

	time.Sleep(100 * time.Millisecond) // Give time for sync core to start

	status, err := mockMeta.GetPairStatus("peerC")
	if err != nil {
		t.Fatalf("Failed to get peerC status: %v", err)
	}
	if status.InitialSyncStatus != types.COMPLETED { // NO_INITIAL_SYNC should immediately complete initial sync state
		t.Errorf("Expected InitialSyncStatus to be COMPLETED, got %s", status.InitialSyncStatus)
	}

	// Verify that 'pre_sync_file.txt' was NOT sent during initial sync
	peerCConn.mu.Lock()
	sentEvents := peerCConn.sentEvents
	peerCConn.mu.Unlock()

	for _, event := range sentEvents {
		if event.Path == "pre_sync_file.txt" {
			t.Errorf("File 'pre_sync_file.txt' was sent during NO_INITIAL_SYNC, but should have been skipped.")
		}
	}

	// Create a file *after* starting sync core (should be sent by ongoing sync)
	postSyncFile := filepath.Join(tmpDir, "post_sync_file.txt")
	mockFS.AtomicWrite(postSyncFile, []byte("post_sync_content"), 0644)
	eventChan <- types.FileSyncEvent{
		Type:      types.CREATE_FILE,
		Path:      "post_sync_file.txt",
		Timestamp: time.Now(),
		Size:      uint64(len("post_sync_content")),
		FileMode:  0644,
		Origin:    types.LOCAL_INOTIFY,
		MessageID: 1,
		FileBody:  []byte("post_sync_content"),
	}

	time.Sleep(300 * time.Millisecond) // Give time for ongoing sync to process

	peerCConn.mu.Lock()
	foundPostSync := false
	for _, event := range peerCConn.sentEvents {
		if event.Path == "post_sync_file.txt" && event.Type == types.CREATE_FILE {
			foundPostSync = true
			break
		}
	}
	peerCConn.mu.Unlock()

	if !foundPostSync {
		t.Errorf("File 'post_sync_file.txt' was not sent during ongoing sync.")
	}
}

// --- Ongoing Sync Tests ---

func TestOngoingSync_CreateFile(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "sync_ongoing")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	cfg := createMockConfig(tmpDir, []types.PairConfig{
		{Name: "peerD", HostnameOrIP: "127.0.0.1", RemotePort: 8084, Direction: types.ONE_WAY_TO_PEER, InitialSyncStrategy: types.NO_INITIAL_SYNC},
	}, types.FULL_FILE)

	core, mockMeta, _, eventChan := setupTestSync(t, cfg, false)
	defer core.Close()

	go core.Start()
	time.Sleep(50 * time.Millisecond) // Let SyncCore start and create PeerHandlers

	peerDConn := NewMockNetworkConnection("peerD")

	// Disable auto-acknowledgment for the queue to allow proper queue processing
	if queue, err := mockMeta.GetQueue("peerD"); err == nil {
		if mockQueue, ok := queue.(*MockQueue); ok {
			mockQueue.DisableAutoAck()
		}
	}

	core.AddPeerConnection(peerDConn)
	time.Sleep(50 * time.Millisecond) // Let initial sync complete if NO_INITIAL_SYNC

	testFile := "new_file.txt"
	testContent := []byte("hello new file")
	event := types.FileSyncEvent{
		Type:      types.CREATE_FILE,
		Path:      testFile,
		Timestamp: time.Now(),
		Size:      uint64(len(testContent)),
		FileMode:  0644,
		Checksum:  []byte("new-checksum"),
		Origin:    types.LOCAL_INOTIFY,
		MessageID: 1, // Unique for this event
		FileBody:  testContent,
	}

	eventChan <- event // Send the event as if from inotify

	// Configure the mock to automatically send ACKs when events are sent
	peerDConn.SetupAutoACK(core, "peerD")

	time.Sleep(300 * time.Millisecond) // Give time for processing

	peerDConn.mu.Lock()
	if len(peerDConn.sentEvents) != 1 || peerDConn.sentEvents[0].Path != testFile {
		t.Errorf("Expected 1 event for %q, got %v", testFile, peerDConn.sentEvents)
	}
	peerDConn.mu.Unlock()

	// Verify event was removed from queue (async, best effort check)
	queue, _ := mockMeta.GetQueue("peerD")
	if mockQueue, ok := queue.(*MockQueue); ok {
		count, _ := mockQueue.Count()
		if count > 0 {
			t.Errorf("Expected queue to be empty after ACK, got %d events", count)
		}
	}

	status, _ := mockMeta.GetPairStatus("peerD")
	if status.TotalSent.Creates != 1 {
		t.Errorf("Expected TotalSent.Creates to be 1, got %d", status.TotalSent.Creates)
	}
}

func TestOngoingSync_WriteFileAndPull(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "sync_ongoing_pull")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	cfg := createMockConfig(tmpDir, []types.PairConfig{
		{Name: "peerE", HostnameOrIP: "127.0.0.1", RemotePort: 8085, Direction: types.BIDIRECTIONAL_NEWEST_WIN, InitialSyncStrategy: types.NO_INITIAL_SYNC},
	}, types.FULL_FILE)

	core, mockMeta, mockFS, _ := setupTestSync(t, cfg, false)
	defer core.Close()

	go core.Start()
	time.Sleep(50 * time.Millisecond) // Let SyncCore start and create PeerHandlers

	peerEConn := NewMockNetworkConnection("peerE")

	// Simulate a remote peer sending a WRITE_FILE event (metadata only)
	remoteFile := "remote_changed.txt"
	remoteContent := []byte("remote_content_v2")
	remoteEvent := types.FileSyncEvent{
		Type:         types.WRITE_FILE,
		Path:         remoteFile,
		Timestamp:    time.Now(),
		Size:         uint64(len(remoteContent)),
		FileMode:     0644,
		Checksum:     []byte("remote-checksum-v2"),
		Origin:       types.REMOTE_PEER,
		MessageID:    2,
		SendsChunks:  false,         // Small file, include body directly
		FileBody:     remoteContent, // Include the file content
		IsCompressed: false,
	}

	// Add the event to the queue before establishing the connection
	peerEConn.SendIncomingEventToCore(core, remoteEvent)

	core.AddPeerConnection(peerEConn)
	time.Sleep(100 * time.Millisecond) // Give more time for message processing

	// Simulate core receiving a PULL_FILE_REQUEST from remote peer for a local change
	// This path will be called by PeerHandler when processing incoming events.
	peerEConn.SetSendResponseFunc(func(msgType types.MessageType, payload interface{}) error {
		if msgType == types.MSG_PULL_FILE_REQUEST {
			req := payload.(types.PullFileRequest)
			if req.Path == remoteFile {
				// Simulating peer responding with the file content
				resp := types.PullFileResponse{
					MessageID:    req.MessageID,
					Path:         req.Path,
					Timestamp:    remoteEvent.Timestamp,
					Size:         remoteEvent.Size,
					FileMode:     remoteEvent.FileMode,
					OwnerUID:     remoteEvent.OwnerUID,
					OwnerGID:     remoteEvent.OwnerGID,
					SendsChunks:  false,
					IsCompressed: false,
					FileBody:     remoteContent,
				}
				// Send the response back to the sync core asynchronously
				go func() {
					time.Sleep(10 * time.Millisecond) // Small delay to simulate network
					if core.ongoingSyncHandler != nil {
						core.ongoingSyncHandler.HandlePeerResponse("peerE", types.MSG_PULL_FILE_RESPONSE, resp)
					}
				}()
			}
		} else if msgType == types.MSG_SYNC_ACKNOWLEDGE {
			ack := payload.(types.SyncAcknowledge)
			if ack.Path == remoteFile {
				peerEConn.mu.Lock()
				peerEConn.receivedResponses = append(peerEConn.receivedResponses, ack)
				peerEConn.mu.Unlock()
			}
		}
		return nil
	})

	time.Sleep(100 * time.Millisecond) // Give time for processing

	// Verify file was written locally
	localFilePath := filepath.Join(tmpDir, remoteFile)
	content, err := mockFS.ReadFile(localFilePath)
	if err != nil {
		t.Fatalf("Expected file %q to be created locally, but got error: %v", remoteFile, err)
	}
	if !bytes.Equal(content, remoteContent) {
		t.Errorf("File content mismatch. Expected %q, got %q", string(remoteContent), string(content))
	}

	// Verify TotalReceived stats
	status, _ := mockMeta.GetPairStatus("peerE")
	if status.TotalReceived.Writes != 1 {
		t.Errorf("Expected TotalReceived.Writes to be 1, got %d", status.TotalReceived.Writes)
	}
	if status.TotalReceived.Bytes != uint64(len(remoteContent)) {
		t.Errorf("Expected TotalReceived.Bytes to be %d, got %d", len(remoteContent), status.TotalReceived.Bytes)
	}
}

func TestOngoingSync_DeleteFile(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "sync_ongoing_delete")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	cfg := createMockConfig(tmpDir, []types.PairConfig{
		{Name: "peerF", HostnameOrIP: "127.0.0.1", RemotePort: 8086, Direction: types.ONE_WAY_TO_PEER, InitialSyncStrategy: types.NO_INITIAL_SYNC},
	}, types.FULL_FILE)

	core, mockMeta, mockFS, eventChan := setupTestSync(t, cfg, false)
	defer core.Close()

	go core.Start()
	time.Sleep(50 * time.Millisecond) // Let SyncCore start and create PeerHandlers

	peerFConn := NewMockNetworkConnection("peerF")

	// Disable auto-acknowledgment for the queue to allow proper queue processing
	if queue, err := mockMeta.GetQueue("peerF"); err == nil {
		if mockQueue, ok := queue.(*MockQueue); ok {
			mockQueue.DisableAutoAck()
		}
	}

	core.AddPeerConnection(peerFConn)
	time.Sleep(50 * time.Millisecond) // Let initial sync complete

	// Create a file locally that will then be deleted
	localFile := filepath.Join(tmpDir, "file_to_delete.txt")
	mockFS.AtomicWrite(localFile, []byte("content"), 0644)

	// Configure the mock to automatically send ACKs when events are sent
	peerFConn.SetupAutoACK(core, "peerF")

	deleteEvent := types.FileSyncEvent{
		Type:      types.DELETE_FILE,
		Path:      "file_to_delete.txt",
		Timestamp: time.Now(),
		Origin:    types.LOCAL_INOTIFY,
		MessageID: 3,
	}
	eventChan <- deleteEvent

	time.Sleep(300 * time.Millisecond)

	peerFConn.mu.Lock()
	if len(peerFConn.sentEvents) != 1 || peerFConn.sentEvents[0].Type != types.DELETE_FILE {
		t.Errorf("Expected 1 DELETE event, got %v", peerFConn.sentEvents)
	}
	peerFConn.mu.Unlock()

	status, _ := mockMeta.GetPairStatus("peerF")
	if status.TotalSent.Deletes != 1 {
		t.Errorf("Expected TotalSent.Deletes to be 1, got %d", status.TotalSent.Deletes)
	}
}

func TestOngoingSync_FileRemovedBeforePull(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "sync_removed_before_pull")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	cfg := createMockConfig(tmpDir, []types.PairConfig{
		{Name: "peerG", HostnameOrIP: "127.0.0.1", RemotePort: 8087, Direction: types.BIDIRECTIONAL_NEWEST_WIN, InitialSyncStrategy: types.NO_INITIAL_SYNC},
	}, types.FULL_FILE)

	core, _, mockFS, _ := setupTestSync(t, cfg, false)
	defer core.Close()

	go core.Start()
	time.Sleep(50 * time.Millisecond) // Let SyncCore start and create PeerHandlers

	peerGConn := NewMockNetworkConnection("peerG")
	core.AddPeerConnection(peerGConn)
	time.Sleep(50 * time.Millisecond)

	// Simulate remote peer sending a WRITE_FILE event (metadata only) for a file that locally exists
	// Then, we'll simulate the local file being deleted before the daemon can respond to a pull request.
	remoteFile := "temp_file_for_pull.txt"
	mockFS.AtomicWrite(filepath.Join(tmpDir, remoteFile), []byte("initial_content"), 0644) // Create file locally

	remoteEvent := types.FileSyncEvent{
		Type:         types.WRITE_FILE,
		Path:         remoteFile,
		Timestamp:    time.Now().Add(time.Hour), // Newer timestamp to trigger pull
		Size:         100,
		FileMode:     0644,
		Checksum:     []byte("remote-new-checksum"),
		Origin:       types.REMOTE_PEER,
		MessageID:    4,
		SendsChunks:  true, // Needs pull
		IsCompressed: false,
		FileBody:     nil,
	}
	// Simulate the remote event coming through the network connection
	peerGConn.SendIncomingEventToCore(core, remoteEvent)
	time.Sleep(100 * time.Millisecond) // Give time for processing

	// Mock FSUtil.ReadFile to simulate file being removed *during* a pull attempt
	mockFS.readFileFunc = func(path string) ([]byte, error) {
		if filepath.Base(path) == remoteFile {
			return nil, os.ErrNotExist // Simulate file removed
		}
		// For other files, use default behavior
		mfs := *mockFS         // Create a shallow copy to avoid recursion in the mock
		mfs.readFileFunc = nil // Clear the mock func to call original behaviour
		return mfs.ReadFile(path)
	}

	// Set up the mock network connection to respond to PULL_FILE_REQUEST with MSG_FILE_REMOVED
	peerGConn.SetSendResponseFunc(func(msgType types.MessageType, payload interface{}) error {
		t.Logf("MockNetworkConnection for peerG received msgType %s, payload %T", msgType.String(), payload)
		if msgType == types.MSG_PULL_FILE_REQUEST {
			// Simulate the peer responding that the file was removed
			pullReq := payload.(types.PullFileRequest)
			fileRemoved := types.FileRemoved{
				MessageID: pullReq.MessageID,
				Path:      pullReq.Path,
			}
			// Simulate the peer sending back MSG_FILE_REMOVED
			go func() {
				time.Sleep(50 * time.Millisecond) // Small delay to simulate network
				if core.ongoingSyncHandler != nil {
					core.ongoingSyncHandler.HandlePeerResponse("peerG", types.MSG_FILE_REMOVED, fileRemoved)
				}
			}()
			return nil
		}
		return nil
	})

	time.Sleep(500 * time.Millisecond) // Give time for processing

	// Verify that MSG_PULL_FILE_REQUEST was sent by the core
	peerGConn.mu.Lock()
	foundPullRequest := false
	for _, msg := range peerGConn.sentMessages {
		if msg.MessageType == types.MSG_PULL_FILE_REQUEST {
			if pullReq, ok := msg.Payload.(types.PullFileRequest); ok && pullReq.Path == remoteEvent.Path {
				foundPullRequest = true
				break
			}
		}
	}
	peerGConn.mu.Unlock()

	if !foundPullRequest {
		t.Errorf("Expected MSG_PULL_FILE_REQUEST to be sent, but it was not.")
	}

	// Verify local file was not created/updated (it was removed)
	_, err = mockFS.ReadFile(filepath.Join(tmpDir, remoteFile))
	if !os.IsNotExist(err) {
		t.Errorf("Expected file %q to be non-existent, but it exists or another error occurred: %v", remoteFile, err)
	}
}

// --- Conflict Resolution Tests (Newest Win) ---

func TestConflictResolution_IdenticalMtimeIdenticalChecksum(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "sync_conflict_same")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	cfg := createMockConfig(tmpDir, []types.PairConfig{
		{Name: "peerH", HostnameOrIP: "127.0.0.1", RemotePort: 8088, Direction: types.BIDIRECTIONAL_NEWEST_WIN, InitialSyncStrategy: types.NO_INITIAL_SYNC},
	}, types.FULL_FILE)

	core, _, mockFS, _ := setupTestSync(t, cfg, false)
	defer core.Close()

	go core.Start()
	time.Sleep(50 * time.Millisecond)

	peerHConn := NewMockNetworkConnection("peerH")
	core.AddPeerConnection(peerHConn)
	time.Sleep(50 * time.Millisecond)

	// Create a file locally
	sharedFile := "shared_file.txt"
	content := []byte("identical content")
	modTime := time.Now().Truncate(time.Second) // Ensure exact match
	localFilePath := filepath.Join(tmpDir, sharedFile)
	mockFS.AtomicWrite(localFilePath, content, 0644)
	mockFS.fileStats[localFilePath].(*mockFileInfo).modTime = modTime
	_, _ = mockFS.CalculateXXHash(localFilePath, types.FULL_FILE)

	// Simulate receiving an event from remote peer with identical mtime and checksum
	remoteEvent := types.FileSyncEvent{
		Type:      types.WRITE_FILE,
		Path:      sharedFile,
		Timestamp: modTime,
		Size:      uint64(len(content)),
		FileMode:  0644,
		Checksum:  []byte("remote-checksum"),
		Origin:    types.REMOTE_PEER,
		MessageID: 5,
		FileBody:  content,
	}

	// Simulate receiving the remote event through the peer connection
	peerHConn.SendIncomingEventToCore(core, remoteEvent)

	// Simulate peer acknowledging with MSG_SAME_CONTENT
	peerHConn.SetSendResponseFunc(func(msgType types.MessageType, payload interface{}) error {
		if msgType == types.MSG_PULL_FILE_REQUEST {
			// Peer might request if it doesn't have it, respond with MSG_SAME_CONTENT
			sameContentMsg := types.SameContent{
				MessageID: payload.(types.PullFileRequest).MessageID,
				Path:      payload.(types.PullFileRequest).Path,
				Timestamp: modTime,
				Size:      uint64(len(content)),
			}
			peerHConn.mu.Lock()
			peerHConn.receivedResponses = append(peerHConn.receivedResponses, sameContentMsg)
			peerHConn.mu.Unlock()
		} else if msgType == types.MSG_SYNC_ACKNOWLEDGE {
			ack := payload.(types.SyncAcknowledge)
			if ack.Path == sharedFile {
				peerHConn.mu.Lock()
				peerHConn.receivedResponses = append(peerHConn.receivedResponses, ack)
				peerHConn.mu.Unlock()
			}
		}
		return nil
	})

	time.Sleep(100 * time.Millisecond)

	// Verify local file content remains unchanged
	finalContent, err := mockFS.ReadFile(localFilePath)
	if err != nil {
		t.Fatalf("Failed to read file: %v", err)
	}
	if !bytes.Equal(finalContent, content) {
		t.Errorf("File content was changed, expected %q, got %q", string(content), string(finalContent))
	}
	// TODO: Verify INFO log message about identical content (requires mock logger)
}

func TestConflictResolution_IdenticalMtimeDifferentChecksum(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "sync_conflict_diff")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	cfg := createMockConfig(tmpDir, []types.PairConfig{
		{Name: "peerI", HostnameOrIP: "127.0.0.1", RemotePort: 8089, Direction: types.BIDIRECTIONAL_NEWEST_WIN, InitialSyncStrategy: types.NO_INITIAL_SYNC},
	}, types.FULL_FILE)

	core, _, mockFS, _ := setupTestSync(t, cfg, false)
	defer core.Close()

	go core.Start()
	time.Sleep(50 * time.Millisecond)

	peerIConn := NewMockNetworkConnection("peerI")
	core.AddPeerConnection(peerIConn)
	time.Sleep(50 * time.Millisecond)

	// Create a file locally
	sharedFile := "conflict_file.txt"
	localContent := []byte("local content")
	modTime := time.Now().Truncate(time.Second) // Ensure exact mtime match
	localFilePath := filepath.Join(tmpDir, sharedFile)
	mockFS.AtomicWrite(localFilePath, localContent, 0644)
	mockFS.fileStats[localFilePath].(*mockFileInfo).modTime = modTime
	_, _ = mockFS.CalculateXXHash(localFilePath, types.FULL_FILE)

	// Simulate receiving an event from remote peer with identical mtime but different checksum
	remoteContent := []byte("remote content different")
	remoteChecksum := []byte("different-checksum")
	remoteEvent := types.FileSyncEvent{
		Type:      types.WRITE_FILE,
		Path:      sharedFile,
		Timestamp: modTime,
		Size:      uint64(len(remoteContent)),
		FileMode:  0644,
		Checksum:  remoteChecksum, // Different checksum
		Origin:    types.REMOTE_PEER,
		MessageID: 6,
		FileBody:  remoteContent,
	}

	// Simulate receiving the remote event through the peer connection
	peerIConn.SendIncomingEventToCore(core, remoteEvent)

	peerIConn.SetSendResponseFunc(func(msgType types.MessageType, payload interface{}) error {
		// No response expected for this conflict scenario as neither side should win automatically.
		return nil
	})

	time.Sleep(100 * time.Millisecond)

	// Verify local file content remains unchanged (no overwrite)
	finalContent, err := mockFS.ReadFile(localFilePath)
	if err != nil {
		t.Fatalf("Failed to read file: %v", err)
	}
	if !bytes.Equal(finalContent, localContent) {
		t.Errorf("File content was overwritten, expected %q, got %q", string(localContent), string(finalContent))
	}
	// TODO: Verify ERROR log message about true conflict (requires mock logger)
}

// --- Writethrough Mode Tests ---

func TestWritethroughMode_ProxyEventPropagationAndCleanup(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "sync_writethrough")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	// Configure for writethrough mode
	cfg := createMockConfig(tmpDir, []types.PairConfig{
		{Name: "peerJ", HostnameOrIP: "127.0.0.1", RemotePort: 8090, Direction: types.ONE_WAY_TO_PEER, InitialSyncStrategy: types.NO_INITIAL_SYNC},
		{Name: "peerK", HostnameOrIP: "127.0.0.1", RemotePort: 8091, Direction: types.ONE_WAY_TO_PEER, InitialSyncStrategy: types.NO_INITIAL_SYNC},
	}, types.FULL_FILE)
	cfg.Folder.Writethrough = true
	cfg.Monitor.SocketPath = "/tmp/mock_proxy_socket" // Placeholder, not used directly by SyncCore

	core, mockMeta, mockFS, eventChan := setupTestSync(t, cfg, true) // Pass isWritethrough = true
	defer core.Close()

	go core.Start()
	time.Sleep(50 * time.Millisecond) // Let SyncCore start and create PeerHandlers

	peerJConn := NewMockNetworkConnection("peerJ")
	peerKConn := NewMockNetworkConnection("peerK")

	// Disable auto-acknowledgment for the queues to allow proper queue processing
	if queue, err := mockMeta.GetQueue("peerJ"); err == nil {
		if mockQueue, ok := queue.(*MockQueue); ok {
			mockQueue.DisableAutoAck()
		}
	}
	if queue, err := mockMeta.GetQueue("peerK"); err == nil {
		if mockQueue, ok := queue.(*MockQueue); ok {
			mockQueue.DisableAutoAck()
		}
	}

	// Set up auto-ACK for both peers with longer delay for testing
	peerJConn.SetupAutoACKWithDelay(core, "peerJ", 800*time.Millisecond)
	peerKConn.SetupAutoACKWithDelay(core, "peerK", 800*time.Millisecond)

	core.AddPeerConnection(peerJConn)
	core.AddPeerConnection(peerKConn)
	time.Sleep(50 * time.Millisecond)

	// Simulate an event from the proxy socket
	proxyFile := "proxy_written_file.txt"
	proxyContent := []byte("content from proxy")
	proxyEvent := types.FileSyncEvent{
		Type:      types.CREATE_FILE,
		Path:      proxyFile,
		Timestamp: time.Now(),
		Size:      uint64(len(proxyContent)),
		FileMode:  0644,
		Checksum:  []byte("proxy-checksum"),
		Origin:    types.PROXY_CLIENT_WRITE, // Crucial for writethrough
		MessageID: 10,
		FileBody:  proxyContent,
	}

	eventChan <- proxyEvent // Ingress sends this to SyncCore

	// Give time for writethrough processing to complete (but before ACKs arrive)
	time.Sleep(300 * time.Millisecond)

	// Verify local file was created as a buffer
	localProxyFile := filepath.Join(tmpDir, proxyFile)
	content, err := mockFS.ReadFile(localProxyFile)
	if err != nil {
		t.Fatalf("Expected proxy file %q to be created locally, but got error: %v", proxyFile, err)
	}
	if !bytes.Equal(content, proxyContent) {
		t.Errorf("Proxy file content mismatch. Expected %q, got %q", string(proxyContent), string(content))
	}

	// ACKs are automatically handled by SetupAutoACK

	// Check if local buffer file is present (should exist before ACKs arrive)
	_, err = mockFS.ReadFile(localProxyFile)
	if err != nil {
		t.Fatalf("Expected buffer file %q to exist before ACKs arrive, but got error: %v", localProxyFile, err)
	}

	// Wait for ACKs to arrive and processing to complete
	time.Sleep(600 * time.Millisecond)

	// Ensure both peers received the event (may be multiple due to queue processing)
	peerJConn.mu.Lock()
	if len(peerJConn.sentEvents) == 0 || peerJConn.sentEvents[0].Path != proxyFile {
		t.Errorf("Peer J did not receive event correctly: %v", peerJConn.sentEvents)
	}
	peerJConn.mu.Unlock()

	peerKConn.mu.Lock()
	if len(peerKConn.sentEvents) == 0 || peerKConn.sentEvents[0].Path != proxyFile {
		t.Errorf("Peer K did not receive event correctly: %v", peerKConn.sentEvents)
	}
	peerKConn.mu.Unlock()

	// After both have acknowledged, the file should be removed
	// The acknowledgement processing happens asynchronously, so we need to wait.
	time.Sleep(100 * time.Millisecond) // Give more time for ack processing

	_, err = mockFS.ReadFile(localProxyFile)
	if !os.IsNotExist(err) {
		t.Errorf("Expected buffer file %q to be removed after all acknowledgements, but it still exists: %v", localProxyFile, err)
	}
}

func TestWritethroughMode_RecursiveDirectoryCreation(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "sync_writethrough_dir_create")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	cfg := createMockConfig(tmpDir, []types.PairConfig{
		{Name: "peerL", HostnameOrIP: "127.0.0.1", RemotePort: 8092, Direction: types.ONE_WAY_TO_PEER, InitialSyncStrategy: types.NO_INITIAL_SYNC},
	}, types.FULL_FILE)
	cfg.Folder.Writethrough = true
	cfg.Monitor.SocketPath = "/tmp/mock_proxy_socket_dir"

	core, mockMeta, mockFS, eventChan := setupTestSync(t, cfg, true)
	defer core.Close()

	go core.Start()
	time.Sleep(50 * time.Millisecond) // Let SyncCore start and create PeerHandlers

	peerLConn := NewMockNetworkConnection("peerL")

	// Disable auto-acknowledgment for the queue to allow proper queue processing
	if queue, err := mockMeta.GetQueue("peerL"); err == nil {
		if mockQueue, ok := queue.(*MockQueue); ok {
			mockQueue.DisableAutoAck()
		}
	}

	peerLConn.SetupAutoACKWithDelay(core, "peerL", 500*time.Millisecond)
	core.AddPeerConnection(peerLConn)
	time.Sleep(50 * time.Millisecond)

	// Simulate an event creating a file in a non-existent nested directory
	nestedFile := "level1/level2/nested_file.txt"
	nestedContent := []byte("nested content")
	proxyEvent := types.FileSyncEvent{
		Type:      types.CREATE_FILE,
		Path:      nestedFile,
		Timestamp: time.Now(),
		Size:      uint64(len(nestedContent)),
		FileMode:  0644,
		Checksum:  []byte("nested-checksum"),
		Origin:    types.PROXY_CLIENT_WRITE,
		MessageID: 11,
		FileBody:  nestedContent,
	}

	eventChan <- proxyEvent

	// ACK is automatically handled by SetupAutoACK
	// Give time for writethrough processing to complete (but before ACK arrives)
	time.Sleep(200 * time.Millisecond)

	// Verify the nested directories and file were created locally
	localNestedFile := filepath.Join(tmpDir, nestedFile)
	_, err = mockFS.ReadFile(localNestedFile)
	if err != nil {
		t.Fatalf("Expected nested file %q to be created locally, but got error: %v", nestedFile, err)
	}

	// Give a small delay to ensure directory creation is complete
	time.Sleep(50 * time.Millisecond)

	// Verify parent directories exist
	parentDir := filepath.Dir(localNestedFile)
	grandparentDir := filepath.Dir(parentDir)
	t.Logf("Checking for parent directory: %q", parentDir)
	t.Logf("Checking for grandparent directory: %q", grandparentDir)
	info, err := mockFS.GetFileStat(parentDir)
	if err != nil || !info.IsDir() {
		t.Errorf("Expected directory %q to be created, but got error: %v", parentDir, err)
	}
	info, err = mockFS.GetFileStat(grandparentDir)
	if err != nil || !info.IsDir() {
		t.Errorf("Expected directory %q to be created, but got error: %v", grandparentDir, err)
	}

	// Wait for ACK to arrive and buffer file cleanup
	time.Sleep(400 * time.Millisecond)

	// Verify buffer file cleanup
	_, err = mockFS.ReadFile(localNestedFile)
	if !os.IsNotExist(err) {
		t.Errorf("Expected buffer file %q to be removed, but it still exists: %v", localNestedFile, err)
	}
}

// --- Concurrency / Goroutine Safety Tests ---

func TestSyncCore_GoroutineSafety(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "sync_concurrency")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	cfg := createMockConfig(tmpDir, []types.PairConfig{
		{Name: "peerM", HostnameOrIP: "127.0.0.1", RemotePort: 8093, Direction: types.ONE_WAY_TO_PEER, InitialSyncStrategy: types.NO_INITIAL_SYNC},
	}, types.FULL_FILE)

	core, mockMeta, _, eventChan := setupTestSync(t, cfg, false)
	defer core.Close()

	go core.Start()
	time.Sleep(50 * time.Millisecond) // Let SyncCore start and create PeerHandlers

	peerMConn := NewMockNetworkConnection("peerM")

	// Disable auto-acknowledgment for the queue to allow proper queue processing
	if queue, err := mockMeta.GetQueue("peerM"); err == nil {
		if mockQueue, ok := queue.(*MockQueue); ok {
			mockQueue.DisableAutoAck()
		}
	}

	peerMConn.SetupAutoACK(core, "peerM")
	core.AddPeerConnection(peerMConn)
	time.Sleep(50 * time.Millisecond)

	numEvents := 20 // Reduced for faster test execution
	var wg sync.WaitGroup

	// Simulate concurrent incoming events
	for i := 0; i < numEvents; i++ {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()
			fileName := fmt.Sprintf("file_%d.txt", i)
			content := []byte(fmt.Sprintf("content_%d", i))
			event := types.FileSyncEvent{
				Type:      types.CREATE_FILE,
				Path:      fileName,
				Timestamp: time.Now(),
				Size:      uint64(len(content)),
				FileMode:  0644,
				Checksum:  []byte(fmt.Sprintf("hash_%d", i)),
				Origin:    types.LOCAL_INOTIFY,
				MessageID: uint64(1000 + i),
				FileBody:  content,
			}
			eventChan <- event
		}(i)
	}

	wg.Wait() // Wait for all events to be sent to eventChan

	// Give some time for events to be processed and ACKs to flow back
	// With 20 events and 100ms send interval, need ~2+ seconds
	time.Sleep(3 * time.Second)

	// Verify all events were eventually processed and acknowledged (approximated by queue empty and sent count)
	queue, _ := mockMeta.GetQueue("peerM")
	if mockQueue, ok := queue.(*MockQueue); ok {
		count, _ := mockQueue.Count()
		if count > 0 {
			t.Errorf("Expected queue to be empty, but has %d events", count)
		}
	}

	status, _ := mockMeta.GetPairStatus("peerM")
	if status.TotalSent.Creates != numEvents {
		t.Errorf("Expected %d creates sent, got %d", numEvents, status.TotalSent.Creates)
	}

	// Check that events were sent (ACKs are handled automatically by SetupAutoACK)
	peerMConn.mu.Lock()
	sentEventsCount := len(peerMConn.sentEvents)
	peerMConn.mu.Unlock()

	if sentEventsCount != numEvents {
		t.Errorf("Expected %d ACKs received by mock peer, got %d", numEvents, sentEventsCount)
	}

	// Verify no panics due to concurrent map writes/reads in SyncCore
	// (implicit if tests complete without runtime errors)
}

// Test RemovePeerConnection
func TestSyncCore_RemovePeerConnection(t *testing.T) {
	tmpDir := t.TempDir()
	cfg := createMockConfig(tmpDir, []types.PairConfig{}, types.FULL_FILE)

	syncCore, _, _, _ := setupTestSync(t, cfg, false)

	// Test removing non-existent peer
	syncCore.RemovePeerConnection("non-existent-peer")

	// Add a peer first
	peerConfig := types.PairConfig{
		Name:                "test-peer",
		HostnameOrIP:        "127.0.0.1",
		Direction:           types.BIDIRECTIONAL_NEWEST_WIN,
		InitialSyncStrategy: types.SYNC,
		RemotePort:          8080,
	}

	status := &types.PairStatus{
		Name:               "test-peer",
		InitialSyncStatus:  types.PENDING,
		CurrentQueueDBFile: filepath.Join(tmpDir, "test-peer.queue.db"),
	}

	// Create a simple logger for the test
	testLogger, _ := logger.NewLogger(tmpDir, false)
	handler := NewPeerHandler(peerConfig, status, syncCore, testLogger)
	syncCore.peerHandlers["test-peer"] = handler

	// Test removing existing peer
	syncCore.RemovePeerConnection("test-peer")

	// Verify peer was removed (the handler might still exist but should be marked as disconnected)
	if handler, exists := syncCore.peerHandlers["test-peer"]; exists {
		// Check if the handler's connection is closed
		if handler.GetConnection() != nil && handler.GetConnection().IsConnected() {
			t.Error("Expected peer connection to be closed")
		}
	}
}

// Test HandlePeerMessage
func TestSyncCore_HandlePeerMessage(t *testing.T) {
	tmpDir := t.TempDir()

	// Add a peer to the config so it's known
	peerConfig := types.PairConfig{
		Name:                "test-peer",
		HostnameOrIP:        "127.0.0.1",
		Direction:           types.BIDIRECTIONAL_NEWEST_WIN,
		InitialSyncStrategy: types.SYNC,
		RemotePort:          8080,
	}
	cfg := createMockConfig(tmpDir, []types.PairConfig{peerConfig}, types.FULL_FILE)

	syncCore, _, _, _ := setupTestSync(t, cfg, false)

	status := &types.PairStatus{
		Name:               "test-peer",
		InitialSyncStatus:  types.PENDING,
		CurrentQueueDBFile: filepath.Join(tmpDir, "test-peer.queue.db"),
	}

	testLogger, _ := logger.NewLogger(tmpDir, false)
	handler := NewPeerHandler(peerConfig, status, syncCore, testLogger)
	syncCore.peerHandlers["test-peer"] = handler

	// Test with unknown message type
	err := syncCore.HandlePeerMessage("test-peer", types.MSG_ERROR, "test payload")
	if err == nil {
		t.Error("Expected error for unknown message type")
	}

	// Test with MSG_FILE_SYNC_EVENT
	event := types.FileSyncEvent{
		Type:      types.CREATE_FILE,
		Path:      "test.txt",
		Size:      100,
		MessageID: 1,
	}

	err = syncCore.HandlePeerMessage("test-peer", types.MSG_FILE_SYNC_EVENT, event)
	if err != nil {
		t.Errorf("HandlePeerMessage failed: %v", err)
	}

	// Test with MSG_SYNC_ACKNOWLEDGE
	ack := types.SyncAcknowledge{
		MessageID:    1,
		Path:         "test.txt",
		EventType:    types.CREATE_FILE,
		Success:      true,
		ErrorMessage: "",
	}

	err = syncCore.HandlePeerMessage("test-peer", types.MSG_SYNC_ACKNOWLEDGE, ack)
	if err != nil {
		t.Errorf("HandlePeerMessage failed: %v", err)
	}
}

// TestSyncCore_ListenerMode tests that the application works correctly in listener mode
// where a server has no configured pairs but accepts incoming connections
func TestSyncCore_ListenerMode(t *testing.T) {
	tmpDir := t.TempDir()

	// Create a config with NO pairs (listener mode)
	cfg := createMockConfig(tmpDir, []types.PairConfig{}, types.FULL_FILE)

	syncCore, mockMeta, _, _ := setupTestSync(t, cfg, false)

	// Simulate an incoming connection from an unknown peer
	mockConn := NewMockNetworkConnection("incoming-peer")

	// This should create a new PeerHandler dynamically
	syncCore.AddPeerConnection(mockConn)

	// Verify that a PeerHandler was created for the incoming peer
	peerHandler := syncCore.GetPeerHandler("incoming-peer")
	if peerHandler == nil {
		t.Fatal("Expected PeerHandler to be created for incoming peer, but got nil")
	}

	// Verify the peer configuration was set correctly for listener mode
	pairConfig := peerHandler.GetPairConfig()
	if pairConfig.Name != "incoming-peer" {
		t.Errorf("Expected peer name 'incoming-peer', got %q", pairConfig.Name)
	}
	if pairConfig.Direction != types.BIDIRECTIONAL_NEWEST_WIN {
		t.Errorf("Expected BIDIRECTIONAL_NEWEST_WIN direction for listener mode, got %s", pairConfig.Direction)
	}
	if pairConfig.InitialSyncStrategy != types.SYNC {
		t.Errorf("Expected SYNC initial sync strategy for listener mode, got %s", pairConfig.InitialSyncStrategy)
	}

	// Test that we can handle remote events from this dynamically created peer
	event := types.FileSyncEvent{
		Type:      types.CREATE_FILE,
		Path:      "remote_file.txt",
		Size:      50,
		MessageID: 1,
		Origin:    types.REMOTE_PEER,
	}

	// This should work without the "unknown peer" error
	err := syncCore.HandlePeerMessage("incoming-peer", types.MSG_FILE_SYNC_EVENT, event)
	if err != nil {
		t.Errorf("Expected no error handling remote event from dynamically created peer, got %v", err)
	}

	// Verify that the peer status was saved in the mock meta manager
	statuses, err := mockMeta.Load()
	if err != nil {
		t.Errorf("Failed to load statuses from mock meta manager: %v", err)
	}
	if _, exists := statuses["incoming-peer"]; !exists {
		t.Errorf("Expected status for 'incoming-peer' to be saved in meta manager")
	}
}

// TestSyncCore_BidirectionalSync tests end-to-end bidirectional synchronization
func TestSyncCore_BidirectionalSync(t *testing.T) {
	tmpDir := t.TempDir()

	// Create two sync cores to simulate two peers
	cfg1 := createMockConfig(tmpDir+"/peer1", []types.PairConfig{}, types.FULL_FILE)
	cfg2 := createMockConfig(tmpDir+"/peer2", []types.PairConfig{}, types.FULL_FILE)

	syncCore1, mockMeta1, mockFS1, _ := setupTestSync(t, cfg1, false)
	syncCore2, mockMeta2, mockFS2, _ := setupTestSync(t, cfg2, false)

	// Create mock connections between the peers
	conn1to2 := NewMockNetworkConnection("peer2")
	conn2to1 := NewMockNetworkConnection("peer1")

	// Add peer connections (simulating incoming connections)
	syncCore1.AddPeerConnection(conn1to2)
	syncCore2.AddPeerConnection(conn2to1)

	// Verify peer handlers were created
	peer1Handler := syncCore2.GetPeerHandler("peer1")
	peer2Handler := syncCore1.GetPeerHandler("peer2")
	if peer1Handler == nil || peer2Handler == nil {
		t.Fatal("Expected peer handlers to be created for both peers")
	}

	// Create a file on peer1's filesystem
	testFile1 := "test_file_1.txt"
	testContent1 := []byte("Hello from peer1")
	testFile1Path := filepath.Join(cfg1.Folder.SyncPath, testFile1)
	mockFS1.SetFileContent(testFile1Path, testContent1, &mockFileInfo{
		name:    testFile1,
		size:    int64(len(testContent1)),
		mode:    0644,
		modTime: time.Now(),
		isDir:   false,
	})

	// Create a file on peer2's filesystem
	testFile2 := "test_file_2.txt"
	testContent2 := []byte("Hello from peer2")
	testFile2Path := filepath.Join(cfg2.Folder.SyncPath, testFile2)
	mockFS2.SetFileContent(testFile2Path, testContent2, &mockFileInfo{
		name:    testFile2,
		size:    int64(len(testContent2)),
		mode:    0644,
		modTime: time.Now(),
		isDir:   false,
	})

	// Simulate file sync events from peer1 to peer2
	event1 := types.FileSyncEvent{
		Type:      types.CREATE_FILE,
		Path:      testFile1,
		Size:      uint64(len(testContent1)),
		MessageID: 1,
		Origin:    types.REMOTE_PEER,
		FileBody:  testContent1,
	}

	err := syncCore2.HandlePeerMessage("peer1", types.MSG_FILE_SYNC_EVENT, event1)
	if err != nil {
		t.Errorf("Failed to handle file sync event from peer1: %v", err)
	}

	// Simulate file sync events from peer2 to peer1
	event2 := types.FileSyncEvent{
		Type:      types.CREATE_FILE,
		Path:      testFile2,
		Size:      uint64(len(testContent2)),
		MessageID: 2,
		Origin:    types.REMOTE_PEER,
		FileBody:  testContent2,
	}

	err = syncCore1.HandlePeerMessage("peer2", types.MSG_FILE_SYNC_EVENT, event2)
	if err != nil {
		t.Errorf("Failed to handle file sync event from peer2: %v", err)
	}

	// Verify that files were created on both sides
	// peer1's file should now exist on peer2
	peer2File1Path := filepath.Join(cfg2.Folder.SyncPath, testFile1)
	if _, err := mockFS2.GetFileStat(peer2File1Path); err != nil {
		t.Errorf("Expected file %s to be created on peer2, but got error: %v", testFile1, err)
	} else {
		content, _ := mockFS2.ReadFile(peer2File1Path)
		if string(content) != string(testContent1) {
			t.Errorf("Expected file content %q, got %q", string(testContent1), string(content))
		}
	}

	// peer2's file should now exist on peer1
	peer1File2Path := filepath.Join(cfg1.Folder.SyncPath, testFile2)
	if _, err := mockFS1.GetFileStat(peer1File2Path); err != nil {
		t.Errorf("Expected file %s to be created on peer1, but got error: %v", testFile2, err)
	} else {
		content, _ := mockFS1.ReadFile(peer1File2Path)
		if string(content) != string(testContent2) {
			t.Errorf("Expected file content %q, got %q", string(testContent2), string(content))
		}
	}

	// Verify that both peers have updated status
	statuses1, _ := mockMeta1.Load()
	statuses2, _ := mockMeta2.Load()

	if _, exists := statuses1["peer2"]; !exists {
		t.Errorf("Expected status for 'peer2' to be saved in peer1's meta manager")
	}
	if _, exists := statuses2["peer1"]; !exists {
		t.Errorf("Expected status for 'peer1' to be saved in peer2's meta manager")
	}
}

// TestSyncCore_IncomingConnectionHandling tests that incoming connections are properly handled
// This simulates the real network layer calling AddPeerConnection for incoming connections
func TestSyncCore_IncomingConnectionHandling(t *testing.T) {
	tmpDir := t.TempDir()

	// Create a config with NO pairs (listener mode)
	cfg := createMockConfig(tmpDir, []types.PairConfig{}, types.FULL_FILE)

	syncCore, mockMeta, mockFS, _ := setupTestSync(t, cfg, false)

	// Simulate what the network layer does when it receives an incoming connection
	// 1. Handshake succeeds and peer name is extracted
	peerName := "incoming-peer"

	// 2. Network layer creates a connection and calls AddPeerConnection
	mockConn := NewMockNetworkConnection(peerName)
	syncCore.AddPeerConnection(mockConn)

	// Verify that a PeerHandler was created
	peerHandler := syncCore.GetPeerHandler(peerName)
	if peerHandler == nil {
		t.Fatal("Expected PeerHandler to be created for incoming peer")
	}

	// 3. Network layer starts receiving messages and calls HandlePeerMessage
	// Simulate receiving a file sync event
	event := types.FileSyncEvent{
		Type:      types.CREATE_FILE,
		Path:      "incoming_file.txt",
		Size:      100,
		MessageID: 1,
		Origin:    types.REMOTE_PEER,
		FileBody:  []byte("Hello from incoming peer"),
	}

	// This should work without any "no peer handler found" errors
	err := syncCore.HandlePeerMessage(peerName, types.MSG_FILE_SYNC_EVENT, event)
	if err != nil {
		t.Errorf("Expected no error handling message from incoming peer, got: %v", err)
	}

	// Verify the file was created locally
	expectedPath := filepath.Join(cfg.Folder.SyncPath, "incoming_file.txt")
	if _, err := mockFS.GetFileStat(expectedPath); err != nil {
		t.Errorf("Expected file to be created locally, but got error: %v", err)
	}

	// Verify status was saved
	statuses, _ := mockMeta.Load()
	if _, exists := statuses[peerName]; !exists {
		t.Errorf("Expected status for incoming peer to be saved")
	}
}

// TestSyncCore_ListenerModeBidirectionalSync tests that a listener server can both receive and send files
func TestSyncCore_ListenerModeBidirectionalSync(t *testing.T) {
	tmpDir := t.TempDir()

	// Create listener config (no pairs)
	listenerCfg := createMockConfig(tmpDir+"/listener", []types.PairConfig{}, types.FULL_FILE)

	// Create client config with one pair
	clientPairConfig := types.PairConfig{
		Name:                "listener",
		HostnameOrIP:        "127.0.0.1",
		Direction:           types.BIDIRECTIONAL_NEWEST_WIN,
		InitialSyncStrategy: types.SYNC,
		RemotePort:          8080,
	}
	clientCfg := createMockConfig(tmpDir+"/client", []types.PairConfig{clientPairConfig}, types.FULL_FILE)

	// Setup both sync cores
	listenerCore, listenerMeta, listenerFS, _ := setupTestSync(t, listenerCfg, false)
	clientCore, clientMeta, clientFS, _ := setupTestSync(t, clientCfg, false)

	// Create files on both sides before connection
	listenerFile := "listener_file.txt"
	listenerContent := []byte("Hello from listener")
	listenerFS.SetFileContent(filepath.Join(listenerCfg.Folder.SyncPath, listenerFile), listenerContent, &mockFileInfo{
		name:    listenerFile,
		size:    int64(len(listenerContent)),
		mode:    0644,
		modTime: time.Now(),
		isDir:   false,
	})

	clientFile := "client_file.txt"
	clientContent := []byte("Hello from client")
	clientFS.SetFileContent(filepath.Join(clientCfg.Folder.SyncPath, clientFile), clientContent, &mockFileInfo{
		name:    clientFile,
		size:    int64(len(clientContent)),
		mode:    0644,
		modTime: time.Now(),
		isDir:   false,
	})

	// Simulate connection establishment
	// Client connects to listener
	connClientToListener := NewMockNetworkConnection("listener")
	connListenerToClient := NewMockNetworkConnection("client")

	// Add connections
	clientCore.AddPeerConnection(connClientToListener)
	listenerCore.AddPeerConnection(connListenerToClient)

	// Give some time for sync processes to start
	time.Sleep(100 * time.Millisecond)

	// Verify peer handlers were created
	listenerPeerHandler := listenerCore.GetPeerHandler("client")
	clientPeerHandler := clientCore.GetPeerHandler("listener")

	if listenerPeerHandler == nil {
		t.Fatal("Expected listener to create peer handler for client")
	}
	if clientPeerHandler == nil {
		t.Fatal("Expected client to create peer handler for listener")
	}

	// Simulate initial sync - client sends its file to listener
	clientEvent := types.FileSyncEvent{
		Type:      types.CREATE_FILE,
		Path:      clientFile,
		Size:      uint64(len(clientContent)),
		MessageID: 1,
		Origin:    types.REMOTE_PEER,
		FileBody:  clientContent,
	}

	err := listenerCore.HandlePeerMessage("client", types.MSG_FILE_SYNC_EVENT, clientEvent)
	if err != nil {
		t.Errorf("Failed to handle client file sync event: %v", err)
	}

	// Simulate initial sync - listener sends its file to client
	listenerEvent := types.FileSyncEvent{
		Type:      types.CREATE_FILE,
		Path:      listenerFile,
		Size:      uint64(len(listenerContent)),
		MessageID: 2,
		Origin:    types.REMOTE_PEER,
		FileBody:  listenerContent,
	}

	err = clientCore.HandlePeerMessage("listener", types.MSG_FILE_SYNC_EVENT, listenerEvent)
	if err != nil {
		t.Errorf("Failed to handle listener file sync event: %v", err)
	}

	// Verify files were synchronized in both directions
	// Client file should exist on listener
	listenerClientFilePath := filepath.Join(listenerCfg.Folder.SyncPath, clientFile)
	if _, err := listenerFS.GetFileStat(listenerClientFilePath); err != nil {
		t.Errorf("Expected client file to be synced to listener, but got error: %v", err)
	} else {
		content, _ := listenerFS.ReadFile(listenerClientFilePath)
		if string(content) != string(clientContent) {
			t.Errorf("Expected client file content %q, got %q", string(clientContent), string(content))
		}
	}

	// Listener file should exist on client
	clientListenerFilePath := filepath.Join(clientCfg.Folder.SyncPath, listenerFile)
	if _, err := clientFS.GetFileStat(clientListenerFilePath); err != nil {
		t.Errorf("Expected listener file to be synced to client, but got error: %v", err)
	} else {
		content, _ := clientFS.ReadFile(clientListenerFilePath)
		if string(content) != string(listenerContent) {
			t.Errorf("Expected listener file content %q, got %q", string(listenerContent), string(content))
		}
	}

	// Verify status files were created on both sides
	listenerStatuses, _ := listenerMeta.Load()
	clientStatuses, _ := clientMeta.Load()

	if _, exists := listenerStatuses["client"]; !exists {
		t.Errorf("Expected listener to have status for client")
	}
	if _, exists := clientStatuses["listener"]; !exists {
		t.Errorf("Expected client to have status for listener")
	}
}

// TestSyncCore_QueueProcessingDebug tests queue processing with detailed logging
func TestSyncCore_QueueProcessingDebug(t *testing.T) {
	tmpDir := t.TempDir()

	// Create a listener config (no pairs) to simulate ca2
	cfg := createMockConfig(tmpDir, []types.PairConfig{}, types.FULL_FILE)

	syncCore, mockMeta, mockFS, eventQueue := setupTestSync(t, cfg, false)

	// Start the sync core to ensure peer readiness monitoring is running
	go syncCore.Start()
	defer syncCore.Close()

	// Give time for sync core to start and all goroutines to be running
	time.Sleep(200 * time.Millisecond)

	// Create a test file
	testFile := "test_queue.txt"
	testContent := []byte("Hello queue test")
	testPath := filepath.Join(cfg.Folder.SyncPath, testFile)
	mockFS.SetFileContent(testPath, testContent, &mockFileInfo{
		name:    testFile,
		size:    int64(len(testContent)),
		mode:    0644,
		modTime: time.Now(),
		isDir:   false,
	})

	// Add connection for the peer
	mockConn := NewMockNetworkConnection("testpeer")

	// Disable auto-acknowledgment for the queue to allow proper queue processing
	if queue, err := mockMeta.GetQueue("testpeer"); err == nil {
		if mockQueue, ok := queue.(*MockQueue); ok {
			mockQueue.DisableAutoAck()
		}
	}

	syncCore.AddPeerConnection(mockConn)

	// Give time for sync processes to start
	time.Sleep(200 * time.Millisecond)

	// Create a local event (this should be queued for the peer)
	localEvent := types.FileSyncEvent{
		Type:      types.CREATE_FILE,
		Path:      testFile,
		Size:      uint64(len(testContent)),
		MessageID: 1,
		Origin:    types.LOCAL_INOTIFY,
		FileBody:  testContent,
	}

	// Send the event to the sync core
	select {
	case eventQueue <- localEvent:
		t.Logf("Sent local event to sync core")
	case <-time.After(1 * time.Second):
		t.Fatal("Failed to send event to sync core")
	}

	// Give time for event processing and queue operations
	time.Sleep(1000 * time.Millisecond)

	// Check if the event was sent to the peer
	mockConn.mu.Lock()
	sentEvents := len(mockConn.sentEvents)
	mockConn.mu.Unlock()

	if sentEvents == 0 {
		t.Errorf("Expected at least 1 event to be sent to peer, got %d", sentEvents)

		// Check queue status for debugging
		peerHandler := syncCore.GetPeerHandler("testpeer")
		if peerHandler != nil {
			status := peerHandler.GetPairStatus()
			t.Logf("Peer status: %+v", status)
		}

		// Check meta manager queues
		statuses, _ := mockMeta.Load()
		t.Logf("Meta statuses: %+v", statuses)
	} else {
		t.Logf("Successfully sent %d events to peer", sentEvents)
	}
}

// Test QueueDuringInitialSync
func TestQueueDuringInitialSync(t *testing.T) {
	tmpDir := t.TempDir()
	cfg := createMockConfig(tmpDir, []types.PairConfig{}, types.FULL_FILE)

	syncCore, metaManager, fsUtil, _ := setupTestSync(t, cfg, false)
	testLogger, _ := logger.NewLogger(tmpDir, false)

	handler := NewInitialSyncHandler(cfg, metaManager, fsUtil, testLogger, syncCore)

	event := types.FileSyncEvent{
		Type:      types.CREATE_FILE,
		Path:      "test.txt",
		Size:      100,
		MessageID: 1,
	}

	// Test QueueDuringInitialSync (this is a void function)
	handler.QueueDuringInitialSync(event, "test-peer")
	// No error to check since it's void, just ensure it doesn't panic
}

// Test sendErrorAck indirectly through HandleRemoteEvent
func TestSendErrorAck(t *testing.T) {
	tmpDir := t.TempDir()
	cfg := createMockConfig(tmpDir, []types.PairConfig{}, types.FULL_FILE)

	syncCore, metaManager, mockFS, _ := setupTestSync(t, cfg, false)
	testLogger, _ := logger.NewLogger(tmpDir, false)

	peerConfig := types.PairConfig{
		Name:                "test-peer",
		HostnameOrIP:        "127.0.0.1",
		Direction:           types.BIDIRECTIONAL_NEWEST_WIN,
		InitialSyncStrategy: types.SYNC,
		RemotePort:          8080,
	}

	status := &types.PairStatus{
		Name:               "test-peer",
		InitialSyncStatus:  types.PENDING,
		CurrentQueueDBFile: filepath.Join(tmpDir, "test-peer.queue.db"),
	}

	handler := NewOngoingSyncHandler(cfg, metaManager, mockFS, testLogger, syncCore)

	event := types.FileSyncEvent{
		Type:      types.CREATE_FILE,
		Path:      "test.txt",
		Size:      100,
		MessageID: 1,
		FileBody:  []byte("test content"),
	}

	// Create a mock peer handler
	peerHandler := NewPeerHandler(peerConfig, status, syncCore, testLogger)
	syncCore.peerHandlers["test-peer"] = peerHandler

	// Set up mock FS to fail on AtomicWrite to trigger error path
	mockFS.atomicWriteFunc = func(filePath string, data []byte, perm os.FileMode) error {
		return fmt.Errorf("mock write error")
	}

	// Test HandleRemoteEvent with error condition
	err := handler.HandleRemoteEvent("test-peer", event)
	// This should trigger sendErrorAck internally
	if err == nil {
		t.Log("HandleRemoteEvent succeeded (may not have triggered error path)")
	}
}

// TestSyncCore_ConfiguredPeerConnection tests that when a peer with configured pairs
// connects, it doesn't create duplicate handlers
func TestSyncCore_ConfiguredPeerConnection(t *testing.T) {
	tmpDir := t.TempDir()

	// Create a config with a configured peer (like ca3 having ca2 configured)
	cfg := createMockConfig(tmpDir, []types.PairConfig{
		{Name: "ca2", HostnameOrIP: "127.0.0.1", RemotePort: 8080, Direction: types.BIDIRECTIONAL_NEWEST_WIN, InitialSyncStrategy: types.SYNC},
	}, types.FULL_FILE)

	syncCore, _, _, _ := setupTestSync(t, cfg, false)

	// Start the sync core to create the configured peer handler
	go syncCore.Start()
	time.Sleep(50 * time.Millisecond) // Let SyncCore start and create PeerHandlers

	// Ensure proper cleanup with sufficient time for goroutines to finish
	defer func() {
		syncCore.Close()
		time.Sleep(100 * time.Millisecond) // Give time for goroutines to finish
	}()

	// Verify that the peer handler was created for the configured peer
	handler := syncCore.GetPeerHandler("ca2")
	if handler == nil {
		t.Fatal("Expected peer handler for 'ca2' to be created from configuration")
	}

	// Simulate the network manager calling AddPeerConnection when ca2 connects
	mockConn := NewMockNetworkConnection("ca2")
	syncCore.AddPeerConnection(mockConn)

	// Verify that no duplicate handler was created
	handler2 := syncCore.GetPeerHandler("ca2")
	if handler != handler2 {
		t.Fatal("Expected the same peer handler instance, but got a different one (duplicate handler created)")
	}

	// Verify that the connection was set on the existing handler
	if handler.GetConnection() != mockConn {
		t.Fatal("Expected the connection to be set on the existing peer handler")
	}

	t.Log("Test passed: No duplicate peer handler was created for configured peer")
}

// TestSyncCore_NewPeerStatusCreation tests that new peer statuses are created correctly
func TestSyncCore_NewPeerStatusCreation(t *testing.T) {
	// This test verifies that when SyncCore creates a new peer status,
	// it has the correct initial state (PENDING for initial sync)

	// Create temporary directories
	tempDir := t.TempDir()
	syncPath := filepath.Join(tempDir, "sync")

	// Create config with a peer
	cfg := createMockConfig(syncPath, []types.PairConfig{
		{
			Name:                "test-peer",
			HostnameOrIP:        "localhost",
			Direction:           types.BIDIRECTIONAL_NEWEST_WIN,
			InitialSyncStrategy: types.SYNC,
			RemotePort:          8080,
		},
	}, types.FULL_FILE)

	// Use a mock meta manager
	mockMetaManager := NewMockMetaManager()

	// Create SyncCore - this should create a new peer status
	fsUtil := NewMockFSUtil()
	eventQueue := make(chan types.FileSyncEvent, 10)
	syncCore := NewSyncCore(cfg, mockMetaManager, fsUtil, eventQueue)

	// Start the sync core to initialize peer handlers
	go syncCore.Start()
	time.Sleep(50 * time.Millisecond) // Give it time to start
	defer syncCore.Close()

	// Verify that the peer handler was created with the correct status
	peerHandler := syncCore.GetPeerHandler("test-peer")
	if peerHandler == nil {
		t.Fatal("Expected peer handler to exist")
	}

	if peerHandler.GetInitialSyncStatus() != types.PENDING {
		t.Errorf("Expected peer handler initial sync status to be PENDING, got %s", peerHandler.GetInitialSyncStatus())
	}

	t.Log("Test passed: New peer status is created with PENDING status")
}
