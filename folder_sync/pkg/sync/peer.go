package sync

import (
	"fmt"
	"sync"
	"time"

	"github.com/real-rm/folder_sync/pkg/logger"
	"github.com/real-rm/folder_sync/pkg/types"
)

// PeerHandler manages the synchronization logic for a single paired server.
type PeerHandler struct {
	pairConfig           types.PairConfig
	pairStatus           *types.PairStatus
	core                 *SyncCore         // Reference back to the SyncCore
	conn                 NetworkConnection // The active connection to this peer
	queue                MetaQueue         // The outgoing event queue for this peer
	log                  *logger.Logger
	done                 chan struct{}
	wg                   sync.WaitGroup
	connMu               sync.RWMutex  // Protects access to 'conn'
	statusMu             sync.RWMutex  // Protects access to 'pairStatus'
	syncLoopDone         chan struct{} // Signals the syncLoop goroutine to stop
	isClientConnection   bool          // True if this is a client-side connection
	initialSyncDone      chan struct{} // Signals that initial sync for this peer is complete
	syncProcessesMu      sync.Mutex    // Protects against concurrent PerformSyncProcesses calls
	syncProcessesStarted bool          // Flag to track if PerformSyncProcesses has been called
	closing              bool          // Flag to indicate if Close() has been called
}

// NewPeerHandler creates a new PeerHandler.
func NewPeerHandler(
	pairConfig types.PairConfig,
	pairStatus *types.PairStatus,
	core *SyncCore,
	log *logger.Logger,
) *PeerHandler {
	queueInterface, err := core.metaManager.GetQueue(pairConfig.Name)
	if err != nil {
		log.Fatal("Failed to get queue for peer %q: %v", pairConfig.Name, err) // Fatal if queue cannot be retrieved
	}
	queue, ok := queueInterface.(MetaQueue)
	if !ok {
		log.Fatal("Failed to cast queue to MetaQueue for peer %q", pairConfig.Name)
	}

	// Create a temporary logger for peer handler - in a real implementation, this should be passed in
	peerLogger, err := logger.NewLogger(core.cfg.Folder.LogsPath, false)
	if err != nil {
		// Fallback to memory logger if we can't create file logger
		peerLogger = logger.NewMemoryLogger()
	}

	return &PeerHandler{
		pairConfig:      pairConfig,
		pairStatus:      pairStatus,
		core:            core,
		queue:           queue,
		log:             peerLogger,
		done:            make(chan struct{}),
		syncLoopDone:    make(chan struct{}),
		initialSyncDone: make(chan struct{}),
	}
}

// SetConnection sets the active network connection for this peer.
// This is called by SyncCore (via network.Manager) when a connection is established or lost.
func (ph *PeerHandler) SetConnection(conn NetworkConnection) {
	ph.connMu.Lock()
	defer ph.connMu.Unlock()
	ph.conn = conn
	if conn != nil {
		ph.log.Info("Connection established with peer %q.", ph.pairConfig.Name)
		// Reset connection retry count on successful connection
		ph.pairStatus.ConnectionRetries = 0
		if err := ph.core.metaManager.Save(map[string]*types.PairStatus{ph.pairConfig.Name: ph.pairStatus}); err != nil {
			ph.log.Error("Failed to save pair status for %q after connection reset: %v", ph.pairConfig.Name, err)
		}

		// If connection just established, kick off sync if not already running
		select {
		case ph.core.peersReady <- ph.pairConfig.Name:
			ph.log.Debug("Signaled peer %q as ready from SetConnection.", ph.pairConfig.Name)
		default:
			ph.log.Warn("peersReady channel full, could not signal %q from SetConnection.", ph.pairConfig.Name)
		}

		// Start message handling goroutines if connection is new
		ph.wg.Add(1)
		go func() {
			defer ph.wg.Done()
			ph.handleIncomingMessages()
		}()
	} else {
		ph.log.Warn("Connection to peer %q lost.", ph.pairConfig.Name)
	}
}

// GetConnection returns the current active network connection for this peer.
func (ph *PeerHandler) GetConnection() NetworkConnection {
	ph.connMu.RLock()
	defer ph.connMu.RUnlock()
	return ph.conn
}

// SetIsClientConnection marks this PeerHandler as a client-side connection
func (ph *PeerHandler) SetIsClientConnection(isClient bool) {
	ph.isClientConnection = isClient
}

// GetPairConfig returns the pair configuration for this peer.
func (ph *PeerHandler) GetPairConfig() types.PairConfig {
	return ph.pairConfig
}

// Start launches the goroutine for the peer's main sync loop.
func (ph *PeerHandler) Start() {
	ph.log.Info("Starting PeerHandler for %q...", ph.pairConfig.Name)
	ph.wg.Add(1)
	go func() {
		defer ph.wg.Done()
		ph.syncLoop()
	}()
}

// Close signals the PeerHandler to shut down gracefully.
func (ph *PeerHandler) Close() {
	ph.log.Info("Closing PeerHandler for %q...", ph.pairConfig.Name)

	// Set closing flag to prevent new goroutines from starting
	ph.syncProcessesMu.Lock()
	ph.closing = true
	ph.syncProcessesMu.Unlock()

	close(ph.done) // Signal shutdown to the main syncLoop

	// Ensure any active network connection is closed
	if conn := ph.GetConnection(); conn != nil {
		if err := conn.Close(); err != nil {
			ph.log.Warn("Failed to close network connection for %q: %v", ph.pairConfig.Name, err)
		}
	}

	ph.wg.Wait() // Wait for all internal goroutines to finish

	if err := ph.queue.Close(); err != nil {
		ph.log.Error("Failed to close queue for peer %q: %v", ph.pairConfig.Name, err)
	}
	// Save final status immediately during shutdown
	if err := ph.core.metaManager.SaveImmediately(map[string]*types.PairStatus{ph.pairConfig.Name: ph.GetPairStatus()}); err != nil {
		ph.log.Error("Failed to save final pair status for %q: %v", ph.pairConfig.Name, err)
	}

	ph.log.Info("PeerHandler for %q gracefully shut down.", ph.pairConfig.Name)
}

// QueueOutgoingEvent adds a FileSyncEvent to this peer's outgoing queue.
func (ph *PeerHandler) QueueOutgoingEvent(event types.FileSyncEvent) error {
	// Only queue events if this server is configured to send to this peer
	if ph.pairConfig.Direction != types.ONE_WAY_TO_PEER && ph.pairConfig.Direction != types.BIDIRECTIONAL_NEWEST_WIN {
		ph.log.Debug("Not queueing event %s for peer %q (direction not outbound)", event.String(), ph.pairConfig.Name)
		return nil
	}

	// If in initial sync, queue it. Otherwise, add to the main queue.
	// This ensures events happening during initial scan are not lost.
	select {
	case <-ph.initialSyncDone: // If initial sync is done, proceed to normal queueing
		return ph.queue.AddEvent(event)
	default: // Initial sync is still ongoing or not started
		// During initial sync, events should be temporarily held or added to a dedicated "during initial sync" queue.
		// For simplicity now, we will just add to the main queue, and the initial sync will ignore already queued items.
		// A more robust implementation would use initialSyncHandler.QueueDuringInitialSync
		ph.log.Debug("Initial sync for %q ongoing, queueing event: %s", ph.pairConfig.Name, event.String())
		return ph.queue.AddEvent(event)
	}
}

// PerformSyncProcesses orchestrates the initial sync and then ongoing sync for this peer.
func (ph *PeerHandler) PerformSyncProcesses() {
	// Prevent concurrent execution of this method
	ph.syncProcessesMu.Lock()
	if ph.syncProcessesStarted {
		ph.syncProcessesMu.Unlock()
		ph.log.Debug("PerformSyncProcesses already started for peer %q, skipping duplicate call", ph.pairConfig.Name)
		return
	}
	ph.syncProcessesStarted = true
	ph.syncProcessesMu.Unlock()
	// 1. Perform Initial Sync if configured
	// Only server side (incoming connections) should perform initial sync to avoid duplication
	if ph.pairConfig.InitialSyncStrategy == types.SYNC && ph.GetInitialSyncStatus() != types.COMPLETED && !ph.isClientConnection {
		ph.log.Info("Starting initial sync for peer %q (strategy: %s)", ph.pairConfig.Name, ph.pairConfig.InitialSyncStrategy)

		// Wait for connection to be established with timeout
		var conn NetworkConnection
		timeout := time.After(5 * time.Second)
		ticker := time.NewTicker(100 * time.Millisecond)
		defer ticker.Stop()

		for {
			conn = ph.GetConnection()
			if conn != nil && conn.IsConnected() {
				break
			}

			select {
			case <-timeout:
				ph.log.Warn("Cannot perform initial sync for %q: no active connection after timeout.", ph.pairConfig.Name)
				return // Exit if no connection after timeout
			case <-ticker.C:
				// Continue waiting
			case <-ph.done:
				ph.log.Debug("PeerHandler for %q shutting down during connection wait.", ph.pairConfig.Name)
				return
			}
		}

		if ph.core.initialSyncHandler == nil {
			ph.log.Error("initialSyncHandler is nil for peer %q", ph.pairConfig.Name)
			return
		}
		err := ph.core.initialSyncHandler.PerformInitialSync(ph.pairConfig.Name, &ph.pairConfig, ph.GetPairStatus(), conn)
		if err != nil {
			ph.log.Error("Initial sync for peer %q failed: %v", ph.pairConfig.Name, err)
			ph.SetInitialSyncStatus(types.FAILED)
		} else {
			ph.log.Info("Initial sync for peer %q completed successfully.", ph.pairConfig.Name)
			ph.SetInitialSyncStatus(types.COMPLETED)
		}
		if err := ph.core.metaManager.SaveImmediately(map[string]*types.PairStatus{ph.pairConfig.Name: ph.GetPairStatus()}); err != nil {
			ph.log.Error("Failed to save pair status for %q after initial sync: %v", ph.pairConfig.Name, err)
		}
	} else if ph.pairConfig.InitialSyncStrategy == types.NO_INITIAL_SYNC {
		ph.log.Info("Skipping initial sync for peer %q (strategy: %s)", ph.pairConfig.Name, ph.pairConfig.InitialSyncStrategy)
		ph.SetInitialSyncStatus(types.COMPLETED)
		if err := ph.core.metaManager.SaveImmediately(map[string]*types.PairStatus{ph.pairConfig.Name: ph.GetPairStatus()}); err != nil {
			ph.log.Error("Failed to save pair status for %q after skipping initial sync: %v", ph.pairConfig.Name, err)
		}
	} else if ph.isClientConnection {
		ph.log.Info("Skipping initial sync for peer %q (client-side connection, server will handle initial sync)", ph.pairConfig.Name)
		ph.SetInitialSyncStatus(types.COMPLETED)
		if err := ph.core.metaManager.Save(map[string]*types.PairStatus{ph.pairConfig.Name: ph.GetPairStatus()}); err != nil {
			ph.log.Error("Failed to save pair status for %q after skipping client-side initial sync: %v", ph.pairConfig.Name, err)
		}
	} else {
		ph.log.Info("Initial sync for peer %q already completed or strategy is NO_INITIAL_SYNC and status is COMPLETED. Skipping.", ph.pairConfig.Name)
	}
	close(ph.initialSyncDone) // Signal that initial sync is done for this peer

	// 2. Start Ongoing Sync (process outbound queue)
	ph.log.Info("Starting ongoing sync for peer %q.", ph.pairConfig.Name)

	// Check if we're closing before starting new goroutines
	ph.syncProcessesMu.Lock()
	if ph.closing {
		ph.syncProcessesMu.Unlock()
		ph.log.Debug("PeerHandler for %q is closing, skipping ongoing sync startup", ph.pairConfig.Name)
		return
	}
	ph.wg.Add(1)
	ph.syncProcessesMu.Unlock()

	go func() {
		defer ph.wg.Done()
		ph.core.ongoingSyncHandler.ProcessPeerQueue(ph.pairConfig.Name)
	}()
}

// syncLoop manages the lifecycle of the PeerHandler, primarily for handling reconnections.
func (ph *PeerHandler) syncLoop() {
	defer close(ph.syncLoopDone)
	reconnectInterval := 5 * time.Second

	for {
		conn := ph.GetConnection()
		if conn == nil || !conn.IsConnected() {
			ph.log.Debug("Peer %q not connected. Waiting for connection...", ph.pairConfig.Name)
			// Wait for connection to be set or for done signal
			select {
			case <-ph.done:
				ph.log.Debug("PeerHandler syncLoop for %q exiting due to done signal.", ph.pairConfig.Name)
				return
			case <-time.After(reconnectInterval):
				// Just loop and check again
				continue
			}
		}

		select {
		case <-ph.done:
			ph.log.Debug("PeerHandler syncLoop for %q exiting due to done signal.", ph.pairConfig.Name)
			return
		case <-time.After(reconnectInterval):
			// If connected, this is just a heartbeat or a periodic check.
			ph.log.Debug("PeerHandler for %q is connected. Periodically checking for new tasks.", ph.pairConfig.Name)
		}
	}
}

// handleIncomingMessages is no longer needed as message handling is done by the network layer.
// The network layer's ReadLoop calls HandleMessage which forwards to SyncCore.HandlePeerMessage.
// This method is kept for interface compatibility but does nothing.
func (ph *PeerHandler) handleIncomingMessages() {
	ph.log.Debug("Message handling for peer %q is managed by network layer, no action needed.", ph.pairConfig.Name)
	// Messages are handled by: NetworkLayer.ReadLoop -> SyncMessageHandler.HandleMessage -> SyncCore.HandlePeerMessage
	// This goroutine just waits for shutdown signal
	<-ph.done
}

// SendMessage provides a way for PeerHandler to send a message via its network connection.
// This is used by InitialSyncHandler and OngoingSyncHandler via the SyncCore reference.
func (ph *PeerHandler) SendMessage(msgType types.MessageType, payload interface{}) error {
	conn := ph.GetConnection()
	if conn == nil || !conn.IsConnected() {
		return fmt.Errorf("no active connection to peer %q to send message type %s", ph.pairConfig.Name, msgType.String())
	}
	return conn.SendMessage(msgType, payload)
}

// GetInitialSyncStatus safely gets the initial sync status.
func (ph *PeerHandler) GetInitialSyncStatus() types.InitialSyncStatus {
	ph.statusMu.RLock()
	defer ph.statusMu.RUnlock()
	return ph.pairStatus.InitialSyncStatus
}

// SetInitialSyncStatus safely sets the initial sync status.
func (ph *PeerHandler) SetInitialSyncStatus(status types.InitialSyncStatus) {
	ph.statusMu.Lock()
	defer ph.statusMu.Unlock()
	ph.pairStatus.InitialSyncStatus = status
}

// GetPairStatus safely gets a copy of the pair status.
func (ph *PeerHandler) GetPairStatus() *types.PairStatus {
	ph.statusMu.RLock()
	defer ph.statusMu.RUnlock()
	// Return a copy to avoid external modification
	statusCopy := types.PairStatus{
		Name:                ph.pairStatus.Name,
		LastSyncedTimestamp: ph.pairStatus.LastSyncedTimestamp,
		InitialSyncStatus:   ph.pairStatus.InitialSyncStatus,
		ConnectionRetries:   ph.pairStatus.ConnectionRetries,
		TotalSent:           ph.pairStatus.TotalSent.Copy(),
		TotalReceived:       ph.pairStatus.TotalReceived.Copy(),
	}
	return &statusCopy
}

// UpdateTotalReceived safely updates the TotalReceived stats.
func (ph *PeerHandler) UpdateTotalReceived(eventType types.FileSyncEventType, bytes uint64) {
	ph.statusMu.Lock()
	defer ph.statusMu.Unlock()
	ph.pairStatus.TotalReceived.Update(eventType, bytes)
}
