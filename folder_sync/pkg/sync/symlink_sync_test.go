package sync

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/fsutil"
	"github.com/real-rm/folder_sync/pkg/logger"
	"github.com/real-rm/folder_sync/pkg/types"
)

// TestSymlinkSyncBehavior tests that symlinks are properly synced as symlinks, not as file content
func TestSymlinkSyncBehavior(t *testing.T) {
	// Create temporary directories for testing
	tempDir, err := os.MkdirTemp("", "symlink_sync_test")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tempDir)

	// Create source and destination sync directories
	srcDir := filepath.Join(tempDir, "src")
	dstDir := filepath.Join(tempDir, "dst")

	if err := os.MkdirAll(srcDir, 0755); err != nil {
		t.Fatal(err)
	}
	if err := os.Mkdir<PERSON>ll(dstDir, 0755); err != nil {
		t.Fatal(err)
	}

	// Create a target file in source
	targetFile := filepath.Join(srcDir, "target.txt")
	targetContent := "This is the target file content"
	if err := os.WriteFile(targetFile, []byte(targetContent), 0644); err != nil {
		t.Fatal(err)
	}

	// Create a subdirectory with a file
	subDir := filepath.Join(srcDir, "subdir")
	if err := os.MkdirAll(subDir, 0755); err != nil {
		t.Fatal(err)
	}
	subFile := filepath.Join(subDir, "subfile.txt")
	subContent := "This is the sub file content"
	if err := os.WriteFile(subFile, []byte(subContent), 0644); err != nil {
		t.Fatal(err)
	}

	testCases := []struct {
		name        string
		linkPath    string
		target      string
		shouldSync  bool
		description string
	}{
		{
			name:        "RelativeSymlinkSameDir",
			linkPath:    "link_to_target.txt",
			target:      "target.txt",
			shouldSync:  true,
			description: "Relative symlink to file in same directory",
		},
		{
			name:        "RelativeSymlinkSubdir",
			linkPath:    "link_to_subfile.txt",
			target:      "subdir/subfile.txt",
			shouldSync:  true,
			description: "Relative symlink to file in subdirectory",
		},
		{
			name:        "RelativeSymlinkFromSubdir",
			linkPath:    "subdir/link_to_parent.txt",
			target:      "../target.txt",
			shouldSync:  true,
			description: "Relative symlink from subdirectory to parent",
		},
		{
			name:        "AbsoluteSymlinkInside",
			linkPath:    "abs_link_inside.txt",
			target:      targetFile,
			shouldSync:  true,
			description: "Absolute symlink pointing inside sync directory",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create the symlink in source directory
			srcLinkPath := filepath.Join(srcDir, tc.linkPath)
			if err := os.MkdirAll(filepath.Dir(srcLinkPath), 0755); err != nil {
				t.Fatal(err)
			}
			if err := os.Symlink(tc.target, srcLinkPath); err != nil {
				t.Fatal(err)
			}
			defer os.Remove(srcLinkPath)

			// Verify the symlink was created correctly
			linkInfo, err := os.Lstat(srcLinkPath)
			if err != nil {
				t.Fatalf("Failed to stat source symlink: %v", err)
			}
			if linkInfo.Mode()&os.ModeSymlink == 0 {
				t.Fatal("Source link is not a symlink")
			}

			// Read the symlink target to verify
			actualTarget, err := os.Readlink(srcLinkPath)
			if err != nil {
				t.Fatalf("Failed to read symlink target: %v", err)
			}
			if actualTarget != tc.target {
				t.Fatalf("Symlink target mismatch: expected %q, got %q", tc.target, actualTarget)
			}

			// Test initial sync behavior
			t.Run("InitialSync", func(t *testing.T) {
				testSymlinkInitialSync(t, srcDir, dstDir, tc.linkPath, tc.target, tc.shouldSync)
			})

			// Test ongoing sync behavior (inotify events)
			t.Run("OngoingSync", func(t *testing.T) {
				testSymlinkOngoingSync(t, srcDir, dstDir, tc.linkPath, tc.target, tc.shouldSync)
			})
		})
	}
}

func testSymlinkInitialSync(t *testing.T, srcDir, dstDir, linkPath, target string, shouldSync bool) {
	// Create mock components for initial sync
	cfg := &config.Config{}
	cfg.Folder.SyncPath = srcDir
	cfg.Folder.Compares = types.SIZE_ONLY

	mockLogger := logger.NewMemoryLogger()

	// Use real FSUtil for testing symlink logic
	fsUtil := NewMockFSUtil()
	// Set up the mock to use real symlink operations
	fsUtil.extractSymlinkTargetFunc = func(path string) (string, error) {
		return os.Readlink(path)
	}

	metaManager := &MockMetaManager{}

	// Create initial sync handler (not used directly, but validates the setup)
	_ = NewInitialSyncHandler(cfg, metaManager, fsUtil, mockLogger, nil)

	// Scan the source directory
	srcLinkPath := filepath.Join(srcDir, linkPath)
	relPath := linkPath

	// Get file info
	info, err := os.Lstat(srcLinkPath)
	if err != nil {
		t.Fatalf("Failed to stat symlink: %v", err)
	}

	// Manually test the symlink detection logic by simulating what happens in initial sync
	var event *types.FileSyncEvent

	// Check if it's a symlink
	if (info.Mode() & os.ModeSymlink) != 0 {
		symlinkTarget, err := fsUtil.ExtractSymlinkTarget(srcLinkPath)
		if err != nil {
			t.Fatalf("Failed to read symlink target: %v", err)
		}

		// Check if symlink should be synced
		if fsUtil.IsSymlinkWithinSyncRoot(srcLinkPath, symlinkTarget, srcDir) {
			event = &types.FileSyncEvent{
				Type:      types.CREATE_SYMLINK,
				Path:      relPath,
				Timestamp: info.ModTime(),
				Size:      0,
				FileMode:  uint32(info.Mode().Perm()),
				FileBody:  []byte(symlinkTarget),
				Origin:    types.LOCAL_INOTIFY,
			}
		}
	}

	if !shouldSync {
		if event != nil {
			t.Errorf("Expected symlink to be ignored, but got event: %v", event)
		}
		return
	}

	if event == nil {
		t.Fatal("Expected symlink event to be created, but got nil")
	}

	// Verify the event is CREATE_SYMLINK
	if event.Type != types.CREATE_SYMLINK {
		t.Errorf("Expected CREATE_SYMLINK event, got %v", event.Type)
	}

	// Verify the target is stored in FileBody
	if string(event.FileBody) != target {
		t.Errorf("Expected symlink target %q in FileBody, got %q", target, string(event.FileBody))
	}

	t.Logf("✅ Initial sync correctly created CREATE_SYMLINK event for %q -> %q", linkPath, target)
}

func testSymlinkOngoingSync(t *testing.T, srcDir, dstDir, linkPath, target string, shouldSync bool) {
	// Test the CREATE_SYMLINK event handling directly using fsutil.CreateSymlink

	// Create a CREATE_SYMLINK event
	event := types.FileSyncEvent{
		Type:      types.CREATE_SYMLINK,
		Path:      linkPath,
		Timestamp: time.Now(),
		FileMode:  0777, // Symlink permissions
		FileBody:  []byte(target),
		MessageID: 1,
		Origin:    types.REMOTE_PEER,
	}

	// Test the core symlink creation logic directly
	dstLinkPath := filepath.Join(dstDir, linkPath)
	targetPath := string(event.FileBody)

	// Ensure parent directory exists
	if err := os.MkdirAll(filepath.Dir(dstLinkPath), 0755); err != nil {
		t.Fatalf("Failed to create parent directory: %v", err)
	}

	// Use the same function that ongoing sync uses
	if err := fsutil.CreateSymlink(targetPath, dstLinkPath); err != nil {
		t.Fatalf("Failed to create symlink using fsutil.CreateSymlink: %v", err)
	}

	// Verify the symlink was created in destination
	linkInfo, err := os.Lstat(dstLinkPath)
	if err != nil {
		t.Fatalf("Symlink was not created at destination: %v", err)
	}

	// Verify it's actually a symlink
	if linkInfo.Mode()&os.ModeSymlink == 0 {
		t.Errorf("Destination file is not a symlink, mode: %v", linkInfo.Mode())

		// Read the file content to see what was created instead
		if content, err := os.ReadFile(dstLinkPath); err == nil {
			t.Errorf("File content instead of symlink: %q", string(content))
		}
		return
	}

	// Verify the symlink target
	actualTarget, err := os.Readlink(dstLinkPath)
	if err != nil {
		t.Fatalf("Failed to read destination symlink target: %v", err)
	}

	if actualTarget != target {
		t.Errorf("Symlink target mismatch: expected %q, got %q", target, actualTarget)
	}

	t.Logf("✅ fsutil.CreateSymlink correctly created symlink %q -> %q", linkPath, target)
}
