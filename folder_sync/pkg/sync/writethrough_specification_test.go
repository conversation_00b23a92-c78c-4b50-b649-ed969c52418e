package sync_test

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/fsutil"
	"github.com/real-rm/folder_sync/pkg/logger"
	"github.com/real-rm/folder_sync/pkg/meta"
	"github.com/real-rm/folder_sync/pkg/sync"
	"github.com/real-rm/folder_sync/pkg/types"
)

// TestWritethroughMode tests the writethrough functionality as documented in section 4.7
func TestWritethroughMode(t *testing.T) {
	t.Run("writethrough mode basic operation", func(t *testing.T) {
		// Create temporary directories
		tmpDir := t.TempDir()
		syncDir := filepath.Join(tmpDir, "sync")
		metaDir := filepath.Join(tmpDir, "meta")
		logsDir := filepath.Join(tmpDir, "logs")

		for _, dir := range []string{syncDir, metaDir, logsDir} {
			if err := os.Mkdir<PERSON>ll(dir, 0755); err != nil {
				t.Fatal(err)
			}
		}

		// Create configuration for writethrough mode
		cfg := &config.Config{}
		cfg.Folder.SyncPath = syncDir
		cfg.Folder.MetaPath = metaDir
		cfg.Folder.LogsPath = logsDir
		cfg.Folder.Writethrough = true

		// Create logger and meta manager
		testLogger, err := logger.NewLogger(logsDir, false)
		if err != nil {
			t.Fatal(err)
		}

		metaManager, err := meta.NewMetaManager(metaDir, testLogger)
		if err != nil {
			t.Fatal(err)
		}

		// Create FSUtil
		fsUtil := fsutil.NewFSUtil(syncDir)

		// Create event channel
		eventQueue := make(chan types.FileSyncEvent, 10)

		// Create sync core
		_ = sync.NewSyncCore(cfg, metaManager, fsUtil, eventQueue)

		// Test: In writethrough mode, no initial sync should be performed
		// This is documented: "If writethrough=true, the sync folder acts as a buffer,
		// and no initial sync shall be performed"

		// Create a file in sync directory before starting
		testFile := filepath.Join(syncDir, "existing.txt")
		if err := os.WriteFile(testFile, []byte("existing content"), 0644); err != nil {
			t.Fatal(err)
		}

		// Verify the file exists before writethrough operation
		if _, err := os.Stat(testFile); os.IsNotExist(err) {
			t.Fatal("Test file should exist before writethrough test")
		}

		t.Log("Writethrough mode test setup completed - no initial sync should occur")
	})

	t.Run("writethrough cleanup after all peers acknowledge", func(t *testing.T) {
		// Create temporary directories
		tmpDir := t.TempDir()
		syncDir := filepath.Join(tmpDir, "sync")
		metaDir := filepath.Join(tmpDir, "meta")
		logsDir := filepath.Join(tmpDir, "logs")

		for _, dir := range []string{syncDir, metaDir, logsDir} {
			if err := os.MkdirAll(dir, 0755); err != nil {
				t.Fatal(err)
			}
		}

		// Create configuration for writethrough mode
		cfg := &config.Config{}
		cfg.Folder.SyncPath = syncDir
		cfg.Folder.MetaPath = metaDir
		cfg.Folder.LogsPath = logsDir
		cfg.Folder.Writethrough = true

		// Create logger and meta manager
		testLogger, err := logger.NewLogger(logsDir, false)
		if err != nil {
			t.Fatal(err)
		}

		metaManager, err := meta.NewMetaManager(metaDir, testLogger)
		if err != nil {
			t.Fatal(err)
		}

		// Create FSUtil
		fsUtil := fsutil.NewFSUtil(syncDir)

		// Create event channel
		eventQueue := make(chan types.FileSyncEvent, 10)

		// Create sync core
		_ = sync.NewSyncCore(cfg, metaManager, fsUtil, eventQueue)

		// Test: File should be removed from buffer after all peers acknowledge
		// This is documented: "Once a file/directory modification has been successfully
		// applied and acknowledged by all currently active paired servers, the corresponding
		// local buffer file will be automatically removed"

		// Create a buffer file
		bufferFile := filepath.Join(syncDir, "buffer.txt")
		if err := os.WriteFile(bufferFile, []byte("buffer content"), 0644); err != nil {
			t.Fatal(err)
		}

		// Simulate proxy socket event (writethrough only receives events via proxy socket)
		event := types.FileSyncEvent{
			Type:      types.CREATE_FILE,
			Path:      "buffer.txt",
			Size:      uint64(len("buffer content")),
			MessageID: 1,
			FileBody:  []byte("buffer content"),
		}

		// Send event to queue
		select {
		case eventQueue <- event:
		case <-time.After(1 * time.Second):
			t.Fatal("Failed to send event to queue")
		}

		t.Log("Writethrough cleanup test setup completed")
		// TODO: Implement actual cleanup verification after peer acknowledgments
	})

	t.Run("writethrough recursive directory creation", func(t *testing.T) {
		// Create temporary directories
		tmpDir := t.TempDir()
		syncDir := filepath.Join(tmpDir, "sync")
		metaDir := filepath.Join(tmpDir, "meta")
		logsDir := filepath.Join(tmpDir, "logs")

		for _, dir := range []string{syncDir, metaDir, logsDir} {
			if err := os.MkdirAll(dir, 0755); err != nil {
				t.Fatal(err)
			}
		}

		// Test: Recursive directory creation in writethrough mode
		// This is documented: "If a new file is created or written into a non-existing folder,
		// the folder and any parent directories must be created recursively on-the-fly"

		// Create event for file in nested directory that doesn't exist
		event := types.FileSyncEvent{
			Type:      types.CREATE_FILE,
			Path:      "deep/nested/path/file.txt",
			Size:      uint64(len("nested content")),
			MessageID: 1,
			FileBody:  []byte("nested content"),
		}

		// The sync core should create the nested directories automatically
		nestedDir := filepath.Join(syncDir, "deep", "nested", "path")
		_ = filepath.Join(nestedDir, "file.txt")

		// Verify directories don't exist initially
		if _, err := os.Stat(nestedDir); !os.IsNotExist(err) {
			t.Fatal("Nested directory should not exist initially")
		}

		// TODO: Process the event and verify recursive directory creation
		t.Logf("Event for nested file: %+v", event)
		t.Log("Writethrough recursive directory creation test setup completed")
	})

	t.Run("writethrough only listens to proxy socket", func(t *testing.T) {
		// Test: In writethrough mode, server only receives events via proxy socket
		// This is documented: "The server will not monitor the sync folder via inotify.
		// It only receives events via the proxy socket"

		// Create temporary directories
		tmpDir := t.TempDir()
		syncDir := filepath.Join(tmpDir, "sync")
		metaDir := filepath.Join(tmpDir, "meta")
		logsDir := filepath.Join(tmpDir, "logs")

		for _, dir := range []string{syncDir, metaDir, logsDir} {
			if err := os.MkdirAll(dir, 0755); err != nil {
				t.Fatal(err)
			}
		}

		// Create configuration for writethrough mode
		cfg := &config.Config{}
		cfg.Folder.SyncPath = syncDir
		cfg.Folder.MetaPath = metaDir
		cfg.Folder.LogsPath = logsDir
		cfg.Folder.Writethrough = true
		cfg.Monitor.SocketPath = filepath.Join(tmpDir, "proxy.sock")

		// Verify that writethrough requires monitor.socket
		// This is documented: "Validate writethrough must accompany monitor.socket"
		if cfg.Folder.Writethrough && cfg.Monitor.SocketPath == "" {
			t.Error("Writethrough mode should require monitor.socket configuration")
		}

		// Create a file directly in the sync folder (simulating inotify event)
		directFile := filepath.Join(syncDir, "direct.txt")
		if err := os.WriteFile(directFile, []byte("direct content"), 0644); err != nil {
			t.Fatal(err)
		}

		// In writethrough mode, this direct file creation should NOT trigger sync
		// Only events from proxy socket should be processed
		t.Log("Writethrough proxy-only mode test setup completed")
	})

	t.Run("writethrough partial acknowledgments", func(t *testing.T) {
		// Test: File remains in buffer until ALL peers acknowledge
		// This is documented: "Handling of partial acknowledgments
		// (file remains until all peers acknowledge)"

		// Create temporary directories
		tmpDir := t.TempDir()
		syncDir := filepath.Join(tmpDir, "sync")
		metaDir := filepath.Join(tmpDir, "meta")
		logsDir := filepath.Join(tmpDir, "logs")

		for _, dir := range []string{syncDir, metaDir, logsDir} {
			if err := os.MkdirAll(dir, 0755); err != nil {
				t.Fatal(err)
			}
		}

		// Create configuration with multiple peers
		cfg := &config.Config{}
		cfg.Folder.SyncPath = syncDir
		cfg.Folder.MetaPath = metaDir
		cfg.Folder.LogsPath = logsDir
		cfg.Folder.Writethrough = true

		// Create multiple peer configs
		peer1 := types.PairConfig{
			Name:         "peer1",
			HostnameOrIP: "127.0.0.1",
			RemotePort:   8081,
		}
		peer2 := types.PairConfig{
			Name:         "peer2",
			HostnameOrIP: "127.0.0.1",
			RemotePort:   8082,
		}

		cfg.Pairs = []types.PairConfig{peer1, peer2}

		// Create buffer file
		bufferFile := filepath.Join(syncDir, "partial.txt")
		if err := os.WriteFile(bufferFile, []byte("partial content"), 0644); err != nil {
			t.Fatal(err)
		}

		// Simulate acknowledgment from only one peer
		// File should remain in buffer until both peers acknowledge
		t.Log("Writethrough partial acknowledgments test setup completed")
		// TODO: Implement actual partial acknowledgment tracking
	})
}

// TestWritethroughValidation tests writethrough configuration validation
func TestWritethroughValidation(t *testing.T) {
	t.Run("writethrough requires monitor socket", func(t *testing.T) {
		// Test the documented requirement: "Validate writethrough must accompany monitor.socket"
		cfg := &config.Config{}
		cfg.Folder.Writethrough = true
		cfg.Monitor.SocketPath = "" // Missing socket path

		// This should be invalid configuration
		if cfg.Folder.Writethrough && cfg.Monitor.SocketPath == "" {
			t.Log("Correctly detected invalid writethrough configuration without monitor.socket")
		} else {
			t.Error("Should detect invalid writethrough configuration")
		}
	})

	t.Run("writethrough conflicts with readonly", func(t *testing.T) {
		// Test configuration conflicts as documented in section 4.1
		cfg := &config.Config{}
		cfg.Folder.Writethrough = true
		cfg.Folder.Readonly = true

		// This should be invalid configuration
		if cfg.Folder.Writethrough && cfg.Folder.Readonly {
			t.Log("Correctly detected conflicting writethrough and readonly configuration")
		} else {
			t.Error("Should detect conflicting writethrough and readonly configuration")
		}
	})

	t.Run("writethrough with destination pairs conflict", func(t *testing.T) {
		// Test the documented validation: "If writethrough is true, but a pairs entry
		// attempts to make this server a destination for sync, the program must output an error"
		cfg := &config.Config{}
		cfg.Folder.Writethrough = true

		// Pair configuration that makes this server a destination
		destinationPair := types.PairConfig{
			Name:         "source-peer",
			HostnameOrIP: "127.0.0.1",
			Direction:    types.ONE_WAY_FROM_PEER, // This makes current server a destination
		}

		cfg.Pairs = []types.PairConfig{destinationPair}

		// This should be invalid configuration in writethrough mode
		if cfg.Folder.Writethrough && destinationPair.Direction == types.ONE_WAY_FROM_PEER {
			t.Log("Correctly detected invalid writethrough configuration with destination pair")
		} else {
			t.Error("Should detect invalid writethrough configuration with destination pair")
		}
	})
}
