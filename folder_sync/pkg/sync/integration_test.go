package sync

import (
	"io/ioutil"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/fsutil"
	"github.com/real-rm/folder_sync/pkg/types"
)

// TestSyncCoreEventSuppression tests the event suppression mechanism to prevent synchronization loops
// This integration test verifies that:
// 1. Events from remote peers are properly suppressed
// 2. No synchronization loops occur when applying remote changes
// 3. Event suppression works for files and directories
// 4. Temporary file patterns are properly suppressed
func TestSyncCoreEventSuppression(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := ioutil.TempDir("", "folder-sync-suppression-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	syncDir := filepath.Join(tempDir, "sync")
	metaDir := filepath.Join(tempDir, "meta")

	// Create directory structure
	for _, dir := range []string{syncDir, metaDir} {
		if err := os.MkdirAll(dir, 0755); err != nil {
			t.Fatalf("Failed to create directory %s: %v", dir, err)
		}
	}

	// Create event queue
	eventQueue := make(chan types.FileSyncEvent, 100)

	// Create minimal config for testing
	cfg := &config.Config{
		Debug: true,
	}
	cfg.Folder.SyncPath = syncDir
	cfg.Folder.MetaPath = metaDir
	cfg.Folder.LogsPath = metaDir // Use metaDir for logs in test

	// Create FSUtil
	fsUtil := fsutil.NewFSUtil(syncDir)

	// Create meta manager
	metaManager := NewMockMetaManager()

	// Create SyncCore
	syncCore := NewSyncCore(cfg, metaManager, fsUtil, eventQueue)

	// Start SyncCore in background
	go syncCore.Start()
	defer syncCore.Close()

	// Wait for SyncCore to start
	time.Sleep(100 * time.Millisecond)

	// Test 1: Test event suppression for files
	t.Run("File_Event_Suppression", func(t *testing.T) {
		peerName := "testpeer"
		filePath := "test-file.txt"

		// Test that events are not suppressed initially
		if syncCore.IsEventSuppressed(peerName, filePath) {
			t.Error("Event should not be suppressed initially")
		}

		// Suppress events for the file
		suppressDuration := 2 * time.Second
		syncCore.SuppressEventForPeer(peerName, filePath, suppressDuration)

		// Test that events are now suppressed
		if !syncCore.IsEventSuppressed(peerName, filePath) {
			t.Error("Event should be suppressed after calling SuppressEventForPeer")
		}

		// Wait for suppression to expire
		time.Sleep(suppressDuration + 100*time.Millisecond)

		// Test that events are no longer suppressed
		if syncCore.IsEventSuppressed(peerName, filePath) {
			t.Error("Event should not be suppressed after expiration")
		}
	})

	// Test 2: Test pattern-based suppression for temporary files
	t.Run("Temporary_File_Pattern_Suppression", func(t *testing.T) {
		peerName := "testpeer"
		baseFile := "test-file.txt"
		tempPattern := baseFile + ".tmp-*"
		tempFile := baseFile + ".tmp-12345"

		// Suppress events for the pattern
		suppressDuration := 2 * time.Second
		syncCore.SuppressEventForPeer(peerName, tempPattern, suppressDuration)

		// Test that the specific temp file is suppressed by the pattern
		if !syncCore.IsEventSuppressed(peerName, tempFile) {
			t.Error("Temporary file should be suppressed by pattern")
		}

		// Test that the base file is not suppressed
		if syncCore.IsEventSuppressed(peerName, baseFile) {
			t.Error("Base file should not be suppressed by temp file pattern")
		}
	})

	// Test 3: Test multiple peer suppression
	t.Run("Multiple_Peer_Suppression", func(t *testing.T) {
		peer1 := "peer1"
		peer2 := "peer2"
		filePath := "shared-file.txt"

		// Suppress for peer1 only
		suppressDuration := 2 * time.Second
		syncCore.SuppressEventForPeer(peer1, filePath, suppressDuration)

		// Test that it's suppressed for peer1 but not peer2
		if !syncCore.IsEventSuppressed(peer1, filePath) {
			t.Error("Event should be suppressed for peer1")
		}
		if syncCore.IsEventSuppressed(peer2, filePath) {
			t.Error("Event should not be suppressed for peer2")
		}
	})

	// Test 4: Test queue processing with suppression
	t.Run("Queue_Processing_With_Suppression", func(t *testing.T) {
		// Create a mock peer connection
		mockConn := NewMockNetworkConnection("testpeer")
		syncCore.AddPeerConnection(mockConn)

		// Wait for peer to be ready
		time.Sleep(100 * time.Millisecond)

		// Create a test file to trigger an event
		testFile := filepath.Join(syncDir, "queue-test.txt")
		testContent := "test content"

		// Suppress events for this file to the test peer
		syncCore.SuppressEventForPeer("testpeer", "queue-test.txt", 5*time.Second)

		// Create the file (this should trigger an inotify event)
		if err := ioutil.WriteFile(testFile, []byte(testContent), 0644); err != nil {
			t.Fatalf("Failed to create test file: %v", err)
		}

		// Wait for event processing
		time.Sleep(500 * time.Millisecond)

		// Check that no file sync events were sent to the peer (due to suppression)
		mockConn.mu.Lock()
		sentEvents := mockConn.sentEvents
		sentMessages := mockConn.sentMessages
		mockConn.mu.Unlock()

		// Check sent events
		for _, event := range sentEvents {
			if event.Path == "queue-test.txt" {
				t.Error("File sync event should have been suppressed but was sent to peer")
			}
		}

		// Check sent messages
		for _, msg := range sentMessages {
			if msg.MessageType == types.MSG_FILE_SYNC_EVENT {
				t.Error("File sync event message should have been suppressed but was sent to peer")
			}
		}
	})
}
