package sync

import (
	"bytes"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/fsutil"
	"github.com/real-rm/folder_sync/pkg/logger"
	"github.com/real-rm/folder_sync/pkg/types"
)

// ChunkTransfer tracks the state of an ongoing chunked file transfer
type ChunkTransfer struct {
	MessageID      uint64
	Path           string
	TotalChunks    uint32
	ReceivedChunks map[uint32][]byte // chunkIndex -> chunkData
	TempFilePath   string
	TempFile       *os.File
	Event          types.FileSyncEvent // Original event for final processing
}

// OngoingSyncHandlerImpl implements the OngoingSyncHandler interface.
type OngoingSyncHandlerImpl struct {
	cfg         *config.Config
	metaManager MetaManager
	fsUtil      FSUtil
	log         *logger.Logger
	syncCore    *SyncCore // Reference back to the SyncCore for peer communication

	// Track stuck messages per peer to prevent infinite loops
	stuckMessages      map[string]map[uint64]int // peerName -> MessageID -> retry count
	stuckMessagesMutex sync.RWMutex              // Protects stuckMessages map

	// Track ongoing chunk transfers per peer
	chunkTransfers map[string]map[uint64]*ChunkTransfer // peerName -> MessageID -> ChunkTransfer
	chunkMutex     sync.RWMutex                         // Protects chunkTransfers map
}

// NewOngoingSyncHandler creates a new OngoingSyncHandlerImpl.
func NewOngoingSyncHandler(
	cfg *config.Config,
	metaManager MetaManager,
	fsUtil FSUtil,
	log *logger.Logger,
	syncCore *SyncCore,
) OngoingSyncHandler {
	return &OngoingSyncHandlerImpl{
		cfg:            cfg,
		metaManager:    metaManager,
		fsUtil:         fsUtil,
		log:            log,
		syncCore:       syncCore,
		stuckMessages:  make(map[string]map[uint64]int),
		chunkTransfers: make(map[string]map[uint64]*ChunkTransfer),
	}
}

// HandleLocalEvent processes a FileSyncEvent originating from the local file system (inotify).
// This involves applying the change locally if applicable (though inotify means it's already applied),
// and then queuing it for all relevant remote peers.
func (h *OngoingSyncHandlerImpl) HandleLocalEvent(event types.FileSyncEvent) error {
	h.log.Debug("Handling local event: %s", event.String())

	// If in writethrough mode, local inotify events are ignored.
	if h.cfg.Folder.Writethrough {
		h.log.Debug("Writethrough mode enabled, ignoring local inotify event: %s", event.String())
		return nil
	}

	// For local events, the file system operation has already occurred.
	// The primary task here is to queue this event for outbound synchronization.
	// The actual queueing to each peer's MetaQueue is done by SyncCore's
	// `processIncomingEvents` loop calling `PeerHandler.QueueOutgoingEvent`.
	// This function primarily serves as a placeholder for any local-specific pre-queue processing
	// or statistics updates that are not handled by the queueing logic itself.

	// Update local statistics if needed (e.g., total operations detected locally)
	// For now, these stats are handled per-peer as they are sent.

	return nil
}

// HandleRemoteEvent processes a FileSyncEvent received from a remote peer.
// This involves applying the change locally, considering conflict resolution.
func (h *OngoingSyncHandlerImpl) HandleRemoteEvent(peerName string, event types.FileSyncEvent) error {
	h.log.Debug("Handling remote event from %q for file %q: %s", peerName, event.Path, event.String())

	// Get local file info for conflict resolution
	fullPath := filepath.Join(h.cfg.Folder.SyncPath, event.Path)
	localFileInfo, localStatErr := h.fsUtil.GetFileStat(fullPath)
	localFileExists := (localStatErr == nil)

	// Get peer handler for status updates and configuration
	peerHandler := h.syncCore.GetPeerHandler(peerName)
	if peerHandler == nil {
		return fmt.Errorf("no peer handler found for %q", peerName)
	}

	// Get the pair configuration from the peer handler
	// This works for both statically configured peers and dynamically created ones (listener mode)
	pairCfg := peerHandler.GetPairConfig()

	// If this is the first event we're receiving and our initial sync status is pending,
	// it means the peer is doing initial sync and we should mark ourselves as receiving initial sync
	if peerHandler.GetInitialSyncStatus() == types.PENDING {
		h.log.Info("Received first event from peer %q during initial sync, marking as IN_PROGRESS", peerName)
		peerHandler.SetInitialSyncStatus(types.IN_PROGRESS)
		if err := h.syncCore.metaManager.SaveImmediately(map[string]*types.PairStatus{peerName: peerHandler.GetPairStatus()}); err != nil {
			h.log.Error("Failed to save pair status for %q during initial sync: %v", peerName, err)
		}
	}

	if pairCfg.Direction == types.ONE_WAY_TO_PEER {
		h.log.Debug("Ignoring remote event from %q as direction is ONE_WAY_TO_PEER: %s", peerName, event.String())
		// Still acknowledge to drain the remote's queue
		h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size)
		return nil
	}
	if h.cfg.Folder.Readonly {
		h.log.Debug("Server is in readonly mode, ignoring remote event from %q: %s", peerName, event.String())
		h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size)
		return nil
	}

	// Statistics will be updated in SendAcknowledgementToPeer method
	// Check if we should mark initial sync as complete
	// This is a heuristic: if we're in IN_PROGRESS and have received some events,
	// we'll mark it complete after processing this event
	if peerHandler.pairStatus.InitialSyncStatus == types.IN_PROGRESS {
		totalReceived := peerHandler.pairStatus.TotalReceived.Creates +
			peerHandler.pairStatus.TotalReceived.Writes +
			peerHandler.pairStatus.TotalReceived.CreateDirs

		// Mark as complete if we've received at least one event
		// In a real implementation, you might want more sophisticated logic
		if totalReceived > 0 {
			h.log.Info("Marking initial sync as COMPLETED for peer %q after receiving %d events", peerName, totalReceived)
			peerHandler.pairStatus.InitialSyncStatus = types.COMPLETED
		}
	}

	// --- Conflict Resolution (for BIDIRECTIONAL_NEWEST_WIN) ---
	if pairCfg.Direction == types.BIDIRECTIONAL_NEWEST_WIN && localFileExists {
		// For directories, if they already exist locally, just acknowledge - no need for timestamp comparison
		if event.Type == types.CREATE_DIR && localFileInfo.IsDir() {
			h.log.Info("Directory %q already exists locally. Acknowledging remote CREATE_DIR event.", event.Path)
			h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size)
			return nil
		}

		// For DELETE events, skip timestamp comparison - deletions should always be applied
		if event.Type == types.DELETE_FILE || event.Type == types.DELETE_DIR {
			h.log.Debug("DELETE event for %q - skipping timestamp comparison, proceeding with deletion", event.Path)
			// Continue to the switch statement below to handle the deletion
		} else {
			// For files, do timestamp-based conflict resolution
			localMtime := localFileInfo.ModTime().Truncate(time.Second) // Ensure same precision
			remoteMtime := event.Timestamp.Truncate(time.Second)        // Ensure same precision

			if localMtime.After(remoteMtime) {
				h.log.Info("Conflict resolution for %q: Local file is newer (local mtime: %s vs remote mtime: %s). Skipping remote change.",
					event.Path, localMtime.Format(time.RFC3339), remoteMtime.Format(time.RFC3339))
				// Inform sender that we have newer content (MSG_SAME_CONTENT)
				err := h.syncCore.GetPeerHandler(peerName).SendMessage(types.MSG_SAME_CONTENT, types.SameContent{
					MessageID: event.MessageID,
					Path:      event.Path,
					Timestamp: localMtime,
					Size:      uint64(localFileInfo.Size()),
				})
				if err != nil {
					h.log.Warn("Failed to send MSG_SAME_CONTENT to peer %q for %q (MsgID: %d): %v. Sending direct acknowledgment instead.",
						peerName, event.Path, event.MessageID, err)
					// Fallback: send direct acknowledgment to prevent infinite loop
					h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size)
				} else {
					h.log.Debug("Sent MSG_SAME_CONTENT to peer %q for %q (MsgID: %d)", peerName, event.Path, event.MessageID)
				}
				return nil // Do not apply remote change
			} else if remoteMtime.After(localMtime) {
				h.log.Info("Conflict resolution for %q: Remote file is newer (remote mtime: %s vs local mtime: %s). Applying remote change.",
					event.Path, remoteMtime.Format(time.RFC3339), localMtime.Format(time.RFC3339))
				// Proceed to apply the remote change
			} else { // mtimes are identical
				// Compare checksums if strategy is not SIZE_ONLY
				if h.cfg.Folder.Compares != types.SIZE_ONLY {
					localChecksum, err := h.fsUtil.CalculateXXHash(fullPath, h.cfg.Folder.Compares)
					if err != nil {
						h.log.Error("Failed to calculate local checksum for %q: %v. Treating as remote is newer.", event.Path, err)
						// Fallback: if local checksum cannot be calculated, assume remote is newer
					} else {
						if bytes.Equal(localChecksum, event.Checksum) {
							h.log.Info("Conflict resolution for %q: Identical mtime and checksum. Content is same. Skipping remote change.", event.Path)
							// Send MSG_SAME_CONTENT to inform sender that content is identical
							err := h.syncCore.GetPeerHandler(peerName).SendMessage(types.MSG_SAME_CONTENT, types.SameContent{
								MessageID: event.MessageID,
								Path:      event.Path,
								Timestamp: localMtime,
								Size:      uint64(localFileInfo.Size()),
							})
							if err != nil {
								h.log.Warn("Failed to send MSG_SAME_CONTENT to peer %q for %q (MsgID: %d): %v. Sending direct acknowledgment instead.",
									peerName, event.Path, event.MessageID, err)
								// Fallback: send direct acknowledgment to prevent infinite loop
								h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size)
							} else {
								h.log.Debug("Sent MSG_SAME_CONTENT to peer %q for %q (MsgID: %d)", peerName, event.Path, event.MessageID)
							}
							return nil // Content is the same, no action needed
						} else {
							h.log.Error("Conflict for %q: Identical mtime (%s) but different checksums. Manual intervention may be required.",
								event.Path, localMtime.Format(time.RFC3339))
							// In a real system, this might trigger a more complex conflict resolution.
							// For now, we log an error and don't apply the remote change to prevent data loss.
							h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size) // Acknowledge to sender to avoid re-send loop
							return fmt.Errorf("conflict: identical mtime but different checksums for %q", event.Path)
						}
					}
				} else { // SIZE_ONLY strategy, mtimes are identical
					h.log.Info("Conflict resolution for %q: Identical mtime with SIZE_ONLY strategy. Assuming content is same. Skipping remote change.", event.Path)
					h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size)
					return nil
				}
			}
		}
	}

	// --- Apply Remote Change ---
	switch event.Type {
	case types.CREATE_DIR:
		if localFileExists && !localFileInfo.IsDir() {
			h.log.Warn("Remote wants to create dir %q, but a file already exists locally. Removing local file.", event.Path)
			if err := h.fsUtil.Remove(fullPath); err != nil {
				h.log.Error("Failed to remove local file %q to create directory: %v", fullPath, err)
				return h.sendErrorAck(peerName, event, err)
			}
		}
		if err := h.fsUtil.CreateDir(fullPath, os.FileMode(event.FileMode)); err != nil {
			if !os.IsExist(err) { // Ignore if it already exists (idempotent)
				h.log.Error("Failed to create directory %q: %v", fullPath, err)
				return h.sendErrorAck(peerName, event, err)
			}
		}
		h.log.Info("Created directory: %q", event.Path)

		// Apply mtime to preserve original modification time from remote peer
		if !event.Timestamp.IsZero() {
			if err := h.fsUtil.Chtimes(fullPath, event.Timestamp, event.Timestamp); err != nil {
				h.log.Warn("Failed to set mtime for directory %q to %s: %v", fullPath, event.Timestamp.Format(time.RFC3339), err)
				// Not a critical error, continue
			} else {
				h.log.Debug("Set mtime for directory %q to %s", event.Path, event.Timestamp.Format(time.RFC3339))
			}
		}

		// Suppress events for this directory AFTER successful creation to prevent synchronization loops
		suppressDuration := 2 * time.Second
		h.syncCore.SuppressEventForPeer(peerName, event.Path, suppressDuration)

		h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size)

	case types.CREATE_FILE, types.WRITE_FILE:
		if localFileExists && localFileInfo.IsDir() {
			h.log.Warn("Remote wants to create/write file %q, but a directory already exists locally. Removing local directory.", event.Path)
			if err := h.fsUtil.Remove(fullPath); err != nil {
				h.log.Error("Failed to remove local directory %q to create file: %v", fullPath, err)
				return h.sendErrorAck(peerName, event, err)
			}
		}

		if event.SendsChunks {
			// Request the file body from the peer
			h.log.Debug("Requesting file pull for large file %q from peer %q (size: %d bytes)", event.Path, peerName, event.Size)
			pullReq := types.PullFileRequest{
				MessageID:           event.MessageID,
				Path:                event.Path,
				Size:                uint64(0), // Indicate we don't have it or have 0 bytes initially
				FileCompareStrategy: h.cfg.Folder.Compares,
				Checksum:            nil,
			}
			// This will be sent as a message by PeerHandler's message sending loop
			// The response (MSG_PULL_FILE_RESPONSE or MSG_FILE_CHUNK) will be handled by HandlePeerResponse
			h.syncCore.GetPeerHandler(peerName).SendMessage(types.MSG_PULL_FILE_REQUEST, pullReq)
			// Do NOT acknowledge here, wait for the actual file content to arrive
		} else {
			// File body is included directly
			if err := h.applyFileChange(fullPath, event, peerName); err != nil {
				return h.sendErrorAck(peerName, event, err)
			}
			h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size)
		}

	case types.DELETE_FILE, types.DELETE_DIR:
		if !localFileExists {
			h.log.Debug("Remote wants to delete %q, but it doesn't exist locally. Skipping.", event.Path)
			// Still suppress events even for non-existent files to prevent loops
			suppressDuration := 2 * time.Second
			h.syncCore.SuppressEventForPeer(peerName, event.Path, suppressDuration)
			h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size)
			return nil // Idempotent: already deleted
		}
		if err := h.fsUtil.Remove(fullPath); err != nil {
			h.log.Error("Failed to delete %q: %v", fullPath, err)
			return h.sendErrorAck(peerName, event, err)
		}
		h.log.Info("Deleted %s: %q", event.Type.String(), event.Path)

		// Suppress events for this path AFTER successful deletion to prevent synchronization loops
		suppressDuration := 2 * time.Second
		h.syncCore.SuppressEventForPeer(peerName, event.Path, suppressDuration)

		h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size)

	case types.RENAME_FILE, types.RENAME_DIR:
		// RENAME events need both old and new paths. Current FileSyncEvent only has 'Path'.
		// The protocol specification implies a single `Path` field for `FileSyncEvent`.
		// If `RENAME_FILE` is sent, `Path` would be the *new* path.
		// A separate field `OldPath` or a `RenameEvent` struct is needed.
		// For now, if a RENAME event comes, we'll treat it as a `DELETE` of the old and `CREATE` of the new.
		// This needs to be carefully coordinated with the sender.
		h.log.Warn("Received RENAME event %s for %q. RENAME handling not fully implemented for atomic renaming. Treating as create/delete pair.", event.Type.String(), event.Path)
		// This is a complex case. For robustness, it's often simpler to rely on
		// `CREATE` + `DELETE` events and let the sync core reconcile.
		// If the source sends `RENAME` it must define source and dest.
		// Assuming the 'Path' field holds the *new* path for RENAME, we create it.
		// The deletion of the old path would ideally come as a separate `DELETE` event.
		if event.FileBody != nil { // If it's a RENAME_FILE with content
			if err := h.applyFileChange(fullPath, event, peerName); err != nil {
				return h.sendErrorAck(peerName, event, err)
			}
		} else if event.Type == types.RENAME_DIR {
			if err := h.fsUtil.CreateDir(fullPath, os.FileMode(event.FileMode)); err != nil {
				h.log.Error("Failed to create renamed directory %q: %v", fullPath, err)
				return h.sendErrorAck(peerName, event, err)
			}
		}
		h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size)

	case types.CHMOD_FILE:
		if !localFileExists {
			h.log.Debug("Remote wants to chmod %q, but it doesn't exist locally. Skipping.", event.Path)
			h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size)
			return nil
		}

		// Check if file content might have changed (size difference indicates content change)
		localSize := uint64(localFileInfo.Size())
		if event.Size != localSize {
			h.log.Info("CHMOD event for %q has size difference (remote: %d, local: %d), requesting file content",
				event.Path, event.Size, localSize)

			// If the event includes file body, apply it directly
			if event.FileBody != nil && len(event.FileBody) > 0 {
				h.log.Info("CHMOD event for %q includes file content, applying as file update", event.Path)
				if err := h.applyFileChange(fullPath, event, peerName); err != nil {
					return h.sendErrorAck(peerName, event, err)
				}
				h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size)
				return nil
			}

			// If no file body but size differs, request the file content
			if event.SendsChunks {
				h.log.Debug("CHMOD event for %q indicates chunks will follow, preparing for chunk reassembly", event.Path)
				h.chunkMutex.Lock()
				if h.chunkTransfers[peerName] == nil {
					h.chunkTransfers[peerName] = make(map[uint64]*ChunkTransfer)
				}
				// Create a modified event for chunk reassembly
				chmodEvent := event
				chmodEvent.Type = types.WRITE_FILE // Treat as file write for content transfer
				h.chunkTransfers[peerName][event.MessageID] = &ChunkTransfer{
					MessageID:      event.MessageID,
					Path:           event.Path,
					TotalChunks:    0, // Will be set when first chunk arrives
					ReceivedChunks: make(map[uint32][]byte),
					Event:          chmodEvent,
				}
				h.chunkMutex.Unlock()
				// Do NOT acknowledge here, wait for chunks to arrive
				return nil
			} else {
				// Request file content via pull request
				h.log.Debug("Requesting file pull for CHMOD event with size difference: %q from peer %q", event.Path, peerName)
				pullReq := types.PullFileRequest{
					MessageID:           event.MessageID,
					Path:                event.Path,
					Size:                localSize,
					FileCompareStrategy: h.cfg.Folder.Compares,
					Checksum:            nil,
				}
				h.syncCore.GetPeerHandler(peerName).SendMessage(types.MSG_PULL_FILE_REQUEST, pullReq)
				// Do NOT acknowledge here, wait for the actual file content to arrive
				return nil
			}
		}

		// No size difference, just apply permissions and metadata
		if err := h.fsUtil.Chmod(fullPath, os.FileMode(event.FileMode)); err != nil {
			h.log.Error("Failed to chmod %q to %o: %v", fullPath, event.FileMode, err)
			return h.sendErrorAck(peerName, event, err)
		}
		// Also apply ownership if changed
		currentUID, currentGID, _ := h.fsUtil.GetFileOwnership(fullPath)
		if currentUID != event.OwnerUID || currentGID != event.OwnerGID {
			if err := h.fsUtil.Chown(fullPath, event.OwnerUID, event.OwnerGID); err != nil {
				h.log.Warn("Failed to chown %q to %d:%d: %v", fullPath, event.OwnerUID, event.OwnerGID, err)
				// Not critical, continue
			}
		}

		// Apply mtime if provided (for CHMOD events that also want to preserve timestamps)
		if !event.Timestamp.IsZero() {
			if err := h.fsUtil.Chtimes(fullPath, event.Timestamp, event.Timestamp); err != nil {
				h.log.Warn("Failed to set mtime for %q to %s during chmod: %v", fullPath, event.Timestamp.Format(time.RFC3339), err)
				// Not critical, continue
			} else {
				h.log.Debug("Set mtime for %q to %s during chmod", event.Path, event.Timestamp.Format(time.RFC3339))
			}
		}

		h.log.Info("Chmod/Chown applied to %q", event.Path)
		h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size)

	case types.CREATE_SYMLINK:
		if localFileExists {
			h.log.Warn("Remote wants to create symlink %q, but a file/dir already exists locally. Removing local item.", event.Path)
			if err := h.fsUtil.Remove(fullPath); err != nil {
				h.log.Error("Failed to remove local item %q to create symlink: %v", fullPath, err)
				return h.sendErrorAck(peerName, event, err)
			}
		}
		// The FileBody contains the target path for the symlink
		targetPath := string(event.FileBody)
		// For symlinks, we need to create the symlink itself pointing to the targetPath.
		// Use the atomic symlink creation function from fsutil
		if err := fsutil.CreateSymlink(targetPath, fullPath); err != nil {
			h.log.Error("Failed to create symlink %q -> %q: %v", fullPath, targetPath, err)
			return h.sendErrorAck(peerName, event, err)
		}
		h.log.Info("Created symlink: %q -> %q", event.Path, targetPath)
		h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size)

	default:
		h.log.Warn("Received unhandled remote event type %s for %q.", event.Type.String(), event.Path)
		h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size) // Acknowledge to avoid re-send
	}

	return nil
}

// ProcessPeerQueue continuously sends events from a peer's outgoing queue to the peer.
func (h *OngoingSyncHandlerImpl) ProcessPeerQueue(peerName string) {
	h.log.Info("Starting queue processing for peer %q...", peerName)
	queueInterface, err := h.metaManager.GetQueue(peerName)
	if err != nil {
		h.log.Error("Failed to get queue for peer %q: %v", peerName, err)
		return
	}
	queue, ok := queueInterface.(MetaQueue)
	if !ok {
		h.log.Error("Failed to cast queue to MetaQueue for peer %q", peerName)
		return
	}

	// Wait for initial sync to complete for this peer before starting full queue processing
	// This channel is closed by InitialSyncHandlerImpl when initial sync is done.
	<-h.syncCore.peerHandlers[peerName].initialSyncDone
	h.log.Info("Initial sync completed for %q. Starting full queue processing.", peerName)

	sendInterval := 100 * time.Millisecond // Adjust as needed
	const maxRetries = 10

	// Initialize stuck message tracking for this peer if not exists
	h.stuckMessagesMutex.Lock()
	if h.stuckMessages[peerName] == nil {
		h.stuckMessages[peerName] = make(map[uint64]int)
	}
	h.stuckMessagesMutex.Unlock()

	for {
		select {
		case <-h.syncCore.done:
			h.log.Debug("Stopping queue processing for %q due to SyncCore shutdown.", peerName)
			return
		case <-h.syncCore.peerHandlers[peerName].done: // PeerHandler shutdown
			h.log.Debug("Stopping queue processing for %q due to PeerHandler shutdown.", peerName)
			return
		case <-time.After(sendInterval):
			// Proceed to try sending events
		}

		conn := h.syncCore.GetPeerHandler(peerName).GetConnection()
		if conn == nil || !conn.IsConnected() {
			h.log.Debug("Peer %q not connected, skipping queue processing.", peerName)
			continue
		}

		event, err := queue.GetNextEvent()
		if err != nil {
			// Check if this is just an empty queue (normal condition) vs actual error
			if err.Error() == "queue is empty" {
				h.log.Debug("Queue for peer %q is empty. Waiting for new events...", peerName)
				// Use select with timer instead of time.Sleep to allow for proper shutdown
				select {
				case <-h.syncCore.done:
					return
				case <-h.syncCore.peerHandlers[peerName].done:
					return
				case <-time.After(sendInterval):
					continue
				}
			}
			// This is an actual error
			h.log.Error("Failed to get next event from queue for %q: %v", peerName, err)
			// Use select with timer instead of time.Sleep to allow for proper shutdown
			select {
			case <-h.syncCore.done:
				return
			case <-h.syncCore.peerHandlers[peerName].done:
				return
			case <-time.After(1 * time.Second):
				continue
			}
		}
		if event == nil {
			h.log.Debug("Queue for peer %q is empty. Waiting for new events...", peerName)
			// Use select with timer instead of time.Sleep to allow for proper shutdown
			select {
			case <-h.syncCore.done:
				return
			case <-h.syncCore.peerHandlers[peerName].done:
				return
			case <-time.After(sendInterval):
				continue
			}
		}

		// Check if this message has been stuck (sent multiple times without acknowledgment)
		h.stuckMessagesMutex.RLock()
		retryCount := h.stuckMessages[peerName][event.MessageID]
		h.stuckMessagesMutex.RUnlock()

		if retryCount >= maxRetries {
			h.log.Error("Message %d for file %q to peer %q has exceeded max retries (%d). Removing from queue to prevent infinite loop.",
				event.MessageID, event.Path, peerName, maxRetries)
			if err := queue.RemoveEvent(event.MessageID); err != nil {
				h.log.Warn("Failed to remove stuck message %d from queue: %v", event.MessageID, err)
			}
			h.stuckMessagesMutex.Lock()
			delete(h.stuckMessages[peerName], event.MessageID)
			h.stuckMessagesMutex.Unlock()
			continue
		}

		// Ensure the event's origin is updated to REMOTE_PEER (from this daemon) if it was LOCAL_INOTIFY
		// This is important for the receiving peer to distinguish local vs remote changes.
		if event.Origin == types.LOCAL_INOTIFY {
			event.Origin = types.REMOTE_PEER // Mark as originating from *this* remote peer
		}

		// Send the event via network connection
		// Note: SendFileSyncEvent will handle chunking if event.SendsChunks is true
		h.log.Debug("Sending event for file %q to peer %q: %s (retry %d/%d)", event.Path, peerName, event.String(), retryCount+1, maxRetries)
		sendErr := conn.SendFileSyncEvent(*event)
		if sendErr != nil {
			h.log.Error("Failed to send event for file %q to peer %q: %v. Will retry.", event.Path, peerName, sendErr)
			// Increment retry count for network send failures
			h.stuckMessagesMutex.Lock()
			h.stuckMessages[peerName][event.MessageID] = retryCount + 1
			h.stuckMessagesMutex.Unlock()
			// Use select with timer instead of time.Sleep to allow for proper shutdown
			select {
			case <-h.syncCore.done:
				return
			case <-h.syncCore.peerHandlers[peerName].done:
				return
			case <-time.After(1 * time.Second):
				continue
			}
		}

		// Event successfully sent (initial message).
		// Increment retry count to track potential stuck messages
		h.stuckMessagesMutex.Lock()
		h.stuckMessages[peerName][event.MessageID] = retryCount + 1
		h.stuckMessagesMutex.Unlock()

		// It will be removed from the queue only after receiving SYNC_ACKNOWLEDGE.
		// For writethrough mode, it's removed after all peers acknowledge.
		h.log.Debug("Event %s sent to peer %q. Awaiting acknowledgment.", event.String(), peerName)
	}
}

// HandlePeerResponse processes a response (ACK, PULL_FILE_REQUEST, FILE_REMOVED, SAME_CONTENT)
// received from a remote peer.
func (h *OngoingSyncHandlerImpl) HandlePeerResponse(peerName string, msgType types.MessageType, payload interface{}) error {
	h.log.Debug("Processing peer response from %q: Type %s, Payload %T", peerName, msgType.String(), payload)

	queueInterface, err := h.metaManager.GetQueue(peerName)
	if err != nil {
		return fmt.Errorf("failed to get queue for peer %q: %w", peerName, err)
	}
	queue, ok := queueInterface.(MetaQueue)
	if !ok {
		return fmt.Errorf("failed to cast queue to MetaQueue for peer %q", peerName)
	}

	switch msgType {
	case types.MSG_FILE_SYNC_EVENT:
		// Handle incoming file sync event from remote peer
		event, ok := payload.(types.FileSyncEvent)
		if !ok {
			return fmt.Errorf("invalid payload for MSG_FILE_SYNC_EVENT")
		}
		h.log.Info("Received file sync event from peer %q: %s", peerName, event.String())

		// Process the remote event (apply changes locally)
		return h.HandleRemoteEvent(peerName, event)

	case types.MSG_SYNC_ACKNOWLEDGE:
		ack, ok := payload.(types.SyncAcknowledge)
		if !ok {
			return fmt.Errorf("invalid payload for MSG_SYNC_ACKNOWLEDGE")
		}
		if ack.Success {
			h.log.Info("Peer %q acknowledged success for %q (MsgID: %d)", peerName, ack.Path, ack.MessageID)

			// Get the original event to extract size information before removing it
			var eventSize uint64 = 0
			if originalEvent, err := queue.GetEventByMessageID(ack.MessageID); err == nil && originalEvent != nil {
				eventSize = originalEvent.Size
			} else {
				h.log.Debug("Could not find original event %d for size tracking: %v", ack.MessageID, err)
			}

			// Remove the acknowledged event from the outgoing queue
			if err := queue.RemoveEvent(ack.MessageID); err != nil {
				// Check if the error is because the event was already removed (idempotent operation)
				if err.Error() == fmt.Sprintf("event with MessageID %d not found", ack.MessageID) {
					h.log.Debug("Event %d already removed from queue for %q (duplicate acknowledgment, ignoring)", ack.MessageID, peerName)
				} else {
					h.log.Warn("Failed to remove acknowledged event %d from queue for %q: %v", ack.MessageID, peerName, err)
				}
				// Continue anyway, it's not fatal - acknowledgments should be idempotent
			}

			// Clean up stuck message tracking for successfully acknowledged messages
			h.stuckMessagesMutex.Lock()
			if h.stuckMessages[peerName] != nil {
				delete(h.stuckMessages[peerName], ack.MessageID)
			}
			h.stuckMessagesMutex.Unlock()

			h.syncCore.RecordPeerAcknowledgement(peerName, ack.MessageID, ack.EventType, eventSize)
		} else {
			h.log.Error("Peer %q reported failure for %q (MsgID: %d): %s", peerName, ack.Path, ack.MessageID, ack.ErrorMessage)
			// Keep in queue for retry, or handle specific error codes
		}

	case types.MSG_PULL_FILE_REQUEST:
		pullReq, ok := payload.(types.PullFileRequest)
		if !ok {
			return fmt.Errorf("invalid payload for MSG_PULL_FILE_REQUEST")
		}
		h.log.Info("Peer %q requested file pull for %q (MsgID: %d)", peerName, pullReq.Path, pullReq.MessageID)

		// Read the file and send back MSG_PULL_FILE_RESPONSE (possibly chunked)
		fullPath := filepath.Join(h.cfg.Folder.SyncPath, pullReq.Path)
		fileInfo, err := h.fsUtil.GetFileStat(fullPath)
		if err != nil {
			if os.IsNotExist(err) {
				h.log.Warn("File %q requested by peer %q not found locally. Sending MSG_FILE_REMOVED.", pullReq.Path, peerName)
				h.syncCore.GetPeerHandler(peerName).SendMessage(types.MSG_FILE_REMOVED, types.FileRemoved{
					MessageID: pullReq.MessageID,
					Path:      pullReq.Path,
				})
				return nil
			}
			h.log.Error("Failed to stat file %q for pull request from %q: %v", pullReq.Path, peerName, err)
			// Send error response
			h.syncCore.GetPeerHandler(peerName).SendMessage(types.MSG_ERROR, types.Error{
				Code:    0x02, // File system error
				Message: fmt.Sprintf("Failed to read local file for pull: %v", err),
			})
			return fmt.Errorf("failed to stat file for pull: %w", err)
		}

		// Prepare PULL_FILE_RESPONSE
		uid, gid, err := h.fsUtil.GetFileOwnership(fullPath)
		if err != nil {
			h.log.Warn("Failed to get ownership for %q during pull response: %v", fullPath, err)
			uid, gid = 0, 0 // Default values if ownership cannot be determined
		}

		// Create the proper PullFileResponse object
		pullResp := types.PullFileResponse{
			MessageID: pullReq.MessageID, // Correlates to original pull request
			Path:      pullReq.Path,
			Timestamp: fileInfo.ModTime(),
			Size:      uint64(fileInfo.Size()),
			FileMode:  uint32(fileInfo.Mode().Perm()),
			OwnerUID:  uid,
			OwnerGID:  gid,
		}

		if pullResp.Size <= uint64(h.cfg.Folder.BlockSize) { // Heuristic for "small" files
			fileBody, err := h.fsUtil.ReadFile(fullPath)
			if err != nil {
				h.log.Error("Failed to read file body for %q during pull response: %v", fullPath, err)
				h.syncCore.GetPeerHandler(peerName).SendMessage(types.MSG_ERROR, types.Error{
					Code:    0x02,
					Message: fmt.Sprintf("Failed to read file body: %v", err),
				})
				return fmt.Errorf("failed to read file body for pull response: %w", err)
			}
			pullResp.FileBody = fileBody
			pullResp.SendsChunks = false
		} else {
			pullResp.SendsChunks = true
			pullResp.FileBody = nil // No body in initial pull response
		}
		pullResp.IsCompressed = (h.cfg.Folder.Compression == types.COMPRESSION_ZSTD)

		// Send MSG_PULL_FILE_RESPONSE
		if err := h.syncCore.GetPeerHandler(peerName).SendMessage(types.MSG_PULL_FILE_RESPONSE, pullResp); err != nil {
			h.log.Error("Failed to send MSG_PULL_FILE_RESPONSE for %q to %q: %v", pullResp.Path, peerName, err)
			return fmt.Errorf("failed to send pull file response: %w", err)
		}
		h.log.Info("Sent MSG_PULL_FILE_RESPONSE for %q to %q (sends chunks: %t)", pullResp.Path, peerName, pullResp.SendsChunks)

		// If sending chunks, read the file and send it in chunks
		if pullResp.SendsChunks {
			h.log.Debug("Sending file %q to %q in chunks (size: %d bytes)", pullResp.Path, peerName, pullResp.Size)
			if err := h.sendFileInChunks(peerName, pullResp.MessageID, fullPath, pullResp.Path); err != nil {
				h.log.Error("Failed to send file chunks for %q to %q: %v", pullResp.Path, peerName, err)
				return fmt.Errorf("failed to send file chunks: %w", err)
			}
		}

	case types.MSG_PULL_FILE_RESPONSE:
		h.log.Debug("Handling MSG_PULL_FILE_RESPONSE from peer %q", peerName)
		pullResp, ok := payload.(types.PullFileResponse)
		if !ok {
			h.log.Error("Invalid payload type for MSG_PULL_FILE_RESPONSE from peer %q: got %T, expected types.PullFileResponse", peerName, payload)
			return fmt.Errorf("invalid payload for MSG_PULL_FILE_RESPONSE")
		}
		h.log.Info("Peer %q sent file pull response for %q (MsgID: %d)", peerName, pullResp.Path, pullResp.MessageID)

		// Convert PullFileResponse to FileSyncEvent for processing
		event := types.FileSyncEvent{
			Type:         types.CREATE_FILE, // Pull responses are always file creations/updates
			Path:         pullResp.Path,
			Timestamp:    pullResp.Timestamp,
			Size:         pullResp.Size,
			FileMode:     pullResp.FileMode,
			OwnerUID:     pullResp.OwnerUID,
			OwnerGID:     pullResp.OwnerGID,
			FileBody:     pullResp.FileBody,
			SendsChunks:  pullResp.SendsChunks,
			IsCompressed: pullResp.IsCompressed,
			MessageID:    pullResp.MessageID,
			Origin:       types.REMOTE_PEER,
		}

		if event.SendsChunks {
			// File will be sent in chunks, prepare for chunk reassembly
			h.log.Debug("Pull file response for %q from %q will be sent in chunks, preparing for reassembly", event.Path, peerName)
			h.chunkMutex.Lock()
			if h.chunkTransfers[peerName] == nil {
				h.chunkTransfers[peerName] = make(map[uint64]*ChunkTransfer)
			}
			h.chunkTransfers[peerName][event.MessageID] = &ChunkTransfer{
				MessageID:      event.MessageID,
				Path:           event.Path,
				TotalChunks:    0, // Will be set when first chunk arrives
				ReceivedChunks: make(map[uint32][]byte),
				Event:          event, // Store the event for final processing
			}
			h.chunkMutex.Unlock()
			// Do NOT acknowledge here, wait for chunks to arrive and be reassembled
		} else {
			// File body is included directly
			fullPath := filepath.Join(h.cfg.Folder.SyncPath, event.Path)
			if err := h.applyFileChange(fullPath, event, peerName); err != nil {
				h.log.Error("Failed to apply pull file response for %q from %q: %v", event.Path, peerName, err)
				return err
			}

			// Send acknowledgment back to the peer
			h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size)
			h.log.Info("Successfully applied pull file response for %q from %q", event.Path, peerName)
		}

	case types.MSG_FILE_REMOVED:
		fileRemoved, ok := payload.(types.FileRemoved)
		if !ok {
			return fmt.Errorf("invalid payload for MSG_FILE_REMOVED")
		}
		h.log.Info("Peer %q reported file %q (MsgID: %d) was removed before pull.", peerName, fileRemoved.Path, fileRemoved.MessageID)
		// Clean up any pending state related to this file, e.g., if we were waiting to apply it.
		// For the current implementation, this mostly means we acknowledge it to the sender.
		h.syncCore.SendAcknowledgementToPeer(peerName, fileRemoved.MessageID, types.DELETE_FILE, fileRemoved.Path, 0) // Treat as a deletion ACK

	case types.MSG_SAME_CONTENT:
		sameContent, ok := payload.(types.SameContent)
		if !ok {
			return fmt.Errorf("invalid payload for MSG_SAME_CONTENT")
		}
		h.log.Info("Peer %q indicated same content for %q (MsgID: %d). Removing from queue.", peerName, sameContent.Path, sameContent.MessageID)
		// MSG_SAME_CONTENT is itself the acknowledgment - just remove from queue
		if err := queue.RemoveEvent(sameContent.MessageID); err != nil {
			// Check if the error is because the event was already removed (idempotent operation)
			if err.Error() == fmt.Sprintf("event with MessageID %d not found", sameContent.MessageID) {
				h.log.Debug("Event %d already removed from queue for %q (duplicate MSG_SAME_CONTENT, ignoring)", sameContent.MessageID, peerName)
			} else {
				h.log.Warn("Failed to remove event %d from queue for %q after MSG_SAME_CONTENT: %v", sameContent.MessageID, peerName, err)
			}
		}

		// Clean up stuck message tracking for MSG_SAME_CONTENT
		h.stuckMessagesMutex.Lock()
		if h.stuckMessages[peerName] != nil {
			delete(h.stuckMessages[peerName], sameContent.MessageID)
		}
		h.stuckMessagesMutex.Unlock()

		h.syncCore.RecordPeerAcknowledgement(peerName, sameContent.MessageID, types.WRITE_FILE, sameContent.Size)

	case types.MSG_FILE_CHUNK:
		// This happens when we are RECEIVING chunks from a peer
		chunk, ok := payload.(types.FileChunk)
		if !ok {
			return fmt.Errorf("invalid payload for MSG_FILE_CHUNK")
		}
		h.log.Debug("Received chunk %d/%d for %q from %q (MsgID: %d)", chunk.ChunkIndex, chunk.TotalChunks, chunk.Path, peerName, chunk.MessageID)

		// Handle chunk reassembly
		if err := h.handleFileChunk(peerName, chunk); err != nil {
			h.log.Error("Failed to handle file chunk %d/%d for %q from %q: %v", chunk.ChunkIndex, chunk.TotalChunks, chunk.Path, peerName, err)
			// Send failure ACK
			h.syncCore.GetPeerHandler(peerName).SendMessage(types.MSG_CHUNK_ACKNOWLEDGE, types.ChunkAcknowledge{
				MessageID:  chunk.MessageID,
				Path:       chunk.Path,
				ChunkIndex: chunk.ChunkIndex,
				Success:    false,
			})
			return err
		}

		// Send success ACK for the chunk
		h.syncCore.GetPeerHandler(peerName).SendMessage(types.MSG_CHUNK_ACKNOWLEDGE, types.ChunkAcknowledge{
			MessageID:  chunk.MessageID,
			Path:       chunk.Path,
			ChunkIndex: chunk.ChunkIndex,
			Success:    true,
		})

	case types.MSG_CHUNK_ACKNOWLEDGE:
		// This happens when we are SENDING chunks to a peer and received an ACK for one
		ack, ok := payload.(types.ChunkAcknowledge)
		if !ok {
			return fmt.Errorf("invalid payload for MSG_CHUNK_ACKNOWLEDGE")
		}
		h.log.Debug("Peer %q acknowledged chunk %d for %q (MsgID: %d)", peerName, ack.ChunkIndex, ack.Path, ack.MessageID)
		// If all chunks for a file are acknowledged, then the original FileSyncEvent
		// can be considered fully delivered and removed from the queue.
		// This requires state tracking for chunked transfers.
		// For simplicity, we assume the final MSG_SYNC_ACKNOWLEDGE for the main event
		// will handle queue removal.

	case types.MSG_INITIAL_SCAN_REQUEST:
		scanReq, ok := payload.(types.InitialScanRequest)
		if !ok {
			return fmt.Errorf("invalid payload for MSG_INITIAL_SCAN_REQUEST")
		}
		h.log.Info("Peer %q requested initial scan (MsgID: %d)", peerName, scanReq.MessageID)

		// Perform local scan and send response
		return h.handleInitialScanRequest(peerName, scanReq)

	case types.MSG_INITIAL_SCAN_RESPONSE:
		scanResp, ok := payload.(types.InitialScanResponse)
		if !ok {
			return fmt.Errorf("invalid payload for MSG_INITIAL_SCAN_RESPONSE")
		}
		h.log.Info("Peer %q sent initial scan response (MsgID: %d, FileCount: %d, Success: %t)",
			peerName, scanResp.MessageID, len(scanResp.Files), scanResp.Success)

		// Process the remote file list for bidirectional sync
		return h.handleInitialScanResponse(peerName, scanResp)

	case types.MSG_ERROR:
		errMsg, ok := payload.(types.Error)
		if !ok {
			return fmt.Errorf("invalid payload for MSG_ERROR")
		}
		h.log.Error("Peer %q sent error: Code %d, Message: %s", peerName, errMsg.Code, errMsg.Message)
		// No further action on a general error, other than logging.

	default:
		h.log.Warn("Received unhandled message type %s from peer %q.", msgType.String(), peerName)
	}

	return nil
}

// applyFileChange handles creating/writing a file locally based on a remote FileSyncEvent.
func (h *OngoingSyncHandlerImpl) applyFileChange(fullPath string, event types.FileSyncEvent, peerName string) error {
	// Suppress events for this file path to prevent synchronization loops
	// The suppression duration should be long enough to cover the file operation and any delayed inotify events
	suppressDuration := 5 * time.Second // Increased duration to handle delayed events
	h.syncCore.SuppressEventForPeer(peerName, event.Path, suppressDuration)

	// Also suppress events for any temporary files that might be created during atomic write
	tempPattern := event.Path + ".tmp-*"
	h.syncCore.SuppressEventForPeer(peerName, tempPattern, suppressDuration)

	h.log.Info("Suppressing events for file %q to peer %q for %v", event.Path, peerName, suppressDuration)

	// Ensure parent directories exist
	parentDir := filepath.Dir(fullPath)
	if err := h.fsUtil.CreateDir(parentDir, 0755); err != nil && !os.IsExist(err) { // Create recursively if not exists
		return fmt.Errorf("failed to create parent directories for %q: %w", fullPath, err)
	}

	// Atomically write file
	if err := h.fsUtil.AtomicWrite(fullPath, event.FileBody, os.FileMode(event.FileMode)); err != nil {
		return fmt.Errorf("failed to write file %q: %w", fullPath, err)
	}

	// Apply ownership (optional, depending on OS and permissions)
	if err := h.fsUtil.Chown(fullPath, event.OwnerUID, event.OwnerGID); err != nil {
		h.log.Warn("Failed to chown %q to %d:%d: %v", fullPath, event.OwnerUID, event.OwnerGID, err)
		// Not a critical error, continue
	}

	// Apply mtime to preserve original modification time from remote peer
	if !event.Timestamp.IsZero() {
		// Use the same time for both access time and modification time
		// This preserves the original modification time from the source
		if err := h.fsUtil.Chtimes(fullPath, event.Timestamp, event.Timestamp); err != nil {
			h.log.Warn("Failed to set mtime for %q to %s: %v", fullPath, event.Timestamp.Format(time.RFC3339), err)
			// Not a critical error, continue - file content is already written correctly
		} else {
			h.log.Debug("Set mtime for %q to %s", event.Path, event.Timestamp.Format(time.RFC3339))
		}
	}

	h.log.Info("Applied %s: %q (Size: %d bytes)", event.Type.String(), event.Path, event.Size)
	return nil
}

// sendErrorAck sends a MSG_SYNC_ACKNOWLEDGE with success=false back to the peer.
func (h *OngoingSyncHandlerImpl) sendErrorAck(peerName string, event types.FileSyncEvent, cause error) error {
	h.log.Error("Sending error acknowledgment to peer %q for %s %q: %v", peerName, event.Type.String(), event.Path, cause)
	ack := types.SyncAcknowledge{
		MessageID:    event.MessageID,
		Path:         event.Path,
		EventType:    event.Type,
		Success:      false,
		ErrorMessage: cause.Error(),
	}
	return h.syncCore.GetPeerHandler(peerName).SendMessage(types.MSG_SYNC_ACKNOWLEDGE, ack)
}

// handleInitialScanRequest processes an initial scan request from a peer
func (h *OngoingSyncHandlerImpl) handleInitialScanRequest(peerName string, request types.InitialScanRequest) error {
	h.log.Info("Processing initial scan request from peer %q (MsgID: %d)", peerName, request.MessageID)

	// Ensure peer handler exists before processing
	peerHandler := h.syncCore.GetPeerHandler(peerName)
	if peerHandler == nil {
		h.log.Error("No peer handler found for %q during initial scan request", peerName)
		return fmt.Errorf("no peer handler found for %q", peerName)
	}

	// Create pattern matcher for ignored files
	patternMatcher, err := h.cfg.NewPatternMatcher()
	if err != nil {
		h.log.Error("Failed to create pattern matcher: %v", err)
		response := types.InitialScanResponse{
			MessageID:    request.MessageID,
			Files:        nil,
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to create pattern matcher: %v", err),
		}
		return peerHandler.SendMessage(types.MSG_INITIAL_SCAN_RESPONSE, response)
	}

	// Scan local sync folder
	var files []types.FileSyncEvent
	err = filepath.Walk(h.cfg.Folder.SyncPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			h.log.Warn("Error walking path %q during initial scan: %v", path, err)
			return nil // Continue walking
		}

		// Get relative path
		relPath, err := filepath.Rel(h.cfg.Folder.SyncPath, path)
		if err != nil {
			h.log.Warn("Failed to get relative path for %q: %v", path, err)
			return nil
		}

		// Skip root directory
		if relPath == "." {
			return nil
		}

		// Skip if ignored
		if patternMatcher.IsIgnored(relPath) {
			h.log.Debug("Skipping ignored path during initial scan: %q", relPath)
			if info.IsDir() {
				return filepath.SkipDir
			}
			return nil
		}

		// Create file sync event
		var eventType types.FileSyncEventType
		if info.IsDir() {
			eventType = types.CREATE_DIR
		} else {
			eventType = types.CREATE_FILE
		}

		event := types.FileSyncEvent{
			Type:      eventType,
			Path:      relPath,
			Timestamp: info.ModTime(),
			Size:      uint64(info.Size()),
			FileMode:  uint32(info.Mode().Perm()),
			Origin:    types.LOCAL_INOTIFY,
			MessageID: h.syncCore.generateMessageID(),
		}

		files = append(files, event)
		h.log.Debug("Added file to initial scan: %q (type: %s, size: %d)", relPath, eventType.String(), info.Size())
		return nil
	})

	// Create response
	response := types.InitialScanResponse{
		MessageID: request.MessageID,
		Files:     files,
		Success:   err == nil,
	}

	if err != nil {
		response.ErrorMessage = err.Error()
		h.log.Error("Initial scan failed for peer %q: %v", peerName, err)
	} else {
		h.log.Info("Initial scan completed for peer %q: found %d files/directories", peerName, len(files))
	}

	// Send response
	return peerHandler.SendMessage(types.MSG_INITIAL_SCAN_RESPONSE, response)
}

// handleInitialScanResponse processes an initial scan response from a peer
func (h *OngoingSyncHandlerImpl) handleInitialScanResponse(peerName string, response types.InitialScanResponse) error {
	if !response.Success {
		h.log.Error("Peer %q initial scan failed: %s", peerName, response.ErrorMessage)
		return fmt.Errorf("peer initial scan failed: %s", response.ErrorMessage)
	}

	h.log.Info("Processing initial scan response from peer %q: %d files/directories", peerName, len(response.Files))

	// Get peer configuration for conflict resolution
	peerHandler := h.syncCore.GetPeerHandler(peerName)
	if peerHandler == nil {
		return fmt.Errorf("no peer handler found for %q", peerName)
	}
	pairCfg := peerHandler.GetPairConfig()

	// For each file in the response, compare with local state and decide what to do
	for _, remoteFile := range response.Files {
		fullPath := filepath.Join(h.cfg.Folder.SyncPath, remoteFile.Path)
		localFileInfo, localStatErr := h.fsUtil.GetFileStat(fullPath)
		localFileExists := (localStatErr == nil)

		// Skip checksum calculation for directories
		if remoteFile.Type == types.CREATE_DIR {
			h.log.Debug("Skipping directory %q in initial scan response", remoteFile.Path)
			continue
		}

		if !localFileExists {
			// File exists on remote but not locally - request it
			h.log.Debug("File %q exists on peer %q but not locally, requesting pull (size: %d, modified: %s)",
				remoteFile.Path, peerName, remoteFile.Size, remoteFile.Timestamp.Format("2006-01-02 15:04:05"))
			pullReq := types.PullFileRequest{
				MessageID:           h.syncCore.generateMessageID(),
				Path:                remoteFile.Path,
				Size:                0, // We don't have the file locally, so size is 0
				FileCompareStrategy: h.cfg.Folder.Compares,
				Checksum:            nil, // No local checksum since file doesn't exist
			}
			if err := h.syncCore.GetPeerHandler(peerName).SendMessage(types.MSG_PULL_FILE_REQUEST, pullReq); err != nil {
				h.log.Error("Failed to send pull request for %q to peer %q: %v", remoteFile.Path, peerName, err)
			}
		} else {
			// File exists on both sides - check if we need to sync based on direction and timestamps
			if pairCfg.Direction == types.BIDIRECTIONAL_NEWEST_WIN {
				// Compare timestamps for conflict resolution
				localMtime := localFileInfo.ModTime().Truncate(time.Second)
				remoteMtime := remoteFile.Timestamp.Truncate(time.Second)

				if remoteMtime.After(localMtime) {
					h.log.Debug("Remote file %q is newer (remote: %s, local: %s), requesting pull",
						remoteFile.Path, remoteMtime.Format("2006-01-02 15:04:05"), localMtime.Format("2006-01-02 15:04:05"))

					// Calculate local checksum for comparison (only for files, not directories)
					var localChecksum []byte
					if h.cfg.Folder.Compares != types.SIZE_ONLY && !localFileInfo.IsDir() {
						var err error
						localChecksum, err = h.fsUtil.CalculateXXHash(fullPath, h.cfg.Folder.Compares)
						if err != nil {
							h.log.Warn("Failed to calculate local checksum for %q: %v", remoteFile.Path, err)
						}
					}

					pullReq := types.PullFileRequest{
						MessageID:           h.syncCore.generateMessageID(),
						Path:                remoteFile.Path,
						Size:                uint64(localFileInfo.Size()),
						FileCompareStrategy: h.cfg.Folder.Compares,
						Checksum:            localChecksum,
					}
					if err := h.syncCore.GetPeerHandler(peerName).SendMessage(types.MSG_PULL_FILE_REQUEST, pullReq); err != nil {
						h.log.Error("Failed to send pull request for %q to peer %q: %v", remoteFile.Path, peerName, err)
					}
				} else if localMtime.Equal(remoteMtime) {
					// Same timestamp - compare checksums if available (only for files, not directories)
					if h.cfg.Folder.Compares != types.SIZE_ONLY && len(remoteFile.Checksum) > 0 && !localFileInfo.IsDir() {
						localChecksum, err := h.fsUtil.CalculateXXHash(fullPath, h.cfg.Folder.Compares)
						if err != nil {
							h.log.Warn("Failed to calculate local checksum for %q: %v", remoteFile.Path, err)
						} else if !bytes.Equal(localChecksum, remoteFile.Checksum) {
							h.log.Warn("File %q has identical timestamp but different checksum - potential conflict", remoteFile.Path)
							// Log the conflict but don't sync to avoid data loss
						} else {
							h.log.Debug("File %q has identical timestamp and checksum - already in sync", remoteFile.Path)
						}
					} else {
						h.log.Debug("File %q has identical timestamp - assuming in sync", remoteFile.Path)
					}
				} else {
					h.log.Debug("Local file %q is newer, keeping local version", remoteFile.Path)
				}
			} else if pairCfg.Direction == types.ONE_WAY_FROM_PEER {
				// Always pull from peer regardless of timestamps
				h.log.Debug("Direction is ONE_WAY_FROM_PEER, requesting pull for %q", remoteFile.Path)

				// Calculate local checksum for comparison (only for files, not directories)
				var localChecksum []byte
				if h.cfg.Folder.Compares != types.SIZE_ONLY && !localFileInfo.IsDir() {
					var err error
					localChecksum, err = h.fsUtil.CalculateXXHash(fullPath, h.cfg.Folder.Compares)
					if err != nil {
						h.log.Warn("Failed to calculate local checksum for %q: %v", remoteFile.Path, err)
					}
				}

				pullReq := types.PullFileRequest{
					MessageID:           h.syncCore.generateMessageID(),
					Path:                remoteFile.Path,
					Size:                uint64(localFileInfo.Size()),
					FileCompareStrategy: h.cfg.Folder.Compares,
					Checksum:            localChecksum,
				}
				if err := h.syncCore.GetPeerHandler(peerName).SendMessage(types.MSG_PULL_FILE_REQUEST, pullReq); err != nil {
					h.log.Error("Failed to send pull request for %q to peer %q: %v", remoteFile.Path, peerName, err)
				}
			}
			// For ONE_WAY_TO_PEER, we don't pull anything from the peer
		}
	}

	h.log.Info("Completed processing initial scan response from peer %q", peerName)
	return nil
}

// handleFileChunk processes an incoming file chunk and reassembles the file when all chunks are received
func (h *OngoingSyncHandlerImpl) handleFileChunk(peerName string, chunk types.FileChunk) error {
	h.chunkMutex.Lock()
	defer h.chunkMutex.Unlock()

	// Initialize peer chunk transfers map if needed
	if h.chunkTransfers[peerName] == nil {
		h.chunkTransfers[peerName] = make(map[uint64]*ChunkTransfer)
	}

	// Get or create chunk transfer state
	transfer := h.chunkTransfers[peerName][chunk.MessageID]
	if transfer == nil {
		// This is the first chunk for this transfer (no prior MSG_PULL_FILE_RESPONSE)
		h.log.Debug("Starting new chunk transfer for %q from %q (MsgID: %d, %d chunks expected)",
			chunk.Path, peerName, chunk.MessageID, chunk.TotalChunks)

		transfer = &ChunkTransfer{
			MessageID:      chunk.MessageID,
			Path:           chunk.Path,
			TotalChunks:    chunk.TotalChunks,
			ReceivedChunks: make(map[uint32][]byte),
			// Event will be constructed when reassembling
		}
		h.chunkTransfers[peerName][chunk.MessageID] = transfer
	} else {
		// Update total chunks if this is the first chunk and we have a transfer from MSG_PULL_FILE_RESPONSE
		if transfer.TotalChunks == 0 {
			transfer.TotalChunks = chunk.TotalChunks
			h.log.Debug("Updated chunk transfer for %q from %q (MsgID: %d) with %d total chunks",
				chunk.Path, peerName, chunk.MessageID, chunk.TotalChunks)
		}
	}

	// Validate chunk consistency
	if transfer.TotalChunks != chunk.TotalChunks {
		return fmt.Errorf("chunk total mismatch for %q: expected %d, got %d",
			chunk.Path, transfer.TotalChunks, chunk.TotalChunks)
	}
	if transfer.Path != chunk.Path {
		return fmt.Errorf("chunk path mismatch for MessageID %d: expected %q, got %q",
			chunk.MessageID, transfer.Path, chunk.Path)
	}

	// Check for duplicate chunk
	if _, exists := transfer.ReceivedChunks[chunk.ChunkIndex]; exists {
		h.log.Warn("Received duplicate chunk %d for %q from %q (MsgID: %d)",
			chunk.ChunkIndex, chunk.Path, peerName, chunk.MessageID)
		return nil // Not an error, just ignore duplicate
	}

	// Store the chunk data
	transfer.ReceivedChunks[chunk.ChunkIndex] = chunk.ChunkData
	h.log.Debug("Received chunk %d/%d for %q from %q (MsgID: %d, size: %d bytes)",
		chunk.ChunkIndex, chunk.TotalChunks, chunk.Path, peerName, chunk.MessageID, len(chunk.ChunkData))

	// Check if we have all chunks
	if uint32(len(transfer.ReceivedChunks)) == transfer.TotalChunks {
		h.log.Info("All chunks received for %q from %q (MsgID: %d), reassembling file",
			chunk.Path, peerName, chunk.MessageID)

		// Reassemble the file
		if err := h.reassembleFile(peerName, transfer); err != nil {
			// Clean up on error
			delete(h.chunkTransfers[peerName], chunk.MessageID)
			return fmt.Errorf("failed to reassemble file %q: %w", chunk.Path, err)
		}

		// Clean up transfer state
		delete(h.chunkTransfers[peerName], chunk.MessageID)
		h.log.Info("Successfully reassembled and applied file %q from %q (MsgID: %d)",
			chunk.Path, peerName, chunk.MessageID)
	}

	return nil
}

// reassembleFile reassembles chunks into a complete file and applies it to the filesystem
func (h *OngoingSyncHandlerImpl) reassembleFile(peerName string, transfer *ChunkTransfer) error {
	// Create a buffer to hold the complete file content
	var fileBuffer bytes.Buffer

	// Reassemble chunks in order
	for i := uint32(0); i < transfer.TotalChunks; i++ {
		chunkData, exists := transfer.ReceivedChunks[i]
		if !exists {
			return fmt.Errorf("missing chunk %d for file %q", i, transfer.Path)
		}
		if _, err := fileBuffer.Write(chunkData); err != nil {
			return fmt.Errorf("failed to write chunk %d to buffer: %w", i, err)
		}
	}

	// Create or use the stored FileSyncEvent
	var event types.FileSyncEvent
	if transfer.Event.MessageID != 0 {
		// We have a stored event from MSG_PULL_FILE_RESPONSE
		event = transfer.Event
	} else {
		// Create a basic event for direct chunk transfer (e.g., from MSG_FILE_SYNC_EVENT with SendsChunks=true)
		event = types.FileSyncEvent{
			Type:      types.CREATE_FILE, // Assume file creation/update
			Path:      transfer.Path,
			Timestamp: time.Now(),
			FileMode:  0644, // Default file mode
			MessageID: transfer.MessageID,
			Origin:    types.REMOTE_PEER,
		}
	}

	// Set the complete file content
	event.FileBody = fileBuffer.Bytes()
	event.Size = uint64(fileBuffer.Len())
	event.SendsChunks = false // Now we have the complete content

	// Apply the file change
	fullPath := filepath.Join(h.cfg.Folder.SyncPath, event.Path)
	if err := h.applyFileChange(fullPath, event, peerName); err != nil {
		return fmt.Errorf("failed to apply reassembled file: %w", err)
	}

	// Send acknowledgment for the original event
	h.syncCore.SendAcknowledgementToPeer(peerName, event.MessageID, event.Type, event.Path, event.Size)

	return nil
}

// sendFileInChunks reads a file and sends it to a peer in chunks
func (h *OngoingSyncHandlerImpl) sendFileInChunks(peerName string, messageID uint64, fullPath string, relPath string) error {
	// Read the file content
	fileBody, err := h.fsUtil.ReadFile(fullPath)
	if err != nil {
		return fmt.Errorf("failed to read file %q: %w", fullPath, err)
	}

	// Calculate chunk parameters
	chunkSize := types.DefaultChunkSize
	totalSize := len(fileBody)
	totalChunks := (totalSize + chunkSize - 1) / chunkSize // Ceiling division

	h.log.Debug("Sending file %q in %d chunks of %d bytes each (total: %d bytes)", relPath, totalChunks, chunkSize, totalSize)

	// Send each chunk
	for i := 0; i < totalChunks; i++ {
		start := i * chunkSize
		end := start + chunkSize
		if end > totalSize {
			end = totalSize
		}
		chunkData := fileBody[start:end]

		chunk := types.FileChunk{
			MessageID:    messageID,
			Path:         relPath,
			ChunkIndex:   uint32(i),
			TotalChunks:  uint32(totalChunks),
			ChunkSize:    uint32(len(chunkData)),
			IsCompressed: false, // TODO: Add compression support if needed
			ChunkData:    chunkData,
		}

		if err := h.syncCore.GetPeerHandler(peerName).SendMessage(types.MSG_FILE_CHUNK, chunk); err != nil {
			return fmt.Errorf("failed to send chunk %d/%d for %q: %w", i+1, totalChunks, relPath, err)
		}

		h.log.Debug("Sent chunk %d/%d for %q to %q (%d bytes)", i+1, totalChunks, relPath, peerName, len(chunkData))
	}

	h.log.Info("Successfully sent all %d chunks for %q to %q", totalChunks, relPath, peerName)
	return nil
}
