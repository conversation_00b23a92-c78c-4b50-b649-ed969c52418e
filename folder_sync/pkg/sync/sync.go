package sync

import (
	"fmt"
	"path/filepath"
	"sync"
	"sync/atomic"
	"time"

	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/logger"
	"github.com/real-rm/folder_sync/pkg/types"
)

// SyncCore is the central orchestration logic for folder synchronization.
type SyncCore struct {
	cfg              *config.Config
	metaManager      MetaManager
	fsUtil           FSUtil
	eventQueue       <-chan types.FileSyncEvent // Incoming events from Ingress
	log              *logger.Logger
	done             chan struct{}
	wg               sync.WaitGroup
	pairConns        map[string]NetworkConnection // Map of peer name to active network connection
	peerHandlers     map[string]*PeerHandler      // Manages state and goroutines for each peer
	peersMu          sync.RWMutex                 // Protects pairConns and peerHandlers maps
	peersReady       chan string                  // Channel to signal when a peer connection is ready
	pendingEvents    map[uint64]*pendingEvent     // Track events awaiting ACKs from all peers in writethrough mode
	pendingEventsMu  sync.RWMutex
	isWritethrough   bool
	messageIDCounter uint64 // Atomic counter for generating unique message IDs

	// Event suppression to prevent synchronization loops
	suppressedEvents   map[string]map[string]time.Time // peerName -> filePath -> suppressUntil
	suppressedEventsMu sync.RWMutex

	// Event deduplication to prevent multiple events for the same file in quick succession
	recentEvents   map[string]time.Time // filePath -> lastEventTime
	recentEventsMu sync.RWMutex

	initialSyncHandler  InitialSyncHandler
	ongoingSyncHandler  OngoingSyncHandler
	writethroughHandler WritethroughHandler
	initialized         chan struct{} // Channel to signal when SyncCore is fully initialized
}

// pendingEvent tracks the state of an event sent to multiple peers in writethrough mode.
type pendingEvent struct {
	Event types.FileSyncEvent
	Acks  map[string]bool // Map peerName to true if acknowledged
}

// NewSyncCore creates a new SyncCore instance.
func NewSyncCore(
	cfg *config.Config,
	metaManager MetaManager,
	fsUtil FSUtil,
	eventQueue <-chan types.FileSyncEvent,
) *SyncCore {
	// Create a temporary logger for sync core - in a real implementation, this should be passed in
	syncLogger, err := logger.NewLogger(cfg.Folder.LogsPath, false)
	if err != nil {
		// Fallback to memory logger if we can't create file logger
		syncLogger = logger.NewMemoryLogger()
	}

	// Calculate buffer size for peersReady channel
	// Need to accommodate both configured peers and potential incoming peers
	bufferSize := len(cfg.Pairs)
	if bufferSize < 10 { // Minimum buffer size for incoming connections in listener mode
		bufferSize = 10
	}

	core := &SyncCore{
		cfg:              cfg,
		metaManager:      metaManager,
		fsUtil:           fsUtil,
		eventQueue:       eventQueue,
		log:              syncLogger,
		done:             make(chan struct{}),
		pairConns:        make(map[string]NetworkConnection),
		peerHandlers:     make(map[string]*PeerHandler),
		peersReady:       make(chan string, bufferSize), // Buffered channel with adequate size
		pendingEvents:    make(map[uint64]*pendingEvent),
		suppressedEvents: make(map[string]map[string]time.Time),
		recentEvents:     make(map[string]time.Time),
		isWritethrough:   cfg.Folder.Writethrough,
		initialized:      make(chan struct{}),
	}

	core.initialSyncHandler = NewInitialSyncHandler(cfg, metaManager, fsUtil, core.log, core)
	core.ongoingSyncHandler = NewOngoingSyncHandler(cfg, metaManager, fsUtil, core.log, core)
	core.writethroughHandler = NewWritethroughHandler(cfg, fsUtil, core.log, core.pendingEvents, &core.pendingEventsMu)

	return core
}

// generateMessageID generates a unique message ID using atomic increment.
func (sc *SyncCore) generateMessageID() uint64 {
	return atomic.AddUint64(&sc.messageIDCounter, 1)
}

// Start begins the synchronization process.
func (sc *SyncCore) Start() {
	sc.log.Info("SyncCore starting...")
	defer sc.log.Debug("SyncCore stopped.")

	// Load existing pair statuses
	pairStatuses, err := sc.metaManager.Load()
	if err != nil {
		sc.log.Error("Failed to load pair statuses, starting with empty state: %v", err)
		pairStatuses = make(map[string]*types.PairStatus)
	}

	// Initialize PeerHandlers and launch goroutines for each configured pair
	for _, pairCfg := range sc.cfg.Pairs {
		status, exists := pairStatuses[pairCfg.Name]
		if !exists {
			// Create a new status if it doesn't exist
			status = types.NewPairStatus(pairCfg.Name, filepath.Join(sc.cfg.Folder.MetaPath, pairCfg.Name+".queue.db"))
			pairStatuses[pairCfg.Name] = status

			// Immediately save the new status to disk to ensure persistence across restarts
			if err := sc.metaManager.SaveImmediately(map[string]*types.PairStatus{pairCfg.Name: status}); err != nil {
				sc.log.Error("Failed to save initial status for peer %q: %v", pairCfg.Name, err)
			} else {
				sc.log.Debug("Created and saved initial status for peer %q", pairCfg.Name)
			}
		}
		sc.log.Debug("Initializing PeerHandler for %q. Status: %s", pairCfg.Name, status.String())

		// A PeerHandler manages the connection and sync state for a single peer.
		// It needs access to the SyncCore for sending events, receiving responses, etc.
		// It will establish its own connection, or receive it from the NetworkManager.
		// For now, we manually signal readiness via peersReady channel in tests.
		// In a real app, network.Manager would call AddPeerConnection.
		peerHandler := NewPeerHandler(pairCfg, status, sc, sc.log)
		sc.peersMu.Lock()
		sc.peerHandlers[pairCfg.Name] = peerHandler
		sc.peersMu.Unlock()
	}

	// Start a goroutine to handle incoming events from the Ingress
	sc.wg.Add(1)
	go func() {
		defer sc.wg.Done()
		sc.processIncomingEvents()
	}()

	// Start a goroutine to process peer readiness signals
	sc.wg.Add(1)
	go func() {
		defer sc.wg.Done()
		sc.monitorPeerReadiness()
	}()

	// Start goroutines for each peer handler
	sc.peersMu.RLock()
	handlers := make([]*PeerHandler, 0, len(sc.peerHandlers))
	for _, handler := range sc.peerHandlers {
		handlers = append(handlers, handler)
	}
	sc.peersMu.RUnlock()

	for _, handler := range handlers {
		handler.Start() // Each peer handler manages its own connection and sync loops
	}

	// Signal that SyncCore is fully initialized
	close(sc.initialized)

	// Keep SyncCore running until done signal
	<-sc.done
	sc.log.Info("SyncCore received shutdown signal. Waiting for goroutines to finish...")
	sc.wg.Wait() // Wait for internal goroutines to finish
	sc.log.Info("All SyncCore goroutines stopped.")
}

// WaitForInitialization waits until the SyncCore is fully initialized
func (sc *SyncCore) WaitForInitialization() {
	<-sc.initialized
}

// Close gracefully shuts down the SyncCore and its components.
func (sc *SyncCore) Close() {
	sc.log.Info("Closing SyncCore...")
	close(sc.done) // Signal shutdown to internal goroutines

	// Signal peer handlers to close and wait for them
	for _, handler := range sc.peerHandlers {
		handler.Close()
	}

	// Wait for the main processing goroutines to finish
	sc.wg.Wait()

	// Save final pair statuses (if metaManager supports it)
	// (Note: MetaManager.Save is called by PeerHandler during its shutdown sequence for its specific status)
	// For global state, could add a final save here if needed.
	if err := sc.metaManager.Close(); err != nil {
		sc.log.Error("Failed to close meta manager: %v", err)
	}
	sc.log.Info("SyncCore gracefully shut down.")
}

// GetPeerHandler returns the PeerHandler for a given peer name.
func (sc *SyncCore) GetPeerHandler(peerName string) *PeerHandler {
	sc.peersMu.RLock()
	defer sc.peersMu.RUnlock()
	return sc.peerHandlers[peerName]
}

// AddPeerConnection registers an active network connection for a peer.
// This is called by the Network Manager when a connection is established.
func (sc *SyncCore) AddPeerConnection(conn NetworkConnection) {
	peerName := conn.PeerName()
	sc.log.Info("Adding active connection for peer: %q", peerName)

	sc.peersMu.Lock()
	defer sc.peersMu.Unlock()

	if handler, ok := sc.peerHandlers[peerName]; ok {
		// Existing configured peer (client side connection)
		// Client side should NOT perform initial sync to avoid duplication
		handler.SetConnection(conn)
		handler.SetIsClientConnection(true) // Mark as client connection
		// Note: SetConnection() will signal readiness, so we don't need to signal here
	} else {
		// This is an incoming connection from a peer not in our config
		// Create a status file and handler for this peer (listener mode)
		sc.log.Info("Received connection from unconfigured peer %q. Creating handler and status file.", peerName)

		// Create a new status for this incoming peer
		queueDBFile := filepath.Join(sc.cfg.Folder.MetaPath, peerName+".queue.db")
		status := types.NewPairStatus(peerName, queueDBFile)

		// Save the status file
		if err := sc.metaManager.Save(map[string]*types.PairStatus{peerName: status}); err != nil {
			sc.log.Error("Failed to save status for incoming peer %q: %v", peerName, err)
		} else {
			sc.log.Info("Created status file for incoming peer %q", peerName)
		}

		// Create a default pair config for incoming connections
		// This assumes bidirectional sync for incoming connections
		// Only the server side (incoming connection) should perform initial sync
		pairConfig := types.PairConfig{
			Name:                peerName,
			HostnameOrIP:        "", // Unknown for incoming connections
			Direction:           types.BIDIRECTIONAL_NEWEST_WIN,
			InitialSyncStrategy: types.SYNC,
			RemotePort:          0, // Unknown for incoming connections
		}

		// Create and initialize the PeerHandler
		handler := NewPeerHandler(pairConfig, status, sc, sc.log)
		sc.peerHandlers[peerName] = handler
		handler.SetConnection(conn)

		// Start the peer handler for dynamically created peers
		handler.Start()

		sc.log.Info("Created PeerHandler for incoming peer %q", peerName)

		// Signal that this peer is ready for sync operations
		select {
		case sc.peersReady <- peerName:
			sc.log.Debug("Signaled incoming peer %q as ready.", peerName)
		default:
			sc.log.Warn("peersReady channel full, could not signal incoming peer %q immediately.", peerName)
		}
	}
}

// RemovePeerConnection removes a network connection for a peer.
// This is called by the Network Manager when a connection is lost.
func (sc *SyncCore) RemovePeerConnection(peerName string) {
	sc.log.Info("Removing connection for peer: %q", peerName)
	sc.peersMu.RLock()
	handler, ok := sc.peerHandlers[peerName]
	sc.peersMu.RUnlock()
	if ok {
		handler.SetConnection(nil) // Mark connection as nil/inactive
	}
	// Note: We don't remove the PeerHandler itself, as it manages persisted state.
}

// SuppressEventForPeer temporarily suppresses events for a specific file path from being sent to a specific peer.
// This prevents synchronization loops when applying changes received from that peer.
func (sc *SyncCore) SuppressEventForPeer(peerName, filePath string, duration time.Duration) {
	sc.suppressedEventsMu.Lock()
	defer sc.suppressedEventsMu.Unlock()

	if sc.suppressedEvents[peerName] == nil {
		sc.suppressedEvents[peerName] = make(map[string]time.Time)
	}

	suppressUntil := time.Now().Add(duration)
	sc.suppressedEvents[peerName][filePath] = suppressUntil
	sc.log.Info("Suppressing events for file %q to peer %q until %s", filePath, peerName, suppressUntil.Format(time.RFC3339))
}

// IsEventSuppressed checks if an event for a specific file path should be suppressed for a specific peer.
func (sc *SyncCore) IsEventSuppressed(peerName, filePath string) bool {
	sc.suppressedEventsMu.RLock()

	peerSuppressed, exists := sc.suppressedEvents[peerName]
	if !exists {
		sc.suppressedEventsMu.RUnlock()
		return false
	}

	now := time.Now()
	var expiredKeys []string

	// Check for exact match first
	if suppressUntil, exists := peerSuppressed[filePath]; exists {
		if now.After(suppressUntil) {
			// Mark for cleanup but don't modify map while holding read lock
			expiredKeys = append(expiredKeys, filePath)
		} else {
			sc.suppressedEventsMu.RUnlock()
			sc.log.Info("Event for file %q to peer %q is suppressed until %s", filePath, peerName, suppressUntil.Format(time.RFC3339))
			return true
		}
	}

	// Check for pattern matches (for temporary files)
	for pattern, suppressUntil := range peerSuppressed {
		if matched, _ := filepath.Match(pattern, filePath); matched {
			if now.After(suppressUntil) {
				// Mark for cleanup but don't modify map while holding read lock
				expiredKeys = append(expiredKeys, pattern)
			} else {
				sc.suppressedEventsMu.RUnlock()
				sc.log.Info("Event for file %q to peer %q is suppressed by pattern %q until %s", filePath, peerName, pattern, suppressUntil.Format(time.RFC3339))
				return true
			}
		}
	}

	sc.suppressedEventsMu.RUnlock()

	// Clean up expired entries if any were found
	if len(expiredKeys) > 0 {
		sc.cleanupExpiredSuppressions(peerName, expiredKeys)
	}

	return false
}

// ShouldDeduplicateEvent checks if an event should be deduplicated (ignored) because
// a similar event for the same file was processed very recently.
func (sc *SyncCore) ShouldDeduplicateEvent(filePath string) bool {
	sc.recentEventsMu.Lock()
	defer sc.recentEventsMu.Unlock()

	now := time.Now()
	deduplicationWindow := 500 * time.Millisecond // 500ms window for deduplication

	// Check if we've seen this file recently
	if lastEventTime, exists := sc.recentEvents[filePath]; exists {
		if now.Sub(lastEventTime) < deduplicationWindow {
			sc.log.Debug("Deduplicating event for file %q (last event %s ago)", filePath, now.Sub(lastEventTime))
			return true // Deduplicate this event
		}
	}

	// Update the last event time for this file
	sc.recentEvents[filePath] = now

	// Clean up old entries (older than 5 seconds)
	cleanupThreshold := now.Add(-5 * time.Second)
	for path, eventTime := range sc.recentEvents {
		if eventTime.Before(cleanupThreshold) {
			delete(sc.recentEvents, path)
		}
	}

	return false // Don't deduplicate
}

// cleanupExpiredSuppressions removes expired suppression entries
func (sc *SyncCore) cleanupExpiredSuppressions(peerName string, expiredKeys []string) {
	sc.suppressedEventsMu.Lock()
	defer sc.suppressedEventsMu.Unlock()

	peerSuppressed, exists := sc.suppressedEvents[peerName]
	if !exists {
		return
	}

	for _, key := range expiredKeys {
		delete(peerSuppressed, key)
	}

	if len(peerSuppressed) == 0 {
		delete(sc.suppressedEvents, peerName)
	}
}

// processIncomingEvents reads FileSyncEvents from the Ingress module
// and dispatches them to appropriate handlers (local application, peer queues).
func (sc *SyncCore) processIncomingEvents() {
	sc.log.Debug("Starting processIncomingEvents loop")
	for {
		select {
		case event, ok := <-sc.eventQueue:
			if !ok {
				sc.log.Debug("Incoming event queue closed.")
				return
			}
			sc.log.Debug("Processing incoming event: %s", event.String())

			// Handle writethrough mode: apply locally, then queue for all peers.
			if sc.isWritethrough && event.Origin == types.PROXY_CLIENT_WRITE {
				sc.wg.Add(1)
				go func(e types.FileSyncEvent) {
					defer sc.wg.Done()
					if err := sc.writethroughHandler.ProcessProxyEvent(e); err != nil {
						sc.log.Error("Failed to process proxy event %s: %v", e.String(), err)
					}
					// After local application, queue for all peers
					for _, handler := range sc.peerHandlers {
						if handler.pairConfig.Direction == types.ONE_WAY_TO_PEER || handler.pairConfig.Direction == types.BIDIRECTIONAL_NEWEST_WIN {
							if err := handler.QueueOutgoingEvent(e); err != nil {
								sc.log.Error("Failed to queue proxy event %s for peer %q: %v", e.String(), handler.pairConfig.Name, err)
							}
						}
					}
				}(event)
			} else {
				// Normal operation: queue local events for all outbound peers.
				// For events coming from remote peers, handle them as incoming remote changes.
				if event.Origin == types.LOCAL_INOTIFY {
					// Check if this event should be deduplicated
					if sc.ShouldDeduplicateEvent(event.Path) {
						sc.log.Debug("Skipping duplicate event for file %q", event.Path)
						continue
					}

					sc.wg.Add(1)
					go func(e types.FileSyncEvent) {
						defer sc.wg.Done()
						if err := sc.ongoingSyncHandler.HandleLocalEvent(e); err != nil {
							sc.log.Error("Failed to handle local event %s: %v", e.String(), err)
						}
						// Queue for all peers configured to receive events from this server
						for _, handler := range sc.peerHandlers {
							if handler.pairConfig.Direction == types.ONE_WAY_TO_PEER || handler.pairConfig.Direction == types.BIDIRECTIONAL_NEWEST_WIN {
								// Check if this event should be suppressed for this peer to prevent loops
								if sc.IsEventSuppressed(handler.pairConfig.Name, e.Path) {
									sc.log.Info("Skipping queuing of local event %s for peer %q (suppressed to prevent loop)", e.String(), handler.pairConfig.Name)
									continue
								}
								sc.log.Info("Queuing local event %s for peer %q", e.String(), handler.pairConfig.Name)
								if err := handler.QueueOutgoingEvent(e); err != nil {
									sc.log.Error("Failed to queue local event %s for peer %q: %v", e.String(), handler.pairConfig.Name, err)
								}
							}
						}
					}(event)
				} else if event.Origin == types.REMOTE_PEER {
					// These events should be handled by the PeerHandler that received them.
					// If they arrive here, it means they weren't matched to a specific PeerHandler,
					// which indicates an architectural issue or a test setup quirk.
					// For now, we'll skip these events and log a warning.
					sc.log.Warn("Received a REMOTE_PEER event in SyncCore's main event loop. This should be handled by a PeerHandler: %s", event.String())
					sc.log.Warn("Skipping REMOTE_PEER event. Remote events should be processed via PeerHandler.handleIncomingMessages.")
				}
			}

		case <-sc.done:
			sc.log.Debug("Stopping incoming event processing.")
			return
		}
	}
}

// monitorPeerReadiness waits for peers to signal they are connected and ready
// to begin initial sync or ongoing sync processing.
func (sc *SyncCore) monitorPeerReadiness() {
	for {
		select {
		case peerName := <-sc.peersReady:
			sc.log.Info("Peer %q signaled readiness. Starting sync processes...", peerName)
			sc.peersMu.RLock()
			handler, ok := sc.peerHandlers[peerName]
			sc.peersMu.RUnlock()
			if ok {
				sc.wg.Add(1)
				go func() {
					defer sc.wg.Done()
					handler.PerformSyncProcesses() // Start initial sync and ongoing sync
				}()
			} else {
				sc.log.Warn("Received readiness signal for unknown peer: %q", peerName)
			}
		case <-sc.done:
			sc.log.Debug("Stopping peer readiness monitoring.")
			return
		}
	}
}

// --- Internal Helper Methods (used by PeerHandler and others) ---

// HandlePeerMessage processes incoming messages from network peers
// This implements the SyncCoreInterface for the network layer
func (sc *SyncCore) HandlePeerMessage(peerName string, msgType types.MessageType, payload interface{}) error {
	sc.log.Debug("Handling peer message from %q: Type %s", peerName, msgType.String())

	// Forward to the ongoing sync handler
	if sc.ongoingSyncHandler != nil {
		return sc.ongoingSyncHandler.HandlePeerResponse(peerName, msgType, payload)
	}

	return fmt.Errorf("no ongoing sync handler available")
}

// SendAcknowledgementToPeer is called when we successfully process an event from a peer.
// This sends a MSG_SYNC_ACKNOWLEDGE back to the peer and updates received statistics.
func (sc *SyncCore) SendAcknowledgementToPeer(peerName string, msgID uint64, eventType types.FileSyncEventType, path string, eventSize uint64) {
	sc.log.Debug("Sending acknowledgment to peer %q, msgID %d, eventType %s, path %q", peerName, msgID, eventType.String(), path)

	// Send MSG_SYNC_ACKNOWLEDGE back to the peer
	sc.peersMu.RLock()
	handler, ok := sc.peerHandlers[peerName]
	sc.peersMu.RUnlock()
	if ok {
		ack := types.SyncAcknowledge{
			MessageID:    msgID,
			Path:         path,
			EventType:    eventType,
			Success:      true,
			ErrorMessage: "",
		}

		if err := handler.SendMessage(types.MSG_SYNC_ACKNOWLEDGE, ack); err != nil {
			sc.log.Error("Failed to send acknowledgment to peer %q for msgID %d: %v", peerName, msgID, err)
		} else {
			sc.log.Debug("Sent acknowledgment to peer %q for msgID %d, path %q", peerName, msgID, path)
		}

		// Update total received stats (we received and processed an event)
		handler.UpdateTotalReceived(eventType, eventSize)
		if err := sc.metaManager.Save(map[string]*types.PairStatus{peerName: handler.GetPairStatus()}); err != nil {
			sc.log.Error("Failed to save pair status for %q after receiving event: %v", peerName, err)
		}
	}
}

// RecordPeerAcknowledgement is called when a peer acknowledges an event we sent to them.
// This updates sent statistics and handles writethrough cleanup.
func (sc *SyncCore) RecordPeerAcknowledgement(peerName string, msgID uint64, eventType types.FileSyncEventType, eventSize uint64) {
	sc.log.Debug("Recording peer acknowledgment from %q, msgID %d, eventType %s, size %d bytes", peerName, msgID, eventType.String(), eventSize)

	// Update total sent stats (they acknowledged an event we sent)
	sc.peersMu.RLock()
	handler, ok := sc.peerHandlers[peerName]
	sc.peersMu.RUnlock()
	if ok {
		handler.pairStatus.TotalSent.Update(eventType, eventSize)
		if err := sc.metaManager.Save(map[string]*types.PairStatus{peerName: handler.pairStatus}); err != nil {
			sc.log.Error("Failed to save pair status for %q after ACK: %v", peerName, err)
		}
	}

	// In writethrough mode, check for cleanup
	if sc.isWritethrough {
		sc.writethroughHandler.RecordPeerAcknowledgement(msgID, peerName)
	}
}
