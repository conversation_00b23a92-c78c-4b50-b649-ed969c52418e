package interfaces

import (
	"os"
	"time"

	"github.com/real-rm/folder_sync/pkg/types"
)

// MetaQueue defines the interface for interacting with a peer's event queue in the metadata manager.
type MetaQueue interface {
	AddEvent(event types.FileSyncEvent) error
	GetNextEvent() (*types.FileSyncEvent, error)
	RemoveEvent(messageID uint64) error
	GetEvents() ([]types.FileSyncEvent, error) // For initial sync, to get all pending events
	Count() (int, error)
	Close() error
	Clear() error
}

// NetworkConnection defines the interface for interacting with a network peer.
type NetworkConnection interface {
	PeerName() string
	IsConnected() bool
	SendFileSyncEvent(event types.FileSyncEvent) error
	// SendMessage is for sending general protocol messages (ACK, PULL_REQUEST, FILE_REMOVED, SAME_CONTENT)
	SendMessage(msgType types.MessageType, payload interface{}) error
	// PullFile is specifically for requesting file bodies from a remote peer
	PullFile(request types.FileSyncEvent) ([]byte, error)
	// ReadResponse is for receiving responses from the peer for sent messages
	ReadResponse() (types.MessageType, interface{}, error)
	Close() error
}

// <PERSON><PERSON><PERSON> defines the interface for file system utilities required by the SyncCore.
type FSUtil interface {
	GetFileStat(path string) (os.FileInfo, error)
	GetFileOwnership(path string) (uint32, uint32, error)
	CalculateXXHash(filePath string, strategy types.CompareStrategy) ([]byte, error)
	ReadFile(path string) ([]byte, error)
	ExtractSymlinkTarget(path string) (string, error)
	IsSymlinkWithinSyncRoot(symlinkPath, symlinkTarget, syncRoot string) bool
	AtomicWrite(filePath string, data []byte, perm os.FileMode) error
	CreateDir(path string, perm os.FileMode) error
	Remove(path string) error
	Chmod(path string, perm os.FileMode) error
	Chown(path string, uid, gid uint32) error
	Chtimes(path string, atime, mtime time.Time) error
	Rename(oldPath, newPath string) error
}

// MetaManager defines the interface for managing peer metadata and queues.
type MetaManager interface {
	Load() (map[string]*types.PairStatus, error)
	Save(statuses map[string]*types.PairStatus) error
	GetQueue(peerName string) (MetaQueue, error)
	Close() error
	ClearQueue(peerName string) error
}
