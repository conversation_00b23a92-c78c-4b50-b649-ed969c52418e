package interfaces

import (
	"os"
	"testing"
	"time"

	"github.com/real-rm/folder_sync/pkg/types"
)

// TestInterfaceDefinitions verifies that all interfaces are properly defined
// and can be implemented by checking their method signatures.
func TestInterfaceDefinitions(t *testing.T) {
	// This test ensures that the interfaces compile and have the expected methods.
	// We don't test functionality here since these are just interface definitions.

	// Test that we can create variables of interface types
	var metaQueue MetaQueue
	var networkConnection NetworkConnection
	var fsUtil FSUtil
	var metaManager MetaManager

	// Verify interfaces are not nil when assigned
	if metaQueue != nil {
		t.Error("MetaQueue interface should be nil when not implemented")
	}
	if networkConnection != nil {
		t.Error("NetworkConnection interface should be nil when not implemented")
	}
	if fsUtil != nil {
		t.Error("FSUtil interface should be nil when not implemented")
	}
	if metaManager != nil {
		t.Error("MetaManager interface should be nil when not implemented")
	}
}

// Mock implementations to verify interfaces can be implemented

type mockMetaQueue struct{}

func (m *mockMetaQueue) AddEvent(event types.FileSyncEvent) error    { return nil }
func (m *mockMetaQueue) GetNextEvent() (*types.FileSyncEvent, error) { return nil, nil }
func (m *mockMetaQueue) RemoveEvent(messageID uint64) error          { return nil }
func (m *mockMetaQueue) GetEventByMessageID(messageID uint64) (*types.FileSyncEvent, error) {
	return nil, nil
}
func (m *mockMetaQueue) GetEvents() ([]types.FileSyncEvent, error) { return nil, nil }
func (m *mockMetaQueue) Count() (int, error)                       { return 0, nil }
func (m *mockMetaQueue) Close() error                              { return nil }
func (m *mockMetaQueue) Clear() error                              { return nil }

type mockNetworkConnection struct{}

func (m *mockNetworkConnection) PeerName() string                                  { return "" }
func (m *mockNetworkConnection) IsConnected() bool                                 { return false }
func (m *mockNetworkConnection) SendFileSyncEvent(event types.FileSyncEvent) error { return nil }
func (m *mockNetworkConnection) SendMessage(msgType types.MessageType, payload interface{}) error {
	return nil
}
func (m *mockNetworkConnection) PullFile(request types.FileSyncEvent) ([]byte, error) {
	return nil, nil
}
func (m *mockNetworkConnection) ReadResponse() (types.MessageType, interface{}, error) {
	return 0, nil, nil
}
func (m *mockNetworkConnection) Close() error { return nil }

type mockFSUtil struct{}

func (m *mockFSUtil) GetFileStat(path string) (os.FileInfo, error)         { return nil, nil }
func (m *mockFSUtil) GetFileOwnership(path string) (uint32, uint32, error) { return 0, 0, nil }
func (m *mockFSUtil) CalculateXXHash(filePath string, strategy types.CompareStrategy) ([]byte, error) {
	return nil, nil
}
func (m *mockFSUtil) ReadFile(path string) ([]byte, error)             { return nil, nil }
func (m *mockFSUtil) ExtractSymlinkTarget(path string) (string, error) { return "", nil }
func (m *mockFSUtil) IsSymlinkWithinSyncRoot(symlinkPath, symlinkTarget, syncRoot string) bool {
	return true
}
func (m *mockFSUtil) AtomicWrite(filePath string, data []byte, perm os.FileMode) error { return nil }
func (m *mockFSUtil) CreateDir(path string, perm os.FileMode) error                    { return nil }
func (m *mockFSUtil) Remove(path string) error                                         { return nil }
func (m *mockFSUtil) Chmod(path string, perm os.FileMode) error                        { return nil }
func (m *mockFSUtil) Chown(path string, uid, gid uint32) error                         { return nil }
func (m *mockFSUtil) Chtimes(path string, atime, mtime time.Time) error                { return nil }
func (m *mockFSUtil) Rename(oldPath, newPath string) error                             { return nil }

type mockMetaManager struct{}

func (m *mockMetaManager) Load() (map[string]*types.PairStatus, error)      { return nil, nil }
func (m *mockMetaManager) Save(statuses map[string]*types.PairStatus) error { return nil }
func (m *mockMetaManager) GetQueue(peerName string) (MetaQueue, error)      { return nil, nil }
func (m *mockMetaManager) Close() error                                     { return nil }
func (m *mockMetaManager) ClearQueue(peerName string) error                 { return nil }

// TestInterfaceImplementations verifies that the mock implementations
// satisfy the interface contracts.
func TestInterfaceImplementations(t *testing.T) {
	// Test that mock implementations satisfy the interfaces
	var metaQueue MetaQueue = &mockMetaQueue{}
	var networkConnection NetworkConnection = &mockNetworkConnection{}
	var fsUtil FSUtil = &mockFSUtil{}
	var metaManager MetaManager = &mockMetaManager{}

	// Verify interfaces are properly implemented
	if metaQueue == nil {
		t.Error("mockMetaQueue should implement MetaQueue interface")
	}
	if networkConnection == nil {
		t.Error("mockNetworkConnection should implement NetworkConnection interface")
	}
	if fsUtil == nil {
		t.Error("mockFSUtil should implement FSUtil interface")
	}
	if metaManager == nil {
		t.Error("mockMetaManager should implement MetaManager interface")
	}
}

// TestMetaQueueInterface tests that MetaQueue interface methods can be called
func TestMetaQueueInterface(t *testing.T) {
	var queue MetaQueue = &mockMetaQueue{}

	// Test all methods can be called without panic
	event := types.FileSyncEvent{MessageID: 1}

	if err := queue.AddEvent(event); err != nil {
		t.Errorf("AddEvent failed: %v", err)
	}

	if _, err := queue.GetNextEvent(); err != nil {
		t.Errorf("GetNextEvent failed: %v", err)
	}

	if err := queue.RemoveEvent(1); err != nil {
		t.Errorf("RemoveEvent failed: %v", err)
	}

	if _, err := queue.GetEvents(); err != nil {
		t.Errorf("GetEvents failed: %v", err)
	}

	if _, err := queue.Count(); err != nil {
		t.Errorf("Count failed: %v", err)
	}

	if err := queue.Clear(); err != nil {
		t.Errorf("Clear failed: %v", err)
	}

	if err := queue.Close(); err != nil {
		t.Errorf("Close failed: %v", err)
	}
}

// TestNetworkConnectionInterface tests that NetworkConnection interface methods can be called
func TestNetworkConnectionInterface(t *testing.T) {
	var conn NetworkConnection = &mockNetworkConnection{}

	// Test all methods can be called without panic
	if name := conn.PeerName(); name != "" {
		t.Errorf("Expected empty peer name, got: %s", name)
	}

	if connected := conn.IsConnected(); connected {
		t.Error("Expected connection to be false")
	}

	event := types.FileSyncEvent{MessageID: 1}
	if err := conn.SendFileSyncEvent(event); err != nil {
		t.Errorf("SendFileSyncEvent failed: %v", err)
	}

	if err := conn.SendMessage(types.MSG_SYNC_ACKNOWLEDGE, nil); err != nil {
		t.Errorf("SendMessage failed: %v", err)
	}

	if _, err := conn.PullFile(event); err != nil {
		t.Errorf("PullFile failed: %v", err)
	}

	if _, _, err := conn.ReadResponse(); err != nil {
		t.Errorf("ReadResponse failed: %v", err)
	}

	if err := conn.Close(); err != nil {
		t.Errorf("Close failed: %v", err)
	}
}

// TestFSUtilInterface tests that FSUtil interface methods can be called
func TestFSUtilInterface(t *testing.T) {
	var fs FSUtil = &mockFSUtil{}

	// Test all methods can be called without panic
	if _, err := fs.GetFileStat("test"); err != nil {
		t.Errorf("GetFileStat failed: %v", err)
	}

	if _, _, err := fs.GetFileOwnership("test"); err != nil {
		t.Errorf("GetFileOwnership failed: %v", err)
	}

	if _, err := fs.CalculateXXHash("test", types.FULL_FILE); err != nil {
		t.Errorf("CalculateXXHash failed: %v", err)
	}

	if _, err := fs.ReadFile("test"); err != nil {
		t.Errorf("ReadFile failed: %v", err)
	}

	if _, err := fs.ExtractSymlinkTarget("test"); err != nil {
		t.Errorf("ExtractSymlinkTarget failed: %v", err)
	}

	if err := fs.AtomicWrite("test", []byte("data"), 0644); err != nil {
		t.Errorf("AtomicWrite failed: %v", err)
	}

	if err := fs.CreateDir("test", 0755); err != nil {
		t.Errorf("CreateDir failed: %v", err)
	}

	if err := fs.Remove("test"); err != nil {
		t.Errorf("Remove failed: %v", err)
	}

	if err := fs.Chmod("test", 0644); err != nil {
		t.Errorf("Chmod failed: %v", err)
	}

	if err := fs.Chown("test", 1000, 1000); err != nil {
		t.Errorf("Chown failed: %v", err)
	}

	if err := fs.Rename("old", "new"); err != nil {
		t.Errorf("Rename failed: %v", err)
	}
}

// TestMetaManagerInterface tests that MetaManager interface methods can be called
func TestMetaManagerInterface(t *testing.T) {
	var manager MetaManager = &mockMetaManager{}

	// Test all methods can be called without panic
	if _, err := manager.Load(); err != nil {
		t.Errorf("Load failed: %v", err)
	}

	statuses := make(map[string]*types.PairStatus)
	if err := manager.Save(statuses); err != nil {
		t.Errorf("Save failed: %v", err)
	}

	if _, err := manager.GetQueue("test"); err != nil {
		t.Errorf("GetQueue failed: %v", err)
	}

	if err := manager.ClearQueue("test"); err != nil {
		t.Errorf("ClearQueue failed: %v", err)
	}

	if err := manager.Close(); err != nil {
		t.Errorf("Close failed: %v", err)
	}
}
