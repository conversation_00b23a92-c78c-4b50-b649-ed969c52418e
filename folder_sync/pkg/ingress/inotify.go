package ingress

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/real-rm/folder_sync/pkg/fsutil"
	"github.com/real-rm/folder_sync/pkg/logger"
	"github.com/real-rm/folder_sync/pkg/types"
)

// F<PERSON>til defines the interface for file system utilities required by InotifyWatcher.
type FSUtil interface {
	GetFileStat(path string) (os.FileInfo, error)
	GetFileOwnership(path string) (uint32, uint32, error)
	CalculateXXHash(filePath string, strategy types.CompareStrategy) ([]byte, error)
	ReadFile(path string) ([]byte, error)
	ExtractSymlinkTarget(path string) (string, error)
	IsSymlinkWithinSyncRoot(symlinkPath, symlinkTarget, syncRoot string) bool
}

// DebounceConfig holds configuration for event debouncing
type DebounceConfig struct {
	WindowMs             int  // Debounce window in milliseconds
	BufferSize           int  // Maximum events to buffer
	ConsolidationEnabled bool // Enable event consolidation
	TempFileDebounceMs   int  // Extended debounce for temp files
}

// InotifyWatcher watches a directory for file system events using inotify.
type InotifyWatcher struct {
	watcher           *fsnotify.Watcher
	syncPath          string
	ignoreMatcher     types.IgnoredPathMatcher
	fsUtil            FSUtil
	compareStrategy   types.CompareStrategy
	eventChan         chan<- types.FileSyncEvent
	errorChan         chan error
	log               *logger.Logger
	done              chan struct{}
	wg                sync.WaitGroup
	idCounter         uint64 // Counter for generating unique MessageIDs
	idCounterMutex    sync.Mutex
	watchingDirs      map[string]bool // Keep track of directories being watched
	watchingDirsMutex sync.RWMutex
	atomicClosed      int32      // Atomic flag to track if Close() has been called
	wgMutex           sync.Mutex // Protects access to the WaitGroup

	// Debounce functionality
	debounceBuffer *types.DebounceBuffer
	debouncedChan  chan types.FileSyncEvent // Internal channel for debounced events
	debounceConfig DebounceConfig           // Configuration for debouncing
}

// NewInotifyWatcher creates and initializes a new InotifyWatcher.
func NewInotifyWatcher(
	syncPath string,
	ignoreMatcher types.IgnoredPathMatcher,
	fsUtil FSUtil,
	compareStrategy types.CompareStrategy,
	eventChan chan<- types.FileSyncEvent,
	logName string,
	debounceConfig DebounceConfig,
) (*InotifyWatcher, error) {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return nil, err
	}

	// Create a temporary logger for inotify watcher - in a real implementation, this should be passed in
	inotifyLogger, err := logger.NewLogger("/tmp/logs", false)
	if err != nil {
		return nil, fmt.Errorf("failed to create inotify logger: %w", err)
	}

	// Create internal channel for debounced events
	debouncedChan := make(chan types.FileSyncEvent, debounceConfig.BufferSize)

	w := &InotifyWatcher{
		watcher:         watcher,
		syncPath:        syncPath,
		ignoreMatcher:   ignoreMatcher,
		fsUtil:          fsUtil,
		compareStrategy: compareStrategy,
		eventChan:       eventChan,
		errorChan:       make(chan error, 1), // Internal error channel for watcher errors
		log:             inotifyLogger,
		done:            make(chan struct{}),
		watchingDirs:    make(map[string]bool),
		debouncedChan:   debouncedChan,
		debounceConfig:  debounceConfig,
	}

	// Initialize debounce buffer
	w.debounceBuffer = types.NewDebounceBuffer(debounceConfig.WindowMs, debouncedChan)

	// Add the syncPath and all its subdirectories to the watcher recursively
	err = w.addPathRecursive(syncPath)
	if err != nil {
		w.watcher.Close()
		return nil, err
	}

	return w, nil
}

// Start begins listening for file system events. This should be run in a goroutine.
func (w *InotifyWatcher) Start() {
	defer close(w.errorChan)
	defer close(w.debouncedChan)
	defer w.log.Debug("InotifyWatcher stopped")

	w.log.Info("InotifyWatcher started for path: %s", w.syncPath)

	// Start debounce flush goroutine
	w.wg.Add(1)
	go func() {
		defer w.wg.Done()
		w.runDebounceFlushLoop()
	}()

	// Start debounced event forwarding goroutine
	w.wg.Add(1)
	go func() {
		defer w.wg.Done()
		w.forwardDebouncedEvents()
	}()

	for {
		// Check if watcher is closed using atomic operation
		if atomic.LoadInt32(&w.atomicClosed) == 1 {
			return
		}

		select {
		case event, ok := <-w.watcher.Events:
			if !ok {
				return // Channel closed
			}
			// Check again after receiving event in case we're closing
			if atomic.LoadInt32(&w.atomicClosed) == 1 {
				return
			}
			w.wgMutex.Lock()
			if atomic.LoadInt32(&w.atomicClosed) == 0 {
				w.wg.Add(1)
				w.wgMutex.Unlock()
				go func(e fsnotify.Event) {
					defer w.wg.Done()
					w.processFsnotifyEvent(e)
				}(event)
			} else {
				w.wgMutex.Unlock()
			}
		case err, ok := <-w.watcher.Errors:
			if !ok {
				return // Channel closed
			}
			// Check again after receiving error in case we're closing
			if atomic.LoadInt32(&w.atomicClosed) == 1 {
				return
			}
			w.log.Error("InotifyWatcher error: %v", err)
			w.errorChan <- err // Send critical errors to an external error channel if needed
		}
	}
}

// Close stops the watcher and cleans up resources.
func (w *InotifyWatcher) Close() {
	w.log.Debug("Closing InotifyWatcher...")

	// Use atomic operation to set closed flag
	if atomic.CompareAndSwapInt32(&w.atomicClosed, 0, 1) {
		// Signal all goroutines to stop
		close(w.done)

		// Close the underlying watcher to unblock the select statement
		w.watcher.Close()
	}

	// Wait for all event processing goroutines to finish
	w.wgMutex.Lock()
	w.wg.Wait()
	w.wgMutex.Unlock()

	w.log.Info("InotifyWatcher gracefully shut down.")
}

// GetErrorChannel returns the internal error channel for watcher errors.
func (w *InotifyWatcher) GetErrorChannel() <-chan error {
	return w.errorChan
}

// addPathRecursive adds a path and all its subdirectories to the watcher.
func (w *InotifyWatcher) addPathRecursive(root string) error {
	return filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			w.log.Error("Error walking path %q: %v", path, err)
			return nil // Continue walking other paths
		}

		// Only add directories to the watcher
		if info.IsDir() {
			relPath, err := filepath.Rel(w.syncPath, path)
			if err != nil {
				w.log.Warn("Could not get relative path for %q: %v", path, err)
				return nil // Skip this path but continue walking
			}
			if w.ignoreMatcher.IsIgnored(relPath) {
				w.log.Debug("Ignoring directory %q from watch", path)
				return filepath.SkipDir // Skip this directory and its contents
			}

			w.watchingDirsMutex.Lock()
			w.watchingDirs[path] = true
			w.watchingDirsMutex.Unlock()

			if err := w.watcher.Add(path); err != nil {
				w.log.Error("Failed to add watch for %q: %v", path, err)
				// Don't return error, try to continue with other paths
			} else {
				w.log.Debug("Added watch for directory: %q", path)
			}
		}
		return nil
	})
}

// removePathFromWatcher removes a path from the watcher.
func (w *InotifyWatcher) removePathFromWatcher(path string) {
	w.watchingDirsMutex.Lock()
	delete(w.watchingDirs, path)
	w.watchingDirsMutex.Unlock()

	if err := w.watcher.Remove(path); err != nil {
		w.log.Warn("Failed to remove watch for %q: %v", path, err)
	} else {
		w.log.Debug("Removed watch for directory: %q", path)
	}
}

// isPathBeingWatched checks if a path is currently being watched.
func (w *InotifyWatcher) isPathBeingWatched(path string) bool {
	w.watchingDirsMutex.RLock()
	defer w.watchingDirsMutex.RUnlock()
	_, ok := w.watchingDirs[path]
	return ok
}

// processFsnotifyEvent translates a fsnotify event into a FileSyncEvent and sends it.
func (w *InotifyWatcher) processFsnotifyEvent(event fsnotify.Event) {
	relPath, err := filepath.Rel(w.syncPath, event.Name)
	if err != nil {
		w.log.Error("Failed to get relative path for %q: %v", event.Name, err)
		return
	}

	if w.ignoreMatcher.IsIgnored(relPath) {
		w.log.Debug("Ignored event for path %q", relPath)
		return
	}

	// For renames, the event.Name might be the *old* path.
	// We need to determine the new path if it's a "MOVED_FROM" event,
	// and track "MOVED_TO" for the full rename operation.
	// fsnotify provides paired RENAME events, but handling them
	// requires state. For simplicity, we'll treat RENAME as a DELETE+CREATE
	// for now, and rely on the sync core to reconcile.
	// However, it's better to explicitly handle RENAME_FILE/DIR.

	// A common pattern with fsnotify is that a RENAME_FROM event is immediately
	// followed by a RENAME_TO event. We can't easily pair them here
	// without adding state to the watcher, which complicates it.
	// The current approach processes each event individually, which might lead
	// to a DELETE and then a CREATE event for a rename.
	// The sync core will need to be smart about consolidating these.

	// For CREATE/WRITE/CHMOD, we need to stat the file. For DELETE, the file might be gone.
	// For RENAME_FROM, we should consider it a delete from the old path.
	// For RENAME_TO, consider it a create at the new path.

	info, statErr := w.fsUtil.GetFileStat(event.Name)
	isDir := false
	if statErr == nil {
		isDir = info.IsDir()
	} else if os.IsNotExist(statErr) {
		// File/dir doesn't exist, likely a DELETE event.
		// For RENAME_FROM, statErr will be non-nil. For actual DELETE, it will be nil.
		// If statErr is not nil, and it's not a Dir event, assume it's a file.
		isDir = event.Op&fsnotify.Remove != 0 && !strings.Contains(event.Name, ".") // Heuristic: if removed and no extension, likely a dir. Not perfect.
	}

	// Special handling for RENAME events
	if event.Op&fsnotify.Rename != 0 {
		// fsnotify sends a RENAME event for both the old path (MOVED_FROM)
		// and the new path (MOVED_TO). It's tricky to pair them without state.
		// For simplicity, treat a RENAME event as a DELETE from the old path
		// and a CREATE at the new path. The sync core will handle the reconciliation.
		// Alternatively, if we get the MOVED_TO event and the old path is known,
		// we can emit a RENAME_FILE/DIR event. This requires more complex state.
		// For now, if we cannot stat the new path, assume it's a delete.
		if os.IsNotExist(statErr) {
			w.log.Debug("Inotify: Treating RENAME (old path) as DELETE for %q", relPath)
			w.handleFsnotifyEvent(event.Name, fsnotify.Remove, false, isDir)
			// Remove watch if it was a directory that was renamed/moved out
			if isDir {
				w.removePathFromWatcher(event.Name)
			}
		} else {
			// This is likely the "MOVED_TO" part of a rename.
			w.log.Debug("Inotify: Treating RENAME (new path) as CREATE/WRITE for %q", relPath)
			if info.IsDir() {
				w.handleFsnotifyEvent(event.Name, fsnotify.Create, false, true)
				// If a new directory was created/renamed into place, add a watch to it synchronously
				if !w.isPathBeingWatched(event.Name) {
					if err := w.addPathRecursive(event.Name); err != nil {
						w.log.Error("Failed to add recursive watch for renamed directory %q: %v", event.Name, err)
					} else {
						// Scan for missed files after adding watches
						go func(dirPath string) {
							time.Sleep(100 * time.Millisecond)
							w.scanForMissedFiles(dirPath)
						}(event.Name)
					}
				}
			} else {
				w.handleFsnotifyEvent(event.Name, fsnotify.Write, false, false)
			}
		}
		return
	}

	// Handle other event types
	if event.Op&fsnotify.Create != 0 {
		w.handleFsnotifyEvent(event.Name, fsnotify.Create, false, isDir)
		if isDir {
			// If a new directory was created, add a watch to it synchronously
			// to prevent race conditions where files are created before watches are established
			if err := w.addPathRecursive(event.Name); err != nil {
				w.log.Error("Failed to add recursive watch for new directory %q: %v", event.Name, err)
			} else {
				// After adding watches, scan for any files that might have been created
				// while we were setting up the watches (race condition mitigation)
				go func(dirPath string) {
					time.Sleep(100 * time.Millisecond) // Small delay to let file operations complete
					w.scanForMissedFiles(dirPath)
				}(event.Name)
			}
		}
	}
	if event.Op&fsnotify.Write != 0 {
		// A write event can also indicate a size change, mtime change, or content change.
		// If it's a directory, it usually means contents changed (file/sub-dir created/deleted)
		// but we add explicit watches for subdirectories, so this is likely a file write.
		w.handleFsnotifyEvent(event.Name, fsnotify.Write, false, isDir)
	}
	if event.Op&fsnotify.Remove != 0 {
		w.handleFsnotifyEvent(event.Name, fsnotify.Remove, false, isDir)
		if isDir {
			// If a directory was removed, remove its watch
			w.removePathFromWatcher(event.Name)
		}
	}
	if event.Op&fsnotify.Chmod != 0 {
		// Chmod events usually don't change size or content, only permissions.
		w.handleFsnotifyEvent(event.Name, fsnotify.Chmod, false, isDir)
	}
}

// handleFsnotifyEvent translates a raw fsnotify event into a FileSyncEvent.
// This function is internal and used by processFsnotifyEvent and for testing.
func (w *InotifyWatcher) handleFsnotifyEvent(fullPath string, op fsnotify.Op, isSymlink, isDirHint bool) {
	relPath, err := filepath.Rel(w.syncPath, fullPath)
	if err != nil {
		w.log.Error("Failed to get relative path for %q: %v", fullPath, err)
		return
	}

	// Check ignore patterns, but allow DELETE events through for cleanup
	// DELETE events should be processed even for ignored files to clean up
	// files that were synced before ignore patterns were applied
	if w.ignoreMatcher.IsIgnored(relPath) && op != fsnotify.Remove {
		w.log.Debug("Ignored event for path %q (not a delete)", relPath)
		return
	}

	event := types.FileSyncEvent{
		Path:      relPath,
		Origin:    types.LOCAL_INOTIFY,
		MessageID: w.generateMessageID(),
	}

	var fileInfo os.FileInfo
	var statErr error
	var uid, gid uint32
	var fileBody []byte
	var checksum []byte

	// Try to get file info for non-delete events
	if op != fsnotify.Remove {
		fileInfo, statErr = w.fsUtil.GetFileStat(fullPath)
		if statErr != nil {
			if os.IsNotExist(statErr) {
				w.log.Warn("Inotify: File %q not found for event %s, treating as delete", fullPath, op)
				event.Type = types.DELETE_FILE // Default to file, might be dir
				if isDirHint {
					event.Type = types.DELETE_DIR
				}
				w.sendEvent(event)
				return
			}
			w.log.Error("Inotify: Failed to stat %q for event %s: %v", fullPath, op, statErr)
			return
		}

		isDirHint = fileInfo.IsDir() // Update isDirHint based on actual stat

		// Symlink handling
		if (fileInfo.Mode()&os.ModeSymlink) != 0 && !isSymlink { // If it's a symlink and not already marked as one
			symlinkTarget, err := w.fsUtil.ExtractSymlinkTarget(fullPath)
			if err != nil {
				w.log.Error("Failed to read symlink target for %q: %v", fullPath, err)
				return
			}
			// Decide if we sync the symlink or ignore it. Specification says "Ignore and discard symlinks pointing outside the sync folder."
			// Use the proper symlink resolution logic to handle both absolute and relative symlinks
			if !w.fsUtil.IsSymlinkWithinSyncRoot(fullPath, symlinkTarget, w.syncPath) {
				w.log.Info("Ignoring symlink %q pointing outside sync folder: %q", relPath, symlinkTarget)
				return
			}
			event.Type = types.CREATE_SYMLINK
			event.Timestamp = fileInfo.ModTime()
			event.FileMode = uint32(fileInfo.Mode().Perm()) // Permissions of the symlink itself
			// For symlinks, Size and FileBody are not typically used to represent target content
			// We might want to put the target path in FileBody later if needed.
			event.Size = 0                         // Size of symlink itself is usually small, not target size
			event.FileBody = []byte(symlinkTarget) // Store target path in FileBody
			w.sendEvent(event)
			return
		}

		event.Timestamp = fileInfo.ModTime()
		event.Size = uint64(fileInfo.Size())
		event.FileMode = uint32(fileInfo.Mode().Perm()) // Only permission bits

		uid, gid, err = w.fsUtil.GetFileOwnership(fullPath)
		if err != nil {
			w.log.Warn("Failed to get ownership for %q: %v", fullPath, err)
		} else {
			event.OwnerUID = uid
			event.OwnerGID = gid
		}
	}

	switch op {
	case fsnotify.Create:
		if isDirHint {
			event.Type = types.CREATE_DIR
		} else {
			event.Type = types.CREATE_FILE
			// Read file body and calculate checksum for small files or initial event metadata
			fileBody, err = w.fsUtil.ReadFile(fullPath)
			if err != nil {
				w.log.Error("Failed to read file %q for CREATE event: %v", fullPath, err)
				return
			}
			event.FileBody = fileBody
			if w.compareStrategy != types.SIZE_ONLY {
				checksum, err = w.fsUtil.CalculateXXHash(fullPath, w.compareStrategy)
				if err != nil {
					w.log.Error("Failed to calculate checksum for %q on CREATE: %v", fullPath, err)
					return
				}
				event.Checksum = checksum
			}
			// Decide whether to send chunks based on file size (configurable, for now just small files)
			// For simplicity, if fileBody is already read, SendsChunks is false.
			event.SendsChunks = false
		}
	case fsnotify.Write:
		if isDirHint {
			// A write event on a directory usually means metadata changed or something inside.
			// We typically don't send explicit WRITE_DIR events with content.
			// Other events (CREATE/DELETE_FILE/DIR) from children will cover this.
			// So, for now, ignore WRITE on directories.
			w.log.Debug("Ignoring WRITE event on directory: %q", relPath)
			return
		}
		event.Type = types.WRITE_FILE
		// Read file body and calculate checksum
		fileBody, err = w.fsUtil.ReadFile(fullPath)
		if err != nil {
			w.log.Error("Failed to read file %q for WRITE event: %v", fullPath, err)
			return
		}
		event.FileBody = fileBody
		if w.compareStrategy != types.SIZE_ONLY {
			checksum, err = w.fsUtil.CalculateXXHash(fullPath, w.compareStrategy)
			if err != nil {
				w.log.Error("Failed to calculate checksum for %q on WRITE: %v", fullPath, err)
				return
			}
			event.Checksum = checksum
		}
		event.SendsChunks = false // For now, assume full body sent with event
	case fsnotify.Remove:
		// When a directory is removed, fsnotify typically sends a REMOVE event for the directory itself,
		// and then no more events for its contents.
		// We need to determine if it was a file or directory based on the path.
		// The isDirHint might be inaccurate if the file/dir is already gone.
		// A common way to differentiate is checking if the path had an extension or was previously known as a dir.
		// For simplicity now, if fullPath was previously known to be a watched directory, assume DELETE_DIR.
		// Otherwise, default to DELETE_FILE.
		if w.isPathBeingWatched(fullPath) { // Check if we were watching this path as a directory
			event.Type = types.DELETE_DIR
			w.removePathFromWatcher(fullPath) // Remove watch for the deleted directory
		} else {
			// If it was a file, or a directory not explicitly watched, treat as file deletion.
			// This needs refinement for accurate directory deletion detection.
			event.Type = types.DELETE_FILE
		}
		event.Size = 0       // Size is 0 for deletions
		event.FileBody = nil // No body for deletions
		event.Checksum = nil // No checksum for deletions
	case fsnotify.Chmod:
		if isDirHint {
			// Chmod on directory, typically not synced as a content change
			w.log.Debug("Ignoring CHMOD event on directory: %q", relPath)
			return
		}
		event.Type = types.CHMOD_FILE
		event.Timestamp = fileInfo.ModTime()

		// Check if this CHMOD event might also involve content changes
		// This can happen with editors like vim that modify both content and permissions
		shouldIncludeContent := false

		// If the file size is reasonable (not too large), read content to detect changes
		if fileInfo.Size() <= int64(types.SmallFileThreshold) {
			// For small files, always include content in CHMOD events to ensure sync
			// This handles cases where editors only generate CHMOD events for file saves
			shouldIncludeContent = true
			w.log.Debug("Including content in CHMOD event for small file %q (size: %d)", relPath, fileInfo.Size())
		} else {
			// For larger files, we'll rely on the receiving side to request content if needed
			// based on size differences
			w.log.Debug("CHMOD event for large file %q (size: %d), content will be pulled if needed", relPath, fileInfo.Size())
		}

		if shouldIncludeContent {
			// Read file content for CHMOD events that might include content changes
			fileBody, err = w.fsUtil.ReadFile(fullPath)
			if err != nil {
				w.log.Error("Failed to read file %q for CHMOD event: %v", fullPath, err)
				return
			}
			event.FileBody = fileBody
			event.SendsChunks = false // Small files are sent directly
		} else {
			event.FileBody = nil // No body for chmod-only events
			// For large files, indicate that chunks might be needed
			event.SendsChunks = (fileInfo.Size() > int64(types.SmallFileThreshold))
		}

		// Calculate checksum for content comparison
		if w.compareStrategy != types.SIZE_ONLY {
			checksum, err = w.fsUtil.CalculateXXHash(fullPath, w.compareStrategy)
			if err != nil {
				w.log.Error("Failed to calculate checksum for %q on CHMOD: %v", fullPath, err)
				return
			}
			event.Checksum = checksum
		}
	default:
		w.log.Debug("Unhandled fsnotify event operation: %s for %q", op, relPath)
		return
	}

	w.sendEvent(event)
}

// generateMessageID generates a unique MessageID for FileSyncEvents.
func (w *InotifyWatcher) generateMessageID() uint64 {
	w.idCounterMutex.Lock()
	defer w.idCounterMutex.Unlock()
	w.idCounter++
	return w.idCounter
}

// InotifyFSUtil implements the FSUtil interface using standard os and fsutil package functions.
type InotifyFSUtil struct {
	syncPath string
}

// NewInotifyFSUtil creates a new InotifyFSUtil instance.
func NewInotifyFSUtil(syncPath string) *InotifyFSUtil {
	return &InotifyFSUtil{syncPath: syncPath}
}

// GetFileStat implements FSUtil.GetFileStat.
func (f *InotifyFSUtil) GetFileStat(path string) (os.FileInfo, error) {
	return os.Lstat(path)
}

// GetFileOwnership implements FSUtil.GetFileOwnership.
func (f *InotifyFSUtil) GetFileOwnership(path string) (uint32, uint32, error) {
	info, err := os.Stat(path)
	if err != nil {
		return 0, 0, err
	}
	if stat, ok := info.Sys().(*syscall.Stat_t); ok {
		return stat.Uid, stat.Gid, nil
	}
	return 0, 0, nil // Not supported on this OS or not a Unix system
}

// runDebounceFlushLoop periodically flushes expired events from the debounce buffer
func (w *InotifyWatcher) runDebounceFlushLoop() {
	// Calculate flush interval, with a minimum of 10ms
	flushInterval := time.Duration(w.debounceConfig.WindowMs/4) * time.Millisecond
	if flushInterval <= 0 {
		flushInterval = 10 * time.Millisecond // Minimum interval
	}

	ticker := time.NewTicker(flushInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			w.debounceBuffer.FlushExpiredEvents()
		case <-w.done:
			// Flush all remaining events before shutting down
			w.debounceBuffer.FlushAll()
			return
		}
	}
}

// forwardDebouncedEvents forwards events from the debounced channel to the main event channel
func (w *InotifyWatcher) forwardDebouncedEvents() {
	for {
		select {
		case event, ok := <-w.debouncedChan:
			if !ok {
				return // Channel closed
			}
			// Apply event consolidation if enabled
			if w.debounceConfig.ConsolidationEnabled {
				event = w.consolidateEvent(event)

				// Filter out invalid events (marked for suppression)
				if event.Type == types.FileSyncEventType(0) {
					w.log.Debug("Filtered out consolidated event for: %s", event.Path)
					continue
				}
			}
			w.eventChan <- event
			w.log.Debug("Forwarded debounced event: %s", event.String())
		case <-w.done:
			return
		}
	}
}

// consolidateEvent applies intelligent event consolidation logic
func (w *InotifyWatcher) consolidateEvent(event types.FileSyncEvent) types.FileSyncEvent {
	// Get the buffered event information for pattern analysis
	buffered, exists := w.debounceBuffer.GetBufferedEvent(event.Path)

	if !exists {
		return event // No buffered event, return as-is
	}

	w.log.Debug("Consolidating event for %s: %s", event.Path, buffered.GetEventSequenceInfo())

	// Rule 1: CHMOD followed by CREATE pattern (common with 'touch' command)
	chmodCreatePattern := []types.FileSyncEventType{types.CHMOD_FILE, types.CREATE_FILE}
	if buffered.HasEventPattern(chmodCreatePattern) && event.Type == types.CREATE_FILE {
		w.log.Debug("Detected CHMOD->CREATE pattern for %s, consolidating to CREATE", event.Path)
		// The CREATE event already has the correct permissions, just ensure we have latest info
		fullPath := filepath.Join(w.syncPath, event.Path)
		if fileInfo, err := w.fsUtil.GetFileStat(fullPath); err == nil {
			event.Timestamp = fileInfo.ModTime()
			event.FileMode = uint32(fileInfo.Mode().Perm())
			event.Size = uint64(fileInfo.Size())
		}
		return event
	}

	// Rule 2: Multiple CHMOD events - only send the last one
	if event.Type == types.CHMOD_FILE && buffered.EventCount > 1 {
		// Check if all recent events are CHMOD
		allChmod := true
		for _, eventType := range buffered.EventTypes {
			if eventType != types.CHMOD_FILE {
				allChmod = false
				break
			}
		}
		if allChmod {
			w.log.Debug("Multiple CHMOD events for %s, sending consolidated CHMOD", event.Path)
			return event // Send the latest CHMOD
		}
	}

	// Rule 3: For temporary files, suppress CHMOD events if followed by CREATE/WRITE
	if w.isTempFile(event.Path) && event.Type == types.CHMOD_FILE {
		// Check if there are more meaningful events in the sequence
		hasCreateOrWrite := false
		for _, eventType := range buffered.EventTypes {
			if eventType == types.CREATE_FILE || eventType == types.WRITE_FILE {
				hasCreateOrWrite = true
				break
			}
		}
		if hasCreateOrWrite {
			w.log.Debug("Suppressing CHMOD event for temporary file %s (has CREATE/WRITE)", event.Path)
			event.Type = types.FileSyncEventType(0) // Mark for filtering
			return event
		}
	}

	// Rule 4: For CREATE events, ensure we have the most recent file information
	if event.Type == types.CREATE_FILE {
		fullPath := filepath.Join(w.syncPath, event.Path)
		if fileInfo, err := w.fsUtil.GetFileStat(fullPath); err == nil {
			event.Timestamp = fileInfo.ModTime()
			event.FileMode = uint32(fileInfo.Mode().Perm())
			event.Size = uint64(fileInfo.Size())

			// Re-read file content to ensure consistency
			if fileBody, err := w.fsUtil.ReadFile(fullPath); err == nil {
				event.FileBody = fileBody

				// Recalculate checksum if needed
				if w.compareStrategy != types.SIZE_ONLY {
					if checksum, err := w.fsUtil.CalculateXXHash(fullPath, w.compareStrategy); err == nil {
						event.Checksum = checksum
					}
				}
			}
		}
	}

	return event
}

// scanForMissedFiles scans a directory for files that might have been created
// while watches were being established, to mitigate race conditions
func (w *InotifyWatcher) scanForMissedFiles(dirPath string) {
	w.log.Debug("Scanning for missed files in directory: %q", dirPath)

	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			w.log.Warn("Error scanning path %q for missed files: %v", path, err)
			return nil // Continue walking
		}

		// Get relative path
		relPath, err := filepath.Rel(w.syncPath, path)
		if err != nil {
			w.log.Warn("Failed to get relative path for %q during missed file scan: %v", path, err)
			return nil
		}

		// Skip root directory and the directory we're scanning itself
		if relPath == "." || path == dirPath {
			return nil
		}

		// Skip if ignored
		if w.ignoreMatcher.IsIgnored(relPath) {
			w.log.Debug("Skipping ignored path during missed file scan: %q", relPath)
			if info.IsDir() {
				return filepath.SkipDir
			}
			return nil
		}

		// Only process files (directories should have been caught by inotify)
		if !info.IsDir() {
			w.log.Debug("Found potentially missed file: %q", relPath)
			// Generate a CREATE_FILE event for this file
			w.handleFsnotifyEvent(path, fsnotify.Create, false, false)
		}

		return nil
	})

	if err != nil {
		w.log.Error("Failed to scan for missed files in %q: %v", dirPath, err)
	}
}

// isTempFile checks if a file path matches common temporary file patterns
func (w *InotifyWatcher) isTempFile(path string) bool {
	filename := filepath.Base(path)

	// Common temporary file patterns
	tempPatterns := []string{
		".tmp-*",    // atomic write temp files
		"*~",        // backup files (any file ending with ~)
		".#*",       // emacs temp files
		"#*#",       // emacs auto-save files
		".DS_Store", // macOS metadata
		"Thumbs.db", // Windows thumbnails
	}

	for _, pattern := range tempPatterns {
		if matched, _ := filepath.Match(pattern, filename); matched {
			return true
		}
	}

	return false
}

// sendEvent sends an event through the debounce buffer or directly based on configuration
func (w *InotifyWatcher) sendEvent(event types.FileSyncEvent) {
	// Determine debounce window based on file type
	windowMs := w.debounceConfig.WindowMs
	if w.isTempFile(event.Path) {
		windowMs = w.debounceConfig.TempFileDebounceMs
	}

	// If debouncing is disabled (windowMs = 0), send directly
	if windowMs == 0 {
		w.eventChan <- event
		w.log.Debug("Sent event directly (no debounce): %s", event.String())
		return
	}

	// Add to debounce buffer
	w.debounceBuffer.AddEvent(event)
	w.log.Debug("Added event to debounce buffer: %s", event.String())
}

// CalculateXXHash implements FSUtil.CalculateXXHash.
func (f *InotifyFSUtil) CalculateXXHash(filePath string, strategy types.CompareStrategy) ([]byte, error) {
	return fsutil.CalculateXXHash(filePath, strategy)
}

// ReadFile implements FSUtil.ReadFile.
func (f *InotifyFSUtil) ReadFile(path string) ([]byte, error) {
	return os.ReadFile(path)
}

// ExtractSymlinkTarget implements FSUtil.ExtractSymlinkTarget.
func (f *InotifyFSUtil) ExtractSymlinkTarget(path string) (string, error) {
	return os.Readlink(path)
}

// IsSymlinkWithinSyncRoot implements FSUtil.IsSymlinkWithinSyncRoot.
func (f *InotifyFSUtil) IsSymlinkWithinSyncRoot(symlinkPath, symlinkTarget, syncRoot string) bool {
	// If the target is an absolute path, check directly
	if filepath.IsAbs(symlinkTarget) {
		return strings.HasPrefix(filepath.Clean(symlinkTarget), filepath.Clean(syncRoot))
	}

	// For relative targets, resolve to absolute path relative to the symlink's directory
	absTargetPath := filepath.Clean(filepath.Join(filepath.Dir(symlinkPath), symlinkTarget))
	return strings.HasPrefix(absTargetPath, filepath.Clean(syncRoot))
}
