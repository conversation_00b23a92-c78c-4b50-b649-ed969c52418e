package ingress

import (
	"testing"

	"github.com/fsnotify/fsnotify"
	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/types"
)

// TestDeleteEventsIgnoreLogic tests the logic for allowing DELETE events through ignore patterns
func TestDeleteEventsIgnoreLogic(t *testing.T) {
	// Create config with ignore patterns for vim files
	cfg := &config.Config{}
	cfg.Folder.Ignores = []struct {
		Pattern string `yaml:"pattern"`
		Type    string `yaml:"type"`
	}{
		{Pattern: `^\..*\.swp$`, Type: "regexp"}, // vim swap files
		{Pattern: `.*~$`, Type: "regexp"},        // vim backup files
	}

	// Create pattern matcher
	ignoreMatcher, err := cfg.NewPatternMatcher()
	if err != nil {
		t.Fatalf("Failed to create pattern matcher: %v", err)
	}

	// Test cases
	testCases := []struct {
		filePath     string
		operation    fsnotify.Op
		shouldIgnore bool
		description  string
	}{
		// Vim swap files
		{".test.txt.swp", fsnotify.Create, true, "CREATE event for vim swap file should be ignored"},
		{".test.txt.swp", fsnotify.Write, true, "WRITE event for vim swap file should be ignored"},
		{".test.txt.swp", fsnotify.Remove, false, "DELETE event for vim swap file should NOT be ignored"},

		// Vim backup files
		{"test.txt~", fsnotify.Create, true, "CREATE event for vim backup file should be ignored"},
		{"test.txt~", fsnotify.Write, true, "WRITE event for vim backup file should be ignored"},
		{"test.txt~", fsnotify.Remove, false, "DELETE event for vim backup file should NOT be ignored"},

		// Normal files
		{"test.txt", fsnotify.Create, false, "CREATE event for normal file should NOT be ignored"},
		{"test.txt", fsnotify.Write, false, "WRITE event for normal file should NOT be ignored"},
		{"test.txt", fsnotify.Remove, false, "DELETE event for normal file should NOT be ignored"},
	}

	for _, tc := range testCases {
		// Apply the same logic as in inotify.go
		shouldIgnore := ignoreMatcher.IsIgnored(tc.filePath) && tc.operation != fsnotify.Remove

		if shouldIgnore != tc.shouldIgnore {
			t.Errorf("%s: expected shouldIgnore=%v, got shouldIgnore=%v",
				tc.description, tc.shouldIgnore, shouldIgnore)
		} else {
			t.Logf("✓ %s", tc.description)
		}
	}
}

// TestProxySocketDeleteEventsLogic tests the logic for allowing DELETE events through ignore patterns in proxy socket
func TestProxySocketDeleteEventsLogic(t *testing.T) {
	// Create config with ignore patterns
	cfg := &config.Config{}
	cfg.Folder.Ignores = []struct {
		Pattern string `yaml:"pattern"`
		Type    string `yaml:"type"`
	}{
		{Pattern: `^\..*\.swp$`, Type: "regexp"}, // vim swap files
	}

	// Create pattern matcher
	ignoreMatcher, err := cfg.NewPatternMatcher()
	if err != nil {
		t.Fatalf("Failed to create pattern matcher: %v", err)
	}

	// Test cases for proxy socket events
	testCases := []struct {
		eventType    types.FileSyncEventType
		filePath     string
		shouldIgnore bool
		description  string
	}{
		// Vim swap files
		{types.CREATE_FILE, ".test.txt.swp", true, "CREATE_FILE event for vim swap file should be ignored"},
		{types.WRITE_FILE, ".test.txt.swp", true, "WRITE_FILE event for vim swap file should be ignored"},
		{types.DELETE_FILE, ".test.txt.swp", false, "DELETE_FILE event for vim swap file should NOT be ignored"},
		{types.DELETE_DIR, ".test.dir.swp", false, "DELETE_DIR event for vim swap dir should NOT be ignored"},

		// Normal files
		{types.CREATE_FILE, "test.txt", false, "CREATE_FILE event for normal file should NOT be ignored"},
		{types.WRITE_FILE, "test.txt", false, "WRITE_FILE event for normal file should NOT be ignored"},
		{types.DELETE_FILE, "test.txt", false, "DELETE_FILE event for normal file should NOT be ignored"},
	}

	for _, tc := range testCases {
		// Apply the same logic as in proxysocket.go
		shouldIgnore := ignoreMatcher.IsIgnored(tc.filePath) && tc.eventType != types.DELETE_FILE && tc.eventType != types.DELETE_DIR

		if shouldIgnore != tc.shouldIgnore {
			t.Errorf("%s: expected shouldIgnore=%v, got shouldIgnore=%v",
				tc.description, tc.shouldIgnore, shouldIgnore)
		} else {
			t.Logf("✓ %s", tc.description)
		}
	}
}
