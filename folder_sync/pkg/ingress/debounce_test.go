package ingress

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/real-rm/folder_sync/pkg/types"
)

// TestDebounceBuffer tests the basic debounce buffer functionality
func TestDebounceBuffer(t *testing.T) {
	eventChan := make(chan types.FileSyncEvent, 10)
	buffer := types.NewDebounceBuffer(100, eventChan) // 100ms window

	// Test adding events
	event1 := types.FileSyncEvent{
		Type: types.CREATE_FILE,
		Path: "test.txt",
	}

	buffer.AddEvent(event1)

	// Check that event is buffered
	buffered, exists := buffer.GetBufferedEvent("test.txt")
	if !exists {
		t.Fatal("Expected event to be buffered")
	}
	if buffered.EventCount != 1 {
		t.Errorf("Expected event count 1, got %d", buffered.EventCount)
	}
	if len(buffered.EventTypes) != 1 || buffered.EventTypes[0] != types.CREATE_FILE {
		t.Errorf("Expected event types [CREATE_FILE], got %v", buffered.EventTypes)
	}

	// Add another event for the same path
	event2 := types.FileSyncEvent{
		Type: types.CHMOD_FILE,
		Path: "test.txt",
	}

	buffer.AddEvent(event2)

	// Check that event is updated
	buffered, exists = buffer.GetBufferedEvent("test.txt")
	if !exists {
		t.Fatal("Expected event to be buffered")
	}
	if buffered.EventCount != 2 {
		t.Errorf("Expected event count 2, got %d", buffered.EventCount)
	}
	if len(buffered.EventTypes) != 2 {
		t.Errorf("Expected 2 event types, got %d", len(buffered.EventTypes))
	}
	if buffered.EventTypes[0] != types.CREATE_FILE || buffered.EventTypes[1] != types.CHMOD_FILE {
		t.Errorf("Expected event types [CREATE_FILE, CHMOD_FILE], got %v", buffered.EventTypes)
	}

	// Test pattern detection
	pattern := []types.FileSyncEventType{types.CREATE_FILE, types.CHMOD_FILE}
	if !buffered.HasEventPattern(pattern) {
		t.Error("Expected to detect CREATE_FILE -> CHMOD_FILE pattern")
	}

	// Test flush
	buffer.FlushAll()

	// Check that event was sent
	select {
	case receivedEvent := <-eventChan:
		if receivedEvent.Type != types.CHMOD_FILE {
			t.Errorf("Expected CHMOD_FILE event, got %v", receivedEvent.Type)
		}
	case <-time.After(100 * time.Millisecond):
		t.Error("Expected event to be flushed")
	}
}

// TestDebounceBufferExpiration tests that events are flushed after the debounce window
func TestDebounceBufferExpiration(t *testing.T) {
	eventChan := make(chan types.FileSyncEvent, 10)
	buffer := types.NewDebounceBuffer(50, eventChan) // 50ms window

	event := types.FileSyncEvent{
		Type: types.CREATE_FILE,
		Path: "test.txt",
	}

	buffer.AddEvent(event)

	// Wait for expiration and flush
	time.Sleep(60 * time.Millisecond)
	buffer.FlushExpiredEvents()

	// Check that event was sent
	select {
	case receivedEvent := <-eventChan:
		if receivedEvent.Type != types.CREATE_FILE {
			t.Errorf("Expected CREATE_FILE event, got %v", receivedEvent.Type)
		}
	case <-time.After(100 * time.Millisecond):
		t.Error("Expected event to be flushed after expiration")
	}

	// Check that buffer is empty
	_, exists := buffer.GetBufferedEvent("test.txt")
	if exists {
		t.Error("Expected buffer to be empty after flush")
	}
}

// TestEventPatternDetection tests the event pattern detection functionality
func TestEventPatternDetection(t *testing.T) {
	buffered := &types.BufferedEvent{
		EventTypes: []types.FileSyncEventType{
			types.CHMOD_FILE,
			types.CREATE_FILE,
			types.WRITE_FILE,
		},
	}

	// Test exact pattern match
	pattern1 := []types.FileSyncEventType{types.CHMOD_FILE, types.CREATE_FILE, types.WRITE_FILE}
	if !buffered.HasEventPattern(pattern1) {
		t.Error("Expected to detect full pattern")
	}

	// Test suffix pattern match
	pattern2 := []types.FileSyncEventType{types.CREATE_FILE, types.WRITE_FILE}
	if !buffered.HasEventPattern(pattern2) {
		t.Error("Expected to detect suffix pattern")
	}

	// Test non-matching pattern
	pattern3 := []types.FileSyncEventType{types.DELETE_FILE, types.CREATE_FILE}
	if buffered.HasEventPattern(pattern3) {
		t.Error("Expected not to detect non-matching pattern")
	}

	// Test longer pattern than available
	pattern4 := []types.FileSyncEventType{
		types.CHMOD_FILE, types.CREATE_FILE, types.WRITE_FILE, types.DELETE_FILE,
	}
	if buffered.HasEventPattern(pattern4) {
		t.Error("Expected not to detect pattern longer than available events")
	}
}

// TestTempFileDetection tests the temporary file detection logic
func TestTempFileDetection(t *testing.T) {
	// Create a temporary directory for testing
	tempDir, err := os.MkdirTemp("", "debounce_test")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tempDir)

	// Create mock components
	ignoreMatcher := NewMockIgnoredPathMatcher()
	fsUtil := &MockFSUtil{}
	eventChan := make(chan types.FileSyncEvent, 10)

	debounceConfig := DebounceConfig{
		WindowMs:             100,
		BufferSize:           10,
		ConsolidationEnabled: true,
		TempFileDebounceMs:   500,
	}

	watcher, err := NewInotifyWatcher(
		tempDir,
		ignoreMatcher,
		fsUtil,
		types.SIZE_ONLY,
		eventChan,
		"test",
		debounceConfig,
	)
	if err != nil {
		t.Fatal(err)
	}
	defer watcher.Close()

	// Test temporary file patterns
	testCases := []struct {
		path     string
		expected bool
	}{
		{"file.txt", false},
		{".tmp-12345", true},
		{"document.txt~", true},
		{".#temp", true},
		{"#autosave#", true},
		{".DS_Store", true},
		{"Thumbs.db", true},
		{"normal.doc", false},
	}

	for _, tc := range testCases {
		result := watcher.isTempFile(tc.path)
		if result != tc.expected {
			t.Errorf("isTempFile(%q) = %v, expected %v", tc.path, result, tc.expected)
		}
	}
}

// TestEventConsolidation tests the event consolidation logic
func TestEventConsolidation(t *testing.T) {
	// Create a temporary directory for testing
	tempDir, err := os.MkdirTemp("", "debounce_consolidation_test")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tempDir)

	// Create mock components
	ignoreMatcher := NewMockIgnoredPathMatcher()
	fsUtil := &MockFSUtil{
		GetFileStatFunc: func(path string) (os.FileInfo, error) {
			// Return mock file info
			return &mockFileInfo{
				name:    filepath.Base(path),
				size:    100,
				mode:    0644,
				modTime: time.Now(),
				isDir:   false,
			}, nil
		},
		ReadFileFunc: func(path string) ([]byte, error) {
			return []byte("test content"), nil
		},
		CalculateXXHashFunc: func(path string, strategy types.CompareStrategy) ([]byte, error) {
			return []byte("mock-hash"), nil
		},
	}

	eventChan := make(chan types.FileSyncEvent, 10)

	debounceConfig := DebounceConfig{
		WindowMs:             50,
		BufferSize:           10,
		ConsolidationEnabled: true,
		TempFileDebounceMs:   100,
	}

	watcher, err := NewInotifyWatcher(
		tempDir,
		ignoreMatcher,
		fsUtil,
		types.SIZE_ONLY,
		eventChan,
		"test",
		debounceConfig,
	)
	if err != nil {
		t.Fatal(err)
	}
	defer watcher.Close()

	// Test consolidation by adding events to the buffer and then consolidating
	testFile := "test.txt"

	// Add CHMOD event first
	chmodEvent := types.FileSyncEvent{
		Type: types.CHMOD_FILE,
		Path: testFile,
	}
	watcher.debounceBuffer.AddEvent(chmodEvent)

	// Add CREATE event
	createEvent := types.FileSyncEvent{
		Type: types.CREATE_FILE,
		Path: testFile,
	}
	watcher.debounceBuffer.AddEvent(createEvent)

	// Test consolidation
	consolidated := watcher.consolidateEvent(createEvent)

	// Should still be CREATE_FILE (the meaningful event)
	if consolidated.Type != types.CREATE_FILE {
		t.Errorf("Expected CREATE_FILE after consolidation, got %v", consolidated.Type)
	}
}
