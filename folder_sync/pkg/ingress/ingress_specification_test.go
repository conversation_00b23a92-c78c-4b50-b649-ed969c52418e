package ingress_test

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/ingress"
	"github.com/real-rm/folder_sync/pkg/types"
)

// TestInotifyWatchRequirements tests inotify watch functionality as documented in section 4.6
func TestInotifyWatchRequirements(t *testing.T) {
	t.Run("inotify watch when monitor.socket not specified", func(t *testing.T) {
		// Test the documented requirement: "If monitor.socket is not specified,
		// use inotify to watch the sync folder for all relevant changes"
		tmpDir := t.TempDir()
		syncDir := filepath.Join(tmpDir, "sync")
		logsDir := filepath.Join(tmpDir, "logs")

		for _, dir := range []string{syncDir, logsDir} {
			if err := os.MkdirAll(dir, 0755); err != nil {
				t.Fatal(err)
			}
		}

		// Create configuration WITHOUT monitor.socket
		cfg := &config.Config{}
		cfg.Folder.SyncPath = syncDir
		cfg.Folder.LogsPath = logsDir
		cfg.Folder.Compares = types.FULL_FILE
		cfg.Monitor.SocketPath = "" // No socket path specified

		mockIgnoreMatcher := &MockIgnoredPathMatcher{}
		eventChan := make(chan types.FileSyncEvent, 10)

		// Create ingress - should use inotify since no socket path
		ingressInstance, err := ingress.NewIngress(cfg, mockIgnoreMatcher, eventChan)
		if err != nil {
			t.Errorf("Failed to create ingress with inotify: %v", err)
		}

		if ingressInstance == nil {
			t.Error("Ingress should be created when no socket path specified")
		}

		// Start ingress in background
		go ingressInstance.Start()
		defer ingressInstance.Close()

		// Give it time to start
		time.Sleep(100 * time.Millisecond)

		// Create a file to trigger inotify event
		testFile := filepath.Join(syncDir, "inotify_test.txt")
		err = os.WriteFile(testFile, []byte("inotify test content"), 0644)
		if err != nil {
			t.Fatal(err)
		}

		// Wait for inotify event
		select {
		case event := <-eventChan:
			if event.Type != types.CREATE_FILE {
				t.Errorf("Expected CREATE_FILE event, got %s", event.Type)
			}
			if event.Path != "inotify_test.txt" {
				t.Errorf("Expected path 'inotify_test.txt', got '%s'", event.Path)
			}
			if event.Origin != types.LOCAL_INOTIFY {
				t.Errorf("Expected origin LOCAL_INOTIFY, got %s", event.Origin)
			}
			t.Log("Successfully detected inotify event when no socket path specified")
		case <-time.After(2 * time.Second):
			t.Error("Timeout waiting for inotify event")
		}
	})

	t.Run("relevant change types detection", func(t *testing.T) {
		// Test the documented requirement: "watch the sync folder for all relevant changes
		// (create, write, delete, rename, chmod, dir creation/deletion/rename, symlink creation)"

		// Add a timeout to prevent the test from hanging indefinitely
		if deadline, ok := t.Deadline(); ok {
			// If test has a deadline, use it
			ctx, cancel := context.WithDeadline(context.Background(), deadline.Add(-1*time.Second))
			defer cancel()
			_ = ctx // Use context if needed
		}

		tmpDir := t.TempDir()
		syncDir := filepath.Join(tmpDir, "sync")
		logsDir := filepath.Join(tmpDir, "logs")

		for _, dir := range []string{syncDir, logsDir} {
			if err := os.MkdirAll(dir, 0755); err != nil {
				t.Fatal(err)
			}
		}

		cfg := &config.Config{}
		cfg.Folder.SyncPath = syncDir
		cfg.Folder.LogsPath = logsDir
		cfg.Folder.Compares = types.FULL_FILE
		cfg.Monitor.SocketPath = ""

		mockIgnoreMatcher := &MockIgnoredPathMatcher{}
		eventChan := make(chan types.FileSyncEvent, 20)

		ingressInstance, err := ingress.NewIngress(cfg, mockIgnoreMatcher, eventChan)
		if err != nil {
			t.Fatal(err)
		}

		go ingressInstance.Start()
		defer ingressInstance.Close()
		time.Sleep(100 * time.Millisecond)

		// Test file creation
		testFile := filepath.Join(syncDir, "test_file.txt")
		err = os.WriteFile(testFile, []byte("test content"), 0644)
		if err != nil {
			t.Fatal(err)
		}

		// Test directory creation
		testDir := filepath.Join(syncDir, "test_dir")
		err = os.MkdirAll(testDir, 0755)
		if err != nil {
			t.Fatal(err)
		}

		// Test file modification
		time.Sleep(100 * time.Millisecond)
		err = os.WriteFile(testFile, []byte("modified content"), 0644)
		if err != nil {
			t.Fatal(err)
		}

		// Test file deletion
		time.Sleep(100 * time.Millisecond)
		err = os.Remove(testFile)
		if err != nil {
			t.Fatal(err)
		}

		// Collect events for a reasonable time period
		var events []types.FileSyncEvent
		timeout := time.After(1 * time.Second) // Reduced timeout to prevent hanging

		// Collect events until timeout - don't wait for a specific count
	collectLoop:
		for {
			select {
			case event := <-eventChan:
				events = append(events, event)
				// Continue collecting events but don't wait indefinitely
				// If we have some events, we can be satisfied
				if len(events) >= 1 {
					// Give a little more time for additional events but don't wait too long
					select {
					case additionalEvent := <-eventChan:
						events = append(events, additionalEvent)
					case <-time.After(200 * time.Millisecond):
						// Stop waiting for more events
						break collectLoop
					}
				}
			case <-timeout:
				break collectLoop
			}
		}

		if len(events) == 0 {
			t.Error("No events detected for file operations")
		}

		// Verify we got relevant event types
		eventTypes := make(map[types.FileSyncEventType]bool)
		for _, event := range events {
			eventTypes[event.Type] = true
		}

		t.Logf("Detected event types: %v", eventTypes)
		// We should have detected some file operations
		if len(eventTypes) == 0 {
			t.Error("No relevant event types detected")
		}
	})
}

// TestProxySocketRequirements tests proxy socket functionality as documented
func TestProxySocketRequirements(t *testing.T) {
	t.Run("proxy socket when monitor.socket specified", func(t *testing.T) {
		// Test the documented requirement: "If monitor.socket is specified,
		// listen on the Unix domain socket for incoming change events"
		tmpDir := t.TempDir()
		syncDir := filepath.Join(tmpDir, "sync")
		logsDir := filepath.Join(tmpDir, "logs")
		socketPath := "/tmp/test_proxy.sock"

		for _, dir := range []string{syncDir, logsDir} {
			if err := os.MkdirAll(dir, 0755); err != nil {
				t.Fatal(err)
			}
		}

		// Create configuration WITH monitor.socket
		cfg := &config.Config{}
		cfg.Folder.SyncPath = syncDir
		cfg.Folder.LogsPath = logsDir
		cfg.Folder.Compares = types.FULL_FILE
		cfg.Monitor.SocketPath = socketPath // Socket path specified

		mockIgnoreMatcher := &MockIgnoredPathMatcher{}
		eventChan := make(chan types.FileSyncEvent, 10)

		// Clean up any existing socket file
		os.Remove(socketPath)
		defer os.Remove(socketPath)

		// Create ingress - should use proxy socket since socket path specified
		ingressInstance, err := ingress.NewIngress(cfg, mockIgnoreMatcher, eventChan)
		if err != nil {
			t.Errorf("Failed to create ingress with proxy socket: %v", err)
			return
		}

		if ingressInstance == nil {
			t.Error("Ingress should be created when socket path specified")
			return
		}

		// Start ingress in background
		go ingressInstance.Start()
		defer ingressInstance.Close()

		// Give it time to start and create socket
		time.Sleep(200 * time.Millisecond)

		// Verify socket file exists
		if _, err := os.Stat(socketPath); os.IsNotExist(err) {
			t.Error("Socket file should be created when monitor.socket is specified")
		}

		t.Log("Successfully created proxy socket when monitor.socket specified")
	})

	t.Run("proxy socket as sole source in writethrough mode", func(t *testing.T) {
		// Test the documented requirement: "In writethrough mode, this module only
		// listens on the proxy socket and does not use inotify"
		tmpDir := t.TempDir()
		syncDir := filepath.Join(tmpDir, "sync")
		logsDir := filepath.Join(tmpDir, "logs")
		socketPath := "/tmp/test_writethrough.sock"

		for _, dir := range []string{syncDir, logsDir} {
			if err := os.MkdirAll(dir, 0755); err != nil {
				t.Fatal(err)
			}
		}

		// Create configuration for writethrough mode
		cfg := &config.Config{}
		cfg.Folder.SyncPath = syncDir
		cfg.Folder.LogsPath = logsDir
		cfg.Folder.Compares = types.FULL_FILE
		cfg.Folder.Writethrough = true
		cfg.Monitor.SocketPath = socketPath

		// Clean up any existing socket file
		os.Remove(socketPath)
		defer os.Remove(socketPath)

		mockIgnoreMatcher := &MockIgnoredPathMatcher{}
		eventChan := make(chan types.FileSyncEvent, 10)

		ingressInstance, err := ingress.NewIngress(cfg, mockIgnoreMatcher, eventChan)
		if err != nil {
			t.Fatal(err)
		}

		go ingressInstance.Start()
		defer ingressInstance.Close()
		time.Sleep(200 * time.Millisecond)

		// In writethrough mode, creating files directly in sync folder should NOT trigger events
		// Only proxy socket events should be processed
		testFile := filepath.Join(syncDir, "direct_file.txt")
		err = os.WriteFile(testFile, []byte("direct content"), 0644)
		if err != nil {
			t.Fatal(err)
		}

		// Wait briefly and check that no events were generated
		select {
		case event := <-eventChan:
			t.Errorf("Unexpected event in writethrough mode: %+v", event)
		case <-time.After(500 * time.Millisecond):
			// Expected: no events from direct file operations in writethrough mode
			t.Log("Correctly ignored direct file operations in writethrough mode")
		}
	})
}

// TestFileSyncEventTranslation tests event translation as documented
func TestFileSyncEventTranslation(t *testing.T) {
	t.Run("translate detected changes to FileSyncEvent objects", func(t *testing.T) {
		// Test the documented requirement: "Translate detected changes
		// (from inotify or proxy socket) into FileSyncEvent objects"
		tmpDir := t.TempDir()
		syncDir := filepath.Join(tmpDir, "sync")
		logsDir := filepath.Join(tmpDir, "logs")

		for _, dir := range []string{syncDir, logsDir} {
			if err := os.MkdirAll(dir, 0755); err != nil {
				t.Fatal(err)
			}
		}

		cfg := &config.Config{}
		cfg.Folder.SyncPath = syncDir
		cfg.Folder.LogsPath = logsDir
		cfg.Folder.Compares = types.FULL_FILE
		cfg.Monitor.SocketPath = ""

		mockIgnoreMatcher := &MockIgnoredPathMatcher{}
		eventChan := make(chan types.FileSyncEvent, 10)

		ingressInstance, err := ingress.NewIngress(cfg, mockIgnoreMatcher, eventChan)
		if err != nil {
			t.Fatal(err)
		}

		go ingressInstance.Start()
		defer ingressInstance.Close()
		time.Sleep(100 * time.Millisecond)

		// Create a file to trigger event translation
		testFile := filepath.Join(syncDir, "translation_test.txt")
		testContent := []byte("test content for translation")
		err = os.WriteFile(testFile, testContent, 0644)
		if err != nil {
			t.Fatal(err)
		}

		// Verify FileSyncEvent is properly translated
		select {
		case event := <-eventChan:
			// Verify all required FileSyncEvent fields are populated
			if event.Type == types.FileSyncEventType(0) {
				t.Error("Event Type should be populated")
			}
			if event.Path == "" {
				t.Error("Event Path should be populated")
			}
			if event.Timestamp.IsZero() {
				t.Error("Event Timestamp should be populated")
			}
			if event.Origin == types.CommunicationOrigin(0) {
				t.Error("Event Origin should be populated")
			}
			if event.MessageID == 0 {
				t.Error("Event MessageID should be populated")
			}

			// Verify path is relative to sync folder
			if event.Path != "translation_test.txt" {
				t.Errorf("Expected relative path 'translation_test.txt', got '%s'", event.Path)
			}

			// Verify origin is correct for inotify
			if event.Origin != types.LOCAL_INOTIFY {
				t.Errorf("Expected origin LOCAL_INOTIFY, got %s", event.Origin)
			}

			t.Log("Successfully translated file system change to FileSyncEvent")
		case <-time.After(2 * time.Second):
			t.Error("Timeout waiting for translated FileSyncEvent")
		}
	})
}

// MockIgnoredPathMatcher implements the IgnoredPathMatcher interface for testing
type MockIgnoredPathMatcher struct {
	ignoredPaths map[string]bool
}

func (m *MockIgnoredPathMatcher) IsIgnored(path string) bool {
	if m.ignoredPaths == nil {
		return false
	}
	return m.ignoredPaths[path]
}

func (m *MockIgnoredPathMatcher) AddIgnoredPath(path string) {
	if m.ignoredPaths == nil {
		m.ignoredPaths = make(map[string]bool)
	}
	m.ignoredPaths[path] = true
}
