package ingress

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/real-rm/folder_sync/pkg/logger"
	"github.com/real-rm/folder_sync/pkg/types"
)

// mockIgnoreMatcher is a simple mock that doesn't ignore anything
type mockIgnoreMatcher struct{}

func (m *mockIgnoreMatcher) IsIgnored(path string) bool {
	return false
}

// TestSymlinkInotifyDetection tests that the inotify watcher correctly detects symlinks
func TestSymlinkInotifyDetection(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "symlink_inotify_test")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tempDir)

	// Create a target file
	targetFile := filepath.Join(tempDir, "target.txt")
	if err := os.WriteFile(targetFile, []byte("target content"), 0644); err != nil {
		t.Fatal(err)
	}

	// Create a subdirectory with a file
	subDir := filepath.Join(tempDir, "subdir")
	if err := os.MkdirAll(subDir, 0755); err != nil {
		t.Fatal(err)
	}
	subFile := filepath.Join(subDir, "subfile.txt")
	if err := os.WriteFile(subFile, []byte("sub content"), 0644); err != nil {
		t.Fatal(err)
	}

	testCases := []struct {
		name         string
		linkPath     string
		target       string
		shouldSync   bool
		expectedType types.FileSyncEventType
	}{
		{
			name:         "RelativeSymlinkSameDir",
			linkPath:     "link_to_target.txt",
			target:       "target.txt",
			shouldSync:   true,
			expectedType: types.CREATE_SYMLINK,
		},
		{
			name:         "RelativeSymlinkSubdir",
			linkPath:     "link_to_subfile.txt",
			target:       "subdir/subfile.txt",
			shouldSync:   true,
			expectedType: types.CREATE_SYMLINK,
		},
		{
			name:         "RelativeSymlinkOutside",
			linkPath:     "link_outside.txt",
			target:       "../outside.txt",
			shouldSync:   false,
			expectedType: types.CREATE_SYMLINK, // Should not be sent
		},
		{
			name:         "AbsoluteSymlinkInside",
			linkPath:     "abs_link_inside.txt",
			target:       targetFile,
			shouldSync:   true,
			expectedType: types.CREATE_SYMLINK,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create the symlink
			linkFullPath := filepath.Join(tempDir, tc.linkPath)
			if err := os.Symlink(tc.target, linkFullPath); err != nil {
				t.Fatalf("Failed to create symlink: %v", err)
			}
			defer os.Remove(linkFullPath)

			// Verify the symlink was created correctly
			linkInfo, err := os.Lstat(linkFullPath)
			if err != nil {
				t.Fatalf("Failed to stat symlink: %v", err)
			}
			if linkInfo.Mode()&os.ModeSymlink == 0 {
				t.Fatal("Created link is not a symlink")
			}

			// Test the inotify watcher's symlink detection
			testInotifySymlinkDetection(t, tempDir, tc.linkPath, tc.target, tc.shouldSync, tc.expectedType)
		})
	}
}

func testInotifySymlinkDetection(t *testing.T, syncDir, linkPath, target string, shouldSync bool, expectedType types.FileSyncEventType) {
	// Create a mock event channel to capture events
	eventChan := make(chan types.FileSyncEvent, 10)

	// Create mock FSUtil
	mockFSUtil := &MockFSUtil{
		ExtractSymlinkTargetFunc: func(path string) (string, error) {
			return os.Readlink(path)
		},
		GetFileStatFunc: func(path string) (os.FileInfo, error) {
			return os.Lstat(path)
		},
	}

	// Create mock logger
	mockLogger := logger.NewMemoryLogger()

	// Create mock ignore matcher
	mockIgnoreMatcher := &mockIgnoreMatcher{}

	// Create inotify watcher
	watcher := &InotifyWatcher{
		syncPath:        syncDir,
		fsUtil:          mockFSUtil,
		log:             mockLogger,
		compareStrategy: types.SIZE_ONLY,
		eventChan:       eventChan,
		ignoreMatcher:   mockIgnoreMatcher,
	}

	// Test the handleFsnotifyEvent method directly
	linkFullPath := filepath.Join(syncDir, linkPath)

	// Simulate a CREATE event for the symlink
	watcher.handleFsnotifyEvent(linkFullPath, fsnotify.Create, false, false)

	// Check if an event was sent
	select {
	case event := <-eventChan:
		if !shouldSync {
			t.Errorf("Expected symlink to be ignored, but got event: %v", event)
			return
		}

		// Verify the event type
		if event.Type != expectedType {
			t.Errorf("Expected event type %v, got %v", expectedType, event.Type)
		}

		// For CREATE_SYMLINK events, verify the target is in FileBody
		if event.Type == types.CREATE_SYMLINK {
			if string(event.FileBody) != target {
				t.Errorf("Expected symlink target %q in FileBody, got %q", target, string(event.FileBody))
			}
		}

		// Verify the path
		if event.Path != linkPath {
			t.Errorf("Expected event path %q, got %q", linkPath, event.Path)
		}

		t.Logf("✅ Correctly detected symlink: %s -> %s (type: %v)", linkPath, target, event.Type)

	case <-time.After(100 * time.Millisecond):
		if shouldSync {
			t.Errorf("Expected symlink event to be sent, but no event received")
		} else {
			t.Logf("✅ Correctly ignored symlink pointing outside sync folder: %s -> %s", linkPath, target)
		}
	}
}

// TestSymlinkVsFileDetection tests that symlinks are not confused with regular files
func TestSymlinkVsFileDetection(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "symlink_vs_file_test")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tempDir)

	// Create a target file
	targetFile := filepath.Join(tempDir, "target.txt")
	targetContent := "This is the target file content"
	if err := os.WriteFile(targetFile, []byte(targetContent), 0644); err != nil {
		t.Fatal(err)
	}

	// Create a symlink to the target
	symlinkPath := filepath.Join(tempDir, "link_to_target.txt")
	if err := os.Symlink("target.txt", symlinkPath); err != nil {
		t.Fatal(err)
	}
	defer os.Remove(symlinkPath)

	// Create a regular file with the same content as the target
	regularFile := filepath.Join(tempDir, "regular_file.txt")
	if err := os.WriteFile(regularFile, []byte(targetContent), 0644); err != nil {
		t.Fatal(err)
	}

	// Test both files with inotify watcher
	eventChan := make(chan types.FileSyncEvent, 10)

	mockFSUtil := &MockFSUtil{
		ExtractSymlinkTargetFunc: func(path string) (string, error) {
			return os.Readlink(path)
		},
		ReadFileFunc: func(path string) ([]byte, error) {
			return os.ReadFile(path)
		},
		GetFileStatFunc: func(path string) (os.FileInfo, error) {
			return os.Lstat(path)
		},
	}

	mockLogger := logger.NewMemoryLogger()

	watcher := &InotifyWatcher{
		syncPath:        tempDir,
		fsUtil:          mockFSUtil,
		log:             mockLogger,
		compareStrategy: types.SIZE_ONLY,
		eventChan:       eventChan,
		ignoreMatcher:   &mockIgnoreMatcher{},
	}

	// Test the symlink
	watcher.handleFsnotifyEvent(symlinkPath, fsnotify.Create, false, false)

	// Test the regular file
	watcher.handleFsnotifyEvent(regularFile, fsnotify.Create, false, false)

	// Collect events
	var events []types.FileSyncEvent
	timeout := time.After(200 * time.Millisecond)
	for len(events) < 2 {
		select {
		case event := <-eventChan:
			events = append(events, event)
		case <-timeout:
			break
		}
	}

	if len(events) != 2 {
		t.Fatalf("Expected 2 events, got %d", len(events))
	}

	// Find the symlink and file events
	var symlinkEvent, fileEvent *types.FileSyncEvent
	for i := range events {
		if events[i].Path == "link_to_target.txt" {
			symlinkEvent = &events[i]
		} else if events[i].Path == "regular_file.txt" {
			fileEvent = &events[i]
		}
	}

	if symlinkEvent == nil {
		t.Fatal("No event found for symlink")
	}
	if fileEvent == nil {
		t.Fatal("No event found for regular file")
	}

	// Verify symlink event
	if symlinkEvent.Type != types.CREATE_SYMLINK {
		t.Errorf("Expected CREATE_SYMLINK for symlink, got %v", symlinkEvent.Type)
	}
	if string(symlinkEvent.FileBody) != "target.txt" {
		t.Errorf("Expected symlink target 'target.txt' in FileBody, got %q", string(symlinkEvent.FileBody))
	}

	// Verify file event
	if fileEvent.Type != types.CREATE_FILE {
		t.Errorf("Expected CREATE_FILE for regular file, got %v", fileEvent.Type)
	}
	if string(fileEvent.FileBody) != targetContent {
		t.Errorf("Expected file content %q in FileBody, got %q", targetContent, string(fileEvent.FileBody))
	}

	t.Logf("✅ Correctly distinguished symlink (CREATE_SYMLINK) from regular file (CREATE_FILE)")
}
