package ingress

import (
	"bytes"
	"encoding/binary"
	"net"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"sync"
	"syscall"
	"testing"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/types"
)

// MockIgnoredPathMatcher is a mock implementation of types.IgnoredPathMatcher for testing.
type MockIgnoredPathMatcher struct {
	ignoredPaths map[string]bool
	mu           sync.RWMutex
}

// NewMockIgnoredPathMatcher creates a new mock matcher.
func NewMockIgnoredPathMatcher() *MockIgnoredPathMatcher {
	return &MockIgnoredPathMatcher{
		ignoredPaths: make(map[string]bool),
	}
}

// AddIgnoredPath adds a path to be considered ignored.
func (m *MockIgnoredPathMatcher) AddIgnoredPath(path string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.ignoredPaths[path] = true
}

// IsIgnored checks if the given path should be ignored.
func (m *MockIgnoredPathMatcher) IsIgnored(path string) bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.ignoredPaths[path]
}

// MockFSUtil provides mock functions for file system operations for testing.
type MockFSUtil struct {
	GetFileStatFunc          func(path string) (os.FileInfo, error)
	GetFileOwnershipFunc     func(path string) (uint32, uint32, error)
	CalculateXXHashFunc      func(filePath string, strategy types.CompareStrategy) ([]byte, error)
	ReadFileFunc             func(path string) ([]byte, error)
	ExtractSymlinkTargetFunc func(path string) (string, error)
}

// GetFileStat mocks os.Stat.
func (m *MockFSUtil) GetFileStat(path string) (os.FileInfo, error) {
	if m.GetFileStatFunc != nil {
		return m.GetFileStatFunc(path)
	}
	return nil, os.ErrNotExist
}

// GetFileOwnership mocks getting UID/GID.
func (m *MockFSUtil) GetFileOwnership(path string) (uint32, uint32, error) {
	if m.GetFileOwnershipFunc != nil {
		return m.GetFileOwnershipFunc(path)
	}
	return 0, 0, nil
}

// CalculateXXHash mocks xxHash calculation.
func (m *MockFSUtil) CalculateXXHash(filePath string, strategy types.CompareStrategy) ([]byte, error) {
	if m.CalculateXXHashFunc != nil {
		return m.CalculateXXHashFunc(filePath, strategy)
	}
	return []byte("mockchecksum"), nil
}

// ReadFile mocks ioutil.ReadFile.
func (m *MockFSUtil) ReadFile(path string) ([]byte, error) {
	if m.ReadFileFunc != nil {
		return m.ReadFileFunc(path)
	}
	return nil, os.ErrNotExist
}

// ExtractSymlinkTarget mocks reading symlink target.
func (m *MockFSUtil) ExtractSymlinkTarget(path string) (string, error) {
	if m.ExtractSymlinkTargetFunc != nil {
		return m.ExtractSymlinkTargetFunc(path)
	}
	return "", nil
}

// IsSymlinkWithinSyncRoot mocks symlink validation.
func (m *MockFSUtil) IsSymlinkWithinSyncRoot(symlinkPath, symlinkTarget, syncRoot string) bool {
	// For mock, use the same logic as the real implementation
	if filepath.IsAbs(symlinkTarget) {
		return strings.HasPrefix(filepath.Clean(symlinkTarget), filepath.Clean(syncRoot))
	}
	absTargetPath := filepath.Clean(filepath.Join(filepath.Dir(symlinkPath), symlinkTarget))
	return strings.HasPrefix(absTargetPath, filepath.Clean(syncRoot))
}

// TestInotifyEventTranslation verifies that fsnotify events are correctly
// translated into types.FileSyncEvent objects.
func TestInotifyEventTranslation(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "inotify_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tmpDir)

	syncPath := tmpDir
	mockIgnoreMatcher := NewMockIgnoredPathMatcher()
	mockFSUtil := &MockFSUtil{}
	eventChan := make(chan types.FileSyncEvent, 10) // Buffered channel for events

	debounceConfig := DebounceConfig{
		WindowMs:             10, // Short window for testing
		BufferSize:           10,
		ConsolidationEnabled: false, // Disable consolidation for this test
		TempFileDebounceMs:   50,
	}
	watcher, err := NewInotifyWatcher(syncPath, mockIgnoreMatcher, mockFSUtil, types.FULL_FILE, eventChan, "test-watcher", debounceConfig)
	if err != nil {
		t.Fatalf("Failed to create inotify watcher: %v", err)
	}
	defer watcher.Close()

	// Start the debounce forwarding goroutine for testing
	go watcher.forwardDebouncedEvents()

	// Simulate a file creation
	filePath := filepath.Join(syncPath, "testfile.txt")
	relPath := "testfile.txt"
	content := []byte("hello world")

	// Mock stat and ownership for the created file
	mockFSUtil.GetFileStatFunc = func(path string) (os.FileInfo, error) {
		if path == filePath {
			// Mock stat info for a regular file
			return &mockFileInfo{
				name:    "testfile.txt",
				size:    int64(len(content)),
				mode:    0644,
				modTime: time.Now(),
				isDir:   false,
			}, nil
		}
		return nil, os.ErrNotExist
	}
	mockFSUtil.GetFileOwnershipFunc = func(path string) (uint32, uint32, error) {
		if path == filePath {
			return 1000, 1000, nil // Mock UID/GID
		}
		return 0, 0, nil
	}
	mockFSUtil.CalculateXXHashFunc = func(path string, strategy types.CompareStrategy) ([]byte, error) {
		if path == filePath && strategy == types.FULL_FILE {
			return []byte("checksum-testfile"), nil
		}
		return nil, nil
	}
	mockFSUtil.ReadFileFunc = func(path string) ([]byte, error) {
		if path == filePath {
			return content, nil
		}
		return nil, os.ErrNotExist
	}

	// Trigger a simulated file creation event (usually from fsnotify)
	// We're directly calling the internal event handler for testing purposes
	// as mocking fsnotify events is complex.
	watcher.handleFsnotifyEvent(filePath, fsnotify.Create, false, false)

	// Manually flush the debounce buffer for testing
	watcher.debounceBuffer.FlushAll()

	select {
	case event := <-eventChan:
		if event.Type != types.CREATE_FILE {
			t.Errorf("Expected CREATE_FILE, got %s", event.Type)
		}
		if event.Path != relPath {
			t.Errorf("Expected path %q, got %q", relPath, event.Path)
		}
		if event.Size != uint64(len(content)) {
			t.Errorf("Expected size %d, got %d", len(content), event.Size)
		}
		if !bytes.Equal(event.Checksum, []byte("checksum-testfile")) {
			t.Errorf("Expected checksum %q, got %q", "checksum-testfile", event.Checksum)
		}
		if !bytes.Equal(event.FileBody, content) {
			t.Errorf("Expected file body %q, got %q", string(content), string(event.FileBody))
		}
		if event.Origin != types.LOCAL_INOTIFY {
			t.Errorf("Expected origin LOCAL_INOTIFY, got %s", event.Origin)
		}
		if event.FileMode != 0644 {
			t.Errorf("Expected FileMode 0644, got %o", event.FileMode)
		}
		if event.OwnerUID != 1000 || event.OwnerGID != 1000 {
			t.Errorf("Expected OwnerUID/GID 1000/1000, got %d/%d", event.OwnerUID, event.OwnerGID)
		}
		if event.SendsChunks != false { // Small files should not send chunks initially
			t.Errorf("Expected SendsChunks to be false for small files, got true")
		}

		// Test a directory creation
		dirPath := filepath.Join(syncPath, "testdir")
		relDirPath := "testdir"
		mockFSUtil.GetFileStatFunc = func(path string) (os.FileInfo, error) {
			if path == dirPath {
				return &mockFileInfo{name: "testdir", isDir: true, mode: 0755, modTime: time.Now()}, nil
			}
			return nil, os.ErrNotExist
		}

		watcher.handleFsnotifyEvent(dirPath, fsnotify.Create, false, true) // isDir=true

		// Manually flush the debounce buffer for testing
		watcher.debounceBuffer.FlushAll()

		select {
		case event = <-eventChan:
			if event.Type != types.CREATE_DIR {
				t.Errorf("Expected CREATE_DIR, got %s", event.Type)
			}
			if event.Path != relDirPath {
				t.Errorf("Expected path %q, got %q", relDirPath, event.Path)
			}
			if event.Size != 0 {
				t.Errorf("Expected size 0 for dir, got %d", event.Size)
			}
			if event.FileMode != 0755 {
				t.Errorf("Expected FileMode 0755, got %o", event.FileMode)
			}
		case <-time.After(200 * time.Millisecond):
			t.Fatal("Timeout waiting for directory creation event")
		}

		// Test an ignored path
		mockIgnoreMatcher.AddIgnoredPath("ignored.txt")
		ignoredPath := filepath.Join(syncPath, "ignored.txt")
		watcher.handleFsnotifyEvent(ignoredPath, fsnotify.Create, false, false)

		select {
		case <-eventChan:
			t.Error("Received event for ignored path, but expected none.")
		case <-time.After(50 * time.Millisecond):
			// Expected: no event received
		}

	case <-time.After(200 * time.Millisecond):
		t.Fatal("Timeout waiting for file creation event")
	}
}

// mockFileInfo implements os.FileInfo for testing purposes.
type mockFileInfo struct {
	name    string
	size    int64
	mode    os.FileMode
	modTime time.Time
	isDir   bool
}

func (m *mockFileInfo) Name() string       { return m.name }
func (m *mockFileInfo) Size() int64        { return m.size }
func (m *mockFileInfo) Mode() os.FileMode  { return m.mode }
func (m *mockFileInfo) ModTime() time.Time { return m.modTime }
func (m *mockFileInfo) IsDir() bool        { return m.isDir }
func (m *mockFileInfo) Sys() interface{} {
	// Provide a mock for Sys() if it's accessed, e.g., for syscall.Stat_t
	return &syscall.Stat_t{
		Uid: 1000,
		Gid: 1000,
	}
}

// TestProxySocketEventParsing verifies that the proxy socket listener
// correctly parses incoming binary protocol messages into FileSyncEvents.
func TestProxySocketEventParsing(t *testing.T) {
	// Create a temporary Unix domain socket path
	sockPath := filepath.Join(os.TempDir(), "test_proxy_socket_"+time.Now().Format("20060102150405"))
	defer os.Remove(sockPath) // Clean up the socket file

	eventChan := make(chan types.FileSyncEvent, 1)

	// Start the proxy socket listener in a goroutine
	proxy, err := NewProxySocketListener(sockPath, eventChan, NewMockIgnoredPathMatcher(), &config.Config{
		Folder: struct {
			SyncPath string `yaml:"sync_path"`
			MetaPath string `yaml:"meta_path"`
			LogsPath string `yaml:"logs_path"`
			Ignores  []struct {
				Pattern string "yaml:\"pattern\""
				Type    string "yaml:\"type\""
			} `yaml:"ignores"`
			Compares                  types.CompareStrategy     `yaml:"compares"`
			Compression               types.FileCompressionType `yaml:"compression"`
			Readonly                  bool                      `yaml:"readonly"`
			Writethrough              bool                      `yaml:"writethrough"`
			Concurrent                int                       `yaml:"concurrent"`
			BlockSize                 int                       `yaml:"block_size"`
			InitialSyncTimeoutMinutes int                       `yaml:"initial_sync_timeout_minutes"`
		}{
			SyncPath:                  "/tmp/sync", // This path isn't directly used here but is needed by config.Config
			BlockSize:                 4096,
			Compares:                  types.FULL_FILE,
			Compression:               types.COMPRESSION_NONE,
			InitialSyncTimeoutMinutes: 20,
		},
		Server: struct {
			Name          string `yaml:"name"`
			ListenAddress string `yaml:"listen_address"`
			AuthToken     string `yaml:"auth_token"`
		}{
			Name: "test-server",
		},
		Monitor: struct {
			SocketPath string `yaml:"socket_path"`
		}{},
		Pairs: nil,
		Debug: false,
	}, "test-proxy-socket")
	if err != nil {
		t.Fatalf("Failed to create proxy socket listener: %v", err)
	}

	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		proxy.Start() // This blocks until Close() is called
	}()

	// Wait for the listener to start
	time.Sleep(200 * time.Millisecond)

	// Create a mock FileSyncEvent to send
	mockEvent := types.FileSyncEvent{
		Type:         types.CREATE_FILE,
		Path:         "some/file.txt",
		Timestamp:    time.Now().Truncate(time.Millisecond), // Truncate to avoid sub-millisecond differences
		Size:         12345,
		FileMode:     0644,
		OwnerUID:     1001,
		OwnerGID:     1002,
		Checksum:     []byte{0x01, 0x02, 0x03, 0x04},
		Origin:       types.PROXY_CLIENT_WRITE,
		MessageID:    123,
		SendsChunks:  false,
		IsCompressed: false,
		FileBody:     []byte("this is some file content"),
	}

	// Connect to the proxy socket
	conn, err := net.Dial("unix", sockPath)
	if err != nil {
		t.Fatalf("Failed to connect to proxy socket: %v", err)
	}
	defer conn.Close()

	// Send MSG_PROXY_WELCOME_REQUEST
	welcomeReqBytes := &bytes.Buffer{}
	welcomeReqBytes.WriteByte(byte(types.MSG_PROXY_WELCOME_REQUEST))
	writeString(welcomeReqBytes, "Hello Daemon") // ClientMessage
	_, err = conn.Write(welcomeReqBytes.Bytes())
	if err != nil {
		t.Fatalf("Failed to send welcome request: %v", err)
	}

	// Read MSG_PROXY_WELCOME_RESPONSE
	respBuf := make([]byte, 1024)
	n, err := conn.Read(respBuf)
	if err != nil {
		t.Fatalf("Failed to read welcome response: %v", err)
	}
	respReader := bytes.NewReader(respBuf[:n])
	msgTypeByte, _ := respReader.ReadByte()
	if types.MessageType(msgTypeByte) != types.MSG_PROXY_WELCOME_RESPONSE {
		t.Fatalf("Expected MSG_PROXY_WELCOME_RESPONSE, got %x", msgTypeByte)
	}
	successByte, _ := respReader.ReadByte()
	if successByte != 0x01 {
		t.Fatal("Welcome response not successful")
	}
	daemonMessage, _ := readString(respReader)
	if daemonMessage != "Welcome to folder-sync proxy!" {
		t.Errorf("Unexpected daemon message: %s", daemonMessage)
	}

	// Add delay to ensure welcome handshake is complete
	time.Sleep(100 * time.Millisecond)

	// Manually encode FileSyncEvent into binary format based on protocol spec
	encodedEvent := &bytes.Buffer{}
	encodedEvent.WriteByte(byte(types.MSG_FILE_SYNC_EVENT))

	// Payload: MessageID
	binary.Write(encodedEvent, binary.BigEndian, mockEvent.MessageID)
	// Origin
	encodedEvent.WriteByte(byte(mockEvent.Origin))
	// EventType
	encodedEvent.WriteByte(byte(mockEvent.Type))
	// Path
	writeString(encodedEvent, mockEvent.Path)
	// Timestamp (Unix Nano)
	binary.Write(encodedEvent, binary.BigEndian, mockEvent.Timestamp.UnixNano())
	// Size
	binary.Write(encodedEvent, binary.BigEndian, mockEvent.Size)
	// FileMode
	binary.Write(encodedEvent, binary.BigEndian, mockEvent.FileMode)
	// OwnerUID
	binary.Write(encodedEvent, binary.BigEndian, mockEvent.OwnerUID)
	// OwnerGID
	binary.Write(encodedEvent, binary.BigEndian, mockEvent.OwnerGID)
	// FileCompareStrategy (not directly part of FileSyncEvent but expected for encoding)
	encodedEvent.WriteByte(byte(types.FULL_FILE)) // Assuming FULL_FILE for mock
	// Checksum (length-prefixed)
	writeBytes(encodedEvent, mockEvent.Checksum)
	// SendsChunks
	if mockEvent.SendsChunks {
		encodedEvent.WriteByte(0x01)
	} else {
		encodedEvent.WriteByte(0x00)
	}
	// IsCompressed
	if mockEvent.IsCompressed {
		encodedEvent.WriteByte(0x01)
	} else {
		encodedEvent.WriteByte(0x00)
	}
	// FileBody (length-prefixed)
	writeBytes(encodedEvent, mockEvent.FileBody)

	// Send the encoded event
	_, err = conn.Write(encodedEvent.Bytes())
	if err != nil {
		t.Fatalf("Failed to send FileSyncEvent: %v", err)
	}

	// Read MSG_PROXY_EVENT_ACKNOWLEDGE
	ackBuf := make([]byte, 1024)
	n, err = conn.Read(ackBuf)
	if err != nil {
		t.Fatalf("Failed to read acknowledge: %v", err)
	}
	ackReader := bytes.NewReader(ackBuf[:n])
	msgTypeByte, _ = ackReader.ReadByte()
	if types.MessageType(msgTypeByte) != types.MSG_PROXY_EVENT_ACKNOWLEDGE {
		t.Fatalf("Expected MSG_PROXY_EVENT_ACKNOWLEDGE, got %x", msgTypeByte)
	}
	var ackMessageID uint64
	binary.Read(ackReader, binary.BigEndian, &ackMessageID)
	// Note: The proxy socket listener generates its own MessageID internally,
	// so we don't expect it to match the original mockEvent.MessageID
	if ackMessageID == 0 {
		t.Errorf("Expected non-zero ACK MessageID, got %d", ackMessageID)
	}
	ackPath, _ := readString(ackReader)
	if ackPath != mockEvent.Path {
		t.Errorf("Expected ACK Path %q, got %q", mockEvent.Path, ackPath)
	}
	ackSuccessByte, _ := ackReader.ReadByte()
	if ackSuccessByte != 0x01 {
		t.Fatal("ACK not successful")
	}

	// Verify the event was received by the ingress module
	select {
	case receivedEvent := <-eventChan:
		// MessageID is generated by the ingress, so it won't match the mockEvent.MessageID directly.
		// Set it to zero for comparison as it's an internal detail of the ingress module.
		receivedEvent.MessageID = 0
		mockEvent.MessageID = 0 // Also set mockEvent's MessageID to 0 for comparison

		// Adjust the timestamp of mockEvent to match the truncation in proxy.handleFileSyncEvent
		receivedEvent.Timestamp = receivedEvent.Timestamp.Truncate(time.Second) // Assuming handleFileSyncEvent truncates to second
		mockEvent.Timestamp = mockEvent.Timestamp.Truncate(time.Second)

		if !reflect.DeepEqual(receivedEvent, mockEvent) {
			t.Errorf("Received event does not match sent event.\nExpected: %+v\nGot: %+v", mockEvent, receivedEvent)
		}
	case <-time.After(2 * time.Second):
		t.Fatal("Timeout waiting for FileSyncEvent from proxy socket")
	}

	// Close the connection first to allow the handler goroutine to exit
	conn.Close()

	// Close the listener
	proxy.Close()
	wg.Wait() // Wait for the goroutine to finish
}

// Test NewIngress
func TestNewIngress(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "ingress_test")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	cfg := &config.Config{}
	cfg.Folder.SyncPath = tmpDir
	cfg.Monitor.SocketPath = filepath.Join(tmpDir, "test.sock")
	cfg.Folder.LogsPath = tmpDir // Set logs path to avoid logger error

	mockIgnoreMatcher := NewMockIgnoredPathMatcher()
	eventChan := make(chan types.FileSyncEvent, 10)

	ingress, err := NewIngress(cfg, mockIgnoreMatcher, eventChan)
	if err != nil {
		t.Fatalf("Failed to create ingress: %v", err)
	}
	if ingress == nil {
		t.Fatal("Ingress is nil")
	}
}

// Test Ingress Start and Close
func TestIngressStartClose(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "ingress_test")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	cfg := &config.Config{}
	cfg.Folder.SyncPath = tmpDir
	cfg.Monitor.SocketPath = filepath.Join(tmpDir, "test.sock")
	cfg.Folder.LogsPath = tmpDir // Set logs path to avoid logger error

	mockIgnoreMatcher := NewMockIgnoredPathMatcher()
	eventChan := make(chan types.FileSyncEvent, 10)

	ingress, err := NewIngress(cfg, mockIgnoreMatcher, eventChan)
	if err != nil {
		t.Fatalf("Failed to create ingress: %v", err)
	}

	// Start ingress
	go ingress.Start()

	// Give it time to start
	time.Sleep(100 * time.Millisecond)

	// Close ingress
	ingress.Close()

	// Give it time to close
	time.Sleep(100 * time.Millisecond)
}

// Test InotifyFSUtil functions
func TestInotifyFSUtil(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "fsutil_test")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	// Create InotifyFSUtil with the temp directory as sync path
	fsutil := NewInotifyFSUtil(tmpDir)

	// Create a test file
	testFile := filepath.Join(tmpDir, "test.txt")
	content := []byte("test content")
	err = os.WriteFile(testFile, content, 0644)
	if err != nil {
		t.Fatal(err)
	}

	// Test GetFileStat
	stat, err := fsutil.GetFileStat(testFile)
	if err != nil {
		t.Errorf("GetFileStat failed: %v", err)
	}
	if stat == nil {
		t.Error("GetFileStat returned nil stat")
	}

	// Test GetFileOwnership
	uid, gid, err := fsutil.GetFileOwnership(testFile)
	if err != nil {
		t.Errorf("GetFileOwnership failed: %v", err)
	}
	if uid == 0 && gid == 0 {
		// This might be expected on some systems, so just log it
		t.Logf("GetFileOwnership returned uid=%d, gid=%d", uid, gid)
	}

	// Test CalculateXXHash
	hash, err := fsutil.CalculateXXHash(testFile, types.FULL_FILE)
	if err != nil {
		t.Errorf("CalculateXXHash failed: %v", err)
	}
	if len(hash) == 0 {
		t.Error("CalculateXXHash returned empty hash")
	}

	// Test ReadFile
	readContent, err := fsutil.ReadFile(testFile)
	if err != nil {
		t.Errorf("ReadFile failed: %v", err)
	}
	if string(readContent) != string(content) {
		t.Errorf("ReadFile content mismatch: expected %s, got %s", content, readContent)
	}

	// Test ExtractSymlinkTarget with a regular file (should return error)
	target, err := fsutil.ExtractSymlinkTarget(testFile)
	if err == nil {
		t.Error("ExtractSymlinkTarget should fail for regular file")
	}
	if target != "" {
		t.Errorf("ExtractSymlinkTarget should return empty for regular file, got %s", target)
	}
}
