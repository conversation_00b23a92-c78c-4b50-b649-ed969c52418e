package ingress

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"io"
	"net"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/logger"
	"github.com/real-rm/folder_sync/pkg/types"
)

// ProxySocketListener listens on a Unix domain socket for FileSyncEvents
// from external clients (Go Client Package).
type ProxySocketListener struct {
	socketPath     string
	listener       net.Listener
	event<PERSON>han      chan<- types.FileSyncEvent
	errorChan      chan error
	ignoreMatcher  types.IgnoredPathMatcher // To ignore events for certain paths
	cfg            *config.Config           // For configuration like syncPath, block size, etc.
	log            *logger.Logger
	done           chan struct{}
	wg             sync.WaitGroup
	idCounter      uint64 // Counter for generating unique MessageIDs from proxy
	idCounterMutex sync.Mutex
}

// NewProxySocketListener creates and initializes a new ProxySocketListener.
func NewProxySocketListener(
	socketPath string,
	event<PERSON>han chan<- types.FileSyncEvent,
	ignoreMatcher types.IgnoredPathMatcher,
	cfg *config.Config,
	logName string,
) (*ProxySocketListener, error) {
	// Clean up any old socket file
	if err := os.RemoveAll(socketPath); err != nil {
		return nil, fmt.Errorf("failed to clean up old socket at %q: %w", socketPath, err)
	}

	listener, err := net.Listen("unix", socketPath)
	if err != nil {
		return nil, fmt.Errorf("failed to listen on Unix socket %q: %w", socketPath, err)
	}

	return &ProxySocketListener{
		socketPath:    socketPath,
		listener:      listener,
		eventChan:     eventChan,
		errorChan:     make(chan error, 10), // Create internal error channel
		ignoreMatcher: ignoreMatcher,
		cfg:           cfg,
		log: func() *logger.Logger {
			l, _ := logger.NewLogger("/tmp/logs", false)
			return l
		}(),
		done: make(chan struct{}),
	}, nil
}

// GetErrorChannel returns the internal error channel for proxy listener errors.
func (psl *ProxySocketListener) GetErrorChannel() <-chan error {
	return psl.errorChan
}

// Start begins accepting connections and processing messages. This should be run in a goroutine.
func (p *ProxySocketListener) Start() {
	defer p.log.Debug("ProxySocketListener stopped")
	p.log.Info("ProxySocketListener started on %q", p.socketPath)

	for {
		conn, err := p.listener.Accept()
		if err != nil {
			select {
			case <-p.done:
				p.log.Debug("ProxySocketListener accept loop exiting due to done signal.")
				return // Listener was closed
			default:
				p.log.Error("Failed to accept proxy socket connection: %v", err)
				p.errorChan <- fmt.Errorf("proxy socket accept error: %w", err)
				time.Sleep(1 * time.Second) // Prevent tight loop on error
				continue
			}
		}

		p.wg.Add(1)
		go func() {
			defer p.wg.Done()
			defer conn.Close()
			p.handleConnection(conn)
		}()
	}
}

// Close stops the listener and cleans up the socket file.
func (p *ProxySocketListener) Close() {
	p.log.Debug("Closing ProxySocketListener...")
	close(p.done)
	if err := p.listener.Close(); err != nil {
		p.log.Warn("Failed to close proxy socket listener: %v", err)
	}
	p.wg.Wait() // Wait for all active connections to be handled
	if err := os.Remove(p.socketPath); err != nil {
		p.log.Warn("Failed to remove proxy socket file %q: %v", p.socketPath, err)
	}
	p.log.Info("ProxySocketListener gracefully shut down.")
}

// handleConnection processes incoming messages from a single client connection.
func (p *ProxySocketListener) handleConnection(conn net.Conn) {
	p.log.Debug("New proxy socket connection from: %s", conn.RemoteAddr().Network())

	// Implement a read loop to process multiple messages from the same client
	reader := newBufReader(conn) // Custom buffer reader for efficient reads

	for {
		select {
		case <-p.done:
			p.log.Debug("Closing proxy connection for %s due to shutdown.", conn.RemoteAddr().Network())
			return
		default:
			// Attempt to read the message type byte
			msgTypeByte, err := reader.ReadByte()
			if err != nil {
				if err != io.EOF {
					p.log.Error("Failed to read message type from proxy socket %s: %v", conn.RemoteAddr().Network(), err)
					p.errorChan <- fmt.Errorf("proxy socket read error: %w", err)
				} else {
					p.log.Debug("Proxy socket client %s disconnected (EOF).", conn.RemoteAddr().Network())
				}
				return
			}
			msgType := types.MessageType(msgTypeByte)

			if err := p.processMessage(conn, reader, msgType); err != nil {
				p.log.Error("Error processing proxy socket message type %s from %s: %v", msgType.String(), conn.RemoteAddr().Network(), err)
				// Send an error message back to the client if possible
				p.sendErrorResponse(conn, err.Error())
				return // Close connection on processing error for simplicity
			}
		}
	}
}

// processMessage parses and handles a single message based on its type.
func (p *ProxySocketListener) processMessage(conn net.Conn, reader *bufReader, msgType types.MessageType) error {
	p.log.Debug("Received proxy socket message type: %s", msgType.String())

	switch msgType {
	case types.MSG_PROXY_WELCOME_REQUEST:
		return p.handleWelcomeRequest(conn, reader)
	case types.MSG_PROXY_TEST_CONNECTION_REQUEST:
		return p.handleTestConnectionRequest(conn)
	case types.MSG_FILE_SYNC_EVENT:
		return p.handleFileSyncEvent(conn, reader)
	case types.MSG_FILE_CHUNK:
		return p.handleFileChunk(conn, reader)
	default:
		return fmt.Errorf("unsupported proxy socket message type: %s (0x%x)", msgType.String(), byte(msgType))
	}
}

// handleWelcomeRequest processes MSG_PROXY_WELCOME_REQUEST.
func (p *ProxySocketListener) handleWelcomeRequest(conn net.Conn, reader *bufReader) error {
	clientMessage, err := readString(reader)
	if err != nil {
		return fmt.Errorf("failed to read client message: %w", err)
	}
	p.log.Debug("Proxy Welcome Request: %s", clientMessage)

	// Send MSG_PROXY_WELCOME_RESPONSE
	respBuf := &bytes.Buffer{}
	respBuf.WriteByte(byte(types.MSG_PROXY_WELCOME_RESPONSE))
	respBuf.WriteByte(0x01)                               // Success: true
	writeString(respBuf, "Welcome to folder-sync proxy!") // DaemonMessage

	if _, err := conn.Write(respBuf.Bytes()); err != nil {
		return fmt.Errorf("failed to send welcome response: %w", err)
	}
	return nil
}

// handleTestConnectionRequest processes MSG_PROXY_TEST_CONNECTION_REQUEST.
func (p *ProxySocketListener) handleTestConnectionRequest(conn net.Conn) error {
	// Send MSG_PROXY_TEST_CONNECTION_RESPONSE
	respBuf := &bytes.Buffer{}
	respBuf.WriteByte(byte(types.MSG_PROXY_TEST_CONNECTION_RESPONSE))
	respBuf.WriteByte(0x01) // Success: true
	writeString(respBuf, "Daemon is responsive.")

	if _, err := conn.Write(respBuf.Bytes()); err != nil {
		return fmt.Errorf("failed to send test connection response: %w", err)
	}
	return nil
}

// handleFileSyncEvent processes MSG_FILE_SYNC_EVENT from the proxy client.
func (p *ProxySocketListener) handleFileSyncEvent(conn net.Conn, reader *bufReader) error {
	event, err := decodeFileSyncEvent(reader)
	if err != nil {
		return fmt.Errorf("failed to decode FileSyncEvent: %w", err)
	}

	// Ensure the origin is correctly set to PROXY_CLIENT_WRITE
	event.Origin = types.PROXY_CLIENT_WRITE
	event.MessageID = p.generateMessageID() // Assign a new internal MessageID

	// Validate path relative to sync folder and against ignore list
	var relPath string
	if filepath.IsAbs(event.Path) {
		// Handle absolute paths
		var err error
		relPath, err = filepath.Rel(p.cfg.Folder.SyncPath, event.Path)
		if err != nil {
			p.log.Warn("Received FileSyncEvent with absolute path %q not relative to sync path %q. Adjusting to relative.", event.Path, p.cfg.Folder.SyncPath)
			// Try to make it relative if it's an absolute path
			if strings.HasPrefix(event.Path, p.cfg.Folder.SyncPath) {
				event.Path = strings.TrimPrefix(event.Path, p.cfg.Folder.SyncPath)
				if strings.HasPrefix(event.Path, string(os.PathSeparator)) {
					event.Path = strings.TrimPrefix(event.Path, string(os.PathSeparator))
				}
				relPath = event.Path
			} else {
				return fmt.Errorf("received FileSyncEvent with path %q outside sync folder %q", event.Path, p.cfg.Folder.SyncPath)
			}
		}
	} else {
		// Path is already relative, use it as-is
		relPath = event.Path
	}
	event.Path = relPath // Update event path to be relative

	// Check ignore patterns, but allow DELETE events through for cleanup
	// DELETE events should be processed even for ignored files to clean up
	// files that were synced before ignore patterns were applied
	if p.ignoreMatcher.IsIgnored(event.Path) && event.Type != types.DELETE_FILE && event.Type != types.DELETE_DIR {
		p.log.Debug("Ignored FileSyncEvent for path %q from proxy client (not a delete)", event.Path)
		p.sendProxyEventAcknowledge(conn, event.MessageID, event.Path, true, "") // Acknowledge even if ignored
		return nil
	}

	// For simplicity, ensure timestamp is truncated to the second as per usual Unix mtime
	event.Timestamp = event.Timestamp.Truncate(time.Second)

	p.eventChan <- *event
	p.log.Debug("Forwarded FileSyncEvent from proxy: %s", event.String())

	// Send MSG_PROXY_EVENT_ACKNOWLEDGE back to client
	return p.sendProxyEventAcknowledge(conn, event.MessageID, event.Path, true, "")
}

// handleFileChunk processes MSG_FILE_CHUNK from the proxy client.
func (p *ProxySocketListener) handleFileChunk(conn net.Conn, reader *bufReader) error {
	chunk, err := decodeFileChunk(reader)
	if err != nil {
		return fmt.Errorf("failed to decode FileChunk: %w", err)
	}
	// Currently, FileSyncEvent doesn't have fields for chunking logic,
	// so for simplicity in this initial phase, we'll assume proxy clients
	// send full files via MSG_FILE_SYNC_EVENT.
	// In a real implementation, this would involve re-assembling chunks.
	p.log.Warn("Received MSG_FILE_CHUNK, but chunk re-assembly is not yet implemented for proxy client. Discarding chunk for %q (MsgID: %d, Chunk: %d/%d)",
		chunk.Path, chunk.MessageID, chunk.ChunkIndex, chunk.TotalChunks)

	// Send an acknowledge back to the client even if not fully processed
	return p.sendProxyEventAcknowledge(conn, chunk.MessageID, chunk.Path, true, "Chunk received (processing not fully implemented)")
}

// sendProxyEventAcknowledge sends MSG_PROXY_EVENT_ACKNOWLEDGE to the client.
func (p *ProxySocketListener) sendProxyEventAcknowledge(conn net.Conn, messageID uint64, path string, success bool, errorMessage string) error {
	ackBuf := &bytes.Buffer{}
	ackBuf.WriteByte(byte(types.MSG_PROXY_EVENT_ACKNOWLEDGE))
	binary.Write(ackBuf, binary.BigEndian, messageID)
	writeString(ackBuf, path)
	if success {
		ackBuf.WriteByte(0x01)
	} else {
		ackBuf.WriteByte(0x00)
	}
	writeString(ackBuf, errorMessage)

	if _, err := conn.Write(ackBuf.Bytes()); err != nil {
		return fmt.Errorf("failed to send proxy event acknowledge: %w", err)
	}
	return nil
}

// sendErrorResponse sends a generic MSG_ERROR response to the client.
func (p *ProxySocketListener) sendErrorResponse(conn net.Conn, errMsg string) {
	errBuf := &bytes.Buffer{}
	errBuf.WriteByte(byte(types.MSG_ERROR))
	binary.Write(errBuf, binary.BigEndian, uint16(0x01)) // Generic error code
	writeString(errBuf, errMsg)
	if _, err := conn.Write(errBuf.Bytes()); err != nil {
		p.log.Error("Failed to send error response to client: %v", err)
	}
}

// generateMessageID generates a unique MessageID for FileSyncEvents from proxy.
func (p *ProxySocketListener) generateMessageID() uint64 {
	p.idCounterMutex.Lock()
	defer p.idCounterMutex.Unlock()
	p.idCounter++
	return p.idCounter
}

// Helper functions for binary encoding/decoding, mirroring network/protocol.go (simplified for internal use)

// readString reads a length-prefixed string from the reader.
func readString(r io.Reader) (string, error) {
	var length uint16
	if err := binary.Read(r, binary.BigEndian, &length); err != nil {
		return "", fmt.Errorf("failed to read string length: %w", err)
	}
	buf := make([]byte, length)
	if _, err := io.ReadFull(r, buf); err != nil {
		return "", fmt.Errorf("failed to read string bytes: %w", err)
	}
	return string(buf), nil
}

// writeString writes a length-prefixed string to the buffer.
func writeString(buf *bytes.Buffer, s string) {
	b := []byte(s)
	binary.Write(buf, binary.BigEndian, uint16(len(b)))
	buf.Write(b)
}

// writeBytes writes a length-prefixed byte slice to the buffer.
func writeBytes(buf *bytes.Buffer, b []byte) {
	binary.Write(buf, binary.BigEndian, uint32(len(b)))
	buf.Write(b)
}

// readBytes reads a length-prefixed byte slice from the reader.
func readBytes(r io.Reader) ([]byte, error) {
	var length uint32
	if err := binary.Read(r, binary.BigEndian, &length); err != nil {
		return nil, fmt.Errorf("failed to read bytes length: %w", err)
	}
	buf := make([]byte, length)
	if _, err := io.ReadFull(r, buf); err != nil {
		return nil, fmt.Errorf("failed to read bytes: %w", err)
	}
	return buf, nil
}

// decodeFileSyncEvent decodes a MSG_FILE_SYNC_EVENT payload.
func decodeFileSyncEvent(reader *bufReader) (*types.FileSyncEvent, error) {
	event := &types.FileSyncEvent{}
	var err error

	// MessageID (uint64)
	var messageID uint64
	if err = binary.Read(reader, binary.BigEndian, &messageID); err != nil {
		return nil, fmt.Errorf("failed to read MessageID: %w", err)
	}
	event.MessageID = messageID

	// Origin (byte)
	originByte, err := reader.ReadByte()
	if err != nil {
		return nil, fmt.Errorf("failed to read Origin: %w", err)
	}
	event.Origin = types.CommunicationOrigin(originByte)

	// EventType (byte)
	eventTypeByte, err := reader.ReadByte()
	if err != nil {
		return nil, fmt.Errorf("failed to read EventType: %w", err)
	}
	event.Type = types.FileSyncEventType(eventTypeByte)

	// Path (string)
	event.Path, err = readString(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to read Path: %w", err)
	}

	// Timestamp (uint64 - Unix Nano)
	var timestampNanos int64
	if err = binary.Read(reader, binary.BigEndian, &timestampNanos); err != nil {
		return nil, fmt.Errorf("failed to read Timestamp: %w", err)
	}
	event.Timestamp = time.Unix(0, timestampNanos)

	// Size (uint64)
	var size uint64
	if err = binary.Read(reader, binary.BigEndian, &size); err != nil {
		return nil, fmt.Errorf("failed to read Size: %w", err)
	}
	event.Size = size

	// FileMode (uint32)
	var fileMode uint32
	if err = binary.Read(reader, binary.BigEndian, &fileMode); err != nil {
		return nil, fmt.Errorf("failed to read FileMode: %w", err)
	}
	event.FileMode = fileMode

	// OwnerUID (uint32)
	var ownerUID uint32
	if err = binary.Read(reader, binary.BigEndian, &ownerUID); err != nil {
		return nil, fmt.Errorf("failed to read OwnerUID: %w", err)
	}
	event.OwnerUID = ownerUID

	// OwnerGID (uint32)
	var ownerGID uint32
	if err = binary.Read(reader, binary.BigEndian, &ownerGID); err != nil {
		return nil, fmt.Errorf("failed to read OwnerGID: %w", err)
	}
	event.OwnerGID = ownerGID

	// FileCompareStrategy (byte) - This is read from the stream but not stored in FileSyncEvent
	_, err = reader.ReadByte() // Discard, as it's for sender's context
	if err != nil {
		return nil, fmt.Errorf("failed to read FileCompareStrategy: %w", err)
	}

	// Checksum ([]byte)
	event.Checksum, err = readBytes(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to read Checksum: %w", err)
	}

	// SendsChunks (bool)
	sendsChunksByte, err := reader.ReadByte()
	if err != nil {
		return nil, fmt.Errorf("failed to read SendsChunks: %w", err)
	}
	event.SendsChunks = (sendsChunksByte != 0x00)

	// IsCompressed (bool)
	isCompressedByte, err := reader.ReadByte()
	if err != nil {
		return nil, fmt.Errorf("failed to read IsCompressed: %w", err)
	}
	event.IsCompressed = (isCompressedByte != 0x00)

	// FileBody ([]byte)
	event.FileBody, err = readBytes(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to read FileBody: %w", err)
	}

	return event, nil
}

// decodeFileChunk decodes a MSG_FILE_CHUNK payload.
func decodeFileChunk(reader *bufReader) (*struct {
	MessageID    uint64
	Path         string
	ChunkIndex   uint32
	TotalChunks  uint32
	IsCompressed bool
	ChunkData    []byte
}, error) {
	chunk := &struct {
		MessageID    uint64
		Path         string
		ChunkIndex   uint32
		TotalChunks  uint32
		IsCompressed bool
		ChunkData    []byte
	}{}
	var err error

	// MessageID (uint64)
	if err = binary.Read(reader, binary.BigEndian, &chunk.MessageID); err != nil {
		return nil, fmt.Errorf("failed to read chunk MessageID: %w", err)
	}

	// Path (string)
	chunk.Path, err = readString(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to read chunk Path: %w", err)
	}

	// ChunkIndex (uint32)
	if err = binary.Read(reader, binary.BigEndian, &chunk.ChunkIndex); err != nil {
		return nil, fmt.Errorf("failed to read ChunkIndex: %w", err)
	}

	// TotalChunks (uint32)
	if err = binary.Read(reader, binary.BigEndian, &chunk.TotalChunks); err != nil {
		return nil, fmt.Errorf("failed to read TotalChunks: %w", err)
	}

	// IsCompressed (bool)
	isCompressedByte, err := reader.ReadByte()
	if err != nil {
		return nil, fmt.Errorf("failed to read IsCompressed: %w", err)
	}
	chunk.IsCompressed = (isCompressedByte != 0x00)

	// ChunkData ([]byte)
	chunk.ChunkData, err = readBytes(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to read ChunkData: %w", err)
	}

	return chunk, nil
}

// bufReader is a wrapper around io.Reader to provide ReadByte and Peek for easier parsing.
// It uses an internal buffer to support peeking.
type bufReader struct {
	reader io.Reader
	buf    []byte
	pos    int
	end    int
	mu     sync.Mutex
}

// newBufReader creates a new bufReader.
func newBufReader(reader io.Reader) *bufReader {
	return &bufReader{
		reader: reader,
		buf:    make([]byte, 4096), // Small buffer for efficiency
		pos:    0,
		end:    0,
	}
}

// fill attempts to fill the buffer.
func (b *bufReader) fill() error {
	if b.pos > 0 {
		copy(b.buf, b.buf[b.pos:b.end])
		b.end -= b.pos
		b.pos = 0
	}
	if b.end == len(b.buf) { // Buffer is full, cannot fill more
		return fmt.Errorf("buffer full, cannot fill more")
	}
	n, err := b.reader.Read(b.buf[b.end:])
	b.end += n
	return err
}

// ReadByte reads a single byte.
func (b *bufReader) ReadByte() (byte, error) {
	b.mu.Lock()
	defer b.mu.Unlock()

	if b.pos == b.end {
		if err := b.fill(); err != nil {
			return 0, err
		}
		if b.pos == b.end { // Still empty after fill, means EOF
			return 0, io.EOF
		}
	}
	c := b.buf[b.pos]
	b.pos++
	return c, nil
}

// Read implements the io.Reader interface.
func (b *bufReader) Read(p []byte) (n int, err error) {
	b.mu.Lock()
	defer b.mu.Unlock()

	if b.pos == b.end {
		if err = b.fill(); err != nil && err != io.EOF {
			return 0, err
		}
		if b.pos == b.end { // Still empty after fill, means EOF
			return 0, io.EOF
		}
	}

	n = copy(p, b.buf[b.pos:b.end])
	b.pos += n
	return n, nil
}
