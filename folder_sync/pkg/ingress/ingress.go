package ingress

import (
	"fmt"
	"sync"

	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/fsutil" // Concrete FSUtil implementation
	"github.com/real-rm/folder_sync/pkg/logger"
	"github.com/real-rm/folder_sync/pkg/types"
)

// Ingress is the main component responsible for monitoring file system changes
// either via inotify or a proxy socket, and translating them into FileSyncEvents.
type Ingress struct {
	cfg           *config.Config
	ignoreMatcher types.IgnoredPathMatcher
	eventChan     chan types.FileSyncEvent
	watcher       *InotifyWatcher      // nil if using proxy socket
	proxyListener *ProxySocketListener // nil if using inotify
	log           *logger.Logger
	done          chan struct{}
	wg            sync.WaitGroup
}

// NewIngress creates and initializes a new Ingress component based on the configuration.
func NewIngress(
	cfg *config.Config,
	ignoreMatcher types.IgnoredPathMatcher,
	event<PERSON>han chan types.FileSyncEvent,
) (*Ingress, error) {
	// Create a temporary logger for ingress - in a real implementation, this should be passed in
	ingressLogger, err := logger.NewLogger(cfg.Folder.LogsPath, false)
	if err != nil {
		return nil, fmt.Errorf("failed to create ingress logger: %w", err)
	}

	ingress := &Ingress{
		cfg:           cfg,
		ignoreMatcher: ignoreMatcher,
		eventChan:     eventChan,
		log:           ingressLogger,
		done:          make(chan struct{}),
	}

	if cfg.Monitor.SocketPath != "" {
		// Use proxy socket
		ingress.log.Info("Ingress mode: Proxy Socket (%q)", cfg.Monitor.SocketPath)
		listener, err := NewProxySocketListener(
			cfg.Monitor.SocketPath,
			eventChan,
			ignoreMatcher,
			cfg,
			"ProxySocket",
		)
		if err != nil {
			return nil, fmt.Errorf("failed to create proxy socket listener: %w", err)
		}
		ingress.proxyListener = listener
	} else {
		// Use inotify watcher
		ingress.log.Info("Ingress mode: Inotify Watcher (%q)", cfg.Folder.SyncPath)
		inotifyFSUtil := fsutil.NewFSUtil(cfg.Folder.SyncPath) // Using concrete fsutil implementation
		// Create debounce configuration from main config
		bufferSize := cfg.Debounce.BufferSize
		if bufferSize == 0 {
			bufferSize = types.DefaultDebounceBufferSize
		}
		debounceConfig := DebounceConfig{
			WindowMs:             cfg.Debounce.WindowMs,
			BufferSize:           bufferSize,
			ConsolidationEnabled: cfg.Debounce.ConsolidationEnabled,
			TempFileDebounceMs:   cfg.Debounce.TempFileDebounceMs,
		}

		watcher, err := NewInotifyWatcher(
			cfg.Folder.SyncPath,
			ignoreMatcher,
			inotifyFSUtil, // Pass the concrete fsutil implementation
			cfg.Folder.Compares,
			eventChan,
			"InotifyWatcher",
			debounceConfig,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to create inotify watcher: %w", err)
		}
		ingress.watcher = watcher
	}

	return ingress, nil
}

// Start begins the ingress process, launching either the inotify watcher or proxy listener.
func (i *Ingress) Start() {
	i.log.Info("Ingress component starting...")
	defer i.log.Debug("Ingress component stopped.")

	if i.proxyListener != nil {
		i.wg.Add(1)
		go func() {
			defer i.wg.Done()
			i.proxyListener.Start()
		}()
		// Also listen for errors from the proxy listener
		i.wg.Add(1)
		go func() {
			defer i.wg.Done()
			i.handleErrorChannel(i.proxyListener.GetErrorChannel())
		}()
	} else if i.watcher != nil {
		i.wg.Add(1)
		go func() {
			defer i.wg.Done()
			i.watcher.Start()
		}()
		// Also listen for errors from the inotify watcher
		i.wg.Add(1)
		go func() {
			defer i.wg.Done()
			i.handleErrorChannel(i.watcher.GetErrorChannel())
		}()
	} else {
		i.log.Error("Ingress started without a valid watcher or proxy listener configured.")
		i.errorChan() <- fmt.Errorf("ingress not initialized correctly")
		return
	}

	// Keep ingress running until done signal
	<-i.done
	i.log.Debug("Ingress received shutdown signal.")
}

// Close stops the ingress component and its underlying watcher/listener.
func (i *Ingress) Close() {
	i.log.Debug("Closing Ingress component...")
	close(i.done) // Signal shutdown

	if i.proxyListener != nil {
		i.proxyListener.Close()
	}
	if i.watcher != nil {
		i.watcher.Close()
	}
	i.wg.Wait() // Wait for all goroutines to finish
	i.log.Info("Ingress component gracefully shut down.")
}

// errorChan creates a new error channel for the Ingress to handle its own errors
// and potentially merge errors from sub-components.
// For now, it just returns a buffered channel for internal use.
func (i *Ingress) errorChan() chan error {
	// In a more complex setup, this might be an aggregated error channel.
	// For this phase, it's a simple internal channel.
	return make(chan error, 1) // Buffered to prevent blocking
}

// handleErrorChannel listens on the provided error channel and logs errors.
// This function exists to bridge potential internal error channels of sub-components
// (like InotifyWatcher or ProxySocketListener) to the main ingress error handling.
func (i *Ingress) handleErrorChannel(errCh <-chan error) {
	for {
		select {
		case err, ok := <-errCh:
			if !ok {
				return // Channel closed
			}
			i.log.Error("Ingress sub-component error: %v", err)
			// Potentially, this error could be propagated further up to the main application
			// for global error handling or graceful shutdown decisions.
			// For now, we just log it.
		case <-i.done:
			return
		}
	}
}

// NOTE: The specification mentions `fsutil.NewFSUtil(cfg.Folder.SyncPath)`
// This implies a concrete implementation of FSUtil exists in pkg/fsutil.
// Ensure that pkg/fsutil/fsutil.go has this constructor and implements the FSUtil interface.
// (This is a cross-module dependency note, as fsutil is part of Phase 2, but used in Phase 3).
