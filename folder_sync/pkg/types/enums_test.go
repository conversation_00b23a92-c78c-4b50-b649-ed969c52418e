package types_test

import (
	"testing"

	"github.com/real-rm/folder_sync/pkg/types"
	"gopkg.in/yaml.v3"
)

// TestCompareStrategy_String tests the String method and values of CompareStrategy.
func TestCompareStrategy_String(t *testing.T) {
	tests := []struct {
		strategy types.CompareStrategy
		expected string
	}{
		{types.SIZE_ONLY, "SIZE_ONLY"},
		{types.FIRST_LAST_BLOCK_SAMPLE, "FIRST_LAST_BLOCK_SAMPLE"},
		{types.FULL_FILE, "FULL_FILE"},
		{types.CompareStrategy(0xFF), "UnknownCompareStrategy(255)"}, // Test unknown value
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			if actual := tt.strategy.String(); actual != tt.expected {
				t.Errorf("String() = %q, want %q", actual, tt.expected)
			}
		})
	}
}

// TestFileCompressionType_String tests the String method and values of FileCompressionType.
func TestFileCompressionType_String(t *testing.T) {
	tests := []struct {
		compType types.FileCompressionType
		expected string
	}{
		{types.COMPRESSION_NONE, "NONE"},
		{types.COMPRESSION_ZSTD, "ZSTD"},
		{types.FileCompressionType(0xFF), "UnknownCompressionType(255)"}, // Test unknown value
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			if actual := tt.compType.String(); actual != tt.expected {
				t.Errorf("String() = %q, want %q", actual, tt.expected)
			}
		})
	}
}

// TestFileSyncEventType_String tests the String method and values of FileSyncEventType.
func TestFileSyncEventType_String(t *testing.T) {
	tests := []struct {
		eventType types.FileSyncEventType
		expected  string
	}{
		{types.CREATE_FILE, "CREATE_FILE"},
		{types.WRITE_FILE, "WRITE_FILE"},
		{types.DELETE_FILE, "DELETE_FILE"},
		{types.RENAME_FILE, "RENAME_FILE"},
		{types.CHMOD_FILE, "CHMOD_FILE"},
		{types.CREATE_DIR, "CREATE_DIR"},
		{types.DELETE_DIR, "DELETE_DIR"},
		{types.RENAME_DIR, "RENAME_DIR"},
		{types.CREATE_SYMLINK, "CREATE_SYMLINK"},
		{types.FileSyncEventType(0xFF), "UnknownFileSyncEventType(255)"}, // Test unknown value
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			if actual := tt.eventType.String(); actual != tt.expected {
				t.Errorf("String() = %q, want %q", actual, tt.expected)
			}
		})
	}
}

// TestCommunicationOrigin_String tests the String method and values of CommunicationOrigin.
func TestCommunicationOrigin_String(t *testing.T) {
	tests := []struct {
		origin   types.CommunicationOrigin
		expected string
	}{
		{types.LOCAL_INOTIFY, "LOCAL_INOTIFY"},
		{types.REMOTE_PEER, "REMOTE_PEER"},
		{types.PROXY_CLIENT_WRITE, "PROXY_CLIENT_WRITE"},
		{types.CommunicationOrigin(0xFF), "UnknownCommunicationOrigin(255)"}, // Test unknown value
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			if actual := tt.origin.String(); actual != tt.expected {
				t.Errorf("String() = %q, want %q", actual, tt.expected)
			}
		})
	}
}

// TestMessageType_String tests the String method and values of MessageType.
func TestMessageType_String(t *testing.T) {
	tests := []struct {
		msgType  types.MessageType
		expected string
	}{
		{types.MSG_HANDSHAKE_REQUEST, "MSG_HANDSHAKE_REQUEST"},
		{types.MSG_HANDSHAKE_RESPONSE, "MSG_HANDSHAKE_RESPONSE"},
		{types.MSG_FILE_SYNC_EVENT, "MSG_FILE_SYNC_EVENT"},
		{types.MSG_PULL_FILE_REQUEST, "MSG_PULL_FILE_REQUEST"},
		{types.MSG_PULL_FILE_RESPONSE, "MSG_PULL_FILE_RESPONSE"},
		{types.MSG_SYNC_ACKNOWLEDGE, "MSG_SYNC_ACKNOWLEDGE"},
		{types.MSG_FILE_REMOVED, "MSG_FILE_REMOVED"},
		{types.MSG_SAME_CONTENT, "MSG_SAME_CONTENT"},
		{types.MSG_FILE_CHUNK, "MSG_FILE_CHUNK"},
		{types.MSG_CHUNK_ACKNOWLEDGE, "MSG_CHUNK_ACKNOWLEDGE"},
		{types.MSG_INITIAL_SCAN_REQUEST, "MSG_INITIAL_SCAN_REQUEST"},
		{types.MSG_INITIAL_SCAN_RESPONSE, "MSG_INITIAL_SCAN_RESPONSE"},
		{types.MSG_PROXY_WELCOME_REQUEST, "MSG_PROXY_WELCOME_REQUEST"},
		{types.MSG_PROXY_WELCOME_RESPONSE, "MSG_PROXY_WELCOME_RESPONSE"},
		{types.MSG_PROXY_TEST_CONNECTION_REQUEST, "MSG_PROXY_TEST_CONNECTION_REQUEST"},
		{types.MSG_PROXY_TEST_CONNECTION_RESPONSE, "MSG_PROXY_TEST_CONNECTION_RESPONSE"},
		{types.MSG_PROXY_EVENT_ACKNOWLEDGE, "MSG_PROXY_EVENT_ACKNOWLEDGE"},
		{types.MSG_ERROR, "MSG_ERROR"},
		{types.MessageType(0x7F), "UNKNOWN_MESSAGE_TYPE(0x7f)"}, // Test unknown value
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			if actual := tt.msgType.String(); actual != tt.expected {
				t.Errorf("String() = %q, want %q", actual, tt.expected)
			}
		})
	}
}

// TestProtocolVersion_String tests the String method of ProtocolVersion.
func TestProtocolVersion_String(t *testing.T) {
	tests := []struct {
		version  types.ProtocolVersion
		expected string
	}{
		{types.ProtocolVersion{Major: 1, Minor: 0, Patch: 0}, "1.0.0"},
		{types.ProtocolVersion{Major: 2, Minor: 15, Patch: 3}, "2.15.3"},
		{types.ProtocolVersion{Major: 0, Minor: 0, Patch: 0}, "0.0.0"},
		{types.ProtocolVersion{Major: 255, Minor: 255, Patch: 255}, "255.255.255"},
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			if actual := tt.version.String(); actual != tt.expected {
				t.Errorf("String() = %q, want %q", actual, tt.expected)
			}
		})
	}
}

// TestEnumValues ensures that distinct enum values have distinct underlying byte values.
func TestEnumValues(t *testing.T) {
	// CompareStrategy
	if types.SIZE_ONLY == types.FIRST_LAST_BLOCK_SAMPLE || types.SIZE_ONLY == types.FULL_FILE || types.FIRST_LAST_BLOCK_SAMPLE == types.FULL_FILE {
		t.Error("CompareStrategy enum values are not distinct")
	}

	// FileCompressionType
	if types.COMPRESSION_NONE == types.COMPRESSION_ZSTD {
		t.Error("FileCompressionType enum values are not distinct")
	}

	// FileSyncEventType
	eventTypes := []types.FileSyncEventType{
		types.CREATE_FILE, types.WRITE_FILE, types.DELETE_FILE, types.RENAME_FILE,
		types.CHMOD_FILE, types.CREATE_DIR, types.DELETE_DIR, types.RENAME_DIR, types.CREATE_SYMLINK,
	}
	seenEventValues := make(map[byte]bool)
	for _, et := range eventTypes {
		if seenEventValues[byte(et)] {
			t.Errorf("FileSyncEventType enum value %d is duplicated", et)
		}
		seenEventValues[byte(et)] = true
	}

	// CommunicationOrigin
	originTypes := []types.CommunicationOrigin{
		types.LOCAL_INOTIFY, types.REMOTE_PEER, types.PROXY_CLIENT_WRITE,
	}
	seenOriginValues := make(map[byte]bool)
	for _, ot := range originTypes {
		if seenOriginValues[byte(ot)] {
			t.Errorf("CommunicationOrigin enum value %d is duplicated", ot)
		}
		seenOriginValues[byte(ot)] = true
	}

	// MessageType
	messageTypes := []types.MessageType{
		types.MSG_HANDSHAKE_REQUEST, types.MSG_HANDSHAKE_RESPONSE,
		types.MSG_FILE_SYNC_EVENT, types.MSG_PULL_FILE_REQUEST, types.MSG_PULL_FILE_RESPONSE,
		types.MSG_SYNC_ACKNOWLEDGE, types.MSG_FILE_REMOVED, types.MSG_SAME_CONTENT,
		types.MSG_FILE_CHUNK, types.MSG_CHUNK_ACKNOWLEDGE,
		types.MSG_INITIAL_SCAN_REQUEST, types.MSG_INITIAL_SCAN_RESPONSE,
		types.MSG_PROXY_WELCOME_REQUEST, types.MSG_PROXY_WELCOME_RESPONSE,
		types.MSG_PROXY_TEST_CONNECTION_REQUEST, types.MSG_PROXY_TEST_CONNECTION_RESPONSE,
		types.MSG_PROXY_EVENT_ACKNOWLEDGE,
		types.MSG_ERROR,
	}
	seenMessageValues := make(map[byte]bool)
	for _, mt := range messageTypes {
		if seenMessageValues[byte(mt)] {
			t.Errorf("MessageType enum value 0x%x is duplicated", mt)
		}
		seenMessageValues[byte(mt)] = true
	}
}

// TestCompareStrategy_UnmarshalYAML tests YAML unmarshaling for CompareStrategy.
func TestCompareStrategy_UnmarshalYAML(t *testing.T) {
	tests := []struct {
		name     string
		yaml     string
		expected types.CompareStrategy
		hasError bool
	}{
		{"String SIZE_ONLY", "compares: SIZE_ONLY", types.SIZE_ONLY, false},
		{"String FIRST_LAST_BLOCK_SAMPLE", "compares: FIRST_LAST_BLOCK_SAMPLE", types.FIRST_LAST_BLOCK_SAMPLE, false},
		{"String FULL_FILE", "compares: FULL_FILE", types.FULL_FILE, false},
		{"Numeric 1", "compares: 1", types.SIZE_ONLY, false},
		{"Numeric 2", "compares: 2", types.FIRST_LAST_BLOCK_SAMPLE, false},
		{"Numeric 3", "compares: 3", types.FULL_FILE, false},
		{"Invalid string", "compares: INVALID", types.CompareStrategy(0), true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var config struct {
				Compares types.CompareStrategy `yaml:"compares"`
			}

			err := yaml.Unmarshal([]byte(tt.yaml), &config)
			if tt.hasError {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if config.Compares != tt.expected {
				t.Errorf("Expected %v, got %v", tt.expected, config.Compares)
			}
		})
	}
}

// TestFileCompressionType_UnmarshalYAML tests YAML unmarshaling for FileCompressionType.
func TestFileCompressionType_UnmarshalYAML(t *testing.T) {
	tests := []struct {
		name     string
		yaml     string
		expected types.FileCompressionType
		hasError bool
	}{
		{"String NONE", "compression: NONE", types.COMPRESSION_NONE, false},
		{"String ZSTD", "compression: ZSTD", types.COMPRESSION_ZSTD, false},
		{"Numeric 0", "compression: 0", types.COMPRESSION_NONE, false},
		{"Numeric 1", "compression: 1", types.COMPRESSION_ZSTD, false},
		{"Invalid string", "compression: INVALID", types.FileCompressionType(0), true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var config struct {
				Compression types.FileCompressionType `yaml:"compression"`
			}

			err := yaml.Unmarshal([]byte(tt.yaml), &config)
			if tt.hasError {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if config.Compression != tt.expected {
				t.Errorf("Expected %v, got %v", tt.expected, config.Compression)
			}
		})
	}
}
