package types_test

import (
	"testing"
	"time"

	"github.com/real-rm/folder_sync/pkg/types"
)

// TestFileSyncEvent_String tests the String method of FileSyncEvent.
func TestFileSyncEvent_String(t *testing.T) {
	// Define a timestamp for consistent testing
	testTime := time.Date(2023, 1, 15, 10, 30, 0, 0, time.UTC)

	tests := []struct {
		name     string
		event    types.FileSyncEvent
		expected string
	}{
		{
			name: "Create file event",
			event: types.FileSyncEvent{
				Type:         types.CREATE_FILE,
				Path:         "testfile.txt",
				Timestamp:    testTime,
				Size:         100,
				FileMode:     0o644,
				OwnerUID:     1000,
				OwnerGID:     1000,
				Checksum:     []byte("checksum123"),
				Origin:       types.LOCAL_INOTIFY,
				MessageID:    1,
				SendsChunks:  false,
				IsCompressed: false,
				FileBody:     []byte("some content"),
			},
			expected: "Event[Type: CREATE_FILE, Path: testfile.txt, Time: 2023-01-15T10:30:00Z, Size: 100, Origin: LOCAL_INOTIFY, MsgID: 1]",
		},
		{
			name: "Delete directory event",
			event: types.FileSyncEvent{
				Type:         types.DELETE_DIR,
				Path:         "testdir/",
				Timestamp:    testTime.Add(time.Hour),
				Size:         0, // Size is 0 for directories
				FileMode:     0o755,
				OwnerUID:     0,
				OwnerGID:     0,
				Checksum:     nil,
				Origin:       types.REMOTE_PEER,
				MessageID:    2,
				SendsChunks:  false,
				IsCompressed: false,
				FileBody:     nil,
			},
			expected: "Event[Type: DELETE_DIR, Path: testdir/, Time: 2023-01-15T11:30:00Z, Size: 0, Origin: REMOTE_PEER, MsgID: 2]",
		},
		{
			name: "Write file event (chunked)",
			event: types.FileSyncEvent{
				Type:         types.WRITE_FILE,
				Path:         "largefile.bin",
				Timestamp:    testTime.Add(2 * time.Hour),
				Size:         1024 * 1024,
				FileMode:     0o600,
				OwnerUID:     500,
				OwnerGID:     500,
				Checksum:     []byte("largefilechecksum"),
				Origin:       types.PROXY_CLIENT_WRITE,
				MessageID:    3,
				SendsChunks:  true,
				IsCompressed: true,
				FileBody:     nil, // FileBody is empty if SendsChunks is true
			},
			expected: "Event[Type: WRITE_FILE, Path: largefile.bin, Time: 2023-01-15T12:30:00Z, Size: 1048576, Origin: PROXY_CLIENT_WRITE, MsgID: 3]",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := tt.event.String()
			if actual != tt.expected {
				t.Errorf("FileSyncEvent.String() = %q, want %q", actual, tt.expected)
			}
		})
	}
}

// TestFileSyncEvent_Fields tests that FileSyncEvent fields can be correctly assigned and retrieved.
func TestFileSyncEvent_Fields(t *testing.T) {
	event := types.FileSyncEvent{
		Type:         types.RENAME_FILE,
		Path:         "oldname.txt",
		Timestamp:    time.Now(),
		Size:         500,
		FileMode:     0o660,
		OwnerUID:     1001,
		OwnerGID:     1002,
		Checksum:     []byte{0x01, 0x02, 0x03},
		Origin:       types.LOCAL_INOTIFY,
		MessageID:    12345,
		SendsChunks:  false,
		IsCompressed: false,
		FileBody:     []byte("some data"),
	}

	if event.Type != types.RENAME_FILE {
		t.Errorf("Expected Type %v, got %v", types.RENAME_FILE, event.Type)
	}
	if event.Path != "oldname.txt" {
		t.Errorf("Expected Path %q, got %q", "oldname.txt", event.Path)
	}
	if event.Size != 500 {
		t.Errorf("Expected Size %d, got %d", 500, event.Size)
	}
	if event.FileMode != 0o660 {
		t.Errorf("Expected FileMode %o, got %o", 0o660, event.FileMode)
	}
	if event.OwnerUID != 1001 {
		t.Errorf("Expected OwnerUID %d, got %d", 1001, event.OwnerUID)
	}
	if event.OwnerGID != 1002 {
		t.Errorf("Expected OwnerGID %d, got %d", 1002, event.OwnerGID)
	}
	if string(event.Checksum) != string([]byte{0x01, 0x02, 0x03}) {
		t.Errorf("Expected Checksum %v, got %v", []byte{0x01, 0x02, 0x03}, event.Checksum)
	}
	if event.Origin != types.LOCAL_INOTIFY {
		t.Errorf("Expected Origin %v, got %v", types.LOCAL_INOTIFY, event.Origin)
	}
	if event.MessageID != 12345 {
		t.Errorf("Expected MessageID %d, got %d", 12345, event.MessageID)
	}
	if event.SendsChunks != false {
		t.Errorf("Expected SendsChunks %t, got %t", false, event.SendsChunks)
	}
	if event.IsCompressed != false {
		t.Errorf("Expected IsCompressed %t, got %t", false, event.IsCompressed)
	}
	if string(event.FileBody) != string([]byte("some data")) {
		t.Errorf("Expected FileBody %q, got %q", "some data", event.FileBody)
	}
	// Check timestamp (allowing for small difference)
	if time.Since(event.Timestamp) > time.Second {
		t.Errorf("Timestamp is too old: %v", event.Timestamp)
	}
}
