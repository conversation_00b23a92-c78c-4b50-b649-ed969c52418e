package types

import (
	"fmt"
	"sync"
	"time"
)

// FileSyncEvent represents a change event in the sync folder.
// This struct will be used throughout the application to represent file system operations.
type FileSyncEvent struct {
	Type         FileSyncEventType   // Type of file system event (e.g., CREATE_FILE, DELETE_DIR)
	Path         string              // Relative path to the sync folder
	Timestamp    time.Time           // Modification timestamp (mtime) of the file or directory
	Size         uint64              // Total size of the file in bytes (0 for directories/deletions)
	FileMode     uint32              // Unix file permissions (e.g., 0o755)
	OwnerUID     uint32              // User ID of the file owner
	OwnerGID     uint32              // Group ID of the file owner
	Checksum     []byte              // xxHash checksum of the file content (if applicable based on strategy)
	Origin       CommunicationOrigin // Origin of the event (e.g., LOCAL_INOTIFY, REMOTE_PEER)
	MessageID    uint64              // Unique ID for this message, used for correlation in network communication
	SendsChunks  bool                // True if file body will be sent in subsequent chunks
	IsCompressed bool                // True if FileBody is zstd compressed (if SendsChunks is false) or chunks will be compressed
	FileBody     []byte              // The content of the file for CREATE/WRITE events (if not chunked)
}

// SyncAcknowledge represents an acknowledgment message for a FileSyncEvent.
type SyncAcknowledge struct {
	MessageID    uint64            // ID of the original message being acknowledged
	Path         string            // Path of the file/directory that was processed
	EventType    FileSyncEventType // Type of event that was acknowledged
	Success      bool              // Whether the operation was successful
	ErrorMessage string            // Error message if Success is false
}

// PullFileRequest represents a request to pull a file from a remote peer.
type PullFileRequest struct {
	MessageID           uint64          // Unique ID for this pull request
	Path                string          // Relative path of the file to pull
	Size                uint64          // Expected size of the file (0 if unknown)
	FileCompareStrategy CompareStrategy // Strategy for comparing file content
	Checksum            []byte          // Expected checksum (if known)
}

// PullFileResponse represents a response to a PullFileRequest.
type PullFileResponse struct {
	MessageID    uint64    // ID correlating to the original PullFileRequest
	Path         string    // Path of the file being sent
	Timestamp    time.Time // Modification timestamp of the file
	Size         uint64    // Total size of the file
	FileMode     uint32    // Unix file permissions
	OwnerUID     uint32    // User ID of the file owner
	OwnerGID     uint32    // Group ID of the file owner
	Checksum     []byte    // xxHash checksum of the file content
	SendsChunks  bool      // True if file will be sent in chunks
	IsCompressed bool      // True if file content is compressed
	FileBody     []byte    // File content (if not chunked)
}

// FileRemoved represents a message indicating a file was removed before it could be sent.
type FileRemoved struct {
	MessageID uint64 // ID of the original message
	Path      string // Path of the file that was removed
}

// SameContent represents a message indicating that the local and remote files have the same content.
type SameContent struct {
	MessageID uint64    // ID of the original message
	Path      string    // Path of the file
	Timestamp time.Time // Timestamp of the local file
	Size      uint64    // Size of the local file
}

// FileChunk represents a chunk of file data for large file transfers.
type FileChunk struct {
	MessageID    uint64 // ID correlating to the original FileSyncEvent
	Path         string // Path of the file being transferred
	ChunkIndex   uint32 // Index of this chunk (0-based)
	TotalChunks  uint32 // Total number of chunks for this file
	ChunkSize    uint32 // Size of this chunk in bytes
	IsCompressed bool   // True if this chunk is compressed
	ChunkData    []byte // The actual chunk data
}

// ChunkAcknowledge represents an acknowledgment for a received file chunk.
type ChunkAcknowledge struct {
	MessageID  uint64 // ID correlating to the original FileSyncEvent
	Path       string // Path of the file being transferred
	ChunkIndex uint32 // Index of the chunk being acknowledged
	Success    bool   // Whether the chunk was received successfully
}

// Error represents a general error message in the protocol.
type Error struct {
	Code    uint16 // Error code (application-specific)
	Message string // Human-readable error message
}

// String provides a human-readable representation of a FileSyncEvent.
func (e FileSyncEvent) String() string {
	return fmt.Sprintf("Event[Type: %s, Path: %s, Time: %s, Size: %d, Origin: %s, MsgID: %d]",
		e.Type.String(), e.Path, e.Timestamp.Format(time.RFC3339), e.Size, e.Origin.String(), e.MessageID)
}

// InitialScanRequest represents a request for a peer to send its complete file list.
type InitialScanRequest struct {
	MessageID uint64 // Unique ID for this scan request
}

// String provides a human-readable representation of InitialScanRequest.
func (isr InitialScanRequest) String() string {
	return fmt.Sprintf("InitialScanRequest{MessageID: %d}", isr.MessageID)
}

// InitialScanResponse represents a response containing the complete file list.
type InitialScanResponse struct {
	MessageID    uint64          // ID correlating to the original InitialScanRequest
	Files        []FileSyncEvent // Complete list of files in the sync directory
	Success      bool            // Whether the scan was successful
	ErrorMessage string          // Error message if Success is false
}

// String provides a human-readable representation of InitialScanResponse.
func (isr InitialScanResponse) String() string {
	return fmt.Sprintf("InitialScanResponse{MessageID: %d, FileCount: %d, Success: %t, ErrorMessage: %q}",
		isr.MessageID, len(isr.Files), isr.Success, isr.ErrorMessage)
}

// DebounceBuffer represents a buffer for debouncing file system events
type DebounceBuffer struct {
	events    map[string]*BufferedEvent // path -> buffered event
	mutex     sync.RWMutex              // protects concurrent access
	windowMs  int                       // debounce window in milliseconds
	flushChan chan<- FileSyncEvent      // channel to send consolidated events
}

// BufferedEvent represents an event being buffered for debouncing
type BufferedEvent struct {
	Event       FileSyncEvent       // the latest event for this path
	FirstSeen   time.Time           // when the first event for this path was seen
	LastUpdated time.Time           // when this event was last updated
	EventCount  int                 // number of events seen for this path
	EventTypes  []FileSyncEventType // sequence of event types seen for this path
}

// NewDebounceBuffer creates a new debounce buffer
func NewDebounceBuffer(windowMs int, flushChan chan<- FileSyncEvent) *DebounceBuffer {
	return &DebounceBuffer{
		events:    make(map[string]*BufferedEvent),
		windowMs:  windowMs,
		flushChan: flushChan,
	}
}

// AddEvent adds an event to the debounce buffer
func (db *DebounceBuffer) AddEvent(event FileSyncEvent) {
	db.mutex.Lock()
	defer db.mutex.Unlock()

	now := time.Now()
	path := event.Path

	if buffered, exists := db.events[path]; exists {
		// Update existing buffered event
		buffered.Event = event
		buffered.LastUpdated = now
		buffered.EventCount++

		// Track event type sequence for consolidation
		buffered.EventTypes = append(buffered.EventTypes, event.Type)

		// Keep only the last 10 event types to prevent unbounded growth
		if len(buffered.EventTypes) > 10 {
			buffered.EventTypes = buffered.EventTypes[len(buffered.EventTypes)-10:]
		}
	} else {
		// Create new buffered event
		db.events[path] = &BufferedEvent{
			Event:       event,
			FirstSeen:   now,
			LastUpdated: now,
			EventCount:  1,
			EventTypes:  []FileSyncEventType{event.Type},
		}
	}
}

// FlushExpiredEvents flushes events that have exceeded the debounce window
func (db *DebounceBuffer) FlushExpiredEvents() {
	db.mutex.Lock()
	defer db.mutex.Unlock()

	now := time.Now()
	windowDuration := time.Duration(db.windowMs) * time.Millisecond

	for path, buffered := range db.events {
		if now.Sub(buffered.LastUpdated) >= windowDuration {
			// Send the consolidated event
			db.flushChan <- buffered.Event
			delete(db.events, path)
		}
	}
}

// FlushAll flushes all buffered events immediately
func (db *DebounceBuffer) FlushAll() {
	db.mutex.Lock()
	defer db.mutex.Unlock()

	for path, buffered := range db.events {
		db.flushChan <- buffered.Event
		delete(db.events, path)
	}
}

// HasEventPattern checks if a buffered event has a specific pattern of event types
func (be *BufferedEvent) HasEventPattern(pattern []FileSyncEventType) bool {
	if len(be.EventTypes) < len(pattern) {
		return false
	}

	// Check if the pattern appears at the end of the event sequence
	start := len(be.EventTypes) - len(pattern)
	for i, eventType := range pattern {
		if be.EventTypes[start+i] != eventType {
			return false
		}
	}
	return true
}

// GetEventSequenceInfo returns information about the event sequence for debugging
func (be *BufferedEvent) GetEventSequenceInfo() string {
	if len(be.EventTypes) == 0 {
		return "no events"
	}

	typeNames := make([]string, len(be.EventTypes))
	for i, eventType := range be.EventTypes {
		typeNames[i] = eventType.String()
	}

	return fmt.Sprintf("sequence: [%s], count: %d",
		fmt.Sprintf("%v", typeNames), be.EventCount)
}

// GetBufferedEvent safely retrieves a copy of the buffered event for a given path
func (db *DebounceBuffer) GetBufferedEvent(path string) (*BufferedEvent, bool) {
	db.mutex.RLock()
	defer db.mutex.RUnlock()

	if buffered, exists := db.events[path]; exists {
		// Return a copy to avoid race conditions
		eventTypesCopy := make([]FileSyncEventType, len(buffered.EventTypes))
		copy(eventTypesCopy, buffered.EventTypes)

		return &BufferedEvent{
			Event:       buffered.Event,
			FirstSeen:   buffered.FirstSeen,
			LastUpdated: buffered.LastUpdated,
			EventCount:  buffered.EventCount,
			EventTypes:  eventTypesCopy,
		}, true
	}
	return nil, false
}
