package types

import "fmt"

// CompareStrategy defines how file content should be compared.
type CompareStrategy byte

const (
	SIZE_ONLY               CompareStrategy = 0x01 // Only compare file size.
	FIRST_LAST_BLOCK_SAMPLE CompareStrategy = 0x02 // Hash the first and last block for comparison.
	FULL_FILE               CompareStrategy = 0x03 // Hash the full file content for comparison.
)

// String provides a human-readable representation of CompareStrategy.
func (cs CompareStrategy) String() string {
	switch cs {
	case SIZE_ONLY:
		return "SIZE_ONLY"
	case FIRST_LAST_BLOCK_SAMPLE:
		return "FIRST_LAST_BLOCK_SAMPLE"
	case FULL_FILE:
		return "FULL_FILE"
	default:
		return fmt.Sprintf("UnknownCompareStrategy(%d)", cs)
	}
}

// UnmarshalYAML implements custom YAML unmarshaling for CompareStrategy.
func (cs *CompareStrategy) UnmarshalYAML(unmarshal func(interface{}) error) error {
	// First try to unmarshal as a string
	var str string
	if err := unmarshal(&str); err == nil {
		switch str {
		case "SIZE_ONLY":
			*cs = SIZE_ONLY
		case "FIRST_LAST_BLOCK_SAMPLE":
			*cs = FIRST_LAST_BLOCK_SAMPLE
		case "FULL_FILE":
			*cs = FULL_FILE
		case "1":
			*cs = SIZE_ONLY
		case "2":
			*cs = FIRST_LAST_BLOCK_SAMPLE
		case "3":
			*cs = FULL_FILE
		default:
			return fmt.Errorf("invalid CompareStrategy: %q", str)
		}
		return nil
	}

	// Try to unmarshal as a number
	var num int
	if err := unmarshal(&num); err != nil {
		return err
	}
	*cs = CompareStrategy(num)
	return nil
}

// FileCompressionType defines the compression algorithm for file transfers.
type FileCompressionType byte

const (
	COMPRESSION_NONE FileCompressionType = 0x00 // No compression.
	COMPRESSION_ZSTD FileCompressionType = 0x01 // Zstandard compression.
)

// String provides a human-readable representation of FileCompressionType.
func (fct FileCompressionType) String() string {
	switch fct {
	case COMPRESSION_NONE:
		return "NONE"
	case COMPRESSION_ZSTD:
		return "ZSTD"
	default:
		return fmt.Sprintf("UnknownCompressionType(%d)", fct)
	}
}

// UnmarshalYAML implements custom YAML unmarshaling for FileCompressionType.
func (fct *FileCompressionType) UnmarshalYAML(unmarshal func(interface{}) error) error {
	// First try to unmarshal as a string
	var str string
	if err := unmarshal(&str); err == nil {
		switch str {
		case "NONE":
			*fct = COMPRESSION_NONE
		case "ZSTD":
			*fct = COMPRESSION_ZSTD
		case "0":
			*fct = COMPRESSION_NONE
		case "1":
			*fct = COMPRESSION_ZSTD
		default:
			return fmt.Errorf("invalid FileCompressionType: %q", str)
		}
		return nil
	}

	// Try to unmarshal as a number
	var num int
	if err := unmarshal(&num); err != nil {
		return err
	}
	*fct = FileCompressionType(num)
	return nil
}

// FileSyncEventType defines the type of file system event.
type FileSyncEventType byte

const (
	CREATE_FILE    FileSyncEventType = 0x01 // A new file was created.
	WRITE_FILE     FileSyncEventType = 0x02 // An existing file was modified.
	DELETE_FILE    FileSyncEventType = 0x03 // A file was deleted.
	RENAME_FILE    FileSyncEventType = 0x04 // A file was renamed/moved.
	CHMOD_FILE     FileSyncEventType = 0x05 // File permissions were changed.
	CREATE_DIR     FileSyncEventType = 0x06 // A new directory was created.
	DELETE_DIR     FileSyncEventType = 0x07 // A directory was deleted.
	RENAME_DIR     FileSyncEventType = 0x08 // A directory was renamed/moved.
	CREATE_SYMLINK FileSyncEventType = 0x09 // A new symlink was created.
)

// String provides a human-readable representation of FileSyncEventType.
func (fet FileSyncEventType) String() string {
	switch fet {
	case CREATE_FILE:
		return "CREATE_FILE"
	case WRITE_FILE:
		return "WRITE_FILE"
	case DELETE_FILE:
		return "DELETE_FILE"
	case RENAME_FILE:
		return "RENAME_FILE"
	case CHMOD_FILE:
		return "CHMOD_FILE"
	case CREATE_DIR:
		return "CREATE_DIR"
	case DELETE_DIR:
		return "DELETE_DIR"
	case RENAME_DIR:
		return "RENAME_DIR"
	case CREATE_SYMLINK:
		return "CREATE_SYMLINK"
	default:
		return fmt.Sprintf("UnknownFileSyncEventType(%d)", fet)
	}
}

// CommunicationOrigin indicates the source/context of a FileSyncEvent.
type CommunicationOrigin byte

const (
	LOCAL_INOTIFY      CommunicationOrigin = 0x01 // Event detected by daemon's inotify watch.
	REMOTE_PEER        CommunicationOrigin = 0x02 // Event received from another folder_sync daemon.
	PROXY_CLIENT_WRITE CommunicationOrigin = 0x03 // Event originated from a Go Client Package writing via the proxy socket.
)

// String provides a human-readable representation of CommunicationOrigin.
func (co CommunicationOrigin) String() string {
	switch co {
	case LOCAL_INOTIFY:
		return "LOCAL_INOTIFY"
	case REMOTE_PEER:
		return "REMOTE_PEER"
	case PROXY_CLIENT_WRITE:
		return "PROXY_CLIENT_WRITE"
	default:
		return fmt.Sprintf("UnknownCommunicationOrigin(%d)", co)
	}
}

// MessageType defines the type of a network message in the custom binary protocol.
type MessageType byte

const (
	// Inter-Server Handshake Messages
	MSG_HANDSHAKE_REQUEST  MessageType = 0x01
	MSG_HANDSHAKE_RESPONSE MessageType = 0x02

	// File Synchronization Messages (Inter-Server and Proxy-Client)
	MSG_FILE_SYNC_EVENT       MessageType = 0x03
	MSG_PULL_FILE_REQUEST     MessageType = 0x04
	MSG_PULL_FILE_RESPONSE    MessageType = 0x05
	MSG_SYNC_ACKNOWLEDGE      MessageType = 0x06
	MSG_FILE_REMOVED          MessageType = 0x07
	MSG_SAME_CONTENT          MessageType = 0x08
	MSG_FILE_CHUNK            MessageType = 0x09
	MSG_CHUNK_ACKNOWLEDGE     MessageType = 0x0A
	MSG_INITIAL_SCAN_REQUEST  MessageType = 0x0B
	MSG_INITIAL_SCAN_RESPONSE MessageType = 0x0C

	// Proxy Socket Communication Messages (Proxy-Client)
	MSG_PROXY_WELCOME_REQUEST          MessageType = 0x10
	MSG_PROXY_WELCOME_RESPONSE         MessageType = 0x11
	MSG_PROXY_TEST_CONNECTION_REQUEST  MessageType = 0x12
	MSG_PROXY_TEST_CONNECTION_RESPONSE MessageType = 0x13
	MSG_PROXY_EVENT_ACKNOWLEDGE        MessageType = 0x14

	// General Error Message
	MSG_ERROR MessageType = 0xFE
)

// String provides a human-readable representation of MessageType.
func (mt MessageType) String() string {
	switch mt {
	case MSG_HANDSHAKE_REQUEST:
		return "MSG_HANDSHAKE_REQUEST"
	case MSG_HANDSHAKE_RESPONSE:
		return "MSG_HANDSHAKE_RESPONSE"
	case MSG_FILE_SYNC_EVENT:
		return "MSG_FILE_SYNC_EVENT"
	case MSG_PULL_FILE_REQUEST:
		return "MSG_PULL_FILE_REQUEST"
	case MSG_PULL_FILE_RESPONSE:
		return "MSG_PULL_FILE_RESPONSE"
	case MSG_SYNC_ACKNOWLEDGE:
		return "MSG_SYNC_ACKNOWLEDGE"
	case MSG_FILE_REMOVED:
		return "MSG_FILE_REMOVED"
	case MSG_SAME_CONTENT:
		return "MSG_SAME_CONTENT"
	case MSG_FILE_CHUNK:
		return "MSG_FILE_CHUNK"
	case MSG_CHUNK_ACKNOWLEDGE:
		return "MSG_CHUNK_ACKNOWLEDGE"
	case MSG_INITIAL_SCAN_REQUEST:
		return "MSG_INITIAL_SCAN_REQUEST"
	case MSG_INITIAL_SCAN_RESPONSE:
		return "MSG_INITIAL_SCAN_RESPONSE"
	case MSG_PROXY_WELCOME_REQUEST:
		return "MSG_PROXY_WELCOME_REQUEST"
	case MSG_PROXY_WELCOME_RESPONSE:
		return "MSG_PROXY_WELCOME_RESPONSE"
	case MSG_PROXY_TEST_CONNECTION_REQUEST:
		return "MSG_PROXY_TEST_CONNECTION_REQUEST"
	case MSG_PROXY_TEST_CONNECTION_RESPONSE:
		return "MSG_PROXY_TEST_CONNECTION_RESPONSE"
	case MSG_PROXY_EVENT_ACKNOWLEDGE:
		return "MSG_PROXY_EVENT_ACKNOWLEDGE"
	case MSG_ERROR:
		return "MSG_ERROR"
	default:
		return fmt.Sprintf("UNKNOWN_MESSAGE_TYPE(0x%x)", byte(mt))
	}
}

// ProtocolVersion represents the semantic version of the protocol.
type ProtocolVersion struct {
	Major byte
	Minor byte
	Patch byte
}

// String provides a human-readable representation of ProtocolVersion.
func (pv ProtocolVersion) String() string {
	return fmt.Sprintf("%d.%d.%d", pv.Major, pv.Minor, pv.Patch)
}

// HandshakeRequest represents a client handshake request message.
type HandshakeRequest struct {
	AuthToken     string          // Authentication token
	ClientVersion ProtocolVersion // Client protocol version
	PeerName      string          // Name of the connecting peer
}

// HandshakeResponse represents a server handshake response message.
type HandshakeResponse struct {
	Success       bool            // Whether authentication was successful
	ServerVersion ProtocolVersion // Server protocol version
	ErrorMessage  string          // Error message if Success is false
}
