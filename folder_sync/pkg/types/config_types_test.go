package types_test

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/real-rm/folder_sync/pkg/types"
)

// TestPairConfig_String tests the String method of PairConfig.
func TestPairConfig_String(t *testing.T) {
	cfg := types.PairConfig{
		Name:                "server-b",
		HostnameOrIP:        "*************",
		Direction:           types.BIDIRECTIONAL_NEWEST_WIN,
		InitialSyncStrategy: types.SYNC,
		RemotePort:          8081,
	}
	expected := "PairConfig[Name: server-b, Host: *************, Direction: BIDIRECTIONAL_NEWEST_WIN, InitialSync: SYNC, RemotePort: 8081]"
	actual := cfg.String()
	if actual != expected {
		t.Errorf("PairConfig.String() = %q, want %q", actual, expected)
	}
}

// TestPairConfig_Fields tests that PairConfig fields can be correctly assigned.
func TestPairConfig_Fields(t *testing.T) {
	cfg := types.PairConfig{
		Name:                "test-peer",
		HostnameOrIP:        "localhost",
		Direction:           types.ONE_WAY_TO_PEER,
		InitialSyncStrategy: types.NO_INITIAL_SYNC,
		RemotePort:          9000,
	}

	if cfg.Name != "test-peer" {
		t.Errorf("Expected Name %q, got %q", "test-peer", cfg.Name)
	}
	if cfg.HostnameOrIP != "localhost" {
		t.Errorf("Expected HostnameOrIP %q, got %q", "localhost", cfg.HostnameOrIP)
	}
	if cfg.Direction != types.ONE_WAY_TO_PEER {
		t.Errorf("Expected Direction %q, got %q", types.ONE_WAY_TO_PEER, cfg.Direction)
	}
	if cfg.InitialSyncStrategy != types.NO_INITIAL_SYNC {
		t.Errorf("Expected InitialSyncStrategy %q, got %q", types.NO_INITIAL_SYNC, cfg.InitialSyncStrategy)
	}
	if cfg.RemotePort != 9000 {
		t.Errorf("Expected RemotePort %d, got %d", 9000, cfg.RemotePort)
	}
}

// TestSyncOpCounts_String tests the String method of SyncOpCounts.
func TestSyncOpCounts_String(t *testing.T) {
	counts := types.SyncOpCounts{
		Creates:           10,
		Writes:            20,
		Deletes:           5,
		Renames:           2,
		Chmods:            1,
		CreateDirs:        3,
		DeleteDirs:        1,
		RenameDirs:        0,
		SentFiles:         15,
		ReceivedFiles:     25,
		RemovedBeforeSent: 1,
		Bytes:             1234567,
	}
	expected := "C:10 W:20 D:5 R:2 Ch:1 CD:3 DD:1 RD:0 S:15 R:25 RBS:1 Bytes:1234567"
	actual := counts.String()
	if actual != expected {
		t.Errorf("SyncOpCounts.String() = %q, want %q", actual, expected)
	}
}

// TestSyncOpCounts_Fields tests that SyncOpCounts fields can be correctly assigned.
func TestSyncOpCounts_Fields(t *testing.T) {
	counts := types.SyncOpCounts{
		Creates:           1,
		Writes:            2,
		Deletes:           3,
		Renames:           4,
		Chmods:            5,
		CreateDirs:        6,
		DeleteDirs:        7,
		RenameDirs:        8,
		SentFiles:         9,
		ReceivedFiles:     10,
		RemovedBeforeSent: 11,
		Bytes:             12345,
	}
	if counts.Creates != 1 {
		t.Errorf("Expected Creates 1, got %d", counts.Creates)
	}
	if counts.Writes != 2 {
		t.Errorf("Expected Writes 2, got %d", counts.Writes)
	}
	// ... continue for all fields
	if counts.Bytes != 12345 {
		t.Errorf("Expected Bytes 12345, got %d", counts.Bytes)
	}
}

// TestNewPairStatus tests the NewPairStatus constructor.
func TestNewPairStatus(t *testing.T) {
	name := "test-peer-status"
	queueDBFile := "/var/lib/folder_sync/meta/test-peer.db"
	ps := types.NewPairStatus(name, queueDBFile)

	if ps.Name != name {
		t.Errorf("Expected Name %q, got %q", name, ps.Name)
	}
	if ps.InitialSyncStatus != types.PENDING {
		t.Errorf("Expected InitialSyncStatus %q, got %q", types.PENDING, ps.InitialSyncStatus)
	}
	if ps.CurrentQueueDBFile != queueDBFile {
		t.Errorf("Expected CurrentQueueDBFile %q, got %q", queueDBFile, ps.CurrentQueueDBFile)
	}
	if ps.ConnectionRetries != 0 {
		t.Errorf("Expected ConnectionRetries 0, got %d", ps.ConnectionRetries)
	}
	// Check initial values of SyncOpCounts
	if ps.TotalSent.Creates != 0 || ps.TotalReceived.Bytes != 0 {
		t.Errorf("Expected TotalSent and TotalReceived to be zero-valued, got %v, %v", ps.TotalSent, ps.TotalReceived)
	}
	// Check timestamp is roughly epoch
	if ps.LastSyncedTimestamp.UnixNano() != time.Unix(0, 0).UnixNano() {
		t.Errorf("Expected LastSyncedTimestamp to be epoch, got %v", ps.LastSyncedTimestamp)
	}
}

// TestPairStatus_String tests the String method of PairStatus.
func TestPairStatus_String(t *testing.T) {
	testTime := time.Date(2023, 5, 10, 14, 0, 0, 0, time.UTC)
	ps := types.PairStatus{
		Name:                "prod-server",
		LastSyncedTimestamp: testTime,
		InitialSyncStatus:   types.COMPLETED,
		CurrentQueueDBFile:  "/path/to/prod-q.db",
		ConnectionRetries:   5,
		TotalSent:           types.SyncOpCounts{Writes: 100, Bytes: 102400},
		TotalReceived:       types.SyncOpCounts{Deletes: 10, Bytes: 500},
	}
	expected := "PairStatus[Name: prod-server, LastSync: 2023-05-10T14:00:00Z, InitialSync: COMPLETED, Retries: 5, Sent: C:0 W:100 D:0 R:0 Ch:0 CD:0 DD:0 RD:0 S:0 R:0 RBS:0 Bytes:102400, Recv: C:0 W:0 D:10 R:0 Ch:0 CD:0 DD:0 RD:0 S:0 R:0 RBS:0 Bytes:500]"
	actual := ps.String()
	if actual != expected {
		t.Errorf("PairStatus.String() = %q, want %q", actual, expected)
	}
}

// TestPairStatus_JSONSerialization tests JSON marshaling and unmarshaling of PairStatus.
func TestPairStatus_JSONSerialization(t *testing.T) {
	testTime := time.Date(2023, 5, 10, 14, 0, 0, 0, time.UTC)
	originalPs := types.PairStatus{
		Name:                "prod-server",
		LastSyncedTimestamp: testTime,
		InitialSyncStatus:   types.COMPLETED,
		CurrentQueueDBFile:  "/path/to/prod-q.db",
		ConnectionRetries:   5,
		TotalSent: types.SyncOpCounts{
			Creates:           1,
			Writes:            100,
			Deletes:           2,
			Renames:           3,
			Chmods:            4,
			CreateDirs:        5,
			DeleteDirs:        6,
			RenameDirs:        7,
			SentFiles:         100,
			ReceivedFiles:     0,
			RemovedBeforeSent: 1,
			Bytes:             102400,
		},
		TotalReceived: types.SyncOpCounts{
			Creates:           10,
			Writes:            0,
			Deletes:           1,
			Renames:           2,
			Chmods:            3,
			CreateDirs:        4,
			DeleteDirs:        5,
			RenameDirs:        6,
			SentFiles:         0,
			ReceivedFiles:     10,
			RemovedBeforeSent: 0,
			Bytes:             500,
		},
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(originalPs)
	if err != nil {
		t.Fatalf("Failed to marshal PairStatus to JSON: %v", err)
	}

	// Unmarshal from JSON
	var unmarshaledPs types.PairStatus
	err = json.Unmarshal(jsonData, &unmarshaledPs)
	if err != nil {
		t.Fatalf("Failed to unmarshal JSON to PairStatus: %v", err)
	}

	// Compare fields
	if originalPs.Name != unmarshaledPs.Name {
		t.Errorf("Name mismatch: got %q, want %q", unmarshaledPs.Name, originalPs.Name)
	}
	if !originalPs.LastSyncedTimestamp.Equal(unmarshaledPs.LastSyncedTimestamp) {
		t.Errorf("LastSyncedTimestamp mismatch: got %v, want %v", unmarshaledPs.LastSyncedTimestamp, originalPs.LastSyncedTimestamp)
	}
	if originalPs.InitialSyncStatus != unmarshaledPs.InitialSyncStatus {
		t.Errorf("InitialSyncStatus mismatch: got %q, want %q", unmarshaledPs.InitialSyncStatus, originalPs.InitialSyncStatus)
	}
	if originalPs.CurrentQueueDBFile != unmarshaledPs.CurrentQueueDBFile {
		t.Errorf("CurrentQueueDBFile mismatch: got %q, want %q", unmarshaledPs.CurrentQueueDBFile, originalPs.CurrentQueueDBFile)
	}
	if originalPs.ConnectionRetries != unmarshaledPs.ConnectionRetries {
		t.Errorf("ConnectionRetries mismatch: got %d, want %d", unmarshaledPs.ConnectionRetries, originalPs.ConnectionRetries)
	}

	// Compare SyncOpCounts (TotalSent)
	if originalPs.TotalSent.Creates != unmarshaledPs.TotalSent.Creates ||
		originalPs.TotalSent.Writes != unmarshaledPs.TotalSent.Writes ||
		originalPs.TotalSent.Deletes != unmarshaledPs.TotalSent.Deletes ||
		originalPs.TotalSent.Renames != unmarshaledPs.TotalSent.Renames ||
		originalPs.TotalSent.Chmods != unmarshaledPs.TotalSent.Chmods ||
		originalPs.TotalSent.CreateDirs != unmarshaledPs.TotalSent.CreateDirs ||
		originalPs.TotalSent.DeleteDirs != unmarshaledPs.TotalSent.DeleteDirs ||
		originalPs.TotalSent.RenameDirs != unmarshaledPs.TotalSent.RenameDirs ||
		originalPs.TotalSent.SentFiles != unmarshaledPs.TotalSent.SentFiles ||
		originalPs.TotalSent.ReceivedFiles != unmarshaledPs.TotalSent.ReceivedFiles ||
		originalPs.TotalSent.RemovedBeforeSent != unmarshaledPs.TotalSent.RemovedBeforeSent ||
		originalPs.TotalSent.Bytes != unmarshaledPs.TotalSent.Bytes {
		t.Errorf("TotalSent mismatch: got %v, want %v", unmarshaledPs.TotalSent, originalPs.TotalSent)
	}

	// Compare SyncOpCounts (TotalReceived)
	if originalPs.TotalReceived.Creates != unmarshaledPs.TotalReceived.Creates ||
		originalPs.TotalReceived.Writes != unmarshaledPs.TotalReceived.Writes ||
		originalPs.TotalReceived.Deletes != unmarshaledPs.TotalReceived.Deletes ||
		originalPs.TotalReceived.Renames != unmarshaledPs.TotalReceived.Renames ||
		originalPs.TotalReceived.Chmods != unmarshaledPs.TotalReceived.Chmods ||
		originalPs.TotalReceived.CreateDirs != unmarshaledPs.TotalReceived.CreateDirs ||
		originalPs.TotalReceived.DeleteDirs != unmarshaledPs.TotalReceived.DeleteDirs ||
		originalPs.TotalReceived.RenameDirs != unmarshaledPs.TotalReceived.RenameDirs ||
		originalPs.TotalReceived.SentFiles != unmarshaledPs.TotalReceived.SentFiles ||
		originalPs.TotalReceived.ReceivedFiles != unmarshaledPs.TotalReceived.ReceivedFiles ||
		originalPs.TotalReceived.RemovedBeforeSent != unmarshaledPs.TotalReceived.RemovedBeforeSent ||
		originalPs.TotalReceived.Bytes != unmarshaledPs.TotalReceived.Bytes {
		t.Errorf("TotalReceived mismatch: got %v, want %v", unmarshaledPs.TotalReceived, originalPs.TotalReceived)
	}
}
