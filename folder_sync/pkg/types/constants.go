package types

// File and Network Transfer Constants
const (
	// DefaultChunkSize is the default size for file chunks during transfer
	// Used for large file transfers over network and proxy connections
	DefaultChunkSize = 64 * 1024 // 64 KB

	// DefaultBlockSize is the default block size for file operations
	// Used for checksum calculations and file I/O operations
	DefaultBlockSize = 4 * 1024 // 4 KB

	// SmallFileThreshold defines the size threshold for direct vs atomic writes
	// Files smaller than this are written directly without temporary files
	SmallFileThreshold = DefaultChunkSize // same as chunk size

	// DefaultIOBufferSize is the default buffer size for I/O operations
	// Used for buffered readers and general I/O operations
	DefaultIOBufferSize = 4 * 1024 // 4 KB

	// OSBlockSizeFallback is the fallback block size when OS block size detection fails
	// Used when syscall to get filesystem block size fails
	OSBlockSizeFallback = 4 * 1024 // 4 KB
)

// Network Protocol Constants
const (
	// MaxStringSize is the maximum allowed size for strings in network protocol
	// Prevents memory exhaustion attacks
	MaxStringSize = 32 * 1024 // 32 KB

	// MaxByteArraySize is the maximum allowed size for byte arrays in network protocol
	// Prevents memory exhaustion attacks
	MaxByteArraySize = 100 * 1024 * 1024 // 100 MB

	// MaxMessageQueueSize is the default size for message queues
	MaxMessageQueueSize = 100
)

// Configuration Defaults
const (
	// DefaultConcurrentTransfers is the default number of concurrent file transfers
	DefaultConcurrentTransfers = 100

	// DefaultInitialSyncTimeoutMinutes is the default timeout for initial sync logging
	DefaultInitialSyncTimeoutMinutes = 20

	// DefaultStatusSaveIntervalSeconds is the default interval for saving status files
	// Status files are saved periodically to reduce I/O overhead
	DefaultStatusSaveIntervalSeconds = 5
)

// Event Debounce Constants
const (
	// DefaultDebounceWindowMs is the default debounce window in milliseconds
	// Events for the same file within this window will be buffered and potentially consolidated
	DefaultDebounceWindowMs = 250

	// DefaultDebounceBufferSize is the default size of the debounce buffer
	// Maximum number of events that can be buffered during debounce window
	DefaultDebounceBufferSize = 100

	// DefaultEventConsolidationEnabled determines if event consolidation is enabled by default
	// When enabled, related events (like CHMOD + CREATE) are consolidated into meaningful events
	DefaultEventConsolidationEnabled = true

	// DefaultTempFilePatterns are common patterns for temporary files that should be debounced more aggressively
	// These patterns help identify temporary files that may generate rapid event sequences
	DefaultTempFileDebounceMs = 1000 // 1 second for temp files
)
