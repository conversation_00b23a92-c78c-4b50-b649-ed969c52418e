package types_test

import (
	"testing"

	"github.com/real-rm/folder_sync/pkg/types"
)

// MockIgnoredPathMatcher is a simple implementation of IgnoredPathMatcher for testing purposes.
type MockIgnoredPathMatcher struct {
	ignoredPaths []string
}

// NewMockIgnoredPathMatcher creates a new mock matcher.
func NewMockIgnoredPathMatcher(paths ...string) *MockIgnoredPathMatcher {
	return &MockIgnoredPathMatcher{
		ignoredPaths: paths,
	}
}

// IsIgnored checks if the path is in the predefined list of ignored paths.
func (m *MockIgnoredPathMatcher) IsIgnored(path string) bool {
	for _, p := range m.ignoredPaths {
		if p == path {
			return true
		}
	}
	return false
}

// TestIgnoredPathMatcher_Interface tests that MockIgnoredPathMatcher correctly implements the interface.
func TestIgnoredPathMatcher_Interface(t *testing.T) {
	// This test ensures that MockIgnoredPathMatcher satisfies the IgnoredPathMatcher interface.
	// If it doesn't, this line will cause a compilation error.
	var _ types.IgnoredPathMatcher = &MockIgnoredPathMatcher{}

	matcher := NewMockIgnoredPathMatcher("temp/", "config.yaml", "*.log")

	tests := []struct {
		path     string
		expected bool
	}{
		{"temp/file.txt", false}, // Mock only checks exact match for simplicity
		{"config.yaml", true},
		{"main.go", false},
		{"sub/dir/config.yaml", false},
		{"*.log", true}, // Mock does not interpret wildcards, it's just an exact string
		{"another.log", false},
	}

	for _, tt := range tests {
		t.Run(tt.path, func(t *testing.T) {
			actual := matcher.IsIgnored(tt.path)
			if actual != tt.expected {
				t.Errorf("IsIgnored(%q) = %t, want %t", tt.path, actual, tt.expected)
			}
		})
	}
}

// TestIgnoredPathMatcher_EmptyList tests behavior with an empty ignored list.
func TestIgnoredPathMatcher_EmptyList(t *testing.T) {
	matcher := NewMockIgnoredPathMatcher()
	if matcher.IsIgnored("any/path.txt") {
		t.Errorf("IsIgnored should return false for any path when list is empty")
	}
}

// TestIgnoredPathMatcher_NoMatch tests when no path matches.
func TestIgnoredPathMatcher_NoMatch(t *testing.T) {
	matcher := NewMockIgnoredPathMatcher("a/b.txt", "c/d.log")
	if matcher.IsIgnored("x/y.json") {
		t.Errorf("IsIgnored should return false when no path matches")
	}
}
