package types

import (
	"fmt"
	"sync"
	"time"
)

// PairDirection defines the synchronization direction for a paired server.
type PairDirection string

const (
	ONE_WAY_TO_PEER          PairDirection = "ONE_WAY_TO_PEER"          // This server sends changes to the peer.
	ONE_WAY_FROM_PEER        PairDirection = "ONE_WAY_FROM_PEER"        // This server receives changes from the peer.
	BIDIRECTIONAL_NEWEST_WIN PairDirection = "BIDIRECTIONAL_NEWEST_WIN" // Both servers sync, newest file wins.
)

// InitialSyncStrategy defines how the initial synchronization should be handled.
type InitialSyncStrategy string

const (
	SYNC            InitialSyncStrategy = "SYNC"            // Perform a full initial sync.
	NO_INITIAL_SYNC InitialSyncStrategy = "NO_INITIAL_SYNC" // Skip initial sync, only process ongoing changes.
)

// PairConfig represents configuration for a single paired server.
type PairConfig struct {
	Name                string              `yaml:"name"`                  // Unique name for the pair (e.g., "remote-server-a")
	HostnameOrIP        string              `yaml:"host"`                  // Hostname or IP address of the paired server.
	Direction           PairDirection       `yaml:"direction"`             // Sync direction.
	InitialSyncStrategy InitialSyncStrategy `yaml:"initial_sync_strategy"` // Strategy for initial sync.
	RemotePort          int                 `yaml:"remote_port"`           // Port on the remote server to connect to.
}

// String provides a human-readable representation of a PairConfig.
func (pc PairConfig) String() string {
	return fmt.Sprintf("PairConfig[Name: %s, Host: %s, Direction: %s, InitialSync: %s, RemotePort: %d]",
		pc.Name, pc.HostnameOrIP, pc.Direction, pc.InitialSyncStrategy, pc.RemotePort)
}

// InitialSyncStatus defines the current status of an initial synchronization.
type InitialSyncStatus string

const (
	PENDING     InitialSyncStatus = "PENDING"     // Initial sync has not started yet.
	IN_PROGRESS InitialSyncStatus = "IN_PROGRESS" // Initial sync is currently running.
	COMPLETED   InitialSyncStatus = "COMPLETED"   // Initial sync has completed successfully.
	FAILED      InitialSyncStatus = "FAILED"      // Initial sync failed.
)

// SyncOpCounts tracks counts for different synchronization operations and bytes.
// This is used for statistics and persisted in PairStatus.
type SyncOpCounts struct {
	Creates           int        // Number of file/dir creations
	Writes            int        // Number of file writes/modifications
	Deletes           int        // Number of file/dir deletions
	Renames           int        // Number of file/dir renames
	Chmods            int        // Number of chmod operations
	CreateDirs        int        // Number of directory creations
	DeleteDirs        int        // Number of directory deletions
	RenameDirs        int        // Number of directory renames
	SentFiles         int        // Number of files sent to this peer
	ReceivedFiles     int        // Number of files received from this peer
	RemovedBeforeSent int        // Number of files that were removed before they could be sent
	Bytes             uint64     // Total bytes transferred for this peer
	mutex             sync.Mutex // Protects concurrent access to all fields
}

// String provides a human-readable representation of SyncOpCounts.
func (sc *SyncOpCounts) String() string {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()
	return fmt.Sprintf("C:%d W:%d D:%d R:%d Ch:%d CD:%d DD:%d RD:%d S:%d R:%d RBS:%d Bytes:%d",
		sc.Creates, sc.Writes, sc.Deletes, sc.Renames, sc.Chmods, sc.CreateDirs, sc.DeleteDirs, sc.RenameDirs, sc.SentFiles, sc.ReceivedFiles, sc.RemovedBeforeSent, sc.Bytes)
}

// Update increments the appropriate counter based on the event type and adds to the byte count.
func (sc *SyncOpCounts) Update(eventType FileSyncEventType, bytes uint64) {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()

	sc.Bytes += bytes

	switch eventType {
	case CREATE_FILE:
		sc.Creates++
		sc.SentFiles++
	case WRITE_FILE:
		sc.Writes++
		sc.SentFiles++
	case DELETE_FILE:
		sc.Deletes++
	case RENAME_FILE:
		sc.Renames++
	case CHMOD_FILE:
		sc.Chmods++
	case CREATE_DIR:
		sc.CreateDirs++
	case DELETE_DIR:
		sc.DeleteDirs++
	case RENAME_DIR:
		sc.RenameDirs++
	case CREATE_SYMLINK:
		sc.Creates++
		sc.SentFiles++
	}
}

// Copy creates a thread-safe copy of the SyncOpCounts.
func (sc *SyncOpCounts) Copy() SyncOpCounts {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()

	return SyncOpCounts{
		Creates:           sc.Creates,
		Writes:            sc.Writes,
		Deletes:           sc.Deletes,
		Renames:           sc.Renames,
		Chmods:            sc.Chmods,
		CreateDirs:        sc.CreateDirs,
		DeleteDirs:        sc.DeleteDirs,
		RenameDirs:        sc.RenameDirs,
		SentFiles:         sc.SentFiles,
		ReceivedFiles:     sc.ReceivedFiles,
		RemovedBeforeSent: sc.RemovedBeforeSent,
		Bytes:             sc.Bytes,
		// mutex is not copied - new instance gets its own mutex
	}
}

// PairStatus represents the persisted status for a paired server.
// This data will be saved to disk and loaded on restart.
type PairStatus struct {
	Name                string            `json:"name"`                  // Name of the paired server (must match PairConfig.Name)
	LastSyncedTimestamp time.Time         `json:"last_synced_timestamp"` // Timestamp of the last successful sync operation for this peer.
	InitialSyncStatus   InitialSyncStatus `json:"initial_sync_status"`   // Current initial sync status.
	CurrentQueueDBFile  string            `json:"current_queue_db_file"` // Path to the SQLite DB file for this peer's event queue.
	ConnectionRetries   int               `json:"connection_retries"`    // Connection retry counter (cleared to 0 when connected).
	TotalSent           SyncOpCounts      `json:"total_sent"`            // Statistics for operations sent to this peer.
	TotalReceived       SyncOpCounts      `json:"total_received"`        // Statistics for operations received from this peer.
}

// NewPairStatus creates a new PairStatus with default values.
func NewPairStatus(name, queueDBFile string) *PairStatus {
	return &PairStatus{
		Name:                name,
		LastSyncedTimestamp: time.Unix(0, 0), // Epoch time
		InitialSyncStatus:   PENDING,
		CurrentQueueDBFile:  queueDBFile,
		ConnectionRetries:   0,
		TotalSent:           SyncOpCounts{},
		TotalReceived:       SyncOpCounts{},
	}
}

// String provides a human-readable representation of a PairStatus.
func (ps *PairStatus) String() string {
	return fmt.Sprintf("PairStatus[Name: %s, LastSync: %s, InitialSync: %s, Retries: %d, Sent: %s, Recv: %s]",
		ps.Name, ps.LastSyncedTimestamp.Format(time.RFC3339), ps.InitialSyncStatus, ps.ConnectionRetries, ps.TotalSent.String(), ps.TotalReceived.String())
}
