package logger_test

import (
	"bytes"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"syscall" // Moved import here
	"testing"
	"time"

	"github.com/real-rm/folder_sync/pkg/logger"
)

// safeBuffer wraps bytes.Buffer with mutex protection
type safeBuffer struct {
	buf *bytes.Buffer
	mu  sync.RWMutex
}

func (sb *safeBuffer) String() string {
	sb.mu.RLock()
	defer sb.mu.RUnlock()
	return sb.buf.String()
}

func (sb *safeBuffer) Write(p []byte) (n int, err error) {
	sb.mu.Lock()
	defer sb.mu.Unlock()
	return sb.buf.Write(p)
}

// captureOutput redirects os.Stdout and os.Stderr to a buffer and returns the buffer.
// Call the returned cleanup function to restore original stdout/stderr.
func captureOutput(t *testing.T) (*safeBuffer, *safeBuffer, func()) {
	t.Helper()
	oldStdout := os.Stdout
	oldStderr := os.Stderr

	rOut, wOut, _ := os.Pipe()
	rErr, wErr, _ := os.Pipe()

	os.Stdout = wOut
	os.Stderr = wErr

	var wg sync.WaitGroup

	// Create thread-safe buffers
	safeBufferOut := &safeBuffer{buf: &bytes.Buffer{}}
	safeBufferErr := &safeBuffer{buf: &bytes.Buffer{}}

	wg.Add(2)

	go func() {
		defer wg.Done()
		io.Copy(safeBufferOut, rOut)
	}()
	go func() {
		defer wg.Done()
		io.Copy(safeBufferErr, rErr)
	}()

	cleanup := func() {
		wOut.Close()
		wErr.Close()
		wg.Wait()
		os.Stdout = oldStdout
		os.Stderr = oldStderr
	}

	return safeBufferOut, safeBufferErr, cleanup
}

// TestLogLevel_String tests the String method of LogLevel.
func TestLogLevel_String(t *testing.T) {
	tests := []struct {
		level    logger.LogLevel
		expected string
	}{
		{logger.DEBUG, "DEBUG"},
		{logger.INFO, "INFO"},
		{logger.WARN, "WARN"},
		{logger.ERROR, "ERROR"},
		{logger.FATAL, "FATAL"},
		{logger.LogLevel(99), "UNKNOWN(99)"}, // Test an unknown level
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			if actual := tt.level.String(); actual != tt.expected {
				t.Errorf("LogLevel.String() = %q, want %q", actual, tt.expected)
			}
		})
	}
}

// TestNewLogger tests the NewLogger constructor.
func TestNewLogger(t *testing.T) {
	tempLogsDir := t.TempDir()
	defer os.RemoveAll(tempLogsDir)

	// Test successful creation with debug mode
	l, err := logger.NewLogger(tempLogsDir, true)
	if err != nil {
		t.Fatalf("NewLogger failed with debug mode: %v", err)
	}
	if l == nil {
		t.Fatal("NewLogger returned nil logger")
	}

	// Test successful creation without debug mode
	l2, err := logger.NewLogger(tempLogsDir, false)
	if err != nil {
		t.Fatalf("NewLogger failed without debug mode: %v", err)
	}
	if l2 == nil {
		t.Fatal("NewLogger returned nil logger")
	}

	// Test creation with empty logsPath
	_, err = logger.NewLogger("", false)
	if err == nil {
		t.Error("NewLogger expected error for empty logsPath, got nil")
	}
	if err != nil && !strings.Contains(err.Error(), "logsPath cannot be empty") {
		t.Errorf("NewLogger expected 'logsPath cannot be empty' error, got: %v", err)
	}

	// Test creation with uncreatable logsPath (e.g., permissions issue)
	uncreatablePath := "/root/uncreatable/logs" // Assume /root is not writable by tests
	if os.Getuid() != 0 {                       // Only run if not root to avoid actual permission issues
		_, err = logger.NewLogger(uncreatablePath, false)
		if err == nil {
			t.Error("NewLogger expected error for uncreatable logsPath, got nil")
		}
		if err != nil && !strings.Contains(err.Error(), "permission denied") {
			t.Logf("Warning: Expected 'permission denied' error for uncreatable logsPath, got: %v", err)
		}
	}
}

// TestLoggingLevels tests that messages are logged according to their level and debug mode.
func TestLoggingLevels(t *testing.T) {
	tempLogsDir := t.TempDir()
	defer os.RemoveAll(tempLogsDir)

	tests := []struct {
		name      string
		debugMode bool
		logFunc   func(*logger.Logger, string, ...interface{})
		levelStr  string // Expected level string in output
		expectLog bool
	}{
		{"DebugMode: DEBUG, Log: DEBUG", true, (*logger.Logger).Debug, "DEBUG", true},
		{"DebugMode: DEBUG, Log: INFO", true, (*logger.Logger).Info, "INFO", true},
		{"DebugMode: DEBUG, Log: WARN", true, (*logger.Logger).Warn, "WARN", true},
		{"DebugMode: DEBUG, Log: ERROR", true, (*logger.Logger).Error, "ERROR", true},
		{"DebugMode: DEBUG, Log: FATAL (will exit)", true, func(l *logger.Logger, format string, args ...interface{}) {
			// FATAL exits, so we'll test it separately or mock os.Exit
		}, "ERROR", true}, // We test with ERROR since we call l.Error() instead of l.Fatal()

		{"DebugMode: INFO, Log: DEBUG", false, (*logger.Logger).Debug, "DEBUG", false},
		{"DebugMode: INFO, Log: INFO", false, (*logger.Logger).Info, "INFO", true},
		{"DebugMode: INFO, Log: WARN", false, (*logger.Logger).Warn, "WARN", true},
		{"DebugMode: INFO, Log: ERROR", false, (*logger.Logger).Error, "ERROR", true},
		{"DebugMode: INFO, Log: FATAL (will exit)", false, func(l *logger.Logger, format string, args ...interface{}) {
			// FATAL exits, so we'll test it separately or mock os.Exit
		}, "ERROR", true}, // We test with ERROR since we call l.Error() instead of l.Fatal()
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stdout, stderr, cleanup := captureOutput(t)
			defer cleanup()

			l, err := logger.NewLogger(tempLogsDir, tt.debugMode)
			if err != nil {
				t.Fatalf("Failed to create logger: %v", err)
			}
			defer l.Close() // Ensure log files are closed

			testMsg := "This is a test message."

			if tt.levelStr != "ERROR" { // Do not call fatal here, it exits (we changed FATAL to ERROR)
				tt.logFunc(l, testMsg)
			} else {
				// For FATAL, we skip testing os.Exit behavior since it's hard to mock
				// Instead, we'll just verify that the log message would be written
				// by testing the underlying log function without calling Fatal
				l.Error(testMsg) // Use Error instead to test the logging mechanism
			}

			// Give time for logs to be written and flushed
			time.Sleep(10 * time.Millisecond)

			// Read combined output from stdout and stderr
			combinedOutput := stdout.String() + stderr.String()

			if tt.expectLog {
				if !strings.Contains(combinedOutput, tt.levelStr) {
					t.Errorf("Expected log message with level %q, but not found in output:\n%s", tt.levelStr, combinedOutput)
				}
				if !strings.Contains(combinedOutput, testMsg) {
					t.Errorf("Expected log message content %q, but not found in output:\n%s", testMsg, combinedOutput)
				}
			} else {
				if strings.Contains(combinedOutput, tt.levelStr) {
					t.Errorf("Did not expect log message with level %q, but found in output:\n%s", tt.levelStr, combinedOutput)
				}
				if strings.Contains(combinedOutput, testMsg) {
					t.Errorf("Did not expect log message content %q, but found in output:\n%s", testMsg, combinedOutput)
				}
			}
		})
	}
}

// TestPeerSpecificLogging tests that logs go to the correct peer-specific files.
func TestPeerSpecificLogging(t *testing.T) {
	tempLogsDir := t.TempDir()
	defer os.RemoveAll(tempLogsDir)

	// Capture stdout/stderr for general and error messages
	stdout, stderr, cleanup := captureOutput(t)
	defer cleanup()

	l, err := logger.NewLogger(tempLogsDir, true) // Debug mode to log everything
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}
	defer l.Close()

	peerID1 := "192.168.1.10_peer_A"
	peerID2 := "2001_0db8_85a3_0000_0000_8a2e_0370_7334_peer_B" // IPv6 example
	generalMsg := "This is a general message."
	peer1Msg := "Message for peer A."
	peer2Msg := "Message for peer B."

	// Log messages
	l.Info(generalMsg)
	l.InfoP(peerID1, peer1Msg)
	l.ErrorP(peerID2, peer2Msg) // Error message should also go to stderr

	// Wait a moment for logs to be written to files
	time.Sleep(50 * time.Millisecond)

	// Verify general message on stdout
	output := stdout.String()
	if !strings.Contains(output, generalMsg) {
		t.Errorf("General message not found on stdout:\n%s", output)
	}

	// Verify error message on stderr
	errorOutput := stderr.String()
	if !strings.Contains(errorOutput, peer2Msg) {
		t.Errorf("Error message for peer2 not found on stderr:\n%s", errorOutput)
	}
	if !strings.Contains(errorOutput, "ERROR") {
		t.Errorf("Error message for peer2 did not contain ERROR level:\n%s", errorOutput)
	}

	// Verify peer1 log file content
	peer1LogFile := filepath.Join(tempLogsDir, "192_168_1_10_peer_A.log")
	content1, err := os.ReadFile(peer1LogFile) // Changed from ioutil.ReadFile
	if err != nil {
		t.Fatalf("Failed to read peer1 log file %q: %v", peer1LogFile, err)
	}
	if !strings.Contains(string(content1), peer1Msg) {
		t.Errorf("Peer1 log file content mismatch. Expected %q, got:\n%s", peer1Msg, string(content1))
	}
	if !strings.Contains(string(content1), "INFO") { // Check level prefix
		t.Errorf("Peer1 log file content missing INFO prefix:\n%s", string(content1))
	}

	// Verify peer2 log file content (should also be there despite being an error)
	peer2LogFile := filepath.Join(tempLogsDir, "2001_0db8_85a3_0000_0000_8a2e_0370_7334_peer_B.log")
	content2, err := os.ReadFile(peer2LogFile) // Changed from ioutil.ReadFile
	if err != nil {
		t.Fatalf("Failed to read peer2 log file %q: %v", peer2LogFile, err)
	}
	if !strings.Contains(string(content2), peer2Msg) {
		t.Errorf("Peer2 log file content mismatch. Expected %q, got:\n%s", peer2Msg, string(content2))
	}
	if !strings.Contains(string(content2), "ERROR") { // Check level prefix
		t.Errorf("Peer2 log file content missing ERROR prefix:\n%s", string(content2))
	}

	// Test if general messages are NOT written to peer-specific files
	if strings.Contains(string(content1), generalMsg) {
		t.Errorf("General message unexpectedly found in peer1 log file")
	}
}

// TestErrorFallbackToStderr tests if logging to file fails, it falls back to stderr.
func TestErrorFallbackToStderr(t *testing.T) {
	// Create a log directory but make it unwritable *after* creation
	tempLogsDir := t.TempDir()
	defer os.RemoveAll(tempLogsDir)

	// Simulate permissions issue by making the directory unwritable for files
	// This might require running as root or using specific ACLs.
	// For simple testing, we'll try to set permissions that prevent new files.
	// This approach might not work on all OS/filesystem combinations.
	// A more robust test would mock os.OpenFile.
	oldUmask := syscall.Umask(0)  // Save current umask
	defer syscall.Umask(oldUmask) // Restore umask

	// Set umask to prevent others from writing (022 means 0o755 becomes 0o733)
	// We want to prevent the process itself from writing.
	// Let's create an unwritable subdir inside tempLogsDir.
	unwritableLogsDir := filepath.Join(tempLogsDir, "unwritable")
	os.Mkdir(unwritableLogsDir, 0555)       // Read and execute, but no write permissions
	defer os.Chmod(unwritableLogsDir, 0755) // Restore permissions for cleanup

	_, stderr, cleanup := captureOutput(t)
	defer cleanup()

	l, err := logger.NewLogger(unwritableLogsDir, true)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}
	defer l.Close()

	peerID := "unwritable_peer"
	testMsg := "This log should go to stderr due to file write error."
	l.InfoP(peerID, testMsg)

	time.Sleep(50 * time.Millisecond) // Give time for logging

	// Check stderr for the message and the error notification
	errorOutput := stderr.String()
	if !strings.Contains(errorOutput, "Failed to open log file for peer") {
		t.Errorf("Expected 'Failed to open log file' error in stderr, but not found:\n%s", errorOutput)
	}
	if !strings.Contains(errorOutput, testMsg) {
		t.Errorf("Expected log message %q in stderr after fallback, but not found:\n%s", testMsg, errorOutput)
	}

	// Verify no file was created (or it's empty) in the unwritable directory
	peerLogFile := filepath.Join(unwritableLogsDir, "unwritable_peer.log")
	if _, err := os.Stat(peerLogFile); !os.IsNotExist(err) {
		content, _ := os.ReadFile(peerLogFile) // Changed from ioutil.ReadFile
		if len(content) > 0 {
			t.Errorf("File %q was created and written to despite permissions. Content:\n%s", peerLogFile, string(content))
		}
	}
}

// TestNewMemoryLogger tests the NewMemoryLogger function.
func TestNewMemoryLogger(t *testing.T) {
	stdout, stderr, cleanup := captureOutput(t)
	defer cleanup()

	memLogger := logger.NewMemoryLogger()
	if memLogger == nil {
		t.Fatal("NewMemoryLogger returned nil")
	}

	testMsgInfo := "Memory logger info message."
	testMsgError := "Memory logger error message."

	memLogger.Info(testMsgInfo)
	memLogger.Error(testMsgError)

	time.Sleep(10 * time.Millisecond) // Give time for writes

	if !strings.Contains(stdout.String(), testMsgInfo) {
		t.Errorf("Expected info message in stdout, got:\n%s", stdout.String())
	}
	if !strings.Contains(stderr.String(), testMsgError) {
		t.Errorf("Expected error message in stderr, got:\n%s", stderr.String())
	}
}
