package logger

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings" // Added strings import for sanitizeFilename
	"sync"
)

// LogLevel defines the verbosity of log messages.
type LogLevel int

const (
	DEBUG LogLevel = iota // Detailed information, typically only of interest when diagnosing problems.
	INFO                  // Routine information, such as startup messages, state changes, and progress.
	WARN                  // Warning conditions that might indicate a problem.
	ERROR                 // Error conditions.
	FATAL                 // Severe error events that will presumably lead the application to abort.
)

// String returns the string representation of the LogLevel.
func (l LogLevel) String() string {
	switch l {
	case DEBUG:
		return "DEBUG"
	case INFO:
		return "INFO"
	case WARN:
		return "WARN"
	case ERROR:
		return "ERROR"
	case FATAL:
		return "FATAL"
	default:
		return fmt.Sprintf("UNKNOWN(%d)", l)
	}
}

// Logger provides structured logging capabilities.
type Logger struct {
	mu         sync.Mutex             // Mutex to protect concurrent writes to log files
	logOutputs map[string]*log.Logger // Map from peer IP/name to dedicated file logger
	defaultLog *log.Logger            // Default logger for non-peer specific messages
	errorLog   *log.Logger            // Logger for critical errors to stderr/systemd
	logLevel   LogLevel               // Current logging level (DEBUG, INFO, etc.)
	logsPath   string                 // Base path for log files
}

// NewLogger creates and initializes a new Logger instance.
// logsPath is the base directory where peer-specific log files will be stored.
// debugMode enables or disables DEBUG level logging.
func NewLogger(logsPath string, debugMode bool) (*Logger, error) {
	if logsPath == "" {
		return nil, fmt.Errorf("logsPath cannot be empty")
	}
	if err := os.MkdirAll(logsPath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create logs directory %q: %w", logsPath, err)
	}

	logger := &Logger{
		logOutputs: make(map[string]*log.Logger),
		logsPath:   logsPath,
		logLevel:   INFO, // Default to INFO
	}

	if debugMode {
		logger.logLevel = DEBUG
	}

	// Setup default console logger (for general messages not tied to a peer)
	logger.defaultLog = log.New(os.Stdout, "", log.LstdFlags|log.Lmicroseconds)
	logger.defaultLog.SetPrefix(fmt.Sprintf("[%s] ", INFO.String())) // Default prefix

	// Setup error logger to stderr (for systemd journal capture)
	logger.errorLog = log.New(os.Stderr, "", log.LstdFlags|log.Lmicroseconds)
	logger.errorLog.SetPrefix(fmt.Sprintf("[%s] ", ERROR.String())) // Default prefix

	return logger, nil
}

// NewMemoryLogger creates a simple logger that prints to os.Stdout/Stderr for initial startup.
// This is a temporary logger before the full configuration-based logger is initialized.
func NewMemoryLogger() *Logger {
	return &Logger{
		logOutputs: make(map[string]*log.Logger),
		defaultLog: log.New(os.Stdout, "", log.LstdFlags|log.Lmicroseconds),
		errorLog:   log.New(os.Stderr, "", log.LstdFlags|log.Lmicroseconds),
		logLevel:   INFO, // Default to INFO for memory logger
	}
}

// getLoggerForPeer retrieves or creates a specific logger for a given peer.
// The peerID should be a safe string to use as a filename (e.g., sanitized IP or name).
func (l *Logger) getLoggerForPeer(peerID string) *log.Logger {
	l.mu.Lock()
	defer l.mu.Unlock()

	if _, ok := l.logOutputs[peerID]; !ok {
		logFilePath := filepath.Join(l.logsPath, fmt.Sprintf("%s.log", sanitizeFilename(peerID)))
		file, err := os.OpenFile(logFilePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err != nil {
			// If we can't open the peer-specific log file, log to stderr
			l.errorLog.Printf("ERROR: Failed to open log file for peer %q at %q: %v. Logging to stderr instead.", peerID, logFilePath, err)
			l.logOutputs[peerID] = l.errorLog // Fallback to stderr
		} else {
			l.logOutputs[peerID] = log.New(io.MultiWriter(file, os.Stdout), "", log.LstdFlags|log.Lmicroseconds)
		}
	}
	return l.logOutputs[peerID]
}

// sanitizeFilename securely replaces characters that are unsafe in filenames.
func sanitizeFilename(s string) string {
	// Remove null bytes and control characters
	s = strings.ReplaceAll(s, "\x00", "")

	// Replace dangerous path characters
	s = strings.ReplaceAll(s, "..", "_") // Prevent directory traversal
	s = strings.ReplaceAll(s, ".", "_")
	s = strings.ReplaceAll(s, ":", "_")
	s = strings.ReplaceAll(s, "/", "_")
	s = strings.ReplaceAll(s, "\\", "_")
	s = strings.ReplaceAll(s, "*", "_")
	s = strings.ReplaceAll(s, "?", "_")
	s = strings.ReplaceAll(s, "\"", "_")
	s = strings.ReplaceAll(s, "<", "_")
	s = strings.ReplaceAll(s, ">", "_")
	s = strings.ReplaceAll(s, "|", "_")

	// Remove control characters (0x00-0x1F and 0x7F-0x9F)
	result := make([]rune, 0, len(s))
	for _, r := range s {
		if r >= 32 && r < 127 || r > 159 {
			result = append(result, r)
		} else {
			result = append(result, '_')
		}
	}

	// Ensure filename is not empty and not reserved names
	filename := string(result)
	if filename == "" || filename == "." || filename == ".." {
		filename = "sanitized_filename"
	}

	// Limit length to prevent filesystem issues
	if len(filename) > 200 {
		filename = filename[:200]
	}

	return filename
}

// output formats and prints the log message to the appropriate logger.
func (l *Logger) output(level LogLevel, peerID string, format string, args ...interface{}) {
	if level < l.logLevel {
		return // Do not log if current level is lower than desired level
	}

	msg := fmt.Sprintf(format, args...)
	prefix := fmt.Sprintf("[%s] ", level.String())

	var targetLogger *log.Logger
	if peerID != "" {
		targetLogger = l.getLoggerForPeer(peerID)
	} else {
		targetLogger = l.defaultLog
	}

	// Always send FATAL and ERROR messages to stderr for systemd capture
	if level >= ERROR {
		l.errorLog.SetPrefix(prefix)
		l.errorLog.Output(2, msg) // Output to stderr
		// If it's a fatal error, also log to the default/peer logger if different
		if targetLogger != nil && targetLogger != l.errorLog {
			targetLogger.SetPrefix(prefix)
			targetLogger.Output(2, msg)
		}
	} else {
		if targetLogger != nil {
			targetLogger.SetPrefix(prefix)
			targetLogger.Output(2, msg)
		}
	}

	if level == FATAL {
		os.Exit(1) // Exit on FATAL errors as per specification
	}
}

// Debug logs a message at DEBUG level.
func (l *Logger) Debug(format string, args ...interface{}) {
	l.output(DEBUG, "", format, args...)
}

// DebugP logs a message at DEBUG level specific to a peer.
func (l *Logger) DebugP(peerID string, format string, args ...interface{}) {
	l.output(DEBUG, peerID, format, args...)
}

// Info logs a message at INFO level.
func (l *Logger) Info(format string, args ...interface{}) {
	l.output(INFO, "", format, args...)
}

// InfoP logs a message at INFO level specific to a peer.
func (l *Logger) InfoP(peerID string, format string, args ...interface{}) {
	l.output(INFO, peerID, format, args...)
}

// Warn logs a message at WARN level.
func (l *Logger) Warn(format string, args ...interface{}) {
	l.output(WARN, "", format, args...)
}

// WarnP logs a message at WARN level specific to a peer.
func (l *Logger) WarnP(peerID string, format string, args ...interface{}) {
	l.output(WARN, peerID, format, args...)
}

// Error logs a message at ERROR level.
func (l *Logger) Error(format string, args ...interface{}) {
	l.output(ERROR, "", format, args...)
}

// ErrorP logs a message at ERROR level specific to a peer.
func (l *Logger) ErrorP(peerID string, format string, args ...interface{}) {
	l.output(ERROR, peerID, format, args...)
}

// Fatal logs a message at FATAL level and then exits the program.
func (l *Logger) Fatal(format string, args ...interface{}) {
	l.output(FATAL, "", format, args...)
}

// Close closes all open log files. This should be called during graceful shutdown.
func (l *Logger) Close() {
	l.mu.Lock()
	defer l.mu.Unlock()

	for peerID, fileLogger := range l.logOutputs {
		// Get the underlying writer of the log.Logger
		// This part is a bit tricky as log.Logger doesn't expose its writer directly.
		// A common way is to reconstruct it if you created the logger with a file.
		// For simplicity, for now, we assume if it's not default/error logger, it's a file.
		// In a real scenario, you might store the *os.File handles directly.
		if fileWriter, ok := fileLogger.Writer().(*os.File); ok && fileWriter != os.Stdout && fileWriter != os.Stderr {
			fileWriter.Close()
		}
		delete(l.logOutputs, peerID)
	}
}
