package meta_test

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/real-rm/folder_sync/pkg/meta"
	"github.com/real-rm/folder_sync/pkg/types"
)

// MockLogger for testing meta package
type MockLogger struct {
	Errors []string
	Infos  []string
	Debugs []string
	Warns  []string // Added Warns slice
	Fatals []string
	mu     sync.Mutex
	t      *testing.T // For reporting test failures directly
}

func NewMockLogger(t *testing.T) *MockLogger {
	return &MockLogger{t: t}
}

func (m *MockLogger) ErrorP(peerID string, format string, args ...interface{}) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.Errors = append(m.Errors, fmt.Sprintf("PEER %s: %s", peerID, fmt.Sprintf(format, args...)))
	m.t.Logf("[MockLogger ERRORP] PEER %s: %s", peerID, fmt.Sprintf(format, args...))
}
func (m *MockLogger) InfoP(peerID string, format string, args ...interface{}) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.Infos = append(m.Infos, fmt.Sprintf("PEER %s: %s", peerID, fmt.Sprintf(format, args...)))
	m.t.Logf("[MockLogger INFOP] PEER %s: %s", peerID, fmt.Sprintf(format, args...))
}
func (m *MockLogger) DebugP(peerID string, format string, args ...interface{}) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.Debugs = append(m.Debugs, fmt.Sprintf("PEER %s: %s", peerID, fmt.Sprintf(format, args...)))
	m.t.Logf("[MockLogger DEBUGP] PEER %s: %s", peerID, fmt.Sprintf(format, args...))
}

// WarnP logs a message at WARN level specific to a peer.
func (m *MockLogger) WarnP(peerID string, format string, args ...interface{}) { // Added WarnP
	m.mu.Lock()
	defer m.mu.Unlock()
	m.Warns = append(m.Warns, fmt.Sprintf("PEER %s: %s", peerID, fmt.Sprintf(format, args...)))
	m.t.Logf("[MockLogger WARNP] PEER %s: %s", peerID, fmt.Sprintf(format, args...))
}
func (m *MockLogger) Fatal(format string, args ...interface{}) {
	m.mu.Lock()
	defer m.mu.Unlock()
	msg := fmt.Sprintf(format, args...)
	m.Fatals = append(m.Fatals, msg)
	m.t.Fatalf("[MockLogger FATAL] %s", msg) // Use t.Fatalf to stop the test on fatal
}
func (m *MockLogger) Error(format string, args ...interface{}) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.Errors = append(m.Errors, fmt.Sprintf(format, args...))
	m.t.Logf("[MockLogger ERROR] %s", fmt.Sprintf(format, args...))
}
func (m *MockLogger) Info(format string, args ...interface{}) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.Infos = append(m.Infos, fmt.Sprintf(format, args...))
	m.t.Logf("[MockLogger INFO] %s", fmt.Sprintf(format, args...))
}
func (m *MockLogger) Debug(format string, args ...interface{}) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.Debugs = append(m.Debugs, fmt.Sprintf(format, args...))
	m.t.Logf("[MockLogger DEBUG] %s", fmt.Sprintf(format, args...))
}

// Warn logs a message at WARN level.
func (m *MockLogger) Warn(format string, args ...interface{}) { // Added Warn
	m.mu.Lock()
	defer m.mu.Unlock()
	m.Warns = append(m.Warns, fmt.Sprintf(format, args...))
	m.t.Logf("[MockLogger WARN] %s", fmt.Sprintf(format, args...))
}

// TestNewMetaManager tests the constructor and table creation.
func TestNewMetaManager(t *testing.T) {
	tempMetaDir := t.TempDir()
	defer os.RemoveAll(tempMetaDir)

	logger := NewMockLogger(t)

	// Test successful creation
	mm, err := meta.NewMetaManager(tempMetaDir, logger)
	if err != nil {
		t.Fatalf("NewMetaManager failed: %v", err)
	}
	if mm == nil {
		t.Fatal("NewMetaManager returned nil")
	}

	// Verify meta directory was created
	info, err := os.Stat(tempMetaDir)
	if err != nil || !info.IsDir() {
		t.Errorf("Meta directory %q was not created or is not a directory", tempMetaDir)
	}

	// Test creation with empty metaPath
	_, err = meta.NewMetaManager("", logger)
	if err == nil {
		t.Error("NewMetaManager expected error for empty metaPath, got nil")
	}
	if err != nil && !strings.Contains(err.Error(), "metaPath cannot be empty") {
		t.Errorf("Expected 'metaPath cannot be empty' error, got: %v", err)
	}

	// Test creation with uncreatable metaPath (e.g., permissions issue)
	uncreatablePath := "/root/uncreatable/meta" // Assume /root is not writable by tests
	if os.Getuid() != 0 {                       // Only run if not root to avoid actual permission issues
		_, err = meta.NewMetaManager(uncreatablePath, logger)
		if err == nil {
			t.Error("NewMetaManager expected error for uncreatable metaPath, got nil")
		}
		if err != nil && !strings.Contains(err.Error(), "permission denied") {
			t.Logf("Warning: Expected 'permission denied' error for uncreatable metaPath, got: %v", err)
		}
	}
}

// TestSaveAndLoadPairStatus tests saving and loading PairStatus.
func TestSaveAndLoadPairStatus(t *testing.T) {
	tempMetaDir := t.TempDir()
	defer os.RemoveAll(tempMetaDir)

	logger := NewMockLogger(t)
	mm, err := meta.NewMetaManager(tempMetaDir, logger)
	if err != nil {
		t.Fatalf("NewMetaManager failed: %v", err)
	}

	pairName := "test-peer-1"
	queueDBFile := filepath.Join(tempMetaDir, "test-peer-1.db")
	originalStatus := types.NewPairStatus(pairName, queueDBFile)
	originalStatus.LastSyncedTimestamp = time.Now().Add(-time.Hour).UTC()
	originalStatus.InitialSyncStatus = types.IN_PROGRESS
	originalStatus.ConnectionRetries = 3
	originalStatus.TotalSent = types.SyncOpCounts{Writes: 5, Bytes: 1024}
	originalStatus.TotalReceived = types.SyncOpCounts{Creates: 2, Bytes: 500}

	// 1. Test SavePairStatus
	err = mm.SavePairStatus(originalStatus)
	if err != nil {
		t.Fatalf("SavePairStatus failed: %v", err)
	}

	filePath := filepath.Join(tempMetaDir, pairName+"_status.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Errorf("PairStatus file was not created at %q", filePath)
	}

	// 2. Test LoadPairStatus
	loadedStatus, err := mm.LoadPairStatus(pairName)
	if err != nil {
		t.Fatalf("LoadPairStatus failed: %v", err)
	}

	// Assertions for loaded status
	if loadedStatus.Name != originalStatus.Name {
		t.Errorf("Name mismatch: got %q, want %q", loadedStatus.Name, originalStatus.Name)
	}
	if !loadedStatus.LastSyncedTimestamp.Equal(originalStatus.LastSyncedTimestamp) {
		t.Errorf("LastSyncedTimestamp mismatch: got %v, want %v", loadedStatus.LastSyncedTimestamp, originalStatus.LastSyncedTimestamp)
	}
	if loadedStatus.InitialSyncStatus != originalStatus.InitialSyncStatus {
		t.Errorf("InitialSyncStatus mismatch: got %q, want %q", loadedStatus.InitialSyncStatus, originalStatus.InitialSyncStatus)
	}
	if loadedStatus.CurrentQueueDBFile != originalStatus.CurrentQueueDBFile {
		t.Errorf("CurrentQueueDBFile mismatch: got %q, want %q", loadedStatus.CurrentQueueDBFile, originalStatus.CurrentQueueDBFile)
	}
	if loadedStatus.ConnectionRetries != originalStatus.ConnectionRetries {
		t.Errorf("ConnectionRetries mismatch: got %d, want %d", loadedStatus.ConnectionRetries, originalStatus.ConnectionRetries)
	}
	if loadedStatus.TotalSent.Writes != originalStatus.TotalSent.Writes || loadedStatus.TotalSent.Bytes != originalStatus.TotalSent.Bytes {
		t.Errorf("TotalSent mismatch: got %+v, want %+v", loadedStatus.TotalSent, originalStatus.TotalSent)
	}
	if loadedStatus.TotalReceived.Creates != originalStatus.TotalReceived.Creates || loadedStatus.TotalReceived.Bytes != originalStatus.TotalReceived.Bytes {
		t.Errorf("TotalReceived mismatch: got %+v, want %+v", loadedStatus.TotalReceived, originalStatus.TotalReceived)
	}
}

// TestSavePairStatus_AtomicWrite tests the atomic write behavior.
func TestSavePairStatus_AtomicWrite(t *testing.T) {
	tempMetaDir := t.TempDir()
	defer os.RemoveAll(tempMetaDir)

	logger := NewMockLogger(t)
	mm, err := meta.NewMetaManager(tempMetaDir, logger)
	if err != nil {
		t.Fatalf("NewMetaManager failed: %v", err)
	}

	pairName := "atomic-test-peer"
	filePath := filepath.Join(tempMetaDir, pairName+"_status.json")
	tempFilePath := filePath + ".tmp"

	// Simulate a crash during write: create a partial temp file
	os.WriteFile(tempFilePath, []byte("invalid json"), 0644)

	originalStatus := types.NewPairStatus(pairName, filepath.Join(tempMetaDir, "atomic.db"))
	originalStatus.InitialSyncStatus = types.COMPLETED

	err = mm.SavePairStatus(originalStatus)
	if err != nil {
		t.Fatalf("SavePairStatus failed during atomic write test: %v", err)
	}

	// Verify the temp file is gone
	if _, err := os.Stat(tempFilePath); !os.IsNotExist(err) {
		t.Errorf("Temporary file %q still exists after atomic write", tempFilePath)
	}

	// Verify the final file exists and contains valid data
	loadedStatus, err := mm.LoadPairStatus(pairName)
	if err != nil {
		t.Fatalf("LoadPairStatus failed after atomic write: %v", err)
	}
	if loadedStatus.InitialSyncStatus != types.COMPLETED {
		t.Errorf("Loaded status not correct after atomic write: got %q, want %q", loadedStatus.InitialSyncStatus, types.COMPLETED)
	}
}

// TestLoadPairStatus_NonExistent tests loading a non-existent status file.
func TestLoadPairStatus_NonExistent(t *testing.T) {
	tempMetaDir := t.TempDir()
	defer os.RemoveAll(tempMetaDir)

	logger := NewMockLogger(t)
	mm, err := meta.NewMetaManager(tempMetaDir, logger)
	if err != nil {
		t.Fatalf("NewMetaManager failed: %v", err)
	}

	pairName := "non-existent-peer"
	status, err := mm.LoadPairStatus(pairName)
	if !os.IsNotExist(err) {
		t.Errorf("Expected os.ErrNotExist, got %v", err)
	}
	if status != nil {
		t.Errorf("Expected nil status, got %+v", status)
	}
	if len(logger.Infos) == 0 || !strings.Contains(logger.Infos[0], "not found. Initializing new status.") {
		t.Errorf("Expected info log about new status initialization, got: %+v", logger.Infos)
	}
}

// TestLoadPairStatus_Corrupted tests loading a corrupted status file.
func TestLoadPairStatus_Corrupted(t *testing.T) {
	tempMetaDir := t.TempDir()
	defer os.RemoveAll(tempMetaDir)

	logger := NewMockLogger(t)
	mm, err := meta.NewMetaManager(tempMetaDir, logger)
	if err != nil {
		t.Fatalf("NewMetaManager failed: %v", err)
	}

	pairName := "corrupted-peer"
	filePath := filepath.Join(tempMetaDir, pairName+"_status.json")

	// Create a corrupted file
	err = os.WriteFile(filePath, []byte("{invalid json"), 0644)
	if err != nil {
		t.Fatalf("Failed to create corrupted file: %v", err)
	}

	status, err := mm.LoadPairStatus(pairName)
	if err == nil {
		t.Errorf("Expected an error for corrupted file, got nil")
	}
	if status != nil {
		t.Errorf("Expected nil status, got %+v", status)
	}
	if !strings.Contains(err.Error(), "failed to unmarshal PairStatus") {
		t.Errorf("Expected unmarshal error, got: %v", err)
	}

	// Verify the corrupted file was removed
	if _, err := os.Stat(filePath); !os.IsNotExist(err) {
		t.Errorf("Corrupted file %q was not removed after recovery attempt", filePath)
	}

	if len(logger.Errors) == 0 || !strings.Contains(logger.Errors[0], "Failed to unmarshal PairStatus") {
		t.Errorf("Expected error log about unmarshal failure, got: %+v", logger.Errors)
	}
}

// TestDeletePairStatus tests deleting a PairStatus file.
func TestDeletePairStatus(t *testing.T) {
	tempMetaDir := t.TempDir()
	defer os.RemoveAll(tempMetaDir)

	logger := NewMockLogger(t)
	mm, err := meta.NewMetaManager(tempMetaDir, logger)
	if err != nil {
		t.Fatalf("NewMetaManager failed: %v", err)
	}

	pairName := "delete-test-peer"
	queueDBFile := filepath.Join(tempMetaDir, "delete.db")
	originalStatus := types.NewPairStatus(pairName, queueDBFile)

	// Save a status first
	err = mm.SavePairStatus(originalStatus)
	if err != nil {
		t.Fatalf("SavePairStatus failed: %v", err)
	}

	filePath := filepath.Join(tempMetaDir, pairName+"_status.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Fatalf("File %q does not exist before deletion test", filePath)
	}

	// Test DeletePairStatus
	err = mm.DeletePairStatus(pairName)
	if err != nil {
		t.Fatalf("DeletePairStatus failed: %v", err)
	}

	// Verify file is gone
	if _, err := os.Stat(filePath); !os.IsNotExist(err) {
		t.Errorf("PairStatus file %q was not deleted", filePath)
	}

	// Test deleting a non-existent file (should not error)
	err = mm.DeletePairStatus("non-existent-to-delete")
	if err != nil {
		t.Errorf("DeletePairStatus failed for non-existent file: %v", err)
	}
	if len(logger.Infos) < 2 || !strings.Contains(logger.Infos[1], "no need to delete") {
		t.Errorf("Expected info log about non-existent file for deletion, got: %+v", logger.Infos)
	}
}

// TestLoadPairStatus_MismatchedName tests loading a file where internal name doesn't match requested name.
func TestLoadPairStatus_MismatchedName(t *testing.T) {
	tempMetaDir := t.TempDir()
	defer os.RemoveAll(tempMetaDir)

	logger := NewMockLogger(t)
	mm, err := meta.NewMetaManager(tempMetaDir, logger)
	if err != nil {
		t.Fatalf("NewMetaManager failed: %v", err)
	}

	correctPairName := "correct-name"
	mismatchedInternalName := "wrong-name"
	filePath := filepath.Join(tempMetaDir, correctPairName+"_status.json")

	// Create a status with a mismatched internal name
	statusWithMismatch := types.NewPairStatus(mismatchedInternalName, filepath.Join(tempMetaDir, "mismatch.db"))
	jsonData, _ := json.Marshal(statusWithMismatch)
	err = os.WriteFile(filePath, jsonData, 0644)
	if err != nil {
		t.Fatalf("Failed to create file with mismatched name: %v", err)
	}

	loadedStatus, err := mm.LoadPairStatus(correctPairName)
	if err != nil {
		t.Fatalf("LoadPairStatus failed: %v", err)
	}

	if loadedStatus.Name != correctPairName {
		t.Errorf("Expected loaded status name to be %q, got %q", correctPairName, loadedStatus.Name)
	}
	if len(logger.Infos) > 0 { // Check logger for a warning
		t.Logf("Logger Infos: %v", logger.Infos)
	}
	if len(logger.Errors) > 0 { // Check logger for a warning
		t.Logf("Logger Errors: %v", logger.Errors)
	}

	if len(logger.Infos) == 0 && len(logger.Errors) == 0 && len(logger.Debugs) == 0 {
		t.Error("No log messages generated, expected a warning for mismatched name")
	} else {
		// More robust check: look for specific warning text
		foundWarning := false
		for _, msg := range logger.Warns { // Changed from Debugs to Warns for better accuracy
			if strings.Contains(msg, "has mismatched name inside") {
				foundWarning = true
				break
			}
		}
		if !foundWarning {
			t.Error("Expected warning about mismatched name, but not found in logs")
		}
	}
}

// Test MetaManager Load and Save methods
func TestMetaManagerLoadSave(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "meta_test")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	logger := NewMockLogger(t)
	mm, err := meta.NewMetaManager(tmpDir, logger)
	if err != nil {
		t.Fatalf("NewMetaManager failed: %v", err)
	}

	// Test Save with multiple statuses
	statuses := map[string]*types.PairStatus{
		"peer1": {
			Name:                "peer1",
			LastSyncedTimestamp: time.Now(),
			InitialSyncStatus:   types.PENDING,
			CurrentQueueDBFile:  filepath.Join(tmpDir, "peer1.queue.db"),
		},
		"peer2": {
			Name:                "peer2",
			LastSyncedTimestamp: time.Now(),
			InitialSyncStatus:   types.COMPLETED,
			CurrentQueueDBFile:  filepath.Join(tmpDir, "peer2.queue.db"),
		},
	}

	err = mm.Save(statuses)
	if err != nil {
		t.Errorf("Save failed: %v", err)
	}

	// Flush pending saves for testing
	err = mm.FlushPendingSaves()
	if err != nil {
		t.Errorf("FlushPendingSaves failed: %v", err)
	}

	// Test Load
	loadedStatuses, err := mm.Load()
	if err != nil {
		t.Errorf("Load failed: %v", err)
	}

	if len(loadedStatuses) != 2 {
		t.Errorf("Expected 2 statuses, got %d", len(loadedStatuses))
	}

	if loadedStatuses["peer1"].Name != "peer1" {
		t.Errorf("Expected peer1, got %s", loadedStatuses["peer1"].Name)
	}

	if loadedStatuses["peer2"].InitialSyncStatus != types.COMPLETED {
		t.Errorf("Expected COMPLETED, got %s", loadedStatuses["peer2"].InitialSyncStatus)
	}
}

// Test MetaManager queue operations
func TestMetaManagerQueueOperations(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "meta_test")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	logger := NewMockLogger(t)
	mm, err := meta.NewMetaManager(tmpDir, logger)
	if err != nil {
		t.Fatalf("NewMetaManager failed: %v", err)
	}

	peerName := "test-peer"

	// Test GetQueue
	queueInterface, err := mm.GetQueue(peerName)
	if err != nil {
		t.Errorf("GetQueue failed: %v", err)
	}
	if queueInterface == nil {
		t.Error("GetQueue returned nil")
	}

	// Cast to SQLiteQueueWrapper to test its methods
	queue, ok := queueInterface.(*meta.SQLiteQueueWrapper)
	if !ok {
		t.Fatal("GetQueue did not return a SQLiteQueueWrapper")
	}

	// Test AddEvent
	event := types.FileSyncEvent{
		Type:      types.CREATE_FILE,
		Path:      "test.txt",
		Size:      100,
		MessageID: 1,
	}

	err = queue.AddEvent(event)
	if err != nil {
		t.Errorf("AddEvent failed: %v", err)
	}

	// Test Count
	count, err := queue.Count()
	if err != nil {
		t.Errorf("Count failed: %v", err)
	}
	if count != 1 {
		t.Errorf("Expected count 1, got %d", count)
	}

	// Test GetNextEvent
	nextEvent, err := queue.GetNextEvent()
	if err != nil {
		t.Errorf("GetNextEvent failed: %v", err)
	}
	if nextEvent == nil {
		t.Error("GetNextEvent returned nil")
	}
	if nextEvent.Path != "test.txt" {
		t.Errorf("Expected path 'test.txt', got '%s'", nextEvent.Path)
	}

	// Test GetEvents (simplified for SQLite implementation)
	events, err := queue.GetEvents()
	if err != nil {
		t.Errorf("GetEvents failed: %v", err)
	}
	// For SQLite implementation, GetEvents returns empty slice for now
	// This is acceptable as it's a simplified implementation
	_ = events

	// Test RemoveEvent (simplified for SQLite implementation)
	// Note: We still have the original event in the queue (GetNextEvent doesn't remove it)
	err = queue.RemoveEvent(event.MessageID)
	if err != nil {
		t.Errorf("RemoveEvent failed: %v", err)
	}

	// Count should be 0 now (since we only had 1 item)
	count, err = queue.Count()
	if err != nil {
		t.Errorf("Count failed: %v", err)
	}
	if count != 0 {
		t.Errorf("Expected count 0 after removal, got %d", count)
	}

	// Test Clear
	// Add an event first
	err = queue.AddEvent(event)
	if err != nil {
		t.Errorf("AddEvent failed: %v", err)
	}

	err = queue.Clear()
	if err != nil {
		t.Errorf("Clear failed: %v", err)
	}

	count, err = queue.Count()
	if err != nil {
		t.Errorf("Count failed: %v", err)
	}
	if count != 0 {
		t.Errorf("Expected count 0 after clear, got %d", count)
	}
}

// Test MetaManager ClearQueue
func TestMetaManagerClearQueue(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "meta_test")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	logger := NewMockLogger(t)
	mm, err := meta.NewMetaManager(tmpDir, logger)
	if err != nil {
		t.Fatalf("NewMetaManager failed: %v", err)
	}

	peerName := "test-peer"

	// Test ClearQueue (currently a no-op)
	err = mm.ClearQueue(peerName)
	if err != nil {
		t.Errorf("ClearQueue failed: %v", err)
	}
}

// Test MetaManager Close
func TestMetaManagerClose(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "meta_test")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	logger := NewMockLogger(t)
	mm, err := meta.NewMetaManager(tmpDir, logger)
	if err != nil {
		t.Fatalf("NewMetaManager failed: %v", err)
	}

	// Get a queue to ensure it's created
	_, err = mm.GetQueue("test-peer")
	if err != nil {
		t.Errorf("GetQueue failed: %v", err)
	}

	// Test Close
	err = mm.Close()
	if err != nil {
		t.Errorf("Close failed: %v", err)
	}

	// Subsequent operations should still work (new queues will be created)
	_, err = mm.GetQueue("test-peer2")
	if err != nil {
		t.Errorf("GetQueue failed after close: %v", err)
	}
}

// Test MetaManager queue caching behavior
func TestMetaManagerQueueCaching(t *testing.T) {
	tmpDir, err := os.MkdirTemp("", "meta_test")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	logger := NewMockLogger(t)
	mm, err := meta.NewMetaManager(tmpDir, logger)
	if err != nil {
		t.Fatalf("NewMetaManager failed: %v", err)
	}
	defer mm.Close()

	peerName := "test-peer"

	// First call to GetQueue should create the queue
	queue1, err := mm.GetQueue(peerName)
	if err != nil {
		t.Errorf("First GetQueue failed: %v", err)
	}
	if queue1 == nil {
		t.Error("First GetQueue returned nil")
	}

	// Second call to GetQueue should return the same cached instance
	queue2, err := mm.GetQueue(peerName)
	if err != nil {
		t.Errorf("Second GetQueue failed: %v", err)
	}
	if queue2 == nil {
		t.Error("Second GetQueue returned nil")
	}

	// Verify they are the same instance (pointer comparison)
	if queue1 != queue2 {
		t.Error("GetQueue should return the same cached instance for the same peer")
	}

	// Test with different peer should create different queue
	queue3, err := mm.GetQueue("different-peer")
	if err != nil {
		t.Errorf("GetQueue for different peer failed: %v", err)
	}
	if queue3 == nil {
		t.Error("GetQueue for different peer returned nil")
	}

	// Should be different instance from the first peer's queue
	if queue1 == queue3 {
		t.Error("GetQueue should return different instances for different peers")
	}
}
