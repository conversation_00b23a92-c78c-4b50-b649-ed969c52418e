package meta_test

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	_ "github.com/mattn/go-sqlite3" // SQLite driver
	"github.com/real-rm/folder_sync/pkg/meta"
)

// TestNewQueueManager tests the constructor and table creation.
func TestNewQueueManager(t *testing.T) {
	tempDir := t.TempDir()
	defer os.RemoveAll(tempDir)

	dbPath := filepath.Join(tempDir, "test.db")
	peerID := "test-peer"
	logger := NewMockLogger(t)

	// Test successful creation
	qm, err := meta.NewQueueManager(dbPath, peerID, logger)
	if err != nil {
		t.Fatalf("NewQueueManager failed: %v", err)
	}
	defer qm.Close() // Ensure DB connection is closed

	// Verify DB file was created
	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		t.Errorf("SQLite DB file was not created at %q", dbPath)
	}

	// Verify table exists by trying to insert
	_, err = qm.AddItem([]byte("test data"), 0)
	if err != nil {
		t.Errorf("Failed to add item to newly created queue: %v", err)
	}

	// Test with empty dbPath
	_, err = meta.NewQueueManager("", peerID, logger)
	if err == nil {
		t.Error("Expected error for empty dbPath, got nil")
	}
	if err != nil && !strings.Contains(err.Error(), "database path cannot be empty") {
		t.Errorf("Expected 'database path cannot be empty' error, got: %v", err)
	}

	// Test with empty peerID
	_, err = meta.NewQueueManager(dbPath, "", logger)
	if err == nil {
		t.Error("Expected error for empty peerID, got nil")
	}
	if err != nil && !strings.Contains(err.Error(), "peerID cannot be empty") {
		t.Errorf("Expected 'peerID cannot be empty' error, got: %v", err)
	}

	// Test with uncreatable dbPath (e.g., permissions issue)
	uncreatablePath := "/root/uncreatable/test.db" // Assume /root is not writable by tests
	if os.Getuid() != 0 {                          // Only run if not root to avoid actual permission issues
		_, err = meta.NewQueueManager(uncreatablePath, peerID, logger)
		if err == nil {
			t.Error("Expected error for uncreatable dbPath, got nil")
		}
		if err != nil && !strings.Contains(err.Error(), "permission denied") && !strings.Contains(err.Error(), "no such file or directory") {
			t.Logf("Warning: Expected 'permission denied' or 'no such file or directory' error for uncreatable dbPath, got: %v", err)
		}
	}
}

// TestAddItem tests adding items to the queue.
func TestAddItem(t *testing.T) {
	tempDir := t.TempDir()
	defer os.RemoveAll(tempDir)
	dbPath := filepath.Join(tempDir, "test.db")
	logger := NewMockLogger(t)
	qm, err := meta.NewQueueManager(dbPath, "test-peer", logger)
	if err != nil {
		t.Fatalf("NewQueueManager failed: %v", err)
	}
	defer qm.Close()

	data1 := []byte("first item data")
	id1, err := qm.AddItem(data1, 10)
	if err != nil {
		t.Fatalf("AddItem failed for first item: %v", err)
	}
	if id1 == 0 {
		t.Error("Expected non-zero ID for first item, got 0")
	}

	data2 := []byte("second item data")
	id2, err := qm.AddItem(data2, 5)
	if err != nil {
		t.Fatalf("AddItem failed for second item: %v", err)
	}
	if id2 == 0 {
		t.Error("Expected non-zero ID for second item, got 0")
	}
	if id2 <= id1 {
		t.Errorf("Expected second ID (%d) to be greater than first ID (%d)", id2, id1)
	}

	size, err := qm.GetQueueSize()
	if err != nil {
		t.Fatalf("GetQueueSize failed: %v", err)
	}
	if size != 2 {
		t.Errorf("Expected queue size 2, got %d", size)
	}
}

// TestGetNextItem tests retrieving the next item from the queue.
func TestGetNextItem(t *testing.T) {
	tempDir := t.TempDir()
	defer os.RemoveAll(tempDir)
	dbPath := filepath.Join(tempDir, "test.db")
	logger := NewMockLogger(t)
	qm, err := meta.NewQueueManager(dbPath, "test-peer", logger)
	if err != nil {
		t.Fatalf("NewQueueManager failed: %v", err)
	}
	defer qm.Close()

	// Add items with different priorities and timestamps
	qm.AddItem([]byte("data_low_prio_first_time"), 1)
	time.Sleep(1 * time.Millisecond) // Ensure distinct timestamp
	qm.AddItem([]byte("data_high_prio_second_time"), 10)
	time.Sleep(1 * time.Millisecond)
	qm.AddItem([]byte("data_medium_prio_third_time"), 5)
	time.Sleep(1 * time.Millisecond)
	qm.AddItem([]byte("data_higher_prio_fourth_time"), 15) // Fourth timestamp, highest priority

	// Let's make sure the order is correct:
	// Order by timestamp ASC, then priority DESC
	// Expected order:
	// 1. data_low_prio_first_time (ts1, prio 1)
	// 2. data_high_prio_second_time (ts2, prio 10)
	// 3. data_medium_prio_third_time (ts3, prio 5)
	// 4. data_higher_prio_fourth_time (ts4, prio 15)

	item, err := qm.GetNextItem()
	if err != nil {
		t.Fatalf("GetNextItem failed: %v", err)
	}
	if item == nil {
		t.Fatal("Expected item, got nil")
	}
	if string(item.Data) != "data_low_prio_first_time" {
		t.Errorf("Expected first item, got %q", string(item.Data))
	}
	qm.RemoveItem(item.ID) // Remove it to get the next one

	item, err = qm.GetNextItem()
	if err != nil {
		t.Fatalf("GetNextItem failed: %v", err)
	}
	if item == nil {
		t.Fatal("Expected item, got nil")
	}
	if string(item.Data) != "data_high_prio_second_time" {
		t.Errorf("Expected second item, got %q", string(item.Data))
	}
	qm.RemoveItem(item.ID)

	item, err = qm.GetNextItem()
	if err != nil {
		t.Fatalf("GetNextItem failed: %v", err)
	}
	if item == nil {
		t.Fatal("Expected item, got nil")
	}
	if string(item.Data) != "data_medium_prio_third_time" {
		t.Errorf("Expected third item, got %q", string(item.Data))
	}
	qm.RemoveItem(item.ID)

	item, err = qm.GetNextItem()
	if err != nil {
		t.Fatalf("GetNextItem failed: %v", err)
	}
	if item == nil {
		t.Fatal("Expected item, got nil")
	}
	if string(item.Data) != "data_higher_prio_fourth_time" {
		t.Errorf("Expected fourth item, got %q", string(item.Data))
	}
	qm.RemoveItem(item.ID)

	// Test getting from empty queue
	item, err = qm.GetNextItem()
	if err != nil {
		t.Fatalf("GetNextItem failed on empty queue: %v", err)
	}
	if item != nil {
		t.Errorf("Expected nil from empty queue, got %+v", item)
	}
}

// TestRemoveItem tests removing items from the queue.
func TestRemoveItem(t *testing.T) {
	tempDir := t.TempDir()
	defer os.RemoveAll(tempDir)
	dbPath := filepath.Join(tempDir, "test.db")
	logger := NewMockLogger(t)
	qm, err := meta.NewQueueManager(dbPath, "test-peer", logger)
	if err != nil {
		t.Fatalf("NewQueueManager failed: %v", err)
	}
	defer qm.Close()

	id1, _ := qm.AddItem([]byte("item1"), 0)
	id2, _ := qm.AddItem([]byte("item2"), 0)
	id3, _ := qm.AddItem([]byte("item3"), 0)

	// Remove middle item
	err = qm.RemoveItem(id2)
	if err != nil {
		t.Fatalf("RemoveItem failed: %v", err)
	}

	size, _ := qm.GetQueueSize()
	if size != 2 {
		t.Errorf("Expected size 2 after removing one item, got %d", size)
	}

	// Verify remaining items
	item := assertGetNextItem(t, qm)
	if item == nil || item.ID != id1 {
		t.Errorf("Expected item with ID %d, got %v", id1, item)
	}
	qm.RemoveItem(item.ID) // Remove first

	item = assertGetNextItem(t, qm)
	if item == nil || item.ID != id3 {
		t.Errorf("Expected item with ID %d, got %v", id3, item)
	}
	qm.RemoveItem(item.ID) // Remove last

	size, _ = qm.GetQueueSize()
	if size != 0 {
		t.Errorf("Expected size 0 after removing all items, got %d", size)
	}

	// Try removing non-existent item
	err = qm.RemoveItem(999)
	if err != nil {
		t.Errorf("RemoveItem failed for non-existent item: %v", err)
	}
	if len(logger.Warns) == 0 || !strings.Contains(logger.Warns[0], "non-existent queue item ID 999") {
		t.Errorf("Expected warning for removing non-existent item, got: %+v", logger.Warns)
	}
}

// TestQueuePersistence tests that queue data survives manager restarts.
func TestQueuePersistence(t *testing.T) {
	tempDir := t.TempDir()
	defer os.RemoveAll(tempDir)
	dbPath := filepath.Join(tempDir, "persistent.db")
	logger := NewMockLogger(t)

	// 1. Create and add items
	qm1, err := meta.NewQueueManager(dbPath, "test-peer-persist", logger)
	if err != nil {
		t.Fatalf("NewQueueManager failed (1): %v", err)
	}
	id1, _ := qm1.AddItem([]byte("item A"), 0)
	id2, _ := qm1.AddItem([]byte("item B"), 0)
	id3, _ := qm1.AddItem([]byte("item C"), 0)
	qm1.Close() // Close the first manager

	// 2. Re-open with a new manager instance
	qm2, err := meta.NewQueueManager(dbPath, "test-peer-persist", logger)
	if err != nil {
		t.Fatalf("NewQueueManager failed (2): %v", err)
	}
	defer qm2.Close()

	// 3. Verify items are still there and in order
	item := assertGetNextItem(t, qm2)
	if item == nil || item.ID != id1 || string(item.Data) != "item A" {
		t.Errorf("Expected item A, got %v", item)
	}
	qm2.RemoveItem(item.ID)

	item = assertGetNextItem(t, qm2)
	if item == nil || item.ID != id2 || string(item.Data) != "item B" {
		t.Errorf("Expected item B, got %v", item)
	}
	qm2.RemoveItem(item.ID)

	item = assertGetNextItem(t, qm2)
	if item == nil || item.ID != id3 || string(item.Data) != "item C" {
		t.Errorf("Expected item C, got %v", item)
	}
	qm2.RemoveItem(item.ID)

	size, _ := qm2.GetQueueSize()
	if size != 0 {
		t.Errorf("Expected size 0 after consuming all items, got %d", size)
	}
}

// TestCloseQueueManager tests closing the queue manager.
func TestCloseQueueManager(t *testing.T) {
	tempDir := t.TempDir()
	defer os.RemoveAll(tempDir)
	dbPath := filepath.Join(tempDir, "test.db")
	logger := NewMockLogger(t)
	qm, err := meta.NewQueueManager(dbPath, "test-peer", logger)
	if err != nil {
		t.Fatalf("NewQueueManager failed: %v", err)
	}

	err = qm.Close()
	if err != nil {
		t.Errorf("Close failed: %v", err)
	}

	// Try to use after close, should error
	_, err = qm.AddItem([]byte("after close"), 0)
	if err == nil {
		t.Error("Expected error when adding to closed queue, got nil")
	}
	if err != nil && !strings.Contains(err.Error(), "database is closed") {
		t.Errorf("Expected 'database is closed' error, got: %v", err)
	}

	// Try to close again, should not error (or gracefully handle)
	err = qm.Close()
	if err != nil {
		t.Errorf("Close failed on second attempt: %v", err)
	}
}

// assertGetNextItem is a helper to get the next item or fail the test.
func assertGetNextItem(t *testing.T, qm *meta.QueueManager) *meta.QueueItem {
	t.Helper()
	item, err := qm.GetNextItem()
	if err != nil {
		t.Fatalf("GetNextItem failed: %v", err)
	}
	if item == nil {
		t.Fatalf("Expected an item, but queue is empty")
	}
	return item
}
