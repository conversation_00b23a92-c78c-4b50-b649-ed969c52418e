package meta

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	_ "github.com/mattn/go-sqlite3" // SQLite driver
	"github.com/real-rm/folder_sync/pkg/types"
)

// QueueItem represents a single item in the synchronization queue.
// This could be a serialized FileSyncEvent or other command.
type QueueItem struct {
	ID        int64     // Unique ID for the queue item (primary key in DB)
	Timestamp time.Time // When the item was added to the queue
	Data      []byte    // Serialized data of the event/command
	Priority  int       // Priority of the item (e.g., 0 for normal, higher for urgent)
}

// QueueManager handles a peer-specific SQLite queue for change events.
type QueueManager struct {
	dbPath string     // Path to the SQLite database file
	db     *sql.DB    // Database connection
	mu     sync.Mutex // Mutex to protect database operations
	logger Logger     // Logger instance
	peerID string     // The ID of the peer this queue belongs to
}

// NewQueueManager creates and initializes a new QueueManager for a given peer.
func NewQueueManager(dbPath string, peerID string, logger Logger) (*QueueManager, error) {
	if dbPath == "" {
		return nil, fmt.Errorf("database path cannot be empty")
	}
	if peerID == "" {
		return nil, fmt.Errorf("peerID cannot be empty")
	}

	// Open SQLite with optimized settings for production
	db, err := sql.Open("sqlite3", dbPath+"?_journal_mode=WAL&_synchronous=NORMAL&_cache_size=10000&_foreign_keys=ON")
	if err != nil {
		return nil, fmt.Errorf("failed to open SQLite database at %q: %w", dbPath, err)
	}

	// Set connection pool limits
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(time.Hour)

	qm := &QueueManager{
		dbPath: dbPath,
		db:     db,
		logger: logger,
		peerID: peerID,
	}

	if err := qm.createTable(); err != nil {
		db.Close() // Close DB if table creation fails
		return nil, fmt.Errorf("failed to create queue table: %w", err)
	}

	qm.logger.InfoP(peerID, "Initialized queue manager for %q", dbPath)
	return qm, nil
}

// createTable creates the 'queue_items' table if it doesn't exist.
func (qm *QueueManager) createTable() error {
	qm.mu.Lock()
	defer qm.mu.Unlock()

	const createTableSQL = `
	CREATE TABLE IF NOT EXISTS queue_items (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		timestamp INTEGER NOT NULL,
		data BLOB NOT NULL,
		priority INTEGER NOT NULL
	);`

	_, err := qm.db.Exec(createTableSQL)
	if err != nil {
		return fmt.Errorf("error creating queue_items table: %w", err)
	}
	return nil
}

// AddItem adds a new item to the queue.
func (qm *QueueManager) AddItem(data []byte, priority int) (int64, error) {
	qm.mu.Lock()
	defer qm.mu.Unlock()

	stmt, err := qm.db.Prepare("INSERT INTO queue_items(timestamp, data, priority) VALUES(?, ?, ?)")
	if err != nil {
		return 0, fmt.Errorf("failed to prepare insert statement: %w", err)
	}
	defer stmt.Close()

	res, err := stmt.Exec(time.Now().UnixNano(), data, priority)
	if err != nil {
		return 0, fmt.Errorf("failed to insert queue item: %w", err)
	}

	id, err := res.LastInsertId()
	if err != nil {
		return 0, fmt.Errorf("failed to get last insert ID: %w", err)
	}
	// Try to decode the data to get file path and event type for better logging
	var eventInfo string
	if len(data) > 0 {
		// Try to extract file path and event type from JSON data for debugging
		var event map[string]interface{}
		if err := json.Unmarshal(data, &event); err == nil {
			// JSON field names are capitalized since the struct doesn't have json tags
			if path, ok := event["Path"].(string); ok {
				if eventType, ok := event["Type"].(float64); ok {
					// Convert numeric event type to string representation
					eventTypeStr := types.FileSyncEventType(eventType).String()
					eventInfo = fmt.Sprintf(" (file: %s, type: %s)", path, eventTypeStr)
				} else {
					eventInfo = fmt.Sprintf(" (file: %s)", path)
				}
			}
		}
	}
	qm.logger.DebugP(qm.peerID, "Added queue item with ID %d, priority %d, size %d bytes%s", id, priority, len(data), eventInfo)
	return id, nil
}

// GetNextItem retrieves the next item from the queue, ordered by timestamp and then priority.
// It does not remove the item from the queue.
func (qm *QueueManager) GetNextItem() (*QueueItem, error) {
	qm.mu.Lock()
	defer qm.mu.Unlock()

	row := qm.db.QueryRow("SELECT id, timestamp, data, priority FROM queue_items ORDER BY timestamp ASC, priority DESC LIMIT 1")

	item := &QueueItem{}
	var timestamp int64
	err := row.Scan(&item.ID, &timestamp, &item.Data, &item.Priority)
	if err == sql.ErrNoRows {
		return nil, nil // No items in queue
	}
	if err != nil {
		return nil, fmt.Errorf("failed to scan next queue item: %w", err)
	}
	item.Timestamp = time.Unix(0, int64(timestamp)) // Cast to int64 for time.Unix

	// Try to decode the data to get file path and event type for better logging
	var eventInfo string
	if len(item.Data) > 0 {
		var event map[string]interface{}
		if err := json.Unmarshal(item.Data, &event); err == nil {
			if path, ok := event["Path"].(string); ok {
				if eventType, ok := event["Type"].(float64); ok {
					eventTypeStr := types.FileSyncEventType(eventType).String()
					eventInfo = fmt.Sprintf(" (file: %s, type: %s)", path, eventTypeStr)
				} else {
					eventInfo = fmt.Sprintf(" (file: %s)", path)
				}
			}
		}
	}

	qm.logger.DebugP(qm.peerID, "Retrieved next queue item ID %d%s", item.ID, eventInfo)
	return item, nil
}

// RemoveItem removes a specific item from the queue by its ID.
func (qm *QueueManager) RemoveItem(id int64) error {
	qm.mu.Lock()
	defer qm.mu.Unlock()

	res, err := qm.db.Exec("DELETE FROM queue_items WHERE id = ?", id)
	if err != nil {
		return fmt.Errorf("failed to delete queue item %d: %w", id, err)
	}

	rowsAffected, err := res.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected for delete item %d: %w", id, err)
	}
	if rowsAffected == 0 {
		qm.logger.WarnP(qm.peerID, "Attempted to remove non-existent queue item ID %d", id)
	} else {
		qm.logger.DebugP(qm.peerID, "Removed queue item ID %d", id)
	}

	return nil
}

// RemoveByMessageID removes an item from the queue by searching for a specific MessageID in the event data
func (qm *QueueManager) RemoveByMessageID(messageID uint64) error {
	qm.mu.Lock()
	defer qm.mu.Unlock()

	// Query all items and check their MessageID
	rows, err := qm.db.Query("SELECT id, data FROM queue_items ORDER BY timestamp ASC, priority DESC")
	if err != nil {
		return fmt.Errorf("failed to query queue items: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var id int64
		var data []byte
		if err := rows.Scan(&id, &data); err != nil {
			qm.logger.WarnP(qm.peerID, "Failed to scan queue item, skipping: %v", err)
			continue // Skip malformed rows
		}

		// Try to parse the event data to get MessageID
		var event map[string]interface{}
		if err := json.Unmarshal(data, &event); err != nil {
			qm.logger.WarnP(qm.peerID, "Failed to unmarshal queue item data, skipping: %v", err)
			continue // Skip malformed events
		}

		// Check if this event has the matching MessageID
		if msgID, ok := event["MessageID"].(float64); ok && uint64(msgID) == messageID {
			// Found the matching event, remove it
			res, err := qm.db.Exec("DELETE FROM queue_items WHERE id = ?", id)
			if err != nil {
				return fmt.Errorf("failed to delete queue item %d: %w", id, err)
			}

			rowsAffected, err := res.RowsAffected()
			if err != nil {
				return fmt.Errorf("failed to get rows affected for delete item %d: %w", id, err)
			}
			if rowsAffected > 0 {
				qm.logger.DebugP(qm.peerID, "Removed queue item ID %d with MessageID %d", id, messageID)
			} else {
				qm.logger.WarnP(qm.peerID, "Queue item ID %d with MessageID %d was already removed", id, messageID)
			}
			return nil
		}
	}

	// Check if there was an error during iteration
	if err := rows.Err(); err != nil {
		return fmt.Errorf("error during queue iteration: %w", err)
	}

	return fmt.Errorf("event with MessageID %d not found", messageID)
}

// GetEventByMessageID retrieves an event by MessageID without removing it
func (qm *QueueManager) GetEventByMessageID(messageID uint64) (*types.FileSyncEvent, error) {
	qm.mu.Lock()
	defer qm.mu.Unlock()

	// Query all items and check their MessageID
	rows, err := qm.db.Query("SELECT id, data FROM queue_items ORDER BY timestamp ASC, priority DESC")
	if err != nil {
		return nil, fmt.Errorf("failed to query queue items: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var id int64
		var data []byte
		if err := rows.Scan(&id, &data); err != nil {
			qm.logger.WarnP(qm.peerID, "Failed to scan queue item, skipping: %v", err)
			continue // Skip malformed rows
		}

		// Try to parse the event data to get MessageID
		var event types.FileSyncEvent
		if err := json.Unmarshal(data, &event); err != nil {
			qm.logger.WarnP(qm.peerID, "Failed to unmarshal queue item data, skipping: %v", err)
			continue // Skip malformed events
		}

		// Check if this event has the matching MessageID
		if event.MessageID == messageID {
			return &event, nil
		}
	}

	// Check if there was an error during iteration
	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error during queue iteration: %w", err)
	}

	return nil, fmt.Errorf("event with MessageID %d not found", messageID)
}

// GetQueueSize returns the current number of items in the queue.
func (qm *QueueManager) GetQueueSize() (int, error) {
	qm.mu.Lock()
	defer qm.mu.Unlock()

	var count int
	err := qm.db.QueryRow("SELECT COUNT(*) FROM queue_items").Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to get queue size: %w", err)
	}
	return count, nil
}

// Close closes the database connection. This should be called during graceful shutdown.
func (qm *QueueManager) Close() error {
	qm.mu.Lock()
	defer qm.mu.Unlock()

	if qm.db != nil {
		err := qm.db.Close()
		if err != nil {
			qm.logger.ErrorP(qm.peerID, "Failed to close queue database %q: %v", qm.dbPath, err)
			return fmt.Errorf("failed to close queue database: %w", err)
		}
		qm.logger.InfoP(qm.peerID, "Closed queue database %q", qm.dbPath)
	}
	return nil
}
