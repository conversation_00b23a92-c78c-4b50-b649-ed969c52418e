package meta

import (
	"encoding/json"
	"fmt"
	"os" // Changed from "io/ioutil"
	"path/filepath"
	"strings" // Added strings import
	"sync"
	"time"

	"github.com/real-rm/folder_sync/pkg/types"
)

// MetaManager handles the persistence and retrieval of PairStatus objects.
type MetaManager struct {
	metaPath string       // Base directory for meta files
	mu       sync.RWMutex // Mutex to protect meta file operations
	logger   Logger       // Logger instance

	// Periodic saving fields
	pendingStatuses map[string]*types.PairStatus // Statuses pending save
	lastSaveTime    time.Time                    // Last time statuses were saved
	saveMu          sync.Mutex                   // Mutex for save operations
	saveInterval    time.Duration                // Interval between saves (default 5 seconds)
	done            chan struct{}                // Channel to signal shutdown
	saveTimer       *time.Timer                  // Timer for periodic saves

	// Queue caching fields
	queueCache map[string]interface{} // Cache of queue instances by peer name
	queueMu    sync.RWMutex           // Mutex to protect queue cache
}

// Logger interface for MetaManager
type Logger interface {
	ErrorP(peerID string, format string, args ...interface{})
	InfoP(peerID string, format string, args ...interface{})
	DebugP(peerID string, format string, args ...interface{})
	WarnP(peerID string, format string, args ...interface{}) // Added WarnP
	Fatal(format string, args ...interface{})
	Error(format string, args ...interface{})
	Info(format string, args ...interface{})
	Debug(format string, args ...interface{})
	Warn(format string, args ...interface{}) // Added Warn
}

// NewMetaManager creates a new MetaManager instance.
// metaPath is the directory where PairStatus files will be stored.
func NewMetaManager(metaPath string, logger Logger) (*MetaManager, error) {
	if metaPath == "" {
		return nil, fmt.Errorf("metaPath cannot be empty")
	}
	if err := os.MkdirAll(metaPath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create meta directory %q: %w", metaPath, err)
	}

	mm := &MetaManager{
		metaPath:        metaPath,
		logger:          logger,
		pendingStatuses: make(map[string]*types.PairStatus),
		saveInterval:    types.DefaultStatusSaveIntervalSeconds * time.Second,
		done:            make(chan struct{}),
		queueCache:      make(map[string]interface{}),
	}

	// Start the periodic save goroutine
	go mm.periodicSaveLoop()

	return mm, nil
}

// getPairStatusFilePath returns the full path for a PairStatus file.
func (mm *MetaManager) getPairStatusFilePath(pairName string) string {
	// Sanitize the pair name to be safe for filenames
	safePairName := sanitizeFilename(pairName)
	return filepath.Join(mm.metaPath, fmt.Sprintf("%s_status.json", safePairName))
}

// SavePairStatus atomically saves the PairStatus for a given peer.
// It writes to a temporary file and then renames it, preventing corruption.
func (mm *MetaManager) SavePairStatus(status *types.PairStatus) error {
	mm.mu.Lock()
	defer mm.mu.Unlock()

	if status == nil {
		return fmt.Errorf("cannot save nil PairStatus")
	}

	filePath := mm.getPairStatusFilePath(status.Name)
	tempFilePath := filePath + ".tmp"

	jsonData, err := json.MarshalIndent(status, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal PairStatus for %q: %w", status.Name, err)
	}

	// Write to a temporary file
	if err := os.WriteFile(tempFilePath, jsonData, 0644); err != nil { // Changed from ioutil.WriteFile
		return fmt.Errorf("failed to write temporary PairStatus file for %q: %w", status.Name, err)
	}

	// Atomically rename the temporary file to the final destination
	if err := os.Rename(tempFilePath, filePath); err != nil {
		return fmt.Errorf("failed to rename temporary PairStatus file for %q: %w", status.Name, err)
	}

	mm.logger.DebugP(status.Name, "Successfully saved PairStatus to %q", filePath)
	return nil
}

// LoadPairStatus loads the PairStatus for a given peer.
// It includes logic to handle corrupted or non-existent files by re-initializing.
func (mm *MetaManager) LoadPairStatus(pairName string) (*types.PairStatus, error) {
	mm.mu.RLock() // Use RLock for read operation
	defer mm.mu.RUnlock()

	filePath := mm.getPairStatusFilePath(pairName)

	jsonData, err := os.ReadFile(filePath) // Changed from ioutil.ReadFile
	if err != nil {
		if os.IsNotExist(err) {
			mm.logger.InfoP(pairName, "PairStatus file %q not found. Initializing new status.", filePath)
			return nil, os.ErrNotExist // Indicate that the file does not exist
		}
		// If file exists but cannot be read (e.g., permissions, corruption)
		mm.logger.ErrorP(pairName, "Failed to read PairStatus file %q: %v. Attempting recovery (will re-initialize).", filePath, err)
		if rErr := os.Remove(filePath); rErr != nil {
			mm.logger.ErrorP(pairName, "Failed to remove corrupted PairStatus file %q during recovery: %v", filePath, rErr)
		}
		return nil, fmt.Errorf("corrupted or unreadable PairStatus file %q: %w", filePath, err)
	}

	status := &types.PairStatus{}
	if err := json.Unmarshal(jsonData, status); err != nil {
		mm.logger.ErrorP(pairName, "Failed to unmarshal PairStatus from %q: %v. Attempting recovery (will re-initialize).", filePath, err)
		if rErr := os.Remove(filePath); rErr != nil {
			mm.logger.ErrorP(pairName, "Failed to remove unmarshalable PairStatus file %q during recovery: %v", filePath, rErr)
		}
		return nil, fmt.Errorf("failed to unmarshal PairStatus from %q: %w", filePath, err)
	}

	// Ensure the loaded status name matches the requested name
	if status.Name != pairName {
		mm.logger.WarnP(pairName, "PairStatus file %q has mismatched name inside: %q. Using requested name.", filePath, status.Name)
		status.Name = pairName
	}

	mm.logger.DebugP(pairName, "Successfully loaded PairStatus from %q", filePath)
	return status, nil
}

// DeletePairStatus deletes the PairStatus file for a given peer.
func (mm *MetaManager) DeletePairStatus(pairName string) error {
	mm.mu.Lock()
	defer mm.mu.Unlock()

	filePath := mm.getPairStatusFilePath(pairName)
	if err := os.Remove(filePath); err != nil {
		if os.IsNotExist(err) {
			mm.logger.InfoP(pairName, "PairStatus file %q does not exist, no need to delete.", filePath)
			return nil
		}
		return fmt.Errorf("failed to delete PairStatus file %q: %w", filePath, err)
	}
	mm.logger.InfoP(pairName, "Successfully deleted PairStatus file %q", filePath)
	return nil
}

// Load loads all PairStatus files from the meta directory.
func (mm *MetaManager) Load() (map[string]*types.PairStatus, error) {
	mm.mu.RLock()
	defer mm.mu.RUnlock()

	statuses := make(map[string]*types.PairStatus)

	// Read all files in the meta directory
	files, err := os.ReadDir(mm.metaPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read meta directory %q: %w", mm.metaPath, err)
	}

	for _, file := range files {
		if file.IsDir() || !strings.HasSuffix(file.Name(), "_status.json") {
			continue
		}

		// Extract peer name from filename
		peerName := strings.TrimSuffix(file.Name(), "_status.json")
		peerName = strings.ReplaceAll(peerName, "_", ".") // Reverse sanitization

		status, err := mm.LoadPairStatus(peerName)
		if err != nil {
			if os.IsNotExist(err) {
				continue // Skip non-existent files
			}
			mm.logger.Warn("Failed to load status for peer %q: %v", peerName, err)
			continue
		}
		statuses[peerName] = status
	}

	return statuses, nil
}

// periodicSaveLoop runs in a goroutine and saves pending statuses every 5 seconds
func (mm *MetaManager) periodicSaveLoop() {
	for {
		select {
		case <-mm.done:
			// Perform final save before shutdown
			mm.flushPendingStatuses()
			return
		case <-time.After(mm.saveInterval):
			mm.flushPendingStatuses()
		}
	}
}

// flushPendingStatuses saves all pending statuses to disk
func (mm *MetaManager) flushPendingStatuses() {
	mm.saveMu.Lock()
	defer mm.saveMu.Unlock()

	if len(mm.pendingStatuses) == 0 {
		return
	}

	// Copy pending statuses and clear the map
	statusesToSave := make(map[string]*types.PairStatus)
	for name, status := range mm.pendingStatuses {
		statusesToSave[name] = status
	}
	mm.pendingStatuses = make(map[string]*types.PairStatus)
	mm.lastSaveTime = time.Now()

	// Save each status (this will use the original SavePairStatus method)
	for _, status := range statusesToSave {
		if err := mm.SavePairStatus(status); err != nil {
			mm.logger.Error("Failed to save pending status for %q: %v", status.Name, err)
			// Put it back in pending if save failed
			mm.pendingStatuses[status.Name] = status
		}
	}
}

// Save saves multiple PairStatus objects using periodic batching.
func (mm *MetaManager) Save(statuses map[string]*types.PairStatus) error {
	mm.saveMu.Lock()
	defer mm.saveMu.Unlock()

	// Add statuses to pending map for periodic saving
	for name, status := range statuses {
		mm.pendingStatuses[name] = status
	}

	return nil
}

// SaveImmediately saves multiple PairStatus objects immediately (for critical operations).
func (mm *MetaManager) SaveImmediately(statuses map[string]*types.PairStatus) error {
	for _, status := range statuses {
		if err := mm.SavePairStatus(status); err != nil {
			return err
		}
	}
	return nil
}

// FlushPendingSaves forces immediate save of all pending statuses (for testing).
func (mm *MetaManager) FlushPendingSaves() error {
	mm.flushPendingStatuses()
	return nil
}

// SimpleQueue is a simple in-memory queue implementation that satisfies the MetaQueue interface
type SimpleQueue struct {
	events []types.FileSyncEvent
	dbPath string // Path where the SQLite database should be created
	mu     sync.Mutex
}

// AddEvent adds an event to the queue
func (sq *SimpleQueue) AddEvent(event types.FileSyncEvent) error {
	sq.mu.Lock()
	defer sq.mu.Unlock()
	sq.events = append(sq.events, event)
	return nil
}

// GetNextEvent returns the next event from the queue
func (sq *SimpleQueue) GetNextEvent() (*types.FileSyncEvent, error) {
	sq.mu.Lock()
	defer sq.mu.Unlock()
	if len(sq.events) == 0 {
		return nil, fmt.Errorf("queue is empty")
	}
	event := sq.events[0]
	sq.events = sq.events[1:]
	return &event, nil
}

// RemoveEvent removes an event by message ID (simplified implementation)
func (sq *SimpleQueue) RemoveEvent(messageID uint64) error {
	sq.mu.Lock()
	defer sq.mu.Unlock()
	for i, event := range sq.events {
		if event.MessageID == messageID {
			sq.events = append(sq.events[:i], sq.events[i+1:]...)
			return nil
		}
	}
	return fmt.Errorf("event with MessageID %d not found", messageID)
}

// GetEventByMessageID gets an event by message ID without removing it
func (sq *SimpleQueue) GetEventByMessageID(messageID uint64) (*types.FileSyncEvent, error) {
	sq.mu.Lock()
	defer sq.mu.Unlock()
	for _, event := range sq.events {
		if event.MessageID == messageID {
			return &event, nil
		}
	}
	return nil, fmt.Errorf("event with MessageID %d not found", messageID)
}

// GetEvents returns all events in the queue
func (sq *SimpleQueue) GetEvents() ([]types.FileSyncEvent, error) {
	sq.mu.Lock()
	defer sq.mu.Unlock()
	return append([]types.FileSyncEvent{}, sq.events...), nil
}

// Count returns the number of events in the queue
func (sq *SimpleQueue) Count() (int, error) {
	sq.mu.Lock()
	defer sq.mu.Unlock()
	return len(sq.events), nil
}

// Close closes the queue (no-op for in-memory queue)
func (sq *SimpleQueue) Close() error {
	return nil
}

// Clear removes all events from the queue
func (sq *SimpleQueue) Clear() error {
	sq.mu.Lock()
	defer sq.mu.Unlock()
	sq.events = nil
	return nil
}

// GetQueue returns a queue for the specified peer.
func (mm *MetaManager) GetQueue(peerName string) (interface{}, error) {
	// Check if queue already exists in cache
	mm.queueMu.RLock()
	if queue, exists := mm.queueCache[peerName]; exists {
		mm.queueMu.RUnlock()
		mm.logger.DebugP(peerName, "Returning cached queue for peer %q", peerName)
		return queue, nil
	}
	mm.queueMu.RUnlock()

	// Queue doesn't exist in cache, create it
	mm.queueMu.Lock()
	defer mm.queueMu.Unlock()

	// Double-check in case another goroutine created it while we were waiting for the lock
	if queue, exists := mm.queueCache[peerName]; exists {
		mm.logger.DebugP(peerName, "Returning cached queue for peer %q (created by another goroutine)", peerName)
		return queue, nil
	}

	// Create the SQLite database file path for this peer
	dbPath := filepath.Join(mm.metaPath, peerName+".queue.db")

	mm.logger.InfoP(peerName, "Creating queue database at %q", dbPath)

	// Create a placeholder file to indicate the database location
	// This helps with debugging and shows where the queue should be
	placeholderPath := dbPath + ".placeholder"
	if err := os.WriteFile(placeholderPath, []byte("Queue database placeholder for peer: "+peerName), 0644); err != nil {
		mm.logger.WarnP(peerName, "Failed to create queue placeholder file %q: %v", placeholderPath, err)
	}

	// Use the actual SQLite queue implementation
	queueManager, err := NewQueueManager(dbPath, peerName, mm.logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create queue manager for peer %q: %w", peerName, err)
	}

	// Wrap the QueueManager to provide the interface expected by tests
	queue := &SQLiteQueueWrapper{
		qm: queueManager,
	}

	// Cache the queue for future use
	mm.queueCache[peerName] = queue
	mm.logger.DebugP(peerName, "Cached queue for peer %q", peerName)

	return queue, nil
}

// Close shuts down the MetaManager and performs final save
func (mm *MetaManager) Close() error {
	close(mm.done)
	// Give some time for the goroutine to finish
	time.Sleep(100 * time.Millisecond)

	// Close all cached queues
	mm.queueMu.Lock()
	defer mm.queueMu.Unlock()

	for peerName, queueInterface := range mm.queueCache {
		if wrapper, ok := queueInterface.(*SQLiteQueueWrapper); ok {
			if err := wrapper.qm.Close(); err != nil {
				mm.logger.ErrorP(peerName, "Failed to close queue for peer %q: %v", peerName, err)
			} else {
				mm.logger.DebugP(peerName, "Closed queue for peer %q", peerName)
			}
		}
	}

	// Clear the cache
	mm.queueCache = make(map[string]interface{})

	return nil
}

// ClearQueue clears the queue for the specified peer (placeholder implementation).
func (mm *MetaManager) ClearQueue(peerName string) error {
	// This is a placeholder - in a real implementation, this would clear the peer's queue
	return nil
}

// sanitizeFilename replaces characters that are unsafe in filenames with underscores.
// This is a duplicate of the one in logger.go, but kept local for now.
// A common utils package might be needed later.
func sanitizeFilename(s string) string {
	s = strings.ReplaceAll(s, ".", "_")
	s = strings.ReplaceAll(s, ":", "_")
	s = strings.ReplaceAll(s, "/", "_")
	s = strings.ReplaceAll(s, "\\", "_")
	return s
}

// SQLiteQueueWrapper wraps QueueManager to provide the interface expected by tests
type SQLiteQueueWrapper struct {
	qm *QueueManager
}

// AddEvent adds an event to the SQLite queue
func (w *SQLiteQueueWrapper) AddEvent(event types.FileSyncEvent) error {
	// Serialize the event to JSON
	data, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to serialize event: %w", err)
	}

	// Add to queue with normal priority
	_, err = w.qm.AddItem(data, 0)
	return err
}

// GetNextEvent gets the next event from the SQLite queue without removing it
func (w *SQLiteQueueWrapper) GetNextEvent() (*types.FileSyncEvent, error) {
	item, err := w.qm.GetNextItem()
	if err != nil {
		return nil, err
	}
	if item == nil {
		return nil, fmt.Errorf("queue is empty")
	}

	// Deserialize the event
	var event types.FileSyncEvent
	if err := json.Unmarshal(item.Data, &event); err != nil {
		return nil, fmt.Errorf("failed to deserialize event: %w", err)
	}

	// Store the queue item ID in the event for later removal
	// We'll use a custom field or find another way to track this
	// For now, we'll rely on MessageID matching for removal

	return &event, nil
}

// RemoveEvent removes an event by message ID from the SQLite queue
func (w *SQLiteQueueWrapper) RemoveEvent(messageID uint64) error {
	return w.qm.RemoveByMessageID(messageID)
}

// GetEventByMessageID gets an event by message ID without removing it
func (w *SQLiteQueueWrapper) GetEventByMessageID(messageID uint64) (*types.FileSyncEvent, error) {
	return w.qm.GetEventByMessageID(messageID)
}

// Count returns the number of events in the SQLite queue
func (w *SQLiteQueueWrapper) Count() (int, error) {
	return w.qm.GetQueueSize()
}

// GetEvents returns all events in the SQLite queue (simplified implementation)
func (w *SQLiteQueueWrapper) GetEvents() ([]types.FileSyncEvent, error) {
	// This is a simplified implementation - we'll get all items one by one
	// In a full implementation, we'd add a method to QueueManager to get all items
	var events []types.FileSyncEvent

	// Get the current queue size
	size, err := w.qm.GetQueueSize()
	if err != nil {
		return nil, err
	}

	// For now, return empty slice since we don't want to consume items
	// In a real implementation, we'd need a method to peek at all items without removing them
	events = make([]types.FileSyncEvent, 0, size)
	return events, nil
}

// Clear removes all events from the SQLite queue (simplified implementation)
func (w *SQLiteQueueWrapper) Clear() error {
	// This is a simplified implementation - we'll remove items one by one
	// In a full implementation, we'd add a method to QueueManager to clear all items
	for {
		item, err := w.qm.GetNextItem()
		if err != nil {
			return err
		}
		if item == nil {
			break // No more items
		}
		if err := w.qm.RemoveItem(item.ID); err != nil {
			return err
		}
	}
	return nil
}

// Close closes the SQLite queue
func (w *SQLiteQueueWrapper) Close() error {
	return w.qm.Close()
}
