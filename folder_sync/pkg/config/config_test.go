package config_test

import (
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/types"
)

// createTempConfigFile creates a temporary YAML config file for testing.
func createTempConfigFile(t *testing.T, content string) string {
	t.Helper()
	tmpFile, err := os.CreateTemp("", "config-*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer tmpFile.Close()

	if _, err := tmpFile.WriteString(content); err != nil {
		t.Fatalf("Failed to write to temp file: %v", err)
	}
	return tmpFile.Name()
}

// TestLoadConfig_Success tests successful loading of a valid config.
func TestLoadConfig_Success(t *testing.T) {
	tempDir := t.TempDir() // Create a temporary directory for sync_path
	syncPath := filepath.Join(tempDir, "mysync")
	os.Mkdir(syncPath, 0755) // Create the sync_path directory

	cfgContent := `
folder:
  sync_path: ` + syncPath + `
  meta_path: ` + filepath.Join(tempDir, "mymeta") + `
  logs_path: ` + filepath.Join(tempDir, "mylogs") + `
  compares: 3
  compression: 1
  readonly: false
  writethrough: false
  concurrent: 50
  initial_sync_timeout_minutes: 30
  ignores:
    - pattern: /\.git/
      type: regexp
    - pattern: "*.tmp"
      type: extension
    - pattern: sensitive.data
      type: exact
server:
  name: "test-server"
  listen_address: "127.0.0.1:8080"
  auth_token: "supersecrettoken123456789012345678"
monitor:
  socket_path: "" # Not required for non-writethrough mode by default
pairs:
  - name: peer1
    host: *************
    direction: ONE_WAY_TO_PEER
    initial_sync_strategy: SYNC
    remote_port: 8081
  - name: peer2
    host: *************
    direction: BIDIRECTIONAL_NEWEST_WIN
    initial_sync_strategy: NO_INITIAL_SYNC
    remote_port: 8082
debug: true
`
	cfgPath := createTempConfigFile(t, cfgContent)
	defer os.Remove(cfgPath)

	cfg, err := config.LoadConfig(cfgPath)
	if err != nil {
		t.Fatalf("LoadConfig failed: %v", err)
	}

	// Validate the config to trigger path creation
	err = cfg.Validate()
	if err != nil {
		t.Fatalf("Config validation failed: %v", err)
	}

	// Assertions for loaded config
	if cfg.Folder.SyncPath != syncPath {
		t.Errorf("SyncPath mismatch: got %q, want %q", cfg.Folder.SyncPath, syncPath)
	}
	if cfg.Folder.MetaPath != filepath.Join(tempDir, "mymeta") {
		t.Errorf("MetaPath mismatch: got %q, want %q", cfg.Folder.MetaPath, filepath.Join(tempDir, "mymeta"))
	}
	if cfg.Folder.LogsPath != filepath.Join(tempDir, "mylogs") {
		t.Errorf("LogsPath mismatch: got %q, want %q", cfg.Folder.LogsPath, filepath.Join(tempDir, "mylogs"))
	}
	if cfg.Folder.Compares != types.FULL_FILE {
		t.Errorf("Compares mismatch: got %v, want %v", cfg.Folder.Compares, types.FULL_FILE)
	}
	if cfg.Folder.Compression != types.COMPRESSION_ZSTD {
		t.Errorf("Compression mismatch: got %v, want %v", cfg.Folder.Compression, types.COMPRESSION_ZSTD)
	}
	if cfg.Folder.Concurrent != 50 {
		t.Errorf("Concurrent mismatch: got %d, want %d", cfg.Folder.Concurrent, 50)
	}
	if cfg.Folder.InitialSyncTimeoutMinutes != 30 {
		t.Errorf("InitialSyncTimeoutMinutes mismatch: got %d, want %d", cfg.Folder.InitialSyncTimeoutMinutes, 30)
	}
	if cfg.Server.ListenAddress != "127.0.0.1:8080" {
		t.Errorf("ListenAddress mismatch: got %q, want %q", cfg.Server.ListenAddress, "127.0.0.1:8080")
	}
	if cfg.Server.AuthToken != "supersecrettoken123456789012345678" {
		t.Errorf("AuthToken mismatch: got %q, want %q", cfg.Server.AuthToken, "supersecrettoken123456789012345678")
	}
	if cfg.Debug != true {
		t.Errorf("Debug mismatch: got %t, want %t", cfg.Debug, true)
	}

	if len(cfg.Folder.Ignores) != 3 {
		t.Fatalf("Expected 3 ignore patterns, got %d", len(cfg.Folder.Ignores))
	}
	if cfg.Folder.Ignores[0].Pattern != "/\\.git/" || cfg.Folder.Ignores[0].Type != "regexp" {
		t.Errorf("Ignore pattern 0 mismatch: %v", cfg.Folder.Ignores[0])
	}
	if cfg.Folder.Ignores[1].Pattern != "*.tmp" || cfg.Folder.Ignores[1].Type != "extension" {
		t.Errorf("Ignore pattern 1 mismatch: %v", cfg.Folder.Ignores[1])
	}
	if cfg.Folder.Ignores[2].Pattern != "sensitive.data" || cfg.Folder.Ignores[2].Type != "exact" {
		t.Errorf("Ignore pattern 2 mismatch: %v", cfg.Folder.Ignores[2])
	}

	if len(cfg.Pairs) != 2 {
		t.Fatalf("Expected 2 pairs, got %d", len(cfg.Pairs))
	}
	if cfg.Pairs[0].Name != "peer1" || cfg.Pairs[0].HostnameOrIP != "*************" || cfg.Pairs[0].Direction != types.ONE_WAY_TO_PEER {
		t.Errorf("Pair 1 mismatch: %v", cfg.Pairs[0])
	}
	if cfg.Pairs[1].Name != "peer2" || cfg.Pairs[1].HostnameOrIP != "*************" || cfg.Pairs[1].Direction != types.BIDIRECTIONAL_NEWEST_WIN {
		t.Errorf("Pair 2 mismatch: %v", cfg.Pairs[1])
	}

	// Test creation of meta and logs paths if they didn't exist
	if _, err := os.Stat(filepath.Join(tempDir, "mymeta")); os.IsNotExist(err) {
		t.Errorf("MetaPath was not created")
	}
	if _, err := os.Stat(filepath.Join(tempDir, "mylogs")); os.IsNotExist(err) {
		t.Errorf("LogsPath was not created")
	}
}

// TestLoadConfig_NonExistentFile tests loading from a non-existent file.
func TestLoadConfig_NonExistentFile(t *testing.T) {
	nonExistentPath := "/path/to/non/existent/config.yaml"
	cfg, err := config.LoadConfig(nonExistentPath)
	if err != nil {
		t.Fatalf("LoadConfig failed for non-existent file: %v", err)
	}
	// Verify that default values are loaded
	if cfg.Folder.Compares != types.SIZE_ONLY {
		t.Errorf("Expected default compares SIZE_ONLY, got %v", cfg.Folder.Compares)
	}
	if cfg.Server.ListenAddress != "0.0.0.0:8080" {
		t.Errorf("Expected default listen address, got %q", cfg.Server.ListenAddress)
	}
}

// TestLoadConfig_InvalidYAML tests loading from an invalid YAML file.
func TestLoadConfig_InvalidYAML(t *testing.T) {
	invalidYamlContent := `
folder:
  sync_path: /tmp/sync
  compares: invalid_enum_value
server:
  listen_address: "127.0.0.1:8080"
  auth_token: "longenoughsecretkey123456789012345"
pairs:
  - name: p1
    host: localhost
    direction: ONE_WAY_TO_PEER
    initial_sync_strategy: SYNC
    remote_port: 8080
`
	cfgPath := createTempConfigFile(t, invalidYamlContent)
	defer os.Remove(cfgPath)

	_, err := config.LoadConfig(cfgPath)
	if err == nil {
		t.Fatalf("LoadConfig did not return error for invalid YAML")
	}
	if !strings.Contains(err.Error(), "failed to parse config file") {
		t.Errorf("Expected parsing error, got: %v", err)
	}
}

// TestConfig_Validate tests basic validation scenarios.
func TestConfig_Validate(t *testing.T) {
	tempDir := t.TempDir()
	validSyncPath := filepath.Join(tempDir, "valid_sync")
	os.Mkdir(validSyncPath, 0755)

	// Test valid configuration
	t.Run("Valid Configuration", func(t *testing.T) {
		cfg := config.Config{}
		cfg.Folder.SyncPath = validSyncPath
		cfg.Folder.Compares = types.FULL_FILE
		cfg.Folder.Compression = types.COMPRESSION_NONE
		cfg.Folder.Concurrent = 10
		cfg.Folder.InitialSyncTimeoutMinutes = 20
		cfg.Server.Name = "test-server"
		cfg.Server.ListenAddress = "0.0.0.0:8080"
		cfg.Server.AuthToken = "longenoughsecretkey123456789012345"
		cfg.Pairs = []types.PairConfig{
			{Name: "p1", HostnameOrIP: "*******", Direction: types.ONE_WAY_TO_PEER, InitialSyncStrategy: types.SYNC, RemotePort: 8080},
		}

		err := cfg.Validate()
		if err != nil {
			t.Errorf("Expected valid config to pass validation, got error: %v", err)
		}
	})

	// Test missing sync path
	t.Run("Missing SyncPath", func(t *testing.T) {
		cfg := config.Config{}
		cfg.Folder.SyncPath = "" // Missing
		cfg.Folder.Compares = types.SIZE_ONLY
		cfg.Folder.Concurrent = 10
		cfg.Server.Name = "test-server"
		cfg.Server.ListenAddress = "0.0.0.0:8080"
		cfg.Server.AuthToken = "longenoughsecretkey"
		cfg.Pairs = []types.PairConfig{
			{Name: "p1", HostnameOrIP: "*******", Direction: types.ONE_WAY_TO_PEER, InitialSyncStrategy: types.SYNC, RemotePort: 8080},
		}

		err := cfg.Validate()
		if err == nil {
			t.Error("Expected validation error for missing sync path, got nil")
		}
		if !strings.Contains(err.Error(), "folder.sync_path must be specified") {
			t.Errorf("Expected 'folder.sync_path must be specified' error, got: %v", err)
		}
	})

	// Test valid configuration with no pairs (listener mode)
	t.Run("Valid Configuration with No Pairs", func(t *testing.T) {
		cfg := config.Config{}
		cfg.Folder.SyncPath = validSyncPath
		cfg.Folder.Compares = types.FULL_FILE
		cfg.Folder.Compression = types.COMPRESSION_NONE
		cfg.Folder.Concurrent = 10
		cfg.Folder.InitialSyncTimeoutMinutes = 20
		cfg.Server.Name = "test-server"
		cfg.Server.ListenAddress = "0.0.0.0:8080"
		cfg.Server.AuthToken = "longenoughsecretkey123456789012345"
		cfg.Pairs = []types.PairConfig{} // No pairs configured - should be valid for listener mode

		err := cfg.Validate()
		if err != nil {
			t.Errorf("Expected valid config with no pairs to pass validation, got error: %v", err)
		}
	})
}

// TestPatternMatcher_IsIgnored tests the IsIgnored method of PatternMatcher.
func TestPatternMatcher_IsIgnored(t *testing.T) {
	tests := []struct {
		name    string
		ignores []struct {
			Pattern string `yaml:"pattern"`
			Type    string `yaml:"type"`
		}
		path     string
		expected bool
	}{
		{
			name: "Regexp match",
			ignores: []struct {
				Pattern string `yaml:"pattern"`
				Type    string `yaml:"type"`
			}{
				{Pattern: ".*\\.log$", Type: "regexp"},
			},
			path:     "app.log",
			expected: true,
		},
		{
			name: "Regexp no match",
			ignores: []struct {
				Pattern string `yaml:"pattern"`
				Type    string `yaml:"type"`
			}{
				{Pattern: ".*\\.log$", Type: "regexp"},
			},
			path:     "app.txt",
			expected: false,
		},
		{
			name: "Extension match",
			ignores: []struct {
				Pattern string `yaml:"pattern"`
				Type    string `yaml:"type"`
			}{
				{Pattern: "*.tmp", Type: "extension"},
			},
			path:     "file.tmp",
			expected: true,
		},
		{
			name: "Exact match",
			ignores: []struct {
				Pattern string `yaml:"pattern"`
				Type    string `yaml:"type"`
			}{
				{Pattern: "secret.data", Type: "exact"},
			},
			path:     "secret.data",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := config.Config{}
			cfg.Folder.Ignores = tt.ignores

			pm, err := cfg.NewPatternMatcher()
			if err != nil {
				t.Fatalf("Failed to create pattern matcher: %v", err)
			}

			result := pm.IsIgnored(tt.path)
			if result != tt.expected {
				t.Errorf("IsIgnored(%q) = %v, want %v", tt.path, result, tt.expected)
			}
		})
	}
}

// TestConfig_Validate_Extended tests additional validation scenarios.
func TestConfig_Validate_Extended(t *testing.T) {
	tempDir := t.TempDir()
	validSyncPath := filepath.Join(tempDir, "valid_sync")
	os.Mkdir(validSyncPath, 0755)

	tests := []struct {
		name      string
		setupCfg  func() config.Config
		expectErr bool
		errString string
	}{
		{
			name: "Invalid compression type",
			setupCfg: func() config.Config {
				cfg := config.Config{}
				cfg.Folder.SyncPath = validSyncPath
				cfg.Folder.Compares = types.SIZE_ONLY
				cfg.Folder.Compression = types.FileCompressionType(0xFF) // Invalid
				cfg.Folder.Concurrent = 10
				cfg.Server.Name = "test-server"
				cfg.Server.ListenAddress = "0.0.0.0:8080"
				cfg.Server.AuthToken = "longenoughsecretkey"
				cfg.Pairs = []types.PairConfig{
					{Name: "p1", HostnameOrIP: "*******", Direction: types.ONE_WAY_TO_PEER, InitialSyncStrategy: types.SYNC, RemotePort: 8080},
				}
				return cfg
			},
			expectErr: true,
			errString: "invalid folder.compression type",
		},
		{
			name: "Invalid concurrent value",
			setupCfg: func() config.Config {
				cfg := config.Config{}
				cfg.Folder.SyncPath = validSyncPath
				cfg.Folder.Compares = types.SIZE_ONLY
				cfg.Folder.Concurrent = 0 // Invalid
				cfg.Server.Name = "test-server"
				cfg.Server.ListenAddress = "0.0.0.0:8080"
				cfg.Server.AuthToken = "longenoughsecretkey"
				cfg.Pairs = []types.PairConfig{
					{Name: "p1", HostnameOrIP: "*******", Direction: types.ONE_WAY_TO_PEER, InitialSyncStrategy: types.SYNC, RemotePort: 8080},
				}
				return cfg
			},
			expectErr: true,
			errString: "folder.concurrent must be between 1 and 1024",
		},
		{
			name: "Missing auth token",
			setupCfg: func() config.Config {
				cfg := config.Config{}
				cfg.Folder.SyncPath = validSyncPath
				cfg.Folder.Compares = types.SIZE_ONLY
				cfg.Folder.Concurrent = 10
				cfg.Server.Name = "test-server"
				cfg.Server.ListenAddress = "0.0.0.0:8080"
				cfg.Server.AuthToken = "" // Missing
				cfg.Pairs = []types.PairConfig{
					{Name: "p1", HostnameOrIP: "*******", Direction: types.ONE_WAY_TO_PEER, InitialSyncStrategy: types.SYNC, RemotePort: 8080},
				}
				return cfg
			},
			expectErr: true,
			errString: "server.auth_token must be specified",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := tt.setupCfg()
			err := cfg.Validate()
			if tt.expectErr {
				if err == nil {
					t.Fatalf("Expected an error for test %q, but got none", tt.name)
				}
				if !strings.Contains(err.Error(), tt.errString) {
					t.Errorf("Error string mismatch for test %q.\nExpected to contain: %q\nActual error: %q", tt.name, tt.errString, err.Error())
				}
			} else {
				if err != nil {
					t.Fatalf("Did not expect an error for test %q, but got: %v", tt.name, err)
				}
			}
		})
	}
}

// TestPatternMatcher_IsIgnored_Extended tests additional IsIgnored scenarios.
func TestPatternMatcher_IsIgnored_Extended(t *testing.T) {
	tests := []struct {
		name    string
		ignores []struct {
			Pattern string `yaml:"pattern"`
			Type    string `yaml:"type"`
		}
		path     string
		expected bool
	}{
		{
			name: "Extension no match",
			ignores: []struct {
				Pattern string `yaml:"pattern"`
				Type    string `yaml:"type"`
			}{
				{Pattern: "*.tmp", Type: "extension"},
			},
			path:     "file.txt",
			expected: false,
		},
		{
			name: "Exact no match",
			ignores: []struct {
				Pattern string `yaml:"pattern"`
				Type    string `yaml:"type"`
			}{
				{Pattern: "secret.data", Type: "exact"},
			},
			path:     "secrets.data",
			expected: false,
		},
		{
			name: "Multiple patterns - match one",
			ignores: []struct {
				Pattern string `yaml:"pattern"`
				Type    string `yaml:"type"`
			}{
				{Pattern: ".*\\.bak$", Type: "regexp"},
				{Pattern: "*.temp", Type: "extension"},
			},
			path:     "document.temp",
			expected: true,
		},
		{
			name: "Multiple patterns - no match",
			ignores: []struct {
				Pattern string `yaml:"pattern"`
				Type    string `yaml:"type"`
			}{
				{Pattern: ".*\\.bak$", Type: "regexp"},
				{Pattern: "*.temp", Type: "extension"},
			},
			path:     "image.png",
			expected: false,
		},
		{
			name: "Empty ignores list",
			ignores: []struct {
				Pattern string `yaml:"pattern"`
				Type    string `yaml:"type"`
			}{},
			path:     "any/file.txt",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := config.Config{}
			cfg.Folder.Ignores = tt.ignores

			matcher, err := cfg.NewPatternMatcher()
			if err != nil {
				t.Fatalf("NewPatternMatcher failed: %v", err)
			}

			actual := matcher.IsIgnored(tt.path)
			if actual != tt.expected {
				t.Errorf("IsIgnored(%q) = %t, want %t", tt.path, actual, tt.expected)
			}
		})
	}
}

// TestConfig_PathValidation tests path validation through the config validation.
func TestConfig_PathValidation(t *testing.T) {
	tempDir := t.TempDir()

	t.Run("Valid paths are created", func(t *testing.T) {
		cfg := config.Config{}
		cfg.Folder.SyncPath = tempDir
		cfg.Folder.MetaPath = filepath.Join(tempDir, "meta")
		cfg.Folder.LogsPath = filepath.Join(tempDir, "logs")
		cfg.Folder.Compares = types.SIZE_ONLY
		cfg.Folder.Concurrent = 10
		cfg.Server.Name = "test-server"
		cfg.Server.ListenAddress = "0.0.0.0:8080"
		cfg.Server.AuthToken = "longenoughsecretkey123456789012345"
		cfg.Pairs = []types.PairConfig{
			{Name: "p1", HostnameOrIP: "*******", Direction: types.ONE_WAY_TO_PEER, InitialSyncStrategy: types.SYNC, RemotePort: 8080},
		}

		err := cfg.Validate()
		if err != nil {
			t.Fatalf("Expected validation to succeed and create paths, got error: %v", err)
		}

		// Check that meta and logs paths were created
		if _, err := os.Stat(cfg.Folder.MetaPath); os.IsNotExist(err) {
			t.Errorf("MetaPath %q was not created", cfg.Folder.MetaPath)
		}
		if _, err := os.Stat(cfg.Folder.LogsPath); os.IsNotExist(err) {
			t.Errorf("LogsPath %q was not created", cfg.Folder.LogsPath)
		}
	})

	t.Run("Non-existent sync path fails validation", func(t *testing.T) {
		cfg := config.Config{}
		cfg.Folder.SyncPath = "/non/existent/path"
		cfg.Folder.Compares = types.SIZE_ONLY
		cfg.Folder.Concurrent = 10
		cfg.Server.Name = "test-server"
		cfg.Server.ListenAddress = "0.0.0.0:8080"
		cfg.Server.AuthToken = "longenoughsecretkey"
		cfg.Pairs = []types.PairConfig{
			{Name: "p1", HostnameOrIP: "*******", Direction: types.ONE_WAY_TO_PEER, InitialSyncStrategy: types.SYNC, RemotePort: 8080},
		}

		err := cfg.Validate()
		if err == nil {
			t.Error("Expected validation to fail for non-existent sync path")
		}
		if !strings.Contains(err.Error(), "does not exist or is not a directory") {
			t.Errorf("Expected path validation error, got: %v", err)
		}
	})
}
