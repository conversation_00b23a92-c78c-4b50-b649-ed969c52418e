package config

import (
	"fmt"
	"io/ioutil"
	"net"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/real-rm/folder_sync/pkg/types"
	"gopkg.in/yaml.v3"
)

// Config holds the entire application configuration.
type Config struct {
	Folder struct {
		SyncPath string `yaml:"sync_path"` // Path to the folder to synchronize.
		MetaPath string `yaml:"meta_path"` // Path for storing program status and SQLite queues.
		LogsPath string `yaml:"logs_path"` // Path for storing log files.
		Ignores  []struct {
			Pattern string `yaml:"pattern"` // Pattern to ignore (regexp, *.ext, or exact).
			Type    string `yaml:"type"`    // "regexp", "extension", or "exact".
		} `yaml:"ignores"`
		Compares                  types.CompareStrategy     `yaml:"compares"`                     // File comparison strategy (size, sample, full).
		Compression               types.FileCompressionType `yaml:"compression"`                  // Compression type for file transfers.
		Readonly                  bool                      `yaml:"readonly"`                     // If true, this server cannot make outbound changes.
		Writethrough              bool                      `yaml:"writethrough"`                 // If true, acts as a buffer, only receives via proxy.
		Concurrent                int                       `yaml:"concurrent"`                   // Max concurrent file transfers (1-1024).
		BlockSize                 int                       `yaml:"block_size"`                   // Block size for sample checksum (derived from OS).
		InitialSyncTimeoutMinutes int                       `yaml:"initial_sync_timeout_minutes"` // Timeout for initial sync logging
	} `yaml:"folder"`
	Server struct {
		Name          string `yaml:"name"`           // Name of this server (used for peer identification).
		ListenAddress string `yaml:"listen_address"` // Address to listen on (e.g., "0.0.0.0:8080").
		AuthToken     string `yaml:"auth_token"`     // Shared secret token for authentication.
	} `yaml:"server"`
	Monitor struct {
		SocketPath string `yaml:"socket_path"` // Path to the Unix domain socket for proxy events.
	} `yaml:"monitor"`
	Debounce struct {
		WindowMs             int  `yaml:"window_ms"`             // Debounce window in milliseconds (default: 250)
		BufferSize           int  `yaml:"buffer_size"`           // Maximum events to buffer during debounce (default: 100)
		ConsolidationEnabled bool `yaml:"consolidation_enabled"` // Enable event consolidation (default: true)
		TempFileDebounceMs   int  `yaml:"temp_file_debounce_ms"` // Extended debounce for temp files (default: 1000)
	} `yaml:"debounce"`
	Pairs []types.PairConfig `yaml:"pairs"` // List of paired servers.
	Debug bool               `yaml:"debug"` // Enable debug logging.
}

// DefaultConfigPath is the default path for the configuration file.
const DefaultConfigPath = "/etc/folder_sync.ini"

// LoadConfig loads the configuration from the specified YAML file.
func LoadConfig(filePath string) (*Config, error) {
	cfg := &Config{}
	// Set default values before loading from file
	cfg.setDefaults()

	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			// If file doesn't exist, return default config.
			// This might be acceptable if the user is expected to generate one later
			// or if some default values are sufficient for a basic run.
			return cfg, nil
		}
		return nil, fmt.Errorf("failed to read config file %q: %w", filePath, err)
	}

	if err := yaml.Unmarshal(data, cfg); err != nil {
		return nil, fmt.Errorf("failed to parse config file %q: %w", filePath, err)
	}

	return cfg, nil
}

// setDefaults initializes default values for the Config struct.
func (c *Config) setDefaults() {
	c.Folder.Compares = types.SIZE_ONLY
	c.Folder.Compression = types.COMPRESSION_NONE
	c.Folder.Readonly = false
	c.Folder.Writethrough = false
	c.Folder.Concurrent = types.DefaultConcurrentTransfers
	c.Folder.BlockSize = types.DefaultBlockSize
	c.Folder.InitialSyncTimeoutMinutes = types.DefaultInitialSyncTimeoutMinutes
	c.Server.ListenAddress = "0.0.0.0:8080"
	c.Debug = false

	// Set debounce defaults
	c.Debounce.WindowMs = types.DefaultDebounceWindowMs
	c.Debounce.BufferSize = types.DefaultDebounceBufferSize
	c.Debounce.ConsolidationEnabled = types.DefaultEventConsolidationEnabled
	c.Debounce.TempFileDebounceMs = types.DefaultTempFileDebounceMs

	// Set default ignore patterns for common temporary files
	c.setDefaultIgnorePatterns()
}

// setDefaultIgnorePatterns sets sensible default ignore patterns for common temporary files
func (c *Config) setDefaultIgnorePatterns() {
	// Only set defaults if no ignore patterns are already configured
	if len(c.Folder.Ignores) == 0 {
		c.Folder.Ignores = []struct {
			Pattern string `yaml:"pattern"`
			Type    string `yaml:"type"`
		}{
			// Vim temporary files
			{Pattern: `^\..*\.swp$`, Type: "regexp"}, // .file.swp (vim swap files)
			{Pattern: `^\..*\.swo$`, Type: "regexp"}, // .file.swo (vim swap files)
			{Pattern: `.*~$`, Type: "regexp"},        // file~ (vim backup files)
			{Pattern: `^\..*~$`, Type: "regexp"},     // .file~ (hidden backup files)

			// Common temporary files
			{Pattern: `.*\.tmp$`, Type: "regexp"},  // *.tmp files
			{Pattern: `.*\.temp$`, Type: "regexp"}, // *.temp files
			{Pattern: `^\.#.*$`, Type: "regexp"},   // .#file (emacs lock files)
			{Pattern: `^#.*#$`, Type: "regexp"},    // #file# (emacs auto-save files)

			// System files
			{Pattern: ".DS_Store", Type: "exact"},   // macOS system files
			{Pattern: "Thumbs.db", Type: "exact"},   // Windows thumbnails
			{Pattern: "desktop.ini", Type: "exact"}, // Windows desktop config

			// Version control
			{Pattern: ".git", Type: "exact"}, // Git directory
			{Pattern: ".svn", Type: "exact"}, // SVN directory
			{Pattern: ".hg", Type: "exact"},  // Mercurial directory
		}
	}
}

// Validate performs comprehensive validation of the configuration.
func (c *Config) Validate() error {
	// Validate folder paths
	if c.Folder.SyncPath == "" {
		return fmt.Errorf("folder.sync_path must be specified")
	}
	if !pathExistsAndIsDir(c.Folder.SyncPath) {
		return fmt.Errorf("folder.sync_path %q does not exist or is not a directory", c.Folder.SyncPath)
	}
	if c.Folder.MetaPath == "" {
		c.Folder.MetaPath = filepath.Join(c.Folder.SyncPath, ".folder_sync_meta")
	}
	if !pathExistsOrCreate(c.Folder.MetaPath) {
		return fmt.Errorf("folder.meta_path %q could not be created or is not a directory", c.Folder.MetaPath)
	}
	if c.Folder.LogsPath == "" {
		c.Folder.LogsPath = filepath.Join(c.Folder.MetaPath, "logs")
	}
	if !pathExistsOrCreate(c.Folder.LogsPath) {
		return fmt.Errorf("folder.logs_path %q could not be created or is not a directory", c.Folder.LogsPath)
	}

	// Validate compares strategy
	if c.Folder.Compares != types.SIZE_ONLY && c.Folder.Compares != types.FIRST_LAST_BLOCK_SAMPLE && c.Folder.Compares != types.FULL_FILE {
		return fmt.Errorf("invalid folder.compares strategy: %v", c.Folder.Compares)
	}

	// Validate compression type
	if c.Folder.Compression != types.COMPRESSION_NONE && c.Folder.Compression != types.COMPRESSION_ZSTD {
		return fmt.Errorf("invalid folder.compression type: %v", c.Folder.Compression)
	}

	// Validate concurrent value
	if c.Folder.Concurrent < 1 || c.Folder.Concurrent > 1024 {
		return fmt.Errorf("folder.concurrent must be between 1 and 1024, got %d", c.Folder.Concurrent)
	}

	// Validate server name
	if c.Server.Name == "" {
		return fmt.Errorf("server.name must be specified")
	}

	// Validate server listen address
	if c.Server.ListenAddress == "" {
		return fmt.Errorf("server.listen_address must be specified")
	}
	// Basic check for valid address:port format. Not exhaustive.
	_, _, err := net.SplitHostPort(c.Server.ListenAddress)
	if err != nil {
		return fmt.Errorf("invalid server.listen_address format: %q (expected host:port)", c.Server.ListenAddress)
	}

	// Validate auth token
	if c.Server.AuthToken == "" {
		return fmt.Errorf("server.auth_token must be specified")
	}
	if len(c.Server.AuthToken) < 32 { // Increased minimum length for production security
		return fmt.Errorf("server.auth_token is too short, require at least 32 characters for production")
	}
	// Check for weak tokens
	if c.Server.AuthToken == "YOUR_VERY_SECRET_AUTH_TOKEN_HERE" ||
		strings.Repeat("1", len(c.Server.AuthToken)) == c.Server.AuthToken ||
		strings.Repeat("a", len(c.Server.AuthToken)) == c.Server.AuthToken {
		return fmt.Errorf("server.auth_token appears to be a placeholder or weak token, please use a strong random token")
	}

	// Validate ignores patterns
	for _, ignore := range c.Folder.Ignores {
		switch ignore.Type {
		case "regexp":
			if _, err := regexp.Compile(ignore.Pattern); err != nil {
				return fmt.Errorf("invalid regexp pattern %q: %w", ignore.Pattern, err)
			}
		case "extension", "exact":
			// No special validation needed for these types beyond being non-empty.
			if ignore.Pattern == "" {
				return fmt.Errorf("ignore pattern of type %q cannot be empty", ignore.Type)
			}
		default:
			return fmt.Errorf("unknown ignore type %q for pattern %q", ignore.Type, ignore.Pattern)
		}
	}

	// Validate pairs
	// Note: A server can run without any configured pairs if it's acting as a listener
	// for other servers to connect to it. The pairs configuration is for outbound connections.

	pairNames := make(map[string]bool)
	for i, pair := range c.Pairs {
		if pair.Name == "" {
			return fmt.Errorf("pair %d: name must be specified", i)
		}
		if pairNames[pair.Name] {
			return fmt.Errorf("pair %d: duplicate pair name %q", i, pair.Name)
		}
		pairNames[pair.Name] = true

		if pair.HostnameOrIP == "" {
			return fmt.Errorf("pair %q: host must be specified", pair.Name)
		}
		if pair.RemotePort == 0 {
			return fmt.Errorf("pair %q: remote_port must be specified", pair.Name)
		}
		if pair.RemotePort < 1 || pair.RemotePort > 65535 {
			return fmt.Errorf("pair %q: remote_port %d is out of valid range (1-65535)", pair.Name, pair.RemotePort)
		}

		if pair.Direction == "" {
			return fmt.Errorf("pair %q: direction must be specified", pair.Name)
		}
		switch pair.Direction {
		case types.ONE_WAY_TO_PEER, types.ONE_WAY_FROM_PEER, types.BIDIRECTIONAL_NEWEST_WIN:
			// Valid directions
		default:
			return fmt.Errorf("pair %q: invalid direction %q", pair.Name, pair.Direction)
		}

		if pair.InitialSyncStrategy == "" {
			return fmt.Errorf("pair %q: initial_sync_strategy must be specified", pair.Name)
		}
		switch pair.InitialSyncStrategy {
		case types.SYNC, types.NO_INITIAL_SYNC:
			// Valid strategies
		default:
			return fmt.Errorf("pair %q: invalid initial_sync_strategy %q", pair.Name, pair.InitialSyncStrategy)
		}

		// Validate readonly/writethrough conflicts with pairs
		if c.Folder.Readonly && (pair.Direction == types.ONE_WAY_FROM_PEER || pair.Direction == types.BIDIRECTIONAL_NEWEST_WIN) {
			return fmt.Errorf("conflict: server is in readonly mode, but pair %q is configured to receive changes (<:host or bidirectional)", pair.Name)
		}
		if c.Folder.Writethrough && (pair.Direction == types.ONE_WAY_FROM_PEER || pair.Direction == types.BIDIRECTIONAL_NEWEST_WIN) {
			return fmt.Errorf("conflict: server is in writethrough mode, but pair %q is configured to receive changes (<:host or bidirectional)", pair.Name)
		}
	}

	// Validate writethrough mode specific requirements
	if c.Folder.Writethrough {
		if c.Monitor.SocketPath == "" {
			return fmt.Errorf("writethrough mode requires monitor.socket_path to be specified")
		}
		// In writethrough mode, the server itself should not initiate outbound syncs,
		// only propagate changes received from the proxy.
		// However, it can *send* changes to peers, so ONE_WAY_TO_PEER is allowed.
		// The specification says "only acts as a conduit for changes from its local proxy socket to remote peers"
		// which implies it always sends, so pairs should be "to peer".
		for _, pair := range c.Pairs {
			if pair.Direction != types.ONE_WAY_TO_PEER {
				return fmt.Errorf("writethrough mode: pair %q must have direction ONE_WAY_TO_PEER", pair.Name)
			}
		}
	} else {
		// If not in writethrough mode, monitor.socket_path should not be specified unless explicitly needed for other reasons
		// For now, we'll just ensure it's compatible.
		// Specification implies if monitor.socket is specified, it's the *sole* source of changes.
		// This can be refined in Ingress package validation.
	}

	// Validate debounce configuration
	if c.Debounce.WindowMs < 0 {
		return fmt.Errorf("debounce.window_ms must be non-negative, got %d", c.Debounce.WindowMs)
	}
	if c.Debounce.WindowMs > 10000 { // 10 seconds max
		return fmt.Errorf("debounce.window_ms must be <= 10000ms (10 seconds), got %d", c.Debounce.WindowMs)
	}
	// BufferSize of 0 is allowed (means use defaults), but if specified must be at least 1
	if c.Debounce.BufferSize < 0 {
		return fmt.Errorf("debounce.buffer_size must be non-negative, got %d", c.Debounce.BufferSize)
	}
	if c.Debounce.BufferSize > 10000 { // reasonable upper limit
		return fmt.Errorf("debounce.buffer_size must be <= 10000, got %d", c.Debounce.BufferSize)
	}
	if c.Debounce.TempFileDebounceMs < 0 {
		return fmt.Errorf("debounce.temp_file_debounce_ms must be non-negative, got %d", c.Debounce.TempFileDebounceMs)
	}
	if c.Debounce.TempFileDebounceMs > 30000 { // 30 seconds max
		return fmt.Errorf("debounce.temp_file_debounce_ms must be <= 30000ms (30 seconds), got %d", c.Debounce.TempFileDebounceMs)
	}

	return nil
}

// pathExistsAndIsDir checks if a path exists and is a directory.
func pathExistsAndIsDir(path string) bool {
	info, err := os.Stat(path)
	if os.IsNotExist(err) {
		return false
	}
	return err == nil && info.IsDir()
}

// pathExistsOrCreate checks if a path exists and is a directory, creating it if it doesn't exist.
func pathExistsOrCreate(path string) bool {
	info, err := os.Stat(path)
	if os.IsNotExist(err) {
		err = os.MkdirAll(path, 0755) // Create with read/write/execute for owner, read/execute for others
		return err == nil
	}
	return err == nil && info.IsDir()
}

// PatternMatcher is a helper struct for compiled ignore patterns.
type PatternMatcher struct {
	RegexMatchers     []*regexp.Regexp
	ExtensionMatchers []string
	ExactMatchers     []string
}

// NewPatternMatcher creates and compiles ignore patterns from config.
func (c *Config) NewPatternMatcher() (*PatternMatcher, error) {
	pm := &PatternMatcher{}
	for _, ignore := range c.Folder.Ignores {
		switch ignore.Type {
		case "regexp":
			r, err := regexp.Compile(ignore.Pattern)
			if err != nil {
				// This should ideally be caught by config validation, but good to double check.
				return nil, fmt.Errorf("failed to compile regexp ignore pattern %q: %w", ignore.Pattern, err)
			}
			pm.RegexMatchers = append(pm.RegexMatchers, r)
		case "extension":
			// Store extensions without the leading '*' for easier comparison (e.g., ".log")
			pm.ExtensionMatchers = append(pm.ExtensionMatchers, strings.TrimPrefix(ignore.Pattern, "*"))
		case "exact":
			pm.ExactMatchers = append(pm.ExactMatchers, ignore.Pattern)
		default:
			// This should also be caught by config validation.
			return nil, fmt.Errorf("unsupported ignore type %q", ignore.Type)
		}
	}
	return pm, nil
}

// IsIgnored checks if a given path should be ignored based on compiled patterns.
func (pm *PatternMatcher) IsIgnored(path string) bool {
	for _, r := range pm.RegexMatchers {
		if r.MatchString(path) {
			return true
		}
	}
	for _, ext := range pm.ExtensionMatchers {
		if strings.HasSuffix(path, ext) {
			return true
		}
	}
	for _, exact := range pm.ExactMatchers {
		if path == exact {
			return true
		}
	}
	return false
}

// GetPairConfig returns the PairConfig for a given peer name.
func (c *Config) GetPairConfig(peerName string) (*types.PairConfig, bool) {
	for _, pair := range c.Pairs {
		if pair.Name == peerName {
			return &pair, true
		}
	}
	return nil, false
}

// ValidateConcurrentValue validates the concurrent value is within the documented range (1-1024)
func ValidateConcurrentValue(cfg *Config) error {
	if cfg.Folder.Concurrent < 1 || cfg.Folder.Concurrent > 1024 {
		return fmt.Errorf("folder.concurrent must be between 1 and 1024, got %d", cfg.Folder.Concurrent)
	}
	return nil
}

// ValidateReadonlyWritethroughConflicts validates readonly/writethrough conflicts with pairs
func ValidateReadonlyWritethroughConflicts(cfg *Config) error {
	for _, pair := range cfg.Pairs {
		if cfg.Folder.Readonly && (pair.Direction == types.ONE_WAY_FROM_PEER || pair.Direction == types.BIDIRECTIONAL_NEWEST_WIN) {
			return fmt.Errorf("conflict: server is in readonly mode, but pair %q is configured to receive changes", pair.Name)
		}
		if cfg.Folder.Writethrough && (pair.Direction == types.ONE_WAY_FROM_PEER || pair.Direction == types.BIDIRECTIONAL_NEWEST_WIN) {
			return fmt.Errorf("conflict: server is in writethrough mode, but pair %q is configured to receive changes", pair.Name)
		}
	}
	return nil
}

// ValidateWritethroughSocketRequirement validates writethrough requires monitor.socket
func ValidateWritethroughSocketRequirement(cfg *Config) error {
	if cfg.Folder.Writethrough && cfg.Monitor.SocketPath == "" {
		return fmt.Errorf("writethrough mode requires monitor.socket_path to be specified")
	}
	return nil
}

// ParsePairSyntax parses pair syntax like ">:host", "<:host", "host"
func ParsePairSyntax(pairString string) (types.PairDirection, string, error) {
	if strings.HasPrefix(pairString, ">>:") || strings.HasPrefix(pairString, "?:") {
		return "", "", fmt.Errorf("invalid pair syntax: %s", pairString)
	}
	if strings.HasPrefix(pairString, ">:") {
		host := strings.TrimPrefix(pairString, ">:")
		if host == "" {
			return "", "", fmt.Errorf("invalid pair syntax: host cannot be empty")
		}
		return types.ONE_WAY_TO_PEER, host, nil
	}
	if strings.HasPrefix(pairString, "<:") {
		host := strings.TrimPrefix(pairString, "<:")
		if host == "" {
			return "", "", fmt.Errorf("invalid pair syntax: host cannot be empty")
		}
		return types.ONE_WAY_FROM_PEER, host, nil
	}
	if pairString == "" {
		return "", "", fmt.Errorf("invalid pair syntax: cannot be empty")
	}
	// Default to bidirectional
	return types.BIDIRECTIONAL_NEWEST_WIN, pairString, nil
}

// NewIgnoredPathMatcher creates a new pattern matcher from ignore patterns
func NewIgnoredPathMatcher(patterns []string) (*PatternMatcher, error) {
	pm := &PatternMatcher{}
	for _, pattern := range patterns {
		// Determine pattern type based on format
		if strings.HasPrefix(pattern, "/") && strings.HasSuffix(pattern, "/") {
			// Regexp pattern
			regexPattern := strings.Trim(pattern, "/")
			r, err := regexp.Compile(regexPattern)
			if err != nil {
				return nil, fmt.Errorf("failed to compile regexp pattern %q: %w", pattern, err)
			}
			pm.RegexMatchers = append(pm.RegexMatchers, r)
		} else if strings.HasPrefix(pattern, "*.") {
			// Extension pattern
			pm.ExtensionMatchers = append(pm.ExtensionMatchers, strings.TrimPrefix(pattern, "*"))
		} else if strings.HasSuffix(pattern, "/") {
			// Directory pattern - convert to regex that matches the directory and its contents
			dirName := strings.TrimSuffix(pattern, "/")
			regexPattern := fmt.Sprintf("^%s(/.*)?$", regexp.QuoteMeta(dirName))
			r, err := regexp.Compile(regexPattern)
			if err != nil {
				return nil, fmt.Errorf("failed to compile directory pattern %q: %w", pattern, err)
			}
			pm.RegexMatchers = append(pm.RegexMatchers, r)
		} else if pattern == ".*" {
			// Hidden files pattern - convert to regex
			r, err := regexp.Compile(`^\..+`)
			if err != nil {
				return nil, fmt.Errorf("failed to compile hidden files pattern %q: %w", pattern, err)
			}
			pm.RegexMatchers = append(pm.RegexMatchers, r)
		} else {
			// Exact pattern
			pm.ExactMatchers = append(pm.ExactMatchers, pattern)
		}
	}
	return pm, nil
}

// NewDefaultConfig creates a new config with default values
func NewDefaultConfig() *Config {
	cfg := &Config{}
	cfg.setDefaults()
	return cfg
}

// LoadFromFile loads configuration from a file (alias for LoadConfig)
func LoadFromFile(filePath string) (*Config, error) {
	return LoadConfig(filePath)
}

// ApplyDebugOverride applies debug flag override from command line
func ApplyDebugOverride(cfg *Config, debugFlag bool) {
	cfg.Debug = debugFlag
}

// GetConfigPath returns the config path, using custom path if provided, otherwise default
func GetConfigPath(customPath string) string {
	if customPath != "" {
		return customPath
	}
	return DefaultConfigPath
}
