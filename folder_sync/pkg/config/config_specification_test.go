package config_test

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/types"
)

// TestConfigurationValidation tests configuration validation as documented in section 4.1
func TestConfigurationValidation(t *testing.T) {
	t.Run("concurrent value validation", func(t *testing.T) {
		// Test the documented requirement: "Validate concurrent value (1-1024)"
		tests := []struct {
			name        string
			concurrent  int
			shouldError bool
		}{
			{"valid minimum", 1, false},
			{"valid middle", 512, false},
			{"valid maximum", 1024, false},
			{"invalid zero", 0, true},
			{"invalid negative", -1, true},
			{"invalid too high", 1025, true},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				cfg := &config.Config{}
				cfg.Folder.Concurrent = tt.concurrent

				err := config.ValidateConcurrentValue(cfg)
				if tt.shouldError && err == nil {
					t.Errorf("Expected error for concurrent value %d", tt.concurrent)
				}
				if !tt.shouldError && err != nil {
					t.Errorf("Unexpected error for concurrent value %d: %v", tt.concurrent, err)
				}
			})
		}
	})

	t.Run("readonly writethrough pairs conflict", func(t *testing.T) {
		// Test the documented validation: "If readonly or writethrough is true, but a pairs entry
		// attempts to make this server a destination for sync, the program must output an error and exit"

		tests := []struct {
			name         string
			readonly     bool
			writethrough bool
			direction    types.PairDirection
			shouldError  bool
		}{
			{"readonly with incoming sync", true, false, types.ONE_WAY_FROM_PEER, true},
			{"readonly with bidirectional", true, false, types.BIDIRECTIONAL_NEWEST_WIN, true},
			{"readonly with outgoing sync", true, false, types.ONE_WAY_TO_PEER, false},
			{"writethrough with incoming sync", false, true, types.ONE_WAY_FROM_PEER, true},
			{"writethrough with bidirectional", false, true, types.BIDIRECTIONAL_NEWEST_WIN, true},
			{"writethrough with outgoing sync", false, true, types.ONE_WAY_TO_PEER, false},
			{"normal with any direction", false, false, types.BIDIRECTIONAL_NEWEST_WIN, false},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				cfg := &config.Config{}
				cfg.Folder.Readonly = tt.readonly
				cfg.Folder.Writethrough = tt.writethrough

				pairConfig := types.PairConfig{
					Name:         "test-peer",
					HostnameOrIP: "127.0.0.1",
					Direction:    tt.direction,
					RemotePort:   8080,
				}
				cfg.Pairs = []types.PairConfig{pairConfig}

				err := config.ValidateReadonlyWritethroughConflicts(cfg)
				if tt.shouldError && err == nil {
					t.Errorf("Expected error for readonly=%v writethrough=%v direction=%v",
						tt.readonly, tt.writethrough, tt.direction)
				}
				if !tt.shouldError && err != nil {
					t.Errorf("Unexpected error for readonly=%v writethrough=%v direction=%v: %v",
						tt.readonly, tt.writethrough, tt.direction, err)
				}
			})
		}
	})

	t.Run("writethrough monitor socket requirement", func(t *testing.T) {
		// Test the documented validation: "Validate writethrough must accompany monitor.socket"
		tests := []struct {
			name         string
			writethrough bool
			socketPath   string
			shouldError  bool
		}{
			{"writethrough with socket", true, "/tmp/test.sock", false},
			{"writethrough without socket", true, "", true},
			{"no writethrough no socket", false, "", false},
			{"no writethrough with socket", false, "/tmp/test.sock", false},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				cfg := &config.Config{}
				cfg.Folder.Writethrough = tt.writethrough
				cfg.Monitor.SocketPath = tt.socketPath

				err := config.ValidateWritethroughSocketRequirement(cfg)
				if tt.shouldError && err == nil {
					t.Errorf("Expected error for writethrough=%v socketPath='%s'",
						tt.writethrough, tt.socketPath)
				}
				if !tt.shouldError && err != nil {
					t.Errorf("Unexpected error for writethrough=%v socketPath='%s': %v",
						tt.writethrough, tt.socketPath, err)
				}
			})
		}
	})

	t.Run("pairs syntax validation", func(t *testing.T) {
		// Test the documented requirement: "Validate pairs syntax (e.g., '>:host', '<:host')"
		tests := []struct {
			name        string
			pairString  string
			shouldError bool
			expectedDir types.PairDirection
		}{
			{"outgoing sync", ">:*************", false, types.ONE_WAY_TO_PEER},
			{"incoming sync", "<:*************", false, types.ONE_WAY_FROM_PEER},
			{"bidirectional sync", "*************", false, types.BIDIRECTIONAL_NEWEST_WIN},
			{"invalid syntax", ">>:*************", true, types.BIDIRECTIONAL_NEWEST_WIN},
			{"empty host", ">:", true, types.BIDIRECTIONAL_NEWEST_WIN},
			{"invalid direction", "?:*************", true, types.BIDIRECTIONAL_NEWEST_WIN},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				direction, host, err := config.ParsePairSyntax(tt.pairString)
				if tt.shouldError && err == nil {
					t.Errorf("Expected error for pair syntax '%s'", tt.pairString)
				}
				if !tt.shouldError && err != nil {
					t.Errorf("Unexpected error for pair syntax '%s': %v", tt.pairString, err)
				}
				if !tt.shouldError && direction != tt.expectedDir {
					t.Errorf("Expected direction %v, got %v", tt.expectedDir, direction)
				}
				if !tt.shouldError && tt.pairString != ">:" && host == "" {
					t.Errorf("Expected non-empty host for valid syntax '%s'", tt.pairString)
				}
			})
		}
	})

	t.Run("ignores pattern validation", func(t *testing.T) {
		// Test the documented requirement: "Write tests for ignores parsing and matching different patterns
		// (/regexp/, *.ext, exact)"
		tests := []struct {
			name        string
			pattern     string
			testPath    string
			shouldMatch bool
		}{
			{"regexp pattern", "/\\.tmp$/", "file.tmp", true},
			{"regexp pattern no match", "/\\.tmp$/", "file.txt", false},
			{"extension pattern", "*.log", "debug.log", true},
			{"extension pattern no match", "*.log", "debug.txt", false},
			{"exact pattern", "temp.txt", "temp.txt", true},
			{"exact pattern no match", "temp.txt", "temp.log", false},
			{"directory pattern", "node_modules/", "node_modules/package.json", true},
			{"hidden file pattern", ".*", ".hidden", true},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				matcher, err := config.NewIgnoredPathMatcher([]string{tt.pattern})
				if err != nil {
					t.Errorf("Failed to create matcher for pattern '%s': %v", tt.pattern, err)
					return
				}

				isIgnored := matcher.IsIgnored(tt.testPath)
				if isIgnored != tt.shouldMatch {
					t.Errorf("Pattern '%s' with path '%s': expected match=%v, got=%v",
						tt.pattern, tt.testPath, tt.shouldMatch, isIgnored)
				}
			})
		}
	})
}

// TestConfigurationDefaults tests default values as documented in section 4.1
func TestConfigurationDefaults(t *testing.T) {
	t.Run("default values", func(t *testing.T) {
		// Test the documented requirement: "Handle default values for optional configurations"
		cfg := config.NewDefaultConfig()

		// Test documented defaults
		if cfg.Folder.Writethrough != false {
			t.Errorf("Expected default writethrough=false, got %v", cfg.Folder.Writethrough)
		}

		if cfg.Folder.Readonly != false {
			t.Errorf("Expected default readonly=false, got %v", cfg.Folder.Readonly)
		}

		if cfg.Debug != false {
			t.Errorf("Expected default debug=false, got %v", cfg.Debug)
		}

		if cfg.Folder.Compression != types.COMPRESSION_NONE {
			t.Errorf("Expected default compress=none, got %v", cfg.Folder.Compression)
		}

		// Default concurrent should be based on CPU cores but within valid range
		if cfg.Folder.Concurrent < 1 || cfg.Folder.Concurrent > 1024 {
			t.Errorf("Expected default concurrent in range 1-1024, got %d", cfg.Folder.Concurrent)
		}

		if cfg.Folder.Compares != types.SIZE_ONLY {
			t.Errorf("Expected default compares=size_only, got %v", cfg.Folder.Compares)
		}
	})
}

// TestYAMLConfigParsing tests YAML configuration parsing as documented
func TestYAMLConfigParsing(t *testing.T) {
	t.Run("basic YAML parsing", func(t *testing.T) {
		// Test the documented requirement: "Write tests for parsing a basic YAML config file"
		yamlContent := `
server:
  listen: "0.0.0.0:8080"
  auth_token: "test-token-12345678901234567890123456"

folder:
  sync_path: "/opt/sync"
  meta_path: "/opt/meta"
  logs_path: "/opt/logs"
  writethrough: false
  readonly: false
  debug: false
  concurrent: 4
  compares: "SIZE_ONLY"
  compression: "NONE"
  ignores:
    - pattern: "*.tmp"
      type: "extension"
    - pattern: "\\.git"
      type: "regexp"
    - pattern: "node_modules"
      type: "exact"

pairs:
  - name: "server-100"
    host: "*************"
    direction: "ONE_WAY_TO_PEER"
    initial_sync_strategy: "SYNC"
    remote_port: 8080
  - name: "server-101"
    host: "*************"
    direction: "ONE_WAY_FROM_PEER"
    initial_sync_strategy: "SYNC"
    remote_port: 8080
  - name: "server-102"
    host: "*************"
    direction: "BIDIRECTIONAL_NEWEST_WIN"
    initial_sync_strategy: "SYNC"
    remote_port: 8080

monitor:
  socket_path: "/tmp/folder-sync.sock"
`

		// Create temporary config file
		tmpDir := t.TempDir()
		configFile := filepath.Join(tmpDir, "test_config.yaml")
		err := os.WriteFile(configFile, []byte(yamlContent), 0644)
		if err != nil {
			t.Fatal(err)
		}

		// Parse configuration
		cfg, err := config.LoadFromFile(configFile)
		if err != nil {
			t.Errorf("Failed to parse YAML config: %v", err)
			return
		}

		// Verify parsed values
		if cfg.Server.ListenAddress != "0.0.0.0:8080" {
			t.Errorf("Expected listen '0.0.0.0:8080', got '%s'", cfg.Server.ListenAddress)
		}

		if cfg.Folder.SyncPath != "/opt/sync" {
			t.Errorf("Expected sync_path '/opt/sync', got '%s'", cfg.Folder.SyncPath)
		}

		if cfg.Folder.Concurrent != 4 {
			t.Errorf("Expected concurrent 4, got %d", cfg.Folder.Concurrent)
		}

		if len(cfg.Pairs) != 3 {
			t.Errorf("Expected 3 pairs, got %d", len(cfg.Pairs))
		}

		if len(cfg.Folder.Ignores) != 3 {
			t.Errorf("Expected 3 ignore patterns, got %d", len(cfg.Folder.Ignores))
		}

		// Verify first pair configuration
		if cfg.Pairs[0].Name != "server-100" {
			t.Errorf("Expected first pair name 'server-100', got '%s'", cfg.Pairs[0].Name)
		}
		if cfg.Pairs[0].Direction != types.ONE_WAY_TO_PEER {
			t.Errorf("Expected first pair direction ONE_WAY_TO_PEER, got %v", cfg.Pairs[0].Direction)
		}

		if cfg.Monitor.SocketPath != "/tmp/folder-sync.sock" {
			t.Errorf("Expected socket_path '/tmp/folder-sync.sock', got '%s'", cfg.Monitor.SocketPath)
		}
	})

	t.Run("invalid YAML handling", func(t *testing.T) {
		// Test handling of invalid YAML
		invalidYaml := `
server:
  listen: "0.0.0.0:8080"
  auth_token: "test-token"
folder:
  sync_path: "/opt/sync"
  # Missing closing quote
  meta_path: "/opt/meta
`

		tmpDir := t.TempDir()
		configFile := filepath.Join(tmpDir, "invalid_config.yaml")
		err := os.WriteFile(configFile, []byte(invalidYaml), 0644)
		if err != nil {
			t.Fatal(err)
		}

		// Should fail to parse
		_, err = config.LoadFromFile(configFile)
		if err == nil {
			t.Error("Expected error parsing invalid YAML")
		}
	})
}

// TestCommandLineOverrides tests CLI argument overrides as documented
func TestCommandLineOverrides(t *testing.T) {
	t.Run("debug flag override", func(t *testing.T) {
		// Test the documented requirement: "Write tests for command-line argument parsing
		// and how they override config values"

		// Create base config with debug=false
		cfg := &config.Config{}
		cfg.Debug = false

		// Simulate -v flag
		debugFlag := true
		config.ApplyDebugOverride(cfg, debugFlag)

		if !cfg.Debug {
			t.Error("Expected debug flag to override config value")
		}
	})

	t.Run("config file override", func(t *testing.T) {
		// Test -c flag for specifying config file
		customConfigPath := "/custom/path/config.yaml"

		configPath := config.GetConfigPath(customConfigPath)
		if configPath != customConfigPath {
			t.Errorf("Expected config path '%s', got '%s'", customConfigPath, configPath)
		}

		// Test default config path when no -c flag
		defaultPath := config.GetConfigPath("")
		expectedDefault := "/etc/folder_sync.ini"
		if defaultPath != expectedDefault {
			t.Errorf("Expected default config path '%s', got '%s'", expectedDefault, defaultPath)
		}
	})
}
