package config

import (
	"testing"
)

// TestDefaultIgnorePatterns tests that default ignore patterns are set correctly
func TestDefaultIgnorePatterns(t *testing.T) {
	cfg := NewDefaultConfig()

	// Verify that default ignore patterns are set
	if len(cfg.Folder.Ignores) == 0 {
		t.Fatal("Expected default ignore patterns to be set, but got none")
	}

	// Create pattern matcher
	matcher, err := cfg.NewPatternMatcher()
	if err != nil {
		t.Fatalf("Failed to create pattern matcher: %v", err)
	}

	// Test cases for files that should be ignored
	ignoredFiles := []string{
		// Vim files
		".test.txt.swp",
		".test.txt.swo", 
		"test.txt~",
		".test.txt~",
		
		// Temporary files
		"temp.tmp",
		"temp.temp",
		".#lockfile",
		"#autosave#",
		
		// System files
		".DS_Store",
		"Thumbs.db",
		"desktop.ini",
		
		// Version control
		".git",
		".svn",
		".hg",
	}

	for _, file := range ignoredFiles {
		if !matcher.IsIgnored(file) {
			t.Errorf("Expected file %q to be ignored by default patterns, but it wasn't", file)
		}
	}

	// Test cases for files that should NOT be ignored
	allowedFiles := []string{
		"test.txt",
		"document.doc",
		"image.jpg",
		"script.sh",
		"config.yaml",
		"README.md",
		"main.go",
		"data.json",
	}

	for _, file := range allowedFiles {
		if matcher.IsIgnored(file) {
			t.Errorf("Expected file %q to NOT be ignored by default patterns, but it was", file)
		}
	}
}

// TestCustomIgnorePatternsPreserved tests that custom ignore patterns are preserved
func TestCustomIgnorePatternsPreserved(t *testing.T) {
	cfg := &Config{}
	
	// Set custom ignore patterns first
	cfg.Folder.Ignores = []struct {
		Pattern string `yaml:"pattern"`
		Type    string `yaml:"type"`
	}{
		{Pattern: "*.log", Type: "extension"},
		{Pattern: "custom.txt", Type: "exact"},
	}

	// Call setDefaults - should NOT override existing patterns
	cfg.setDefaults()

	// Verify custom patterns are preserved
	if len(cfg.Folder.Ignores) != 2 {
		t.Errorf("Expected 2 custom ignore patterns to be preserved, got %d", len(cfg.Folder.Ignores))
	}

	// Create pattern matcher
	matcher, err := cfg.NewPatternMatcher()
	if err != nil {
		t.Fatalf("Failed to create pattern matcher: %v", err)
	}

	// Test that custom patterns work
	if !matcher.IsIgnored("test.log") {
		t.Error("Expected custom pattern *.log to ignore test.log")
	}
	if !matcher.IsIgnored("custom.txt") {
		t.Error("Expected custom pattern custom.txt to be ignored")
	}

	// Test that default vim patterns are NOT applied when custom patterns exist
	if matcher.IsIgnored(".test.swp") {
		t.Error("Expected default vim patterns to NOT be applied when custom patterns exist")
	}
}

// TestVimFilePatterns specifically tests vim-related ignore patterns
func TestVimFilePatterns(t *testing.T) {
	cfg := NewDefaultConfig()
	matcher, err := cfg.NewPatternMatcher()
	if err != nil {
		t.Fatalf("Failed to create pattern matcher: %v", err)
	}

	// Test various vim file patterns
	vimFiles := []struct {
		file     string
		expected bool
		desc     string
	}{
		{".test.txt.swp", true, "vim swap file"},
		{".test.txt.swo", true, "vim swap file variant"},
		{"test.txt~", true, "vim backup file"},
		{".test.txt~", true, "hidden vim backup file"},
		{"test.txt", false, "normal text file"},
		{"test.swp", false, "file ending with swp but not vim pattern"},
		{"~test.txt", false, "file starting with ~ but not vim pattern"},
	}

	for _, tc := range vimFiles {
		actual := matcher.IsIgnored(tc.file)
		if actual != tc.expected {
			t.Errorf("File %q (%s): expected ignored=%v, got ignored=%v", 
				tc.file, tc.desc, tc.expected, actual)
		}
	}
}

// TestEventDeduplication tests the event deduplication logic
func TestEventDeduplication(t *testing.T) {
	// This test would need to be in the sync package, but we can test the concept here
	// by testing that the deduplication window logic works as expected
	
	// Note: The actual deduplication test should be in pkg/sync/sync_test.go
	// This is just a placeholder to document the expected behavior
	t.Log("Event deduplication should prevent multiple events for the same file within 500ms")
	t.Log("This test should be implemented in pkg/sync/sync_test.go")
}
