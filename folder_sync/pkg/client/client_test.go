package client

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"io"
	"net"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/real-rm/folder_sync/pkg/types"
)

// MockDaemon represents a mock folder-sync daemon listening on a Unix socket.
// It simulates the behavior of the daemon's proxy socket.
type MockDaemon struct {
	socketPath string
	listener   net.Listener
	event<PERSON>han  chan types.FileSyncEvent // Events received from the client
	chunkChan  chan types.FileChunk     // Chunks received from the client
	done       chan struct{}
	wg         sync.WaitGroup
	t          *testing.T
}

// NewMockDaemon creates and starts a new MockDaemon.
func NewMockDaemon(t *testing.T, socketPath string) *MockDaemon {
	md := &MockDaemon{
		socketPath: socketPath,
		eventChan:  make(chan types.FileSyncEvent, 10), // Buffered
		chunkChan:  make(chan types.FileChunk, 10),     // Buffered
		done:       make(chan struct{}),
		t:          t,
	}

	if err := os.RemoveAll(socketPath); err != nil {
		t.Fatalf("Failed to clean up old socket: %v", err)
	}

	listener, err := net.Listen("unix", socketPath)
	if err != nil {
		t.Fatalf("Failed to listen on mock socket %q: %v", socketPath, err)
	}
	md.listener = listener

	md.wg.Add(1)
	go md.start()

	return md
}

// start begins accepting connections.
func (md *MockDaemon) start() {
	defer md.wg.Done()
	for {
		conn, err := md.listener.Accept()
		if err != nil {
			select {
			case <-md.done:
				return // Listener closed gracefully
			default:
				md.t.Errorf("MockDaemon accept error: %v", err)
				return
			}
		}
		md.wg.Add(1)
		go md.handleConnection(conn)
	}
}

// handleConnection processes messages from a single client connection.
func (md *MockDaemon) handleConnection(conn net.Conn) {
	defer md.wg.Done()
	defer conn.Close()

	reader := newBufReader(conn)

	for {
		select {
		case <-md.done:
			return
		default:
			msgTypeByte, err := reader.ReadByte()
			if err != nil {
				if err != io.EOF {
					md.t.Errorf("MockDaemon failed to read message type: %v", err)
				}
				return // Client disconnected or error
			}
			msgType := types.MessageType(msgTypeByte)
			md.t.Logf("MockDaemon received message type: %s (0x%x)", msgType.String(), byte(msgType))

			if err := md.processMessage(conn, reader, msgType); err != nil {
				md.t.Errorf("MockDaemon error processing message %s: %v", msgType.String(), err)
				md.sendErrorResponse(conn, err.Error())
				return // Close connection on processing error
			}
		}
	}
}

// processMessage parses and handles a single message based on its type.
func (md *MockDaemon) processMessage(conn net.Conn, reader io.Reader, msgType types.MessageType) error {
	switch msgType {
	case types.MSG_PROXY_WELCOME_REQUEST:
		return md.handleWelcomeRequest(conn, reader)
	case types.MSG_PROXY_TEST_CONNECTION_REQUEST:
		return md.handleTestConnectionRequest(conn)
	case types.MSG_FILE_SYNC_EVENT:
		return md.handleFileSyncEvent(conn, reader)
	case types.MSG_FILE_CHUNK:
		return md.handleFileChunk(conn, reader)
	default:
		return fmt.Errorf("unsupported proxy socket message type: %s (0x%x)", msgType.String(), byte(msgType))
	}
}

// handleWelcomeRequest processes MSG_PROXY_WELCOME_REQUEST.
func (md *MockDaemon) handleWelcomeRequest(conn net.Conn, reader io.Reader) error {
	clientMessage, err := readString(reader)
	if err != nil {
		return fmt.Errorf("failed to read client message: %w", err)
	}
	md.t.Logf("MockDaemon received Welcome Request: %s", clientMessage)

	respBuf := &bytes.Buffer{}
	respBuf.WriteByte(byte(types.MSG_PROXY_WELCOME_RESPONSE))
	respBuf.WriteByte(0x01) // Success: true
	writeString(respBuf, "Welcome to mock folder-sync proxy!")

	if _, err := conn.Write(respBuf.Bytes()); err != nil {
		return fmt.Errorf("failed to send welcome response: %w", err)
	}
	return nil
}

// handleTestConnectionRequest processes MSG_PROXY_TEST_CONNECTION_REQUEST.
func (md *MockDaemon) handleTestConnectionRequest(conn net.Conn) error {
	respBuf := &bytes.Buffer{}
	respBuf.WriteByte(byte(types.MSG_PROXY_TEST_CONNECTION_RESPONSE))
	respBuf.WriteByte(0x01) // Success: true
	writeString(respBuf, "MockDaemon is responsive.")

	if _, err := conn.Write(respBuf.Bytes()); err != nil {
		return fmt.Errorf("failed to send test connection response: %w", err)
	}
	return nil
}

// handleFileSyncEvent processes MSG_FILE_SYNC_EVENT.
func (md *MockDaemon) handleFileSyncEvent(conn net.Conn, reader io.Reader) error {
	event, err := decodeFileSyncEvent(reader)
	if err != nil {
		return fmt.Errorf("failed to decode FileSyncEvent: %w", err)
	}
	md.eventChan <- *event

	md.t.Logf("MockDaemon received FileSyncEvent: %s", event.String())

	// Send MSG_PROXY_EVENT_ACKNOWLEDGE
	respBuf := &bytes.Buffer{}
	respBuf.WriteByte(byte(types.MSG_PROXY_EVENT_ACKNOWLEDGE))
	binary.Write(respBuf, binary.BigEndian, event.MessageID)
	writeString(respBuf, event.Path)
	respBuf.WriteByte(0x01)  // Success: true
	writeString(respBuf, "") // No error message

	if _, err := conn.Write(respBuf.Bytes()); err != nil {
		return fmt.Errorf("failed to send proxy event acknowledge: %w", err)
	}
	return nil
}

// handleFileChunk processes MSG_FILE_CHUNK.
func (md *MockDaemon) handleFileChunk(conn net.Conn, reader io.Reader) error {
	chunk, err := decodeFileChunk(reader)
	if err != nil {
		return fmt.Errorf("failed to decode FileChunk: %w", err)
	}
	md.chunkChan <- *chunk

	md.t.Logf("MockDaemon received FileChunk: %d/%d for %s (MsgID: %d)", chunk.ChunkIndex, chunk.TotalChunks, chunk.Path, chunk.MessageID)

	// Send MSG_PROXY_EVENT_ACKNOWLEDGE for the chunk
	respBuf := &bytes.Buffer{}
	respBuf.WriteByte(byte(types.MSG_PROXY_EVENT_ACKNOWLEDGE))
	binary.Write(respBuf, binary.BigEndian, chunk.MessageID) // Acknowledge with original MessageID
	writeString(respBuf, chunk.Path)
	respBuf.WriteByte(0x01)  // Success: true
	writeString(respBuf, "") // No error message

	if _, err := conn.Write(respBuf.Bytes()); err != nil {
		return fmt.Errorf("failed to send chunk acknowledge: %w", err)
	}
	return nil
}

// sendErrorResponse sends a generic MSG_ERROR.
func (md *MockDaemon) sendErrorResponse(conn net.Conn, errMsg string) {
	errBuf := &bytes.Buffer{}
	errBuf.WriteByte(byte(types.MSG_ERROR))
	binary.Write(errBuf, binary.BigEndian, uint16(0x01)) // Generic error code
	writeString(errBuf, errMsg)
	if _, err := conn.Write(errBuf.Bytes()); err != nil {
		md.t.Errorf("MockDaemon failed to send error response to client: %v", err)
	}
}

// Close stops the mock daemon.
func (md *MockDaemon) Close() {
	close(md.done)
	if err := md.listener.Close(); err != nil {
		md.t.Errorf("Failed to close mock daemon listener: %v", err)
	}
	md.wg.Wait()
	close(md.eventChan)
	close(md.chunkChan)
	md.t.Log("MockDaemon closed.")
}

// GetReceivedEvent retrieves a FileSyncEvent received by the mock daemon.
func (md *MockDaemon) GetReceivedEvent(timeout time.Duration) (types.FileSyncEvent, error) {
	select {
	case event := <-md.eventChan:
		return event, nil
	case <-time.After(timeout):
		return types.FileSyncEvent{}, fmt.Errorf("timeout waiting for FileSyncEvent")
	}
}

// GetReceivedChunk retrieves a FileChunk received by the mock daemon.
func (md *MockDaemon) GetReceivedChunk(timeout time.Duration) (types.FileChunk, error) {
	select {
	case chunk := <-md.chunkChan:
		return chunk, nil
	case <-time.After(timeout):
		return types.FileChunk{}, fmt.Errorf("timeout waiting for FileChunk")
	}
}

// --- Helper functions for binary encoding/decoding ---
// Note: Using functions from client.go to avoid duplication

func decodeFileSyncEvent(reader interface{}) (*types.FileSyncEvent, error) {
	// Convert to bufReader for consistent interface
	var bufR *bufReader
	if br, ok := reader.(*bufReader); ok {
		bufR = br
	} else {
		return nil, fmt.Errorf("reader must be *bufReader")
	}
	event := &types.FileSyncEvent{}
	var err error

	// MessageID (uint64)
	var messageID uint64
	if err = binary.Read(bufR, binary.BigEndian, &messageID); err != nil {
		return nil, fmt.Errorf("failed to read MessageID: %w", err)
	}
	event.MessageID = messageID

	// Origin (byte)
	originByte, err := bufR.ReadByte()
	if err != nil {
		return nil, fmt.Errorf("failed to read Origin: %w", err)
	}
	event.Origin = types.CommunicationOrigin(originByte)

	// EventType (byte)
	eventTypeByte, err := bufR.ReadByte()
	if err != nil {
		return nil, fmt.Errorf("failed to read EventType: %w", err)
	}
	event.Type = types.FileSyncEventType(eventTypeByte)

	// Path (string)
	event.Path, err = readString(bufR)
	if err != nil {
		return nil, fmt.Errorf("failed to read Path: %w", err)
	}

	// Timestamp (uint64 - Unix Nano)
	var timestampNanos int64
	if err = binary.Read(bufR, binary.BigEndian, &timestampNanos); err != nil {
		return nil, fmt.Errorf("failed to read Timestamp: %w", err)
	}
	event.Timestamp = time.Unix(0, timestampNanos)

	// Size (uint64)
	var size uint64
	if err = binary.Read(bufR, binary.BigEndian, &size); err != nil {
		return nil, fmt.Errorf("failed to read Size: %w", err)
	}
	event.Size = size

	// FileMode (uint32)
	var fileMode uint32
	if err = binary.Read(bufR, binary.BigEndian, &fileMode); err != nil {
		return nil, fmt.Errorf("failed to read FileMode: %w", err)
	}
	event.FileMode = fileMode

	// OwnerUID (uint32)
	var ownerUID uint32
	if err = binary.Read(bufR, binary.BigEndian, &ownerUID); err != nil {
		return nil, fmt.Errorf("failed to read OwnerUID: %w", err)
	}
	event.OwnerUID = ownerUID

	// OwnerGID (uint32)
	var ownerGID uint32
	if err = binary.Read(bufR, binary.BigEndian, &ownerGID); err != nil {
		return nil, fmt.Errorf("failed to read OwnerGID: %w", err)
	}
	event.OwnerGID = ownerGID

	// FileCompareStrategy (byte) - This is read from the stream but not stored in FileSyncEvent
	_, err = bufR.ReadByte() // Discard, as it's for sender's context
	if err != nil {
		return nil, fmt.Errorf("failed to read FileCompareStrategy: %w", err)
	}

	// Checksum ([]byte)
	event.Checksum, err = readBytes(bufR)
	if err != nil {
		return nil, fmt.Errorf("failed to read Checksum: %w", err)
	}

	// SendsChunks (bool)
	sendsChunksByte, err := bufR.ReadByte()
	if err != nil {
		return nil, fmt.Errorf("failed to read SendsChunks: %w", err)
	}
	event.SendsChunks = (sendsChunksByte != 0x00)

	// IsCompressed (bool)
	isCompressedByte, err := bufR.ReadByte()
	if err != nil {
		return nil, fmt.Errorf("failed to read IsCompressed: %w", err)
	}
	event.IsCompressed = (isCompressedByte != 0x00)

	// FileBody ([]byte)
	event.FileBody, err = readBytes(bufR)
	if err != nil {
		return nil, fmt.Errorf("failed to read FileBody: %w", err)
	}

	return event, nil
}

// decodeFileChunk decodes a MSG_FILE_CHUNK payload.
func decodeFileChunk(reader interface{}) (*types.FileChunk, error) {
	// Convert to bufReader for consistent interface
	var bufR *bufReader
	if br, ok := reader.(*bufReader); ok {
		bufR = br
	} else {
		return nil, fmt.Errorf("reader must be *bufReader")
	}
	chunk := &types.FileChunk{}
	var err error

	// MessageID (uint64)
	if err = binary.Read(bufR, binary.BigEndian, &chunk.MessageID); err != nil {
		return nil, fmt.Errorf("failed to read chunk MessageID: %w", err)
	}

	// Path (string)
	chunk.Path, err = readString(bufR)
	if err != nil {
		return nil, fmt.Errorf("failed to read chunk Path: %w", err)
	}

	// ChunkIndex (uint32)
	if err = binary.Read(bufR, binary.BigEndian, &chunk.ChunkIndex); err != nil {
		return nil, fmt.Errorf("failed to read ChunkIndex: %w", err)
	}

	// TotalChunks (uint32)
	if err = binary.Read(bufR, binary.BigEndian, &chunk.TotalChunks); err != nil {
		return nil, fmt.Errorf("failed to read TotalChunks: %w", err)
	}

	// IsCompressed (bool)
	isCompressedByte, err := bufR.ReadByte()
	if err != nil {
		return nil, fmt.Errorf("failed to read IsCompressed: %w", err)
	}
	chunk.IsCompressed = (isCompressedByte != 0x00)

	// ChunkData ([]byte)
	chunk.ChunkData, err = readBytes(bufR)
	if err != nil {
		return nil, fmt.Errorf("failed to read ChunkData: %w", err)
	}

	return chunk, nil
}

// --- Tests for Client Package ---

func TestClient_Initialize(t *testing.T) {
	daemonSockPath := filepath.Join(os.TempDir(), "test_daemon_sock_init")
	daemon := NewMockDaemon(t, daemonSockPath)
	defer daemon.Close()

	pairPrefix := "/mnt/sync_folder"
	testClient, err := NewClient(daemonSockPath, pairPrefix)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer testClient.Close()

	if testClient.proxySocketPath != daemonSockPath {
		t.Errorf("Expected proxySocketPath to be %q, got %q", daemonSockPath, testClient.proxySocketPath)
	}
	if testClient.folderPrefix != pairPrefix {
		t.Errorf("Expected folderPrefix to be %q, got %q", pairPrefix, testClient.folderPrefix)
	}

	// Verify welcome handshake
	// The mock daemon handles the handshake internally.
	// We can check if the daemon received the welcome request by peaking into its logs
	// or by ensuring the client connection is active.
	// For now, if NewClient returns without error, we assume welcome was successful.
}

func TestClient_TestConnection(t *testing.T) {
	daemonSockPath := filepath.Join(os.TempDir(), "test_daemon_sock_test_conn")
	daemon := NewMockDaemon(t, daemonSockPath)
	defer daemon.Close()

	testClient, err := NewClient(daemonSockPath, "/mnt/sync_folder")
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer testClient.Close()

	isConnected, err := testClient.TestConnection()
	if err != nil {
		t.Fatalf("TestConnection failed: %v", err)
	}
	if !isConnected {
		t.Errorf("Expected TestConnection to return true, got false")
	}
}

func TestClient_CreateFile(t *testing.T) {
	daemonSockPath := filepath.Join(os.TempDir(), "test_daemon_sock_create_file")
	daemon := NewMockDaemon(t, daemonSockPath)
	defer daemon.Close()

	syncFolder := filepath.Join(os.TempDir(), "client_test_sync")
	os.MkdirAll(syncFolder, 0755)
	defer os.RemoveAll(syncFolder)

	testClient, err := NewClient(daemonSockPath, syncFolder)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer testClient.Close()

	filePath := filepath.Join(syncFolder, "newfile.txt")
	relPath := "newfile.txt"
	content := []byte("test content for new file")

	err = testClient.WriteFile(filePath, content, 0644)
	if err != nil {
		t.Fatalf("Client.WriteFile failed: %v", err)
	}

	// Verify daemon received the FileSyncEvent
	receivedEvent, err := daemon.GetReceivedEvent(2 * time.Second)
	if err != nil {
		t.Fatal(err)
	}

	if receivedEvent.Type != types.CREATE_FILE && receivedEvent.Type != types.WRITE_FILE { // Create or Write is acceptable for new file
		t.Errorf("Expected event type CREATE_FILE/WRITE_FILE, got %s", receivedEvent.Type)
	}
	if receivedEvent.Path != relPath {
		t.Errorf("Expected path %q, got %q", relPath, receivedEvent.Path)
	}
	if !bytes.Equal(receivedEvent.FileBody, content) {
		t.Errorf("File body mismatch. Expected %q, got %q", string(content), string(receivedEvent.FileBody))
	}
	if receivedEvent.Origin != types.PROXY_CLIENT_WRITE {
		t.Errorf("Expected origin PROXY_CLIENT_WRITE, got %s", receivedEvent.Origin)
	}
	if receivedEvent.SendsChunks { // Should not send chunks for small file
		t.Errorf("Expected SendsChunks to be false, got true")
	}
}

func TestClient_WriteLargeFileChunked(t *testing.T) {
	daemonSockPath := filepath.Join(os.TempDir(), "test_daemon_sock_large_file")
	daemon := NewMockDaemon(t, daemonSockPath)
	defer daemon.Close()

	syncFolder := filepath.Join(os.TempDir(), "client_test_large_file_sync")
	os.MkdirAll(syncFolder, 0755)
	defer os.RemoveAll(syncFolder)

	// Set a small chunk size for testing large files
	testClient, err := NewClient(daemonSockPath, syncFolder)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	testClient.chunkSize = 10 // Small chunk size for testing
	defer testClient.Close()

	filePath := filepath.Join(syncFolder, "largefile.bin")
	relPath := "largefile.bin"
	content := make([]byte, 25) // 25 bytes, will be 3 chunks with 10 byte chunk size
	for i := 0; i < len(content); i++ {
		content[i] = byte('A' + (i % 26))
	}

	err = testClient.WriteFile(filePath, content, 0644)
	if err != nil {
		t.Fatalf("Client.WriteFile failed for large file: %v", err)
	}

	// Verify daemon received the initial FileSyncEvent
	receivedEvent, err := daemon.GetReceivedEvent(2 * time.Second)
	if err != nil {
		t.Fatal(err)
	}
	if receivedEvent.Type != types.CREATE_FILE && receivedEvent.Type != types.WRITE_FILE {
		t.Errorf("Expected event type CREATE_FILE/WRITE_FILE, got %s", receivedEvent.Type)
	}
	if receivedEvent.Path != relPath {
		t.Errorf("Expected path %q, got %q", relPath, receivedEvent.Path)
	}
	if receivedEvent.FileBody != nil && len(receivedEvent.FileBody) > 0 {
		t.Errorf("Expected initial FileSyncEvent to have empty FileBody, got %d bytes", len(receivedEvent.FileBody))
	}
	if !receivedEvent.SendsChunks {
		t.Errorf("Expected SendsChunks to be true, got false")
	}

	// Verify daemon received chunks
	expectedChunks := [][]byte{
		content[0:10],
		content[10:20],
		content[20:25],
	}

	for i, expectedChunkData := range expectedChunks {
		receivedChunk, err := daemon.GetReceivedChunk(2 * time.Second)
		if err != nil {
			t.Fatalf("Failed to receive chunk %d: %v", i, err)
		}
		if receivedChunk.Path != relPath {
			t.Errorf("Chunk %d: Expected path %q, got %q", i, relPath, receivedChunk.Path)
		}
		if receivedChunk.ChunkIndex != uint32(i) {
			t.Errorf("Chunk %d: Expected index %d, got %d", i, i, receivedChunk.ChunkIndex)
		}
		if receivedChunk.TotalChunks != uint32(len(expectedChunks)) {
			t.Errorf("Chunk %d: Expected total chunks %d, got %d", i, len(expectedChunks), receivedChunk.TotalChunks)
		}
		if !bytes.Equal(receivedChunk.ChunkData, expectedChunkData) {
			t.Errorf("Chunk %d: Data mismatch.\nExpected: %q\nGot:      %q", i, string(expectedChunkData), string(receivedChunk.ChunkData))
		}
	}
}

func TestClient_RemoveFile(t *testing.T) {
	daemonSockPath := filepath.Join(os.TempDir(), "test_daemon_sock_remove_file")
	daemon := NewMockDaemon(t, daemonSockPath)
	defer daemon.Close()

	syncFolder := filepath.Join(os.TempDir(), "client_test_sync_remove")
	os.MkdirAll(syncFolder, 0755)
	defer os.RemoveAll(syncFolder)

	// Create a dummy file locally for the Remove operation to act on
	dummyFile := filepath.Join(syncFolder, "file_to_remove.txt")
	os.WriteFile(dummyFile, []byte("dummy"), 0644)

	testClient, err := NewClient(daemonSockPath, syncFolder)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer testClient.Close()

	relPath := "file_to_remove.txt"
	err = testClient.Remove(dummyFile)
	if err != nil {
		t.Fatalf("Client.Remove failed: %v", err)
	}

	// Verify daemon received the FileSyncEvent for deletion
	receivedEvent, err := daemon.GetReceivedEvent(2 * time.Second)
	if err != nil {
		t.Fatal(err)
	}

	if receivedEvent.Type != types.DELETE_FILE {
		t.Errorf("Expected event type DELETE_FILE, got %s", receivedEvent.Type)
	}
	if receivedEvent.Path != relPath {
		t.Errorf("Expected path %q, got %q", relPath, receivedEvent.Path)
	}
	if receivedEvent.Origin != types.PROXY_CLIENT_WRITE {
		t.Errorf("Expected origin PROXY_CLIENT_WRITE, got %s", receivedEvent.Origin)
	}
}

func TestClient_CreateDir(t *testing.T) {
	daemonSockPath := filepath.Join(os.TempDir(), "test_daemon_sock_create_dir")
	daemon := NewMockDaemon(t, daemonSockPath)
	defer daemon.Close()

	syncFolder := filepath.Join(os.TempDir(), "client_test_sync_dir")
	os.MkdirAll(syncFolder, 0755)
	defer os.RemoveAll(syncFolder)

	testClient, err := NewClient(daemonSockPath, syncFolder)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer testClient.Close()

	dirPath := filepath.Join(syncFolder, "new_dir")
	relPath := "new_dir"

	err = testClient.CreateDir(dirPath, 0755)
	if err != nil {
		t.Fatalf("Client.CreateDir failed: %v", err)
	}

	// Verify daemon received the FileSyncEvent
	receivedEvent, err := daemon.GetReceivedEvent(2 * time.Second)
	if err != nil {
		t.Fatal(err)
	}

	if receivedEvent.Type != types.CREATE_DIR {
		t.Errorf("Expected event type CREATE_DIR, got %s", receivedEvent.Type)
	}
	if receivedEvent.Path != relPath {
		t.Errorf("Expected path %q, got %q", relPath, receivedEvent.Path)
	}
	if receivedEvent.Origin != types.PROXY_CLIENT_WRITE {
		t.Errorf("Expected origin PROXY_CLIENT_WRITE, got %s", receivedEvent.Origin)
	}
}

func TestClient_Chmod(t *testing.T) {
	daemonSockPath := filepath.Join(os.TempDir(), "test_daemon_sock_chmod")
	daemon := NewMockDaemon(t, daemonSockPath)
	defer daemon.Close()

	syncFolder := filepath.Join(os.TempDir(), "client_test_sync_chmod")
	os.MkdirAll(syncFolder, 0755)
	defer os.RemoveAll(syncFolder)

	// Create a dummy file locally
	dummyFile := filepath.Join(syncFolder, "chmod_file.txt")
	os.WriteFile(dummyFile, []byte("dummy"), 0644)

	testClient, err := NewClient(daemonSockPath, syncFolder)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer testClient.Close()

	relPath := "chmod_file.txt"
	newMode := os.FileMode(0777)
	err = testClient.Chmod(dummyFile, newMode)
	if err != nil {
		t.Fatalf("Client.Chmod failed: %v", err)
	}

	// Verify daemon received the FileSyncEvent
	receivedEvent, err := daemon.GetReceivedEvent(2 * time.Second)
	if err != nil {
		t.Fatal(err)
	}

	if receivedEvent.Type != types.CHMOD_FILE {
		t.Errorf("Expected event type CHMOD_FILE, got %s", receivedEvent.Type)
	}
	if receivedEvent.Path != relPath {
		t.Errorf("Expected path %q, got %q", relPath, receivedEvent.Path)
	}
	if receivedEvent.FileMode != uint32(newMode.Perm()) { // .Perm() to strip file type bits
		t.Errorf("Expected file mode %o, got %o", newMode.Perm(), receivedEvent.FileMode)
	}
	if receivedEvent.Origin != types.PROXY_CLIENT_WRITE {
		t.Errorf("Expected origin PROXY_CLIENT_WRITE, got %s", receivedEvent.Origin)
	}
}

func TestClient_OperationsOutsideFolderPrefix(t *testing.T) {
	daemonSockPath := filepath.Join(os.TempDir(), "test_daemon_sock_outside_prefix")
	daemon := NewMockDaemon(t, daemonSockPath)
	defer daemon.Close()

	syncFolder := filepath.Join(os.TempDir(), "client_test_prefix")
	os.MkdirAll(syncFolder, 0755)
	defer os.RemoveAll(syncFolder)

	testClient, err := NewClient(daemonSockPath, syncFolder)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer testClient.Close()

	outsideFile := filepath.Join(os.TempDir(), "outside_file.txt")
	outsideDir := filepath.Join(os.TempDir(), "outside_dir")
	os.WriteFile(outsideFile, []byte("outside content"), 0644)
	os.Mkdir(outsideDir, 0755)
	defer os.Remove(outsideFile)
	defer os.Remove(outsideDir)

	// Test WriteFile outside prefix
	err = testClient.WriteFile(outsideFile, []byte("new content"), 0644)
	if err != nil {
		t.Fatalf("WriteFile for outside file failed: %v", err)
	}
	// Verify daemon did NOT receive an event
	select {
	case event := <-daemon.eventChan:
		t.Errorf("Received unexpected event for outside file: %s", event.String())
	case <-time.After(100 * time.Millisecond):
		// Expected: no event received
	}
	// Verify content was actually written locally by OS
	content, err := os.ReadFile(outsideFile)
	if err != nil {
		t.Fatalf("Failed to read outside file after write: %v", err)
	}
	if string(content) != "new content" {
		t.Errorf("Outside file content mismatch after direct OS write: %q", string(content))
	}

	// Test Remove outside prefix
	err = testClient.Remove(outsideFile)
	if err != nil {
		t.Fatalf("Remove for outside file failed: %v", err)
	}
	// Verify daemon did NOT receive an event
	select {
	case event := <-daemon.eventChan:
		t.Errorf("Received unexpected event for outside file removal: %s", event.String())
	case <-time.After(100 * time.Millisecond):
		// Expected: no event received
	}
	// Verify file was actually removed by OS
	_, err = os.Stat(outsideFile)
	if !os.IsNotExist(err) {
		t.Errorf("Expected outside file to be removed, but it still exists: %v", err)
	}

	// Test CreateDir outside prefix
	err = testClient.CreateDir(outsideDir, 0755)
	if err != nil {
		t.Fatalf("CreateDir for outside dir failed: %v", err)
	}
	// Verify daemon did NOT receive an event
	select {
	case event := <-daemon.eventChan:
		t.Errorf("Received unexpected event for outside dir creation: %s", event.String())
	case <-time.After(100 * time.Millisecond):
		// Expected: no event received
	}
	// Verify directory was actually created by OS
	info, err := os.Stat(outsideDir)
	if err != nil || !info.IsDir() {
		t.Errorf("Expected outside dir to be created, but got error: %v", err)
	}

	// Test ReadFile (should always proxy to OS)
	content, err = testClient.ReadFile(filepath.Join(syncFolder, "non_existent_file.txt"))
	if err == nil || !os.IsNotExist(err) {
		t.Errorf("ReadFile for non-existent file expected os.IsNotExist error, got %v, content: %q", err, string(content))
	}

	// Test Stat (should always proxy to OS)
	_, err = testClient.Stat(filepath.Join(syncFolder, "non_existent_file.txt"))
	if err == nil || !os.IsNotExist(err) {
		t.Errorf("Stat for non-existent file expected os.IsNotExist error, got %v", err)
	}

	// Test ReadDir (should always proxy to OS)
	_, err = testClient.ReadDir(filepath.Join(syncFolder, "non_existent_dir"))
	if err == nil || !os.IsNotExist(err) {
		t.Errorf("ReadDir for non-existent dir expected os.IsNotExist error, got %v", err)
	}
}

func TestClient_Close(t *testing.T) {
	daemonSockPath := filepath.Join(os.TempDir(), "test_daemon_sock_close")
	daemon := NewMockDaemon(t, daemonSockPath)
	defer daemon.Close()

	syncFolder := filepath.Join(os.TempDir(), "client_test_sync_close")
	os.MkdirAll(syncFolder, 0755)
	defer os.RemoveAll(syncFolder)

	testClient, err := NewClient(daemonSockPath, syncFolder)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}

	// Perform some operation to ensure connection is active
	testClient.WriteFile(filepath.Join(testClient.folderPrefix, "dummy.txt"), []byte("dummy"), 0644)
	_, err = daemon.GetReceivedEvent(1 * time.Second)
	if err != nil {
		t.Fatal("Dummy event not received before close test: ", err)
	}

	err = testClient.Close()
	if err != nil {
		t.Fatalf("Client.Close failed: %v", err)
	}

	// After closing, operations should fail with a "closed network connection" error
	err = testClient.WriteFile(filepath.Join(testClient.folderPrefix, "another.txt"), []byte("data"), 0644)
	if err == nil {
		t.Error("Expected WriteFile to fail after client close, but it succeeded.")
	}
	if !strings.Contains(err.Error(), "use of closed network connection") && !strings.Contains(err.Error(), "broken pipe") && !strings.Contains(err.Error(), "client connection is closed") {
		t.Errorf("Expected 'closed network connection', 'broken pipe', or 'client connection is closed' error, got: %v", err)
	}
}

func TestClient_Rename(t *testing.T) {
	daemonSockPath := filepath.Join(os.TempDir(), "test_daemon_sock_rename")
	daemon := NewMockDaemon(t, daemonSockPath)
	defer daemon.Close()

	syncFolder := filepath.Join(os.TempDir(), "client_test_sync_rename")
	os.MkdirAll(syncFolder, 0755)
	defer os.RemoveAll(syncFolder)

	oldPath := filepath.Join(syncFolder, "old_name.txt")
	newPath := filepath.Join(syncFolder, "new_name.txt")
	os.WriteFile(oldPath, []byte("original content"), 0644)

	testClient, err := NewClient(daemonSockPath, syncFolder)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer testClient.Close()

	err = testClient.Rename(oldPath, newPath)
	if err != nil {
		t.Fatalf("Client.Rename failed: %v", err)
	}

	// Verify daemon received a DELETE_FILE for old_name.txt
	deleteEvent, err := daemon.GetReceivedEvent(2 * time.Second)
	if err != nil {
		t.Fatal(err)
	}
	if deleteEvent.Type != types.DELETE_FILE {
		t.Errorf("Expected first event type DELETE_FILE, got %s", deleteEvent.Type)
	}
	if deleteEvent.Path != "old_name.txt" {
		t.Errorf("Expected first event path 'old_name.txt', got %q", deleteEvent.Path)
	}

	// Verify daemon received a CREATE_FILE for new_name.txt
	createEvent, err := daemon.GetReceivedEvent(2 * time.Second)
	if err != nil {
		t.Fatal(err)
	}
	if createEvent.Type != types.CREATE_FILE {
		t.Errorf("Expected second event type CREATE_FILE, got %s", createEvent.Type)
	}
	if createEvent.Path != "new_name.txt" {
		t.Errorf("Expected second event path 'new_name.txt', got %q", createEvent.Path)
	}
	if !bytes.Equal(createEvent.FileBody, []byte("original content")) {
		t.Errorf("New file content mismatch. Expected %q, got %q", "original content", string(createEvent.FileBody))
	}

	// Verify local OS operation (actual file moved)
	_, err = os.Stat(oldPath)
	if !os.IsNotExist(err) {
		t.Errorf("Expected old file %q to be gone, but it exists: %v", oldPath, err)
	}
	content, err := os.ReadFile(newPath)
	if err != nil {
		t.Fatalf("Failed to read new file %q: %v", newPath, err)
	}
	if string(content) != "original content" {
		t.Errorf("New file content mismatch locally: %q", string(content))
	}
}

func TestClient_CreateSymlink(t *testing.T) {
	daemonSockPath := filepath.Join(os.TempDir(), "test_daemon_sock_symlink")
	daemon := NewMockDaemon(t, daemonSockPath)
	defer daemon.Close()

	syncFolder := filepath.Join(os.TempDir(), "client_test_sync_symlink")
	os.MkdirAll(syncFolder, 0755)
	defer os.RemoveAll(syncFolder)

	targetFile := filepath.Join(syncFolder, "target.txt")
	os.WriteFile(targetFile, []byte("symlink target content"), 0644)

	testClient, err := NewClient(daemonSockPath, syncFolder)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer testClient.Close()

	symlinkPath := filepath.Join(syncFolder, "my_symlink")
	relPath := "my_symlink"
	targetRelPath := "target.txt" // Relative target path to sync folder

	err = testClient.CreateSymlink(targetRelPath, symlinkPath)
	if err != nil {
		t.Fatalf("Client.CreateSymlink failed: %v", err)
	}

	// Verify daemon received the FileSyncEvent
	receivedEvent, err := daemon.GetReceivedEvent(2 * time.Second)
	if err != nil {
		t.Fatal(err)
	}

	if receivedEvent.Type != types.CREATE_SYMLINK {
		t.Errorf("Expected event type CREATE_SYMLINK, got %s", receivedEvent.Type)
	}
	if receivedEvent.Path != relPath {
		t.Errorf("Expected path %q, got %q", relPath, receivedEvent.Path)
	}
	if string(receivedEvent.FileBody) != targetRelPath { // Symlink target in FileBody
		t.Errorf("Expected symlink target %q in FileBody, got %q", targetRelPath, string(receivedEvent.FileBody))
	}
	if receivedEvent.Origin != types.PROXY_CLIENT_WRITE {
		t.Errorf("Expected origin PROXY_CLIENT_WRITE, got %s", receivedEvent.Origin)
	}

	// Verify local OS operation (actual symlink created)
	linkedTarget, err := os.Readlink(symlinkPath)
	if err != nil {
		t.Fatalf("Failed to read local symlink: %v", err)
	}
	if linkedTarget != targetRelPath { // Readlink returns the string stored in symlink
		t.Errorf("Local symlink target mismatch. Expected %q, got %q", targetRelPath, linkedTarget)
	}
}
