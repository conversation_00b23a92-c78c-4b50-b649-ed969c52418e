package client

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"syscall"
	"time"

	"net" // Import net for Unix socket communication

	"github.com/real-rm/folder_sync/pkg/types"
)

// Client provides an API for external Go programs to interact with the
// folder-sync daemon's proxy socket.
type Client struct {
	proxySocketPath string
	folderPrefix    string // The sync folder prefix for this client's operations
	conn            net.Conn
	mu              sync.Mutex // Protects access to the connection
	idCounter       uint64     // Counter for generating unique MessageIDs
	chunkSize       int        // Configurable chunk size for large file transfers
}

// NewClient creates and initializes a new folder-sync client.
// It establishes a connection to the daemon's proxy socket and performs a welcome handshake.
// `proxySocketPath` is the path to the Unix domain socket.
// `folderPrefix` is the absolute path to the sync folder this client is associated with.
func NewClient(proxySocketPath, folderPrefix string) (*Client, error) {
	conn, err := net.Dial("unix", proxySocketPath)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to proxy socket %q: %w", proxySocketPath, err)
	}

	client := &Client{
		proxySocketPath: proxySocketPath,
		folderPrefix:    folderPrefix,
		conn:            conn,
		chunkSize:       types.DefaultChunkSize,
	}

	// Perform welcome handshake
	if err := client.sendWelcomeRequest(); err != nil {
		client.Close() // Close connection on handshake failure
		return nil, fmt.Errorf("failed to perform welcome handshake with daemon: %w", err)
	}

	return client, nil
}

// Close closes the client's connection to the proxy socket.
func (c *Client) Close() error {
	c.mu.Lock()
	defer c.mu.Unlock()
	if c.conn != nil {
		err := c.conn.Close()
		c.conn = nil // Mark as closed
		return err
	}
	return nil
}

// TestConnection sends a test connection request to the daemon and returns true if responsive.
func (c *Client) TestConnection() (bool, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.conn == nil {
		return false, fmt.Errorf("client connection is closed")
	}

	// Send MSG_PROXY_TEST_CONNECTION_REQUEST
	reqBuf := &bytes.Buffer{}
	reqBuf.WriteByte(byte(types.MSG_PROXY_TEST_CONNECTION_REQUEST))
	if _, err := c.conn.Write(reqBuf.Bytes()); err != nil {
		return false, fmt.Errorf("failed to send test connection request: %w", err)
	}

	// Read MSG_PROXY_TEST_CONNECTION_RESPONSE
	respBuf := make([]byte, 1024)      // Enough for response header + small message
	reader := bytes.NewReader(respBuf) // Use bytes.Reader for parsing, not io.Reader directly

	// Read raw bytes from connection into buffer first
	n, err := c.conn.Read(respBuf)
	if err != nil {
		return false, fmt.Errorf("failed to read test connection response: %w", err)
	}
	reader.Reset(respBuf[:n]) // Reset reader to new data slice

	msgTypeByte, err := reader.ReadByte()
	if err != nil {
		return false, fmt.Errorf("failed to read message type from test connection response: %w", err)
	}
	if types.MessageType(msgTypeByte) != types.MSG_PROXY_TEST_CONNECTION_RESPONSE {
		return false, fmt.Errorf("expected MSG_PROXY_TEST_CONNECTION_RESPONSE, got 0x%x", msgTypeByte)
	}

	successByte, err := reader.ReadByte()
	if err != nil {
		return false, fmt.Errorf("failed to read success byte from test connection response: %w", err)
	}
	success := (successByte != 0x00)

	_, err = readString(reader) // Read and discard status message
	if err != nil {
		return false, fmt.Errorf("failed to read message from test connection response: %w", err)
	}

	return success, nil
}

// WriteFile writes data to a file at the given path with specified permissions.
// If the path is within the client's folder prefix, the operation is proxied to the daemon.
// Otherwise, it performs a direct OS write.
func (c *Client) WriteFile(path string, data []byte, perm os.FileMode) error {
	if c.isPathWithinPrefix(path) {
		return c.sendFileSyncEvent(types.WRITE_FILE, path, data, perm, nil, false)
	}
	// Operation outside prefix, perform directly using OS
	return os.WriteFile(path, data, perm)
}

// Remove removes a file or directory at the given path.
// If the path is within the client's folder prefix, the operation is proxied to the daemon.
// Otherwise, it performs a direct OS remove.
func (c *Client) Remove(path string) error {
	if c.isPathWithinPrefix(path) {
		// Determine if it's a file or directory for the event type
		info, err := os.Stat(path)
		eventType := types.DELETE_FILE
		if err == nil && info.IsDir() {
			eventType = types.DELETE_DIR
		} else if err != nil && !os.IsNotExist(err) {
			return fmt.Errorf("failed to stat %q for remove operation: %w", path, err)
		}
		return c.sendFileSyncEvent(eventType, path, nil, 0, nil, false)
	}
	// Operation outside prefix, perform directly using OS
	return os.RemoveAll(path) // Use RemoveAll for directories
}

// CreateDir creates a new directory at the given path with specified permissions.
// If the path is within the client's folder prefix, the operation is proxied to the daemon.
// Otherwise, it performs a direct OS mkdir.
func (c *Client) CreateDir(path string, perm os.FileMode) error {
	if c.isPathWithinPrefix(path) {
		return c.sendFileSyncEvent(types.CREATE_DIR, path, nil, perm, nil, false)
	}
	return os.MkdirAll(path, perm)
}

// Chmod changes the permissions of the file or directory at the given path.
// If the path is within the client's folder prefix, the operation is proxied to the daemon.
// Otherwise, it performs a direct OS chmod.
func (c *Client) Chmod(path string, perm os.FileMode) error {
	if c.isPathWithinPrefix(path) {
		// Perform local chmod first to ensure it succeeds before sending event
		if err := os.Chmod(path, perm); err != nil {
			return fmt.Errorf("local chmod failed: %w", err)
		}
		return c.sendFileSyncEvent(types.CHMOD_FILE, path, nil, perm, nil, false)
	}
	return os.Chmod(path, perm)
}

// Rename renames (moves) a file or directory from oldPath to newPath.
// Both paths must be within the client's folder prefix for the operation to be proxied.
// Otherwise, it performs a direct OS rename.
// The daemon will receive a DELETE for oldPath and a CREATE for newPath.
func (c *Client) Rename(oldPath, newPath string) error {
	if c.isPathWithinPrefix(oldPath) && c.isPathWithinPrefix(newPath) {
		// Perform local rename first to ensure it succeeds before sending events
		if err := os.Rename(oldPath, newPath); err != nil {
			return fmt.Errorf("local rename failed: %w", err)
		}

		// Send a DELETE event for the old path
		if err := c.sendFileSyncEvent(types.DELETE_FILE, oldPath, nil, 0, nil, false); err != nil {
			return fmt.Errorf("failed to send delete event for old path %q: %w", oldPath, err)
		}

		// Send a CREATE/WRITE event for the new path with its new content/metadata
		info, err := os.Stat(newPath)
		if err != nil {
			return fmt.Errorf("failed to stat new path %q after rename: %w", newPath, err)
		}
		var fileBody []byte
		if !info.IsDir() {
			fileBody, err = os.ReadFile(newPath)
			if err != nil {
				return fmt.Errorf("failed to read content of new path %q after rename: %w", newPath, err)
			}
		}
		eventType := types.CREATE_FILE
		if info.IsDir() {
			eventType = types.CREATE_DIR
		}
		return c.sendFileSyncEvent(eventType, newPath, fileBody, info.Mode().Perm(), nil, true) // Pass true for isRename so it can adjust MessageID if needed
	}
	return os.Rename(oldPath, newPath)
}

// CreateSymlink creates a new symbolic link (symlink) at linkPath pointing to targetPath.
// Both paths must be relative to the sync folder for the operation to be proxied.
// Otherwise, it performs a direct OS symlink creation.
func (c *Client) CreateSymlink(targetPath string, linkPath string) error {
	// The targetPath should be relative to the sync folder for cross-system consistency.
	// We'll pass the 'targetPath' in the FileBody of the event.
	if c.isPathWithinPrefix(linkPath) {
		// The actual symlink creation happens locally first for immediate feedback
		if err := os.Symlink(targetPath, linkPath); err != nil {
			return fmt.Errorf("local symlink creation failed: %w", err)
		}

		// Send a CREATE_SYMLINK event with the target path in FileBody
		return c.sendFileSyncEvent(types.CREATE_SYMLINK, linkPath, []byte(targetPath), 0777, nil, false) // Perm for symlink itself
	}
	return os.Symlink(targetPath, linkPath)
}

// ReadFile reads the content of a file directly from the OS.
// This operation does NOT go through the proxy socket.
func (c *Client) ReadFile(path string) ([]byte, error) {
	return os.ReadFile(path)
}

// Stat returns FileInfo for the given path directly from the OS.
// This operation does NOT go through the proxy socket.
func (c *Client) Stat(path string) (os.FileInfo, error) {
	return os.Stat(path)
}

// ReadDir reads the directory entries directly from the OS.
// This operation does NOT go through the proxy socket.
func (c *Client) ReadDir(path string) ([]os.DirEntry, error) {
	return os.ReadDir(path)
}

// --- Internal Helper Functions ---

// sendWelcomeRequest sends the initial MSG_PROXY_WELCOME_REQUEST.
func (c *Client) sendWelcomeRequest() error {
	reqBuf := &bytes.Buffer{}
	reqBuf.WriteByte(byte(types.MSG_PROXY_WELCOME_REQUEST))
	writeString(reqBuf, "Hello folder-sync client!") // ClientMessage

	if _, err := c.conn.Write(reqBuf.Bytes()); err != nil {
		return fmt.Errorf("failed to send welcome request: %w", err)
	}

	// Read MSG_PROXY_WELCOME_RESPONSE
	respBuf := make([]byte, 1024)
	reader := bytes.NewReader(respBuf)

	n, err := c.conn.Read(respBuf)
	if err != nil {
		return fmt.Errorf("failed to read welcome response: %w", err)
	}
	reader.Reset(respBuf[:n])

	msgTypeByte, err := reader.ReadByte()
	if err != nil {
		return fmt.Errorf("failed to read message type from welcome response: %w", err)
	}
	if types.MessageType(msgTypeByte) != types.MSG_PROXY_WELCOME_RESPONSE {
		return fmt.Errorf("expected MSG_PROXY_WELCOME_RESPONSE, got 0x%x", msgTypeByte)
	}

	successByte, err := reader.ReadByte()
	if err != nil {
		return fmt.Errorf("failed to read success byte from welcome response: %w", err)
	}
	if successByte == 0x00 {
		daemonMessage, _ := readString(reader)
		return fmt.Errorf("daemon denied welcome: %s", daemonMessage)
	}
	_, err = readString(reader) // Read and discard welcome message
	if err != nil {
		return fmt.Errorf("failed to read message from welcome response: %w", err)
	}

	return nil
}

// sendFileSyncEvent constructs and sends a FileSyncEvent to the daemon.
// If fileBody is large, it will be sent in chunks.
func (c *Client) sendFileSyncEvent(
	eventType types.FileSyncEventType,
	fullPath string,
	fileBody []byte,
	perm os.FileMode,
	checksum []byte, // Checksum is optional and might be calculated by daemon
	isRename bool, // Flag to indicate if this is part of a rename operation's new path
) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.conn == nil {
		return fmt.Errorf("client connection is closed")
	}

	relPath, err := filepath.Rel(c.folderPrefix, fullPath)
	if err != nil {
		return fmt.Errorf("failed to get relative path for %q from prefix %q: %w", fullPath, c.folderPrefix, err)
	}
	// Ensure relative path uses forward slashes for consistency if on Windows
	relPath = strings.ReplaceAll(relPath, `\`, `/`)

	info, statErr := os.Stat(fullPath)
	var fileSize uint64
	var fileMode uint32
	var ownerUID, ownerGID uint32
	var timestamp time.Time
	isDir := false

	if statErr == nil {
		fileSize = uint64(info.Size())
		fileMode = uint32(info.Mode().Perm()) // Only permission bits
		isDir = info.IsDir()
		timestamp = info.ModTime()

		// Get ownership info (Unix-specific)
		if stat, ok := info.Sys().(*syscall.Stat_t); ok {
			ownerUID = stat.Uid
			ownerGID = stat.Gid
		}
	} else if os.IsNotExist(statErr) && (eventType == types.DELETE_FILE || eventType == types.DELETE_DIR) {
		// For deletions, file might not exist, use default zero values for size/mode
		fileSize = 0
		fileMode = 0
		ownerUID = 0
		ownerGID = 0
		timestamp = time.Now() // Use current time for deleted item event
	} else if os.IsNotExist(statErr) && (eventType == types.WRITE_FILE || eventType == types.CREATE_FILE || eventType == types.CREATE_DIR) {
		// For new file/directory creation, item doesn't exist yet, use provided data and permissions
		fileSize = uint64(len(fileBody))
		fileMode = uint32(perm)
		ownerUID = 0 // Will be set by daemon/OS when item is created
		ownerGID = 0
		timestamp = time.Now()
	} else if eventType == types.CREATE_SYMLINK {
		// For symlinks, `fileBody` holds the target path, `size` is usually 0
		fileSize = 0
		fileMode = uint32(perm) // Corrected from uint33
		timestamp = time.Now()
		// No need to get ownership from target; symlink owner is creator.
	} else {
		return fmt.Errorf("failed to stat path %q: %w", fullPath, statErr)
	}

	// For CREATE/WRITE events on directories, fileBody should be nil
	if isDir {
		fileBody = nil
	}

	// Generate a unique MessageID for this event
	c.idCounter++
	messageID := c.idCounter

	// Decide if we need to send chunks
	sendsChunks := false
	if eventType == types.CREATE_FILE || eventType == types.WRITE_FILE {
		if len(fileBody) > c.chunkSize {
			sendsChunks = true
		}
	}

	// Construct the FileSyncEvent message payload
	eventPayload := &bytes.Buffer{}
	binary.Write(eventPayload, binary.BigEndian, messageID)
	eventPayload.WriteByte(byte(types.PROXY_CLIENT_WRITE)) // Origin
	eventPayload.WriteByte(byte(eventType))
	writeString(eventPayload, relPath)
	binary.Write(eventPayload, binary.BigEndian, timestamp.UnixNano())
	binary.Write(eventPayload, binary.BigEndian, fileSize)
	binary.Write(eventPayload, binary.BigEndian, fileMode)
	binary.Write(eventPayload, binary.BigEndian, ownerUID)
	binary.Write(eventPayload, binary.BigEndian, ownerGID)
	eventPayload.WriteByte(byte(types.SIZE_ONLY)) // Assume SIZE_ONLY for client checksum logic, daemon can re-calculate
	writeBytes(eventPayload, checksum)            // Checksum can be empty if not provided/calculated
	if sendsChunks {
		eventPayload.WriteByte(0x01) // SendsChunks = true
	} else {
		eventPayload.WriteByte(0x00) // SendsChunks = false
	}
	eventPayload.WriteByte(0x00) // IsCompressed = false for now (client doesn't compress)
	if !sendsChunks {
		writeBytes(eventPayload, fileBody) // Include full body for small files
	} else {
		writeBytes(eventPayload, nil) // Empty body if sending chunks
	}

	// Send MSG_FILE_SYNC_EVENT
	fullMessage := &bytes.Buffer{}
	fullMessage.WriteByte(byte(types.MSG_FILE_SYNC_EVENT))
	fullMessage.Write(eventPayload.Bytes())

	if _, err := c.conn.Write(fullMessage.Bytes()); err != nil {
		return fmt.Errorf("failed to send FileSyncEvent: %w", err)
	}

	// Wait for MSG_PROXY_EVENT_ACKNOWLEDGE for the initial event
	ackReader := newBufReader(c.conn) // Use a new reader for response
	ackMsgTypeByte, err := ackReader.ReadByte()
	if err != nil {
		return fmt.Errorf("failed to read acknowledgment message type: %w", err)
	}
	if types.MessageType(ackMsgTypeByte) != types.MSG_PROXY_EVENT_ACKNOWLEDGE {
		return fmt.Errorf("expected MSG_PROXY_EVENT_ACKNOWLEDGE, got 0x%x", ackMsgTypeByte)
	}
	ackMessageID, err := readUint64(ackReader)
	if err != nil {
		return fmt.Errorf("failed to read acknowledgment MessageID: %w", err)
	}
	if ackMessageID != messageID {
		return fmt.Errorf("acknowledgment MessageID mismatch. Expected %d, got %d", messageID, ackMessageID)
	}
	_, err = readString(ackReader) // Read and discard path
	if err != nil {
		return fmt.Errorf("failed to read acknowledgment path: %w", err)
	}
	ackSuccessByte, err := ackReader.ReadByte()
	if err != nil {
		return fmt.Errorf("failed to read acknowledgment success byte: %w", err)
	}
	ackSuccess := (ackSuccessByte != 0x00)
	ackErrorMessage, err := readString(ackReader)
	if err != nil {
		return fmt.Errorf("failed to read acknowledgment error message: %w", err)
	}

	if !ackSuccess {
		return fmt.Errorf("daemon failed to acknowledge FileSyncEvent for %q: %s", relPath, ackErrorMessage)
	}

	// If sending chunks, proceed with sending chunks
	if sendsChunks {
		return c.sendFileChunks(messageID, relPath, fileBody, c.chunkSize)
	}

	return nil
}

// sendFileChunks sends a large file's body in chunks.
func (c *Client) sendFileChunks(messageID uint64, relPath string, fileBody []byte, chunkSize int) error {
	totalSize := len(fileBody)
	totalChunks := (totalSize + chunkSize - 1) / chunkSize // Ceiling division

	for i := 0; i < totalChunks; i++ {
		start := i * chunkSize
		end := start + chunkSize
		if end > totalSize {
			end = totalSize
		}
		chunkData := fileBody[start:end]

		chunkPayload := &bytes.Buffer{}
		binary.Write(chunkPayload, binary.BigEndian, messageID)
		writeString(chunkPayload, relPath)
		binary.Write(chunkPayload, binary.BigEndian, uint32(i))           // ChunkIndex
		binary.Write(chunkPayload, binary.BigEndian, uint32(totalChunks)) // TotalChunks
		chunkPayload.WriteByte(0x00)                                      // IsCompressed = false
		writeBytes(chunkPayload, chunkData)

		fullMessage := &bytes.Buffer{}
		fullMessage.WriteByte(byte(types.MSG_FILE_CHUNK))
		fullMessage.Write(chunkPayload.Bytes())

		if _, err := c.conn.Write(fullMessage.Bytes()); err != nil {
			return fmt.Errorf("failed to send chunk %d for %q: %w", i, relPath, err)
		}

		// Wait for MSG_PROXY_EVENT_ACKNOWLEDGE for each chunk
		ackReader := newBufReader(c.conn)
		ackMsgTypeByte, err := ackReader.ReadByte()
		if err != nil {
			return fmt.Errorf("failed to read chunk acknowledgment message type for chunk %d: %w", i, err)
		}
		if types.MessageType(ackMsgTypeByte) != types.MSG_PROXY_EVENT_ACKNOWLEDGE {
			return fmt.Errorf("expected MSG_PROXY_EVENT_ACKNOWLEDGE for chunk %d, got 0x%x", i, ackMsgTypeByte)
		}
		ackMessageID, err := readUint64(ackReader)
		if err != nil {
			return fmt.Errorf("failed to read chunk acknowledgment MessageID for chunk %d: %w", i, err)
		}
		// The daemon sends the original event's MessageID for the chunk ACK
		if ackMessageID != messageID {
			return fmt.Errorf("chunk acknowledgment MessageID mismatch for chunk %d. Expected %d, got %d", i, messageID, ackMessageID)
		}
		_, err = readString(ackReader) // Read and discard path
		if err != nil {
			return fmt.Errorf("failed to read chunk acknowledgment path for chunk %d: %w", i, err)
		}
		ackSuccessByte, err := ackReader.ReadByte()
		if err != nil {
			return fmt.Errorf("failed to read chunk acknowledgment success byte for chunk %d: %w", i, err)
		}
		ackSuccess := (ackSuccessByte != 0x00)
		ackErrorMessage, err := readString(ackReader)
		if err != nil {
			return fmt.Errorf("failed to read chunk acknowledgment error message for chunk %d: %w", i, err)
		}

		if !ackSuccess {
			return fmt.Errorf("daemon failed to acknowledge chunk %d for %q: %s", i, relPath, ackErrorMessage)
		}
	}
	return nil
}

// isPathWithinPrefix checks if the given path is within the client's configured folder prefix.
// It normalizes paths to handle OS differences (e.g., Windows backslashes).
func (c *Client) isPathWithinPrefix(path string) bool {
	absPath, err := filepath.Abs(path)
	if err != nil {
		return false
	}
	absPrefix, err := filepath.Abs(c.folderPrefix)
	if err != nil {
		return false
	}
	return strings.HasPrefix(absPath, absPrefix)
}

// generateMessageID generates a unique MessageID for outgoing events.
func (c *Client) generateMessageID() uint64 {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.idCounter++
	return c.idCounter
}

// --- Binary Protocol Helper Functions (copied from ingress/proxysocket.go for consistency) ---
// These helpers are duplicated to avoid circular dependencies between packages
// and to ensure the client package is self-contained for its protocol needs.

// readString reads a length-prefixed string from the reader.
func readString(r io.Reader) (string, error) {
	var length uint16
	if err := binary.Read(r, binary.BigEndian, &length); err != nil {
		return "", fmt.Errorf("failed to read string length: %w", err)
	}
	buf := make([]byte, length)
	if _, err := io.ReadFull(r, buf); err != nil {
		return "", fmt.Errorf("failed to read string bytes: %w", err)
	}
	return string(buf), nil
}

// writeString writes a length-prefixed string to the buffer.
func writeString(buf *bytes.Buffer, s string) {
	b := []byte(s)
	binary.Write(buf, binary.BigEndian, uint16(len(b)))
	buf.Write(b)
}

// readBytes reads a length-prefixed byte slice from the reader.
func readBytes(r io.Reader) ([]byte, error) {
	var length uint32
	if err := binary.Read(r, binary.BigEndian, &length); err != nil {
		return nil, fmt.Errorf("failed to read bytes length: %w", err)
	}
	if length == 0 { // Handle empty byte slice explicitly
		return []byte{}, nil
	}
	buf := make([]byte, length)
	if _, err := io.ReadFull(r, buf); err != nil {
		return nil, fmt.Errorf("failed to read bytes: %w", err)
	}
	return buf, nil
}

// writeBytes writes a length-prefixed byte slice to the buffer.
func writeBytes(buf *bytes.Buffer, b []byte) {
	if b == nil { // Treat nil as empty slice for writing length 0
		binary.Write(buf, binary.BigEndian, uint32(0))
		return
	}
	binary.Write(buf, binary.BigEndian, uint32(len(b)))
	buf.Write(b)
}

// readUint64 reads a uint64 from the reader.
func readUint64(r io.Reader) (uint64, error) {
	var val uint64
	err := binary.Read(r, binary.BigEndian, &val)
	return val, err
}

// bufReader is a wrapper around io.Reader to provide ReadByte and Peek for easier parsing.
// It uses an internal buffer to support peeking.
// (Copied from ingress/proxysocket.go)
type bufReader struct {
	reader io.Reader
	buf    []byte
	pos    int
	end    int
	mu     sync.Mutex
}

// newBufReader creates a new bufReader.
func newBufReader(reader io.Reader) *bufReader {
	return &bufReader{
		reader: reader,
		buf:    make([]byte, types.DefaultIOBufferSize),
		pos:    0,
		end:    0,
	}
}

// fill attempts to fill the buffer.
func (b *bufReader) fill() error {
	if b.pos > 0 {
		copy(b.buf, b.buf[b.pos:b.end])
		b.end -= b.pos
		b.pos = 0
	}
	if b.end == len(b.buf) { // Buffer is full, cannot fill more
		return fmt.Errorf("buffer full, cannot fill more")
	}
	n, err := b.reader.Read(b.buf[b.end:])
	b.end += n
	return err
}

// ReadByte reads a single byte.
func (b *bufReader) ReadByte() (byte, error) {
	b.mu.Lock()
	defer b.mu.Unlock()

	if b.pos == b.end {
		if err := b.fill(); err != nil {
			return 0, err
		}
		if b.pos == b.end { // Still empty after fill, means EOF
			return 0, io.EOF
		}
	}
	c := b.buf[b.pos]
	b.pos++
	return c, nil
}

// Read implements the io.Reader interface.
func (b *bufReader) Read(p []byte) (n int, err error) {
	b.mu.Lock()
	defer b.mu.Unlock()

	if b.pos == b.end {
		if err = b.fill(); err != nil && err != io.EOF {
			return 0, err
		}
		if b.pos == b.end { // Still empty after fill, means EOF
			return 0, io.EOF
		}
	}

	n = copy(p, b.buf[b.pos:b.end])
	b.pos += n
	return n, nil
}
