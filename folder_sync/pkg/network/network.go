package network

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"sync"
	"time"

	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/types"
)

// Network timeouts and limits for security
const (
	HandshakeTimeout    = 10 * time.Second
	ReadTimeout         = 30 * time.Second
	WriteTimeout        = 30 * time.Second
	MaxConnections      = 100 // Maximum concurrent connections
	MaxConnectionsPerIP = 5   // Maximum connections per IP address
)

// Logger interface for the network package
type Logger interface {
	Error(format string, args ...interface{})
	Info(format string, args ...interface{})
	Debug(format string, args ...interface{})
	Warn(format string, args ...interface{})
	ErrorP(peerID string, format string, args ...interface{})
	InfoP(peerID string, format string, args ...interface{})
	DebugP(peerID string, format string, args ...interface{})
	WarnP(peerID string, format string, args ...interface{})
}

// Handler defines the interface for handling incoming messages.
type Handler interface {
	HandleMessage(conn *Connection, msgType types.MessageType, payload []byte)
}

// Server represents a folder_sync server that listens for incoming connections.
type Server struct {
	listenAddress     string       // Address to listen on (e.g., "0.0.0.0:8080")
	authToken         string       // Shared secret token for authentication
	listener          net.Listener // TCP listener
	handler           Handler      // Handler for incoming messages
	logger            Logger
	wg                sync.WaitGroup
	quit              chan struct{}
	activeConnsMu     sync.Mutex
	activeConns       int            // Track active connections for limiting
	connectionLimiter chan struct{}  // Semaphore for connection limiting
	ipConnections     map[string]int // Track connections per IP
	ipConnectionsMu   sync.Mutex     // Protect IP connections map
	startTime         time.Time      // Server start time for health checks
}

// NewServer creates a new Server instance.
func NewServer(listenAddress, authToken string, handler Handler, logger Logger) *Server {
	return &Server{
		listenAddress:     listenAddress,
		authToken:         authToken,
		handler:           handler,
		logger:            logger,
		quit:              make(chan struct{}),
		connectionLimiter: make(chan struct{}, MaxConnections),
		ipConnections:     make(map[string]int),
		startTime:         time.Now(),
	}
}

// Start begins listening for incoming connections.
func (s *Server) Start() error {
	listener, err := net.Listen("tcp", s.listenAddress)
	if err != nil {
		return fmt.Errorf("failed to listen on %q: %w", s.listenAddress, err)
	}
	s.listener = listener
	s.logger.Info("Server listening on %s", s.listenAddress)

	s.wg.Add(1)
	go s.acceptLoop()
	return nil
}

// acceptLoop continuously accepts new connections.
func (s *Server) acceptLoop() {
	defer s.wg.Done()
	for {
		conn, err := s.listener.Accept()
		if err != nil {
			select {
			case <-s.quit:
				s.logger.Info("Server listener closed.")
				return
			default:
				s.logger.Error("Failed to accept connection: %v", err)
				// Avoid tight loop on transient errors
				select {
				case <-s.quit:
					return
				case <-time.After(100 * time.Millisecond):
					// Continue after brief delay
				}
			}
			continue
		}

		// Extract IP address for rate limiting
		clientIP, _, err := net.SplitHostPort(conn.RemoteAddr().String())
		if err != nil {
			s.logger.Error("Failed to parse client address %s: %v", conn.RemoteAddr().String(), err)
			conn.Close()
			continue
		}

		// Check per-IP connection limit
		s.ipConnectionsMu.Lock()
		ipConnCount := s.ipConnections[clientIP]
		if ipConnCount >= MaxConnectionsPerIP {
			s.ipConnectionsMu.Unlock()
			s.logger.Warn("Per-IP connection limit reached for %s (%d connections), rejecting", clientIP, ipConnCount)
			conn.Close()
			continue
		}
		s.ipConnections[clientIP]++
		s.ipConnectionsMu.Unlock()

		// Check global connection limit
		select {
		case s.connectionLimiter <- struct{}{}:
			// Connection allowed, proceed
			s.wg.Add(1)
			go s.handleConnection(conn, clientIP)
		default:
			// Global connection limit reached, reject and decrement IP count
			s.ipConnectionsMu.Lock()
			s.ipConnections[clientIP]--
			if s.ipConnections[clientIP] <= 0 {
				delete(s.ipConnections, clientIP)
			}
			s.ipConnectionsMu.Unlock()
			s.logger.Warn("Global connection limit reached, rejecting connection from %s", clientIP)
			conn.Close()
		}
	}
}

// handleConnection handles a single incoming client connection.
func (s *Server) handleConnection(conn net.Conn, clientIP string) {
	defer s.wg.Done()
	defer conn.Close()
	defer func() {
		// Release connection slot
		<-s.connectionLimiter
		s.activeConnsMu.Lock()
		s.activeConns--
		s.activeConnsMu.Unlock()

		// Decrement IP connection count
		s.ipConnectionsMu.Lock()
		s.ipConnections[clientIP]--
		if s.ipConnections[clientIP] <= 0 {
			delete(s.ipConnections, clientIP)
		}
		s.ipConnectionsMu.Unlock()
	}()

	// Track active connection
	s.activeConnsMu.Lock()
	s.activeConns++
	s.activeConnsMu.Unlock()

	peerAddr := conn.RemoteAddr().String()
	s.logger.InfoP(peerAddr, "New incoming connection from %s", peerAddr)

	// Set initial connection timeout
	conn.SetDeadline(time.Now().Add(HandshakeTimeout))

	// Perform server-side handshake
	peerName, err := s.serverHandshake(conn, peerAddr)
	if err != nil {
		s.logger.ErrorP(peerAddr, "Handshake failed with %s: %v", peerAddr, err)
		return
	}

	// Clear handshake timeout and set read/write timeouts
	conn.SetDeadline(time.Time{})

	c := NewConnection(conn, s.logger)
	s.logger.InfoP(peerName, "Handshake successful with %s (peer: %s)", peerAddr, peerName)

	// Start read loop with peer name for proper identification
	c.SetPeerID(peerName) // Update the peer ID to use the logical peer name

	// Notify SyncCore about the new incoming connection
	if handler, ok := s.handler.(*SyncMessageHandler); ok {
		networkConn := NewNetworkConnectionAdapter(c, peerName, s.logger)
		handler.addPeerConnection(networkConn)
		defer handler.removePeerConnection(peerName)
	}

	c.ReadLoop(s.handler)
	s.logger.InfoP(peerName, "Connection with %s (peer: %s) closed.", peerAddr, peerName)
}

// serverHandshake performs the server-side authentication handshake.
func (s *Server) serverHandshake(conn net.Conn, peerID string) (string, error) {
	reader := bufio.NewReader(conn)
	writer := bufio.NewWriter(conn)

	// Set a deadline for handshake completion
	conn.SetReadDeadline(time.Now().Add(HandshakeTimeout))
	defer conn.SetReadDeadline(time.Time{}) // Clear deadline

	// 1. Read MSG_HANDSHAKE_REQUEST
	msgTypeByte, err := reader.ReadByte()
	if err != nil {
		return "", fmt.Errorf("failed to read message type during handshake: %w", err)
	}
	if types.MessageType(msgTypeByte) != types.MSG_HANDSHAKE_REQUEST {
		return "", fmt.Errorf("unexpected message type during handshake: got 0x%02x, want 0x%02x (MSG_HANDSHAKE_REQUEST)", msgTypeByte, byte(types.MSG_HANDSHAKE_REQUEST))
	}

	req, err := DecodeHandshakeRequest(reader)
	if err != nil {
		return "", fmt.Errorf("failed to decode handshake request: %w", err)
	}

	// 2. Authenticate
	if req.AuthToken != s.authToken {
		resp := NewHandshakeResponse(false, types.ProtocolVersion{Major: 1, Minor: 0, Patch: 0}, "Authentication failed")
		if err := resp.Encode(writer); err != nil {
			return "", fmt.Errorf("failed to send auth failure response: %w", err)
		}
		writer.Flush()
		return "", fmt.Errorf("invalid authentication token from %s", peerID)
	}

	// 3. Check version compatibility
	serverVersion := types.ProtocolVersion{Major: 1, Minor: 0, Patch: 0}
	if !isVersionCompatible(req.ClientVersion, serverVersion) {
		// Send failure response with version mismatch error
		resp := NewHandshakeResponse(false, serverVersion,
			fmt.Sprintf("Version mismatch: client %s, server %s", req.ClientVersion.String(), serverVersion.String()))
		if err := resp.Encode(writer); err != nil {
			return "", fmt.Errorf("failed to send version mismatch response: %w", err)
		}
		writer.Flush()
		return "", fmt.Errorf("version mismatch: client %s, server %s", req.ClientVersion.String(), serverVersion.String())
	}

	// 4. Respond with success
	resp := NewHandshakeResponse(true, serverVersion, "")
	if err := resp.Encode(writer); err != nil {
		return "", fmt.Errorf("failed to send handshake success response: %w", err)
	}
	writer.Flush()
	return req.PeerName, nil
}

// GetListenAddr returns the actual listen address of the server.
// This is useful when the server was started with port 0 (auto-assigned port).
func (s *Server) GetListenAddr() string {
	if s.listener != nil {
		return s.listener.Addr().String()
	}
	return s.listenAddress
}

// Stop gracefully stops the server.
func (s *Server) Stop() {
	close(s.quit)
	if s.listener != nil {
		s.listener.Close()
	}
	s.wg.Wait()
	s.logger.Info("Server stopped.")
}

// GetListenPort returns the actual port the server is listening on.
// This is useful when the server was started with port 0 (random port).
func (s *Server) GetListenPort() int {
	if s.listener == nil {
		return 0
	}
	if addr, ok := s.listener.Addr().(*net.TCPAddr); ok {
		return addr.Port
	}
	return 0
}

// GetHealthStatus returns the current health status of the server
func (s *Server) GetHealthStatus() map[string]interface{} {
	s.activeConnsMu.Lock()
	activeConns := s.activeConns
	s.activeConnsMu.Unlock()

	s.ipConnectionsMu.Lock()
	uniqueIPs := len(s.ipConnections)
	s.ipConnectionsMu.Unlock()

	uptime := time.Since(s.startTime)

	return map[string]interface{}{
		"status":             "healthy",
		"uptime_seconds":     int64(uptime.Seconds()),
		"active_connections": activeConns,
		"unique_ips":         uniqueIPs,
		"max_connections":    MaxConnections,
		"start_time":         s.startTime.Format(time.RFC3339),
	}
}

// Client represents a folder_sync client that connects to a remote server.
type Client struct {
	remoteAddress string // Address of the remote server (host:port)
	authToken     string // Shared secret token for authentication
	serverName    string // Name of this server (for peer identification)
	logger        Logger
	conn          *Connection // The active connection
}

// NewClient creates a new Client instance.
func NewClient(remoteAddress, authToken, serverName string, logger Logger) *Client {
	return &Client{
		remoteAddress: remoteAddress,
		authToken:     authToken,
		serverName:    serverName,
		logger:        logger,
	}
}

// Connect establishes a connection to the remote server and performs handshake.
func (c *Client) Connect() (*Connection, error) {
	conn, err := net.DialTimeout("tcp", c.remoteAddress, 5*time.Second) // 5-second dial timeout
	if err != nil {
		return nil, fmt.Errorf("failed to connect to %q: %w", c.remoteAddress, err)
	}

	peerID := conn.RemoteAddr().String()
	c.logger.InfoP(peerID, "Connected to %s", c.remoteAddress)

	// Perform client-side handshake
	if err := c.clientHandshake(conn, peerID); err != nil {
		conn.Close()
		c.logger.ErrorP(peerID, "Handshake failed with %s: %v", c.remoteAddress, err)
		return nil, fmt.Errorf("handshake failed with %q: %w", c.remoteAddress, err)
	}

	c.conn = NewConnection(conn, c.logger)
	c.logger.InfoP(peerID, "Handshake successful with %s", peerID)
	return c.conn, nil
}

// clientHandshake performs the client-side authentication handshake.
func (c *Client) clientHandshake(conn net.Conn, peerID string) error {
	reader := bufio.NewReader(conn)
	writer := bufio.NewWriter(conn)

	// Set a deadline for handshake completion
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	defer conn.SetReadDeadline(time.Time{}) // Clear deadline

	// 1. Send MSG_HANDSHAKE_REQUEST
	req := NewHandshakeRequest(c.authToken, types.ProtocolVersion{Major: 1, Minor: 0, Patch: 0}, c.serverName)
	if err := req.Encode(writer); err != nil {
		return fmt.Errorf("failed to send handshake request: %w", err)
	}
	writer.Flush()

	// 2. Read MSG_HANDSHAKE_RESPONSE
	msgTypeByte, err := reader.ReadByte()
	if err != nil {
		return fmt.Errorf("failed to read message type during handshake response: %w", err)
	}
	if types.MessageType(msgTypeByte) != types.MSG_HANDSHAKE_RESPONSE {
		return fmt.Errorf("unexpected message type during handshake response: got %x, want %x", msgTypeByte, types.MSG_HANDSHAKE_RESPONSE)
	}

	resp, err := DecodeHandshakeResponse(reader)
	if err != nil {
		return fmt.Errorf("failed to decode handshake response: %w", err)
	}

	if !resp.Success {
		return fmt.Errorf("handshake failed: %s", resp.ErrorMessage)
	}

	// Check version compatibility
	clientVersion := types.ProtocolVersion{Major: 1, Minor: 0, Patch: 0}
	if !isVersionCompatible(clientVersion, resp.ServerVersion) {
		return fmt.Errorf("version incompatible: client %s, server %s",
			clientVersion.String(), resp.ServerVersion.String())
	}

	return nil
}

// isVersionCompatible checks if the client and server versions are compatible.
// Compatible versions have the same major version number.
// Minor and patch version differences are allowed for backward compatibility.
func isVersionCompatible(clientVersion, serverVersion types.ProtocolVersion) bool {
	// Major version must match exactly
	if clientVersion.Major != serverVersion.Major {
		return false
	}

	// Minor version compatibility: server should support client's minor version or higher
	// This allows newer servers to work with older clients
	if serverVersion.Minor < clientVersion.Minor {
		return false
	}

	// Patch versions are always compatible within the same major.minor
	return true
}

// Close closes the client's connection.
func (c *Client) Close() error {
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}

// Connection represents an active network connection.
type Connection struct {
	net.Conn // Embedded net.Conn for direct access to underlying connection methods
	reader   *bufio.Reader
	writer   *bufio.Writer
	logger   Logger
	peerID   string      // Identifier for logging purposes (e.g., remote address)
	msgQueue chan []byte // Channel for sending messages (for future non-blocking send)
	quit     chan struct{}
	wg       sync.WaitGroup
}

// NewConnection creates a new Connection instance.
func NewConnection(conn net.Conn, logger Logger) *Connection {
	peerID := conn.RemoteAddr().String()
	c := &Connection{
		Conn:     conn,
		reader:   bufio.NewReader(conn),
		writer:   bufio.NewWriter(conn),
		logger:   logger,
		peerID:   peerID,
		msgQueue: make(chan []byte, types.MaxMessageQueueSize), // Buffered channel for messages
		quit:     make(chan struct{}),
	}
	c.wg.Add(1)
	go c.writeLoop() // Start the write loop
	return c
}

// IsConnected returns whether the connection is still active
func (c *Connection) IsConnected() bool {
	select {
	case <-c.quit:
		return false
	default:
		return true
	}
}

// SetPeerID updates the peer identifier for this connection
func (c *Connection) SetPeerID(peerID string) {
	c.peerID = peerID
}

// ReadLoop continuously reads messages from the connection.
func (c *Connection) ReadLoop(handler Handler) {
	for {
		select {
		case <-c.quit:
			return
		default:
			// Read message type
			msgTypeByte, err := c.reader.ReadByte()
			if err != nil {
				if err == io.EOF {
					c.logger.InfoP(c.peerID, "Peer closed connection.")
				} else {
					c.logger.ErrorP(c.peerID, "Failed to read message type: %v", err)
				}
				c.Close() // Close connection on read error
				return
			}

			msgType := types.MessageType(msgTypeByte)

			// Read payload length (uint32, 4 bytes)
			payloadLengthBytes := make([]byte, 4)
			if _, err := io.ReadFull(c.reader, payloadLengthBytes); err != nil {
				c.logger.ErrorP(c.peerID, "Failed to read payload length for msgType %x: %v", msgType, err)
				c.Close()
				return
			}
			payloadLength := bytesToUint32(payloadLengthBytes)

			// Read payload
			payload := make([]byte, payloadLength)
			if _, err := io.ReadFull(c.reader, payload); err != nil {
				c.logger.ErrorP(c.peerID, "Failed to read payload for msgType %x, length %d: %v", msgType, payloadLength, err)
				c.Close()
				return
			}

			c.logger.DebugP(c.peerID, "Received message: Type=%s (0x%x), Length=%d", msgType.String(), msgType, payloadLength)
			handler.HandleMessage(c, msgType, payload)
		}
	}
}

// SendMessage sends a message over the connection.
// This is non-blocking as it sends to a channel. The actual write happens in writeLoop.
func (c *Connection) SendMessage(msgType types.MessageType, payload []byte) error {
	// Construct the full message (type + length + payload)
	var fullMessage bytes.Buffer
	fullMessage.WriteByte(byte(msgType))
	fullMessage.Write(uint32ToBytes(uint32(len(payload)))) // Length prefix for payload
	fullMessage.Write(payload)

	// Extract additional information for better logging
	eventInfo := c.extractEventInfo(msgType, payload)

	select {
	case c.msgQueue <- fullMessage.Bytes():
		c.logger.DebugP(c.peerID, "Queued message to send: Type=%s (0x%x), Length=%d%s", msgType.String(), msgType, len(payload), eventInfo)
		return nil
	case <-c.quit:
		return fmt.Errorf("connection is closing, cannot send message")
	default:
		// This case means the queue is full, indicating a backpressure issue
		c.logger.WarnP(c.peerID, "Message queue full, dropping message: Type=%s (0x%x)%s", msgType.String(), msgType, eventInfo)
		return fmt.Errorf("message queue full, message dropped")
	}
}

// extractEventInfo extracts event type and path information from message payload for better logging
func (c *Connection) extractEventInfo(msgType types.MessageType, payload []byte) string {
	switch msgType {
	case types.MSG_FILE_SYNC_EVENT:
		var event map[string]interface{}
		if err := json.Unmarshal(payload, &event); err == nil {
			if path, ok := event["Path"].(string); ok {
				if eventType, ok := event["Type"].(float64); ok {
					eventTypeStr := types.FileSyncEventType(eventType).String()
					return fmt.Sprintf(" (file: %s, type: %s)", path, eventTypeStr)
				} else {
					return fmt.Sprintf(" (file: %s)", path)
				}
			}
		}
	case types.MSG_PULL_FILE_REQUEST:
		var request map[string]interface{}
		if err := json.Unmarshal(payload, &request); err == nil {
			if path, ok := request["Path"].(string); ok {
				return fmt.Sprintf(" (file: %s)", path)
			}
		}
	case types.MSG_PULL_FILE_RESPONSE:
		var response map[string]interface{}
		if err := json.Unmarshal(payload, &response); err == nil {
			if path, ok := response["Path"].(string); ok {
				return fmt.Sprintf(" (file: %s)", path)
			}
		}
	}
	return ""
}

// writeLoop handles sending messages from the queue to the network.
func (c *Connection) writeLoop() {
	defer c.wg.Done()
	defer c.writer.Flush() // Ensure any buffered data is written before exiting

	c.logger.DebugP(c.peerID, "Write loop started for peer %s", c.peerID)

	for {
		select {
		case msgBytes := <-c.msgQueue:
			c.logger.DebugP(c.peerID, "Writing message to network: %d bytes", len(msgBytes))
			if _, err := c.writer.Write(msgBytes); err != nil {
				c.logger.ErrorP(c.peerID, "Failed to write message to network: %v", err)
				// Attempt to flush even on error, then close connection
				c.writer.Flush()
				c.Close() // Trigger connection close on write error
				return
			}
			if err := c.writer.Flush(); err != nil {
				c.logger.ErrorP(c.peerID, "Failed to flush writer to network: %v", err)
				c.Close()
				return
			}
			c.logger.DebugP(c.peerID, "Successfully sent message to network: %d bytes", len(msgBytes))
		case <-c.quit:
			c.logger.DebugP(c.peerID, "Write loop stopped.")
			return
		}
	}
}

// Close closes the connection gracefully.
func (c *Connection) Close() error {
	select {
	case <-c.quit:
		return nil // Already closing
	default:
		close(c.quit)
		c.wg.Wait() // Wait for writeLoop to finish
		return c.Conn.Close()
	}
}

// Helper functions for binary encoding/decoding of basic types
// These will eventually move to protocol.go for cleaner separation.

func uint32ToBytes(n uint32) []byte {
	b := make([]byte, 4)
	b[0] = byte(n >> 24)
	b[1] = byte(n >> 16)
	b[2] = byte(n >> 8)
	b[3] = byte(n)
	return b
}

func bytesToUint32(b []byte) uint32 {
	return uint32(b[0])<<24 | uint32(b[1])<<16 | uint32(b[2])<<8 | uint32(b[3])
}

// --- Placeholder for a simple message handler for testing purposes ---
type TestMessageHandler struct {
	ReceivedMessages chan ReceivedMessage
	Logger           Logger
}

type ReceivedMessage struct {
	Conn    *Connection
	MsgType types.MessageType
	Payload []byte
}

func NewTestMessageHandler(logger Logger) *TestMessageHandler {
	return &TestMessageHandler{
		ReceivedMessages: make(chan ReceivedMessage, 10), // Buffered channel
		Logger:           logger,
	}
}

// NetworkConnection interface to match sync package interface
type NetworkConnection interface {
	PeerName() string
	IsConnected() bool
	SendFileSyncEvent(event types.FileSyncEvent) error
	SendMessage(msgType types.MessageType, payload interface{}) error
	PullFile(request types.FileSyncEvent) ([]byte, error)
	ReadResponse() (types.MessageType, interface{}, error)
	Close() error
}

// SyncCoreInterface defines the interface for sync core operations needed by the network layer
type SyncCoreInterface interface {
	HandlePeerMessage(peerName string, msgType types.MessageType, payload interface{}) error
}

// NetworkManager manages network connections and coordinates with SyncCore.
type NetworkManager struct {
	cfg                  interface{} // Config interface (placeholder)
	addPeerConnection    func(conn interface{})
	removePeerConnection func(peerName string)
	syncCore             SyncCoreInterface // Reference to sync core for message handling
	logger               Logger
	done                 chan struct{}
	server               *Server                // The actual server instance
	clients              map[string]*Client     // Map of peer name to client instances
	clientConnections    map[string]*Connection // Map of peer name to active connections
	wg                   sync.WaitGroup         // Wait group for goroutines
	messageHandler       *SyncMessageHandler    // Handler for decoding and processing messages
}

// NewNetworkManager creates a new NetworkManager instance.
func NewNetworkManager(cfg interface{}, addPeerConnection func(conn interface{}), removePeerConnection func(peerName string), syncCore SyncCoreInterface, logger Logger) (*NetworkManager, error) {
	// Create the message handler for decoding and processing messages
	messageHandler := NewSyncMessageHandler(addPeerConnection, removePeerConnection, syncCore, logger)

	return &NetworkManager{
		cfg:                  cfg,
		addPeerConnection:    addPeerConnection,
		removePeerConnection: removePeerConnection,
		syncCore:             syncCore,
		logger:               logger,
		done:                 make(chan struct{}),
		clients:              make(map[string]*Client),
		clientConnections:    make(map[string]*Connection),
		messageHandler:       messageHandler,
	}, nil
}

// HandleMessage implements the Handler interface for NetworkManager
func (nm *NetworkManager) HandleMessage(conn *Connection, msgType types.MessageType, payload []byte) {
	// Delegate to the SyncMessageHandler for proper message decoding and processing
	nm.messageHandler.HandleMessage(conn, msgType, payload)
}

// Start begins the network manager operations.
func (nm *NetworkManager) Start() {
	nm.logger.Info("NetworkManager starting...")

	// Cast the config to the proper type
	if config, ok := nm.cfg.(*config.Config); ok {
		// Create a message handler that will integrate with SyncCore
		handler := NewSyncMessageHandler(nm.addPeerConnection, nm.removePeerConnection, nm.syncCore, nm.logger)

		// Create and start the server
		nm.server = NewServer(config.Server.ListenAddress, config.Server.AuthToken, handler, nm.logger)
		if err := nm.server.Start(); err != nil {
			nm.logger.Error("Failed to start server: %v", err)
			return
		}

		// Establish outgoing connections to configured peers
		for _, pairConfig := range config.Pairs {
			nm.wg.Add(1)
			go nm.connectToPeer(pairConfig, config.Server.AuthToken, config.Server.Name)
		}

		// Wait for shutdown signal
		<-nm.done

		// Wait for all peer connection goroutines to finish
		nm.wg.Wait()
	} else {
		nm.logger.Error("Invalid config type passed to NetworkManager")
		<-nm.done
	}

	nm.logger.Info("NetworkManager stopped.")
}

// connectToPeer establishes and maintains a connection to a specific peer
func (nm *NetworkManager) connectToPeer(pairConfig types.PairConfig, authToken string, serverName string) {
	defer nm.wg.Done()

	peerAddr := fmt.Sprintf("%s:%d", pairConfig.HostnameOrIP, pairConfig.RemotePort)
	nm.logger.Info("Attempting to connect to peer %s at %s", pairConfig.Name, peerAddr)

	// Create client for this peer
	client := NewClient(peerAddr, authToken, serverName, nm.logger)
	nm.clients[pairConfig.Name] = client

	// Connection retry loop
	reconnectInterval := 5 * time.Second
	maxRetries := 10
	retryCount := 0

	for {
		select {
		case <-nm.done:
			nm.logger.Info("Stopping connection attempts to peer %s due to shutdown", pairConfig.Name)
			return
		default:
			// Attempt to connect
			conn, err := client.Connect()
			if err != nil {
				retryCount++
				nm.logger.Warn("Failed to connect to peer %s (attempt %d/%d): %v", pairConfig.Name, retryCount, maxRetries, err)

				if retryCount >= maxRetries {
					nm.logger.Error("Max retries reached for peer %s, giving up", pairConfig.Name)
					return
				}

				// Wait before retrying
				select {
				case <-nm.done:
					return
				case <-time.After(reconnectInterval):
					continue
				}
			}

			// Connection successful
			retryCount = 0 // Reset retry count on successful connection
			nm.logger.Info("Successfully connected to peer %s", pairConfig.Name)
			nm.clientConnections[pairConfig.Name] = conn

			// Set the correct peer ID for the connection
			conn.SetPeerID(pairConfig.Name)

			// Create a NetworkConnection adapter and notify SyncCore
			networkConn := NewNetworkConnectionAdapter(conn, pairConfig.Name, nm.logger)
			nm.addPeerConnection(networkConn)

			// Start readLoop for client connection to receive messages
			go conn.ReadLoop(nm)

			// Monitor the connection - this will block until connection is lost
			nm.monitorConnection(pairConfig.Name, conn)

			// Connection lost, clean up and retry
			nm.logger.Warn("Connection to peer %s lost, will retry", pairConfig.Name)
			nm.removePeerConnection(pairConfig.Name)
			delete(nm.clientConnections, pairConfig.Name)

			// Wait before retrying
			select {
			case <-nm.done:
				return
			case <-time.After(reconnectInterval):
				continue
			}
		}
	}
}

// monitorConnection monitors a connection and returns when it's lost
func (nm *NetworkManager) monitorConnection(peerName string, conn *Connection) {
	// This is a simple implementation - in practice you might want more sophisticated monitoring
	// For now, we'll just wait for the connection to close
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for conn.IsConnected() {
		select {
		case <-nm.done:
			return
		case <-ticker.C:
			// Check if connection is still alive
			continue
		}
	}
}

// Close gracefully shuts down the NetworkManager.
func (nm *NetworkManager) Close() {
	nm.logger.Info("Closing NetworkManager...")

	// Signal all goroutines to stop
	close(nm.done)

	// Close all client connections
	for peerName, conn := range nm.clientConnections {
		nm.logger.Debug("Closing connection to peer %s", peerName)
		conn.Close()
	}

	// Stop the server
	if nm.server != nil {
		nm.server.Stop()
	}

	// Wait for all goroutines to finish
	nm.wg.Wait()
}

// GetListenPort returns the actual port the server is listening on.
// This is useful when the server was started with port 0 (random port).
func (nm *NetworkManager) GetListenPort() int {
	if nm.server == nil {
		return 0
	}
	return nm.server.GetListenPort()
}

// NetworkConnectionAdapter adapts a Connection to implement the NetworkConnection interface
type NetworkConnectionAdapter struct {
	conn     *Connection
	peerName string
	logger   Logger
}

// NewNetworkConnectionAdapter creates a new NetworkConnectionAdapter
func NewNetworkConnectionAdapter(conn *Connection, peerName string, logger Logger) *NetworkConnectionAdapter {
	return &NetworkConnectionAdapter{
		conn:     conn,
		peerName: peerName,
		logger:   logger,
	}
}

// PeerName returns the name of the peer
func (nca *NetworkConnectionAdapter) PeerName() string {
	return nca.peerName
}

// IsConnected returns whether the connection is active
func (nca *NetworkConnectionAdapter) IsConnected() bool {
	return nca.conn.IsConnected()
}

// SendFileSyncEvent sends a file sync event to the peer
func (nca *NetworkConnectionAdapter) SendFileSyncEvent(event types.FileSyncEvent) error {
	// For now, use a simple JSON encoding - in production this should use the proper binary protocol
	payload, err := nca.encodeFileSyncEvent(event)
	if err != nil {
		return fmt.Errorf("failed to encode FileSyncEvent: %w", err)
	}
	return nca.conn.SendMessage(types.MSG_FILE_SYNC_EVENT, payload)
}

// SendMessage sends a message to the peer
func (nca *NetworkConnectionAdapter) SendMessage(msgType types.MessageType, payload interface{}) error {
	// Convert payload to bytes
	var payloadBytes []byte
	var err error

	switch p := payload.(type) {
	case []byte:
		payloadBytes = p
	case string:
		payloadBytes = []byte(p)
	case types.FileSyncEvent:
		payloadBytes, err = nca.encodeFileSyncEvent(p)
		if err != nil {
			return fmt.Errorf("failed to encode FileSyncEvent: %w", err)
		}
	case types.InitialScanRequest:
		payloadBytes, err = json.Marshal(p)
		if err != nil {
			return fmt.Errorf("failed to encode InitialScanRequest: %w", err)
		}
	case types.InitialScanResponse:
		payloadBytes, err = json.Marshal(p)
		if err != nil {
			return fmt.Errorf("failed to encode InitialScanResponse: %w", err)
		}
	case types.PullFileRequest:
		payloadBytes, err = json.Marshal(p)
		if err != nil {
			return fmt.Errorf("failed to encode PullFileRequest: %w", err)
		}
	case types.PullFileResponse:
		payloadBytes, err = json.Marshal(p)
		if err != nil {
			return fmt.Errorf("failed to encode PullFileResponse: %w", err)
		}
	case types.SyncAcknowledge:
		payloadBytes, err = json.Marshal(p)
		if err != nil {
			return fmt.Errorf("failed to encode SyncAcknowledge: %w", err)
		}
	case types.FileRemoved:
		payloadBytes, err = json.Marshal(p)
		if err != nil {
			return fmt.Errorf("failed to encode FileRemoved: %w", err)
		}
	case types.Error:
		payloadBytes, err = json.Marshal(p)
		if err != nil {
			return fmt.Errorf("failed to encode Error: %w", err)
		}
	case types.FileChunk:
		payloadBytes, err = json.Marshal(p)
		if err != nil {
			return fmt.Errorf("failed to encode FileChunk: %w", err)
		}
	case types.SameContent:
		payloadBytes, err = json.Marshal(p)
		if err != nil {
			return fmt.Errorf("failed to encode SameContent: %w", err)
		}
	default:
		return fmt.Errorf("unsupported payload type: %T", payload)
	}

	return nca.conn.SendMessage(msgType, payloadBytes)
}

// PullFile requests a file from the peer
func (nca *NetworkConnectionAdapter) PullFile(request types.FileSyncEvent) ([]byte, error) {
	// Convert the FileSyncEvent to a PullFileRequest
	pullReq := types.PullFileRequest{
		MessageID:           request.MessageID,
		Path:                request.Path,
		Size:                request.Size,
		FileCompareStrategy: types.SIZE_ONLY, // Default strategy
		Checksum:            nil,
	}

	// Send pull request asynchronously - response will be handled by HandlePeerResponse
	if err := nca.SendMessage(types.MSG_PULL_FILE_REQUEST, pullReq); err != nil {
		return nil, fmt.Errorf("failed to send pull file request: %w", err)
	}

	// Note: This method is part of a synchronous interface but the actual implementation
	// is asynchronous. In practice, the response will be handled by the message handling
	// system and the file content will be applied directly to the filesystem.
	// For now, return empty data to indicate the request was sent successfully.
	nca.logger.DebugP(nca.peerName, "Pull file request sent for %q, response will be handled asynchronously", request.Path)
	return []byte{}, nil
}

// ReadResponse reads a response message from the peer
func (nca *NetworkConnectionAdapter) ReadResponse() (types.MessageType, interface{}, error) {
	// Note: This method is part of the NetworkConnection interface but is not used
	// in the current asynchronous message handling architecture. All message responses
	// are handled through the ReadLoop -> HandleMessage -> HandlePeerMessage chain.
	// This method exists for interface compatibility but should not be called.
	return types.MSG_ERROR, nil, fmt.Errorf("ReadResponse is not used in asynchronous message handling architecture")
}

// Close closes the connection
func (nca *NetworkConnectionAdapter) Close() error {
	return nca.conn.Close()
}

// encodeFileSyncEvent encodes a FileSyncEvent to bytes
// This is a simplified implementation - in production this should use the proper binary protocol
func (nca *NetworkConnectionAdapter) encodeFileSyncEvent(event types.FileSyncEvent) ([]byte, error) {
	// For now, use JSON encoding as a placeholder
	// In production, this should implement the binary protocol from the documentation
	return json.Marshal(event)
}

// SyncMessageHandler integrates with SyncCore to handle incoming network messages
type SyncMessageHandler struct {
	addPeerConnection    func(conn interface{})
	removePeerConnection func(peerName string)
	syncCore             SyncCoreInterface // Interface to the sync core for message processing
	logger               Logger
}

// NewSyncMessageHandler creates a new SyncMessageHandler
func NewSyncMessageHandler(addPeerConnection func(conn interface{}), removePeerConnection func(peerName string), syncCore SyncCoreInterface, logger Logger) *SyncMessageHandler {
	return &SyncMessageHandler{
		addPeerConnection:    addPeerConnection,
		removePeerConnection: removePeerConnection,
		syncCore:             syncCore,
		logger:               logger,
	}
}

// HandleMessage processes incoming messages from network connections
func (h *SyncMessageHandler) HandleMessage(conn *Connection, msgType types.MessageType, payload []byte) {
	h.logger.DebugP(conn.peerID, "SyncHandler received: Type=%s, PayloadLen=%d", msgType.String(), len(payload))

	// Decode the payload based on message type and forward to sync core
	switch msgType {
	case types.MSG_FILE_SYNC_EVENT:
		h.logger.DebugP(conn.peerID, "Received file sync event from peer")

		// Decode the FileSyncEvent from JSON payload (temporary implementation)
		var event types.FileSyncEvent
		if err := json.Unmarshal(payload, &event); err != nil {
			h.logger.ErrorP(conn.peerID, "Failed to decode FileSyncEvent: %v", err)
			return
		}

		h.logger.DebugP(conn.peerID, "Decoded file sync event for file %q: %s", event.Path, event.String())

		// Forward to sync core for processing
		if h.syncCore != nil {
			if err := h.syncCore.HandlePeerMessage(conn.peerID, msgType, event); err != nil {
				h.logger.ErrorP(conn.peerID, "Failed to handle file sync event: %v", err)
			}
		} else {
			h.logger.WarnP(conn.peerID, "No sync core available to handle message")
		}

	case types.MSG_INITIAL_SCAN_REQUEST:
		h.logger.DebugP(conn.peerID, "Received initial scan request from peer")

		var request types.InitialScanRequest
		if err := json.Unmarshal(payload, &request); err != nil {
			h.logger.ErrorP(conn.peerID, "Failed to decode InitialScanRequest: %v", err)
			return
		}

		if h.syncCore != nil {
			if err := h.syncCore.HandlePeerMessage(conn.peerID, msgType, request); err != nil {
				h.logger.ErrorP(conn.peerID, "Failed to handle initial scan request: %v", err)
			}
		}

	case types.MSG_INITIAL_SCAN_RESPONSE:
		h.logger.DebugP(conn.peerID, "Received initial scan response from peer")

		var response types.InitialScanResponse
		if err := json.Unmarshal(payload, &response); err != nil {
			h.logger.ErrorP(conn.peerID, "Failed to decode InitialScanResponse: %v", err)
			return
		}

		if h.syncCore != nil {
			if err := h.syncCore.HandlePeerMessage(conn.peerID, msgType, response); err != nil {
				h.logger.ErrorP(conn.peerID, "Failed to handle initial scan response: %v", err)
			}
		}

	case types.MSG_PULL_FILE_REQUEST:
		h.logger.DebugP(conn.peerID, "Received pull file request from peer")

		var request types.PullFileRequest
		if err := json.Unmarshal(payload, &request); err != nil {
			h.logger.ErrorP(conn.peerID, "Failed to decode PullFileRequest: %v", err)
			return
		}

		h.logger.DebugP(conn.peerID, "Pull file request for file %q (MsgID: %d, Size: %d)", request.Path, request.MessageID, request.Size)

		if h.syncCore != nil {
			if err := h.syncCore.HandlePeerMessage(conn.peerID, msgType, request); err != nil {
				h.logger.ErrorP(conn.peerID, "Failed to handle pull file request: %v", err)
			}
		}

	case types.MSG_PULL_FILE_RESPONSE:
		h.logger.DebugP(conn.peerID, "Received pull file response from peer")

		var response types.PullFileResponse
		if err := json.Unmarshal(payload, &response); err != nil {
			h.logger.ErrorP(conn.peerID, "Failed to decode PullFileResponse: %v", err)
			return
		}

		if h.syncCore != nil {
			if err := h.syncCore.HandlePeerMessage(conn.peerID, msgType, response); err != nil {
				h.logger.ErrorP(conn.peerID, "Failed to handle pull file response: %v", err)
			}
		}

	case types.MSG_SYNC_ACKNOWLEDGE:
		h.logger.DebugP(conn.peerID, "Received sync acknowledge from peer")

		var ack types.SyncAcknowledge
		if err := json.Unmarshal(payload, &ack); err != nil {
			h.logger.ErrorP(conn.peerID, "Failed to decode SyncAcknowledge: %v", err)
			return
		}

		if h.syncCore != nil {
			if err := h.syncCore.HandlePeerMessage(conn.peerID, msgType, ack); err != nil {
				h.logger.ErrorP(conn.peerID, "Failed to handle sync acknowledge: %v", err)
			}
		}

	case types.MSG_FILE_REMOVED:
		h.logger.DebugP(conn.peerID, "Received file removed from peer")

		var removed types.FileRemoved
		if err := json.Unmarshal(payload, &removed); err != nil {
			h.logger.ErrorP(conn.peerID, "Failed to decode FileRemoved: %v", err)
			return
		}

		if h.syncCore != nil {
			if err := h.syncCore.HandlePeerMessage(conn.peerID, msgType, removed); err != nil {
				h.logger.ErrorP(conn.peerID, "Failed to handle file removed: %v", err)
			}
		}

	case types.MSG_SAME_CONTENT:
		h.logger.DebugP(conn.peerID, "Received same content from peer")

		var sameContent types.SameContent
		if err := json.Unmarshal(payload, &sameContent); err != nil {
			h.logger.ErrorP(conn.peerID, "Failed to decode SameContent: %v", err)
			return
		}

		if h.syncCore != nil {
			if err := h.syncCore.HandlePeerMessage(conn.peerID, msgType, sameContent); err != nil {
				h.logger.ErrorP(conn.peerID, "Failed to handle same content: %v", err)
			}
		}

	case types.MSG_FILE_CHUNK:
		h.logger.DebugP(conn.peerID, "Received file chunk from peer")

		var chunk types.FileChunk
		if err := json.Unmarshal(payload, &chunk); err != nil {
			h.logger.ErrorP(conn.peerID, "Failed to decode FileChunk: %v", err)
			return
		}

		if h.syncCore != nil {
			if err := h.syncCore.HandlePeerMessage(conn.peerID, msgType, chunk); err != nil {
				h.logger.ErrorP(conn.peerID, "Failed to handle file chunk: %v", err)
			}
		}

	case types.MSG_CHUNK_ACKNOWLEDGE:
		h.logger.DebugP(conn.peerID, "Received chunk acknowledge from peer")

		var chunkAck types.ChunkAcknowledge
		if err := json.Unmarshal(payload, &chunkAck); err != nil {
			h.logger.ErrorP(conn.peerID, "Failed to decode ChunkAcknowledge: %v", err)
			return
		}

		if h.syncCore != nil {
			if err := h.syncCore.HandlePeerMessage(conn.peerID, msgType, chunkAck); err != nil {
				h.logger.ErrorP(conn.peerID, "Failed to handle chunk acknowledge: %v", err)
			}
		}

	case types.MSG_ERROR:
		h.logger.DebugP(conn.peerID, "Received error from peer")

		var errorMsg ErrorMessage
		if err := json.Unmarshal(payload, &errorMsg); err != nil {
			h.logger.ErrorP(conn.peerID, "Failed to decode ErrorMessage: %v", err)
			return
		}

		if h.syncCore != nil {
			if err := h.syncCore.HandlePeerMessage(conn.peerID, msgType, errorMsg); err != nil {
				h.logger.ErrorP(conn.peerID, "Failed to handle error message: %v", err)
			}
		}

	case types.MSG_HANDSHAKE_REQUEST:
		h.logger.DebugP(conn.peerID, "Received handshake request from peer")
		// Handshake is handled elsewhere, just log

	case types.MSG_HANDSHAKE_RESPONSE:
		h.logger.DebugP(conn.peerID, "Received handshake response from peer")
		// Handshake is handled elsewhere, just log

	default:
		h.logger.WarnP(conn.peerID, "Received unknown message type: %s", msgType.String())
	}
}

func (h *TestMessageHandler) HandleMessage(conn *Connection, msgType types.MessageType, payload []byte) {
	h.Logger.DebugP(conn.peerID, "TestHandler received: Type=%s, PayloadLen=%d", msgType.String(), len(payload))
	select {
	case h.ReceivedMessages <- ReceivedMessage{Conn: conn, MsgType: msgType, Payload: payload}:
		// Message sent to channel
	default:
		h.Logger.WarnP(conn.peerID, "TestHandler: ReceivedMessages channel full, dropping message.")
	}
}
