package network

import (
	"bufio"
	"encoding/binary"
	"fmt"
	"io"
	"time"

	"github.com/real-rm/folder_sync/pkg/types"
)

// --- Data Type Encoding Helpers (Big-Endian) ---

// writeByte writes a single byte.
func writeByte(w *bufio.Writer, b byte) error {
	return w.WriteByte(b)
}

// readByte reads a single byte.
func readByte(r *bufio.Reader) (byte, error) {
	return r.ReadByte()
}

// writeUint16 writes a uint16 in big-endian byte order.
func writeUint16(w *bufio.Writer, n uint16) error {
	return binary.Write(w, binary.BigEndian, n)
}

// readUint16 reads a uint16 in big-endian byte order.
func readUint16(r *bufio.Reader) (uint16, error) {
	var n uint16
	err := binary.Read(r, binary.BigEndian, &n)
	return n, err
}

// WriteUint32 writes a uint32 in big-endian byte order.
func WriteUint32(w *bufio.Writer, n uint32) error {
	return binary.Write(w, binary.BigEndian, n)
}

// ReadUint32 reads a uint32 in big-endian byte order.
func ReadUint32(r *bufio.Reader) (uint32, error) {
	var n uint32
	err := binary.Read(r, binary.BigEndian, &n)
	return n, err
}

// WriteUint64 writes a uint64 in big-endian byte order.
func WriteUint64(w *bufio.Writer, n uint64) error {
	return binary.Write(w, binary.BigEndian, n)
}

// ReadUint64 reads a uint64 in big-endian byte order.
func ReadUint64(r *bufio.Reader) (uint64, error) {
	var n uint64
	err := binary.Read(r, binary.BigEndian, &n)
	return n, err
}

// writeBool writes a bool as 0x00 for false, 0x01 for true.
func writeBool(w *bufio.Writer, b bool) error {
	if b {
		return w.WriteByte(0x01)
	}
	return w.WriteByte(0x00)
}

// readBool reads a bool.
func readBool(r *bufio.Reader) (bool, error) {
	b, err := r.ReadByte()
	if err != nil {
		return false, err
	}
	return b != 0x00, nil
}

// WriteString writes a length-prefixed UTF-8 string (exported for tests).
func WriteString(w *bufio.Writer, s string) error {
	return writeString(w, s)
}

// writeString writes a length-prefixed UTF-8 string.
func writeString(w *bufio.Writer, s string) error {
	strBytes := []byte(s)
	if len(strBytes) > 65535 {
		return fmt.Errorf("string length %d exceeds max uint16 size (65535 bytes)", len(strBytes))
	}
	if err := writeUint16(w, uint16(len(strBytes))); err != nil {
		return err
	}
	_, err := w.Write(strBytes)
	return err
}

// Maximum allowed size for strings to prevent memory exhaustion attacks
const MaxStringSize uint16 = types.MaxStringSize // 32KB limit for strings (within uint16 range)

// readString reads a length-prefixed UTF-8 string with size validation.
func readString(r *bufio.Reader) (string, error) {
	length, err := readUint16(r)
	if err != nil {
		return "", err
	}

	// Validate size to prevent memory exhaustion attacks
	if length > MaxStringSize {
		return "", fmt.Errorf("string size %d exceeds maximum allowed size %d", length, MaxStringSize)
	}

	strBytes := make([]byte, length)
	if _, err := io.ReadFull(r, strBytes); err != nil {
		return "", err
	}
	return string(strBytes), nil
}

// WriteBytes writes a length-prefixed byte array (exported for tests).
func WriteBytes(w *bufio.Writer, b []byte) error {
	return writeBytes(w, b)
}

// writeBytes writes a length-prefixed byte array.
func writeBytes(w *bufio.Writer, b []byte) error {
	if len(b) > 0xFFFFFFFF { // Max uint32 size
		return fmt.Errorf("byte array length %d exceeds max uint32 size (4GB)", len(b))
	}
	if err := WriteUint32(w, uint32(len(b))); err != nil {
		return err
	}
	_, err := w.Write(b)
	return err
}

// Maximum allowed size for byte arrays to prevent memory exhaustion attacks
const MaxByteArraySize = types.MaxByteArraySize // 100MB limit

// readBytes reads a length-prefixed byte array with size validation.
func readBytes(r *bufio.Reader) ([]byte, error) {
	length, err := ReadUint32(r)
	if err != nil {
		return nil, err
	}

	// Validate size to prevent memory exhaustion attacks
	if length > MaxByteArraySize {
		return nil, fmt.Errorf("byte array size %d exceeds maximum allowed size %d", length, MaxByteArraySize)
	}

	data := make([]byte, length)
	if _, err := io.ReadFull(r, data); err != nil {
		return nil, err
	}
	return data, nil
}

// WriteTime writes a time.Time as a uint64 Unix nanosecond timestamp (exported for tests).
func WriteTime(w *bufio.Writer, t time.Time) error {
	return writeTime(w, t)
}

// ReadTime reads a time.Time from a uint64 Unix nanosecond timestamp (exported for tests).
func ReadTime(r *bufio.Reader) (time.Time, error) {
	return readTime(r)
}

// writeTime writes a time.Time as a uint64 Unix nanosecond timestamp.
func writeTime(w *bufio.Writer, t time.Time) error {
	return WriteUint64(w, uint64(t.UnixNano()))
}

// readTime reads a time.Time from a uint64 Unix nanosecond timestamp.
func readTime(r *bufio.Reader) (time.Time, error) {
	nano, err := ReadUint64(r)
	if err != nil {
		return time.Time{}, err
	}
	return time.Unix(0, int64(nano)), nil
}

// WriteProtocolVersion writes a ProtocolVersion as 3 bytes (major.minor.patch).
func WriteProtocolVersion(w *bufio.Writer, version types.ProtocolVersion) error {
	if err := writeByte(w, version.Major); err != nil {
		return err
	}
	if err := writeByte(w, version.Minor); err != nil {
		return err
	}
	return writeByte(w, version.Patch)
}

// ReadProtocolVersion reads a ProtocolVersion from 3 bytes.
func ReadProtocolVersion(r *bufio.Reader) (types.ProtocolVersion, error) {
	major, err := readByte(r)
	if err != nil {
		return types.ProtocolVersion{}, err
	}
	minor, err := readByte(r)
	if err != nil {
		return types.ProtocolVersion{}, err
	}
	patch, err := readByte(r)
	if err != nil {
		return types.ProtocolVersion{}, err
	}
	return types.ProtocolVersion{Major: major, Minor: minor, Patch: patch}, nil
}

// WriteHandshakeRequest writes a HandshakeRequest message.
func WriteHandshakeRequest(w *bufio.Writer, req types.HandshakeRequest) error {
	// Write message type
	if err := writeByte(w, byte(types.MSG_HANDSHAKE_REQUEST)); err != nil {
		return err
	}
	// Write auth token
	if err := writeString(w, req.AuthToken); err != nil {
		return err
	}
	// Write client version
	if err := WriteProtocolVersion(w, req.ClientVersion); err != nil {
		return err
	}
	// Write peer name
	return writeString(w, req.PeerName)
}

// WriteHandshakeResponse writes a HandshakeResponse message.
func WriteHandshakeResponse(w *bufio.Writer, resp types.HandshakeResponse) error {
	// Write message type
	if err := writeByte(w, byte(types.MSG_HANDSHAKE_RESPONSE)); err != nil {
		return err
	}
	// Write success flag
	if err := writeBool(w, resp.Success); err != nil {
		return err
	}
	// Write server version
	if err := WriteProtocolVersion(w, resp.ServerVersion); err != nil {
		return err
	}
	// Write error message
	return writeString(w, resp.ErrorMessage)
}

// writeProtocolVersion writes a ProtocolVersion (3 bytes: Major, Minor, Patch).
func writeProtocolVersion(w *bufio.Writer, v types.ProtocolVersion) error {
	if err := writeByte(w, v.Major); err != nil {
		return err
	}
	if err := writeByte(w, v.Minor); err != nil {
		return err
	}
	return writeByte(w, v.Patch)
}

// readProtocolVersion reads a ProtocolVersion (3 bytes: Major, Minor, Patch).
func readProtocolVersion(r *bufio.Reader) (types.ProtocolVersion, error) {
	major, err := readByte(r)
	if err != nil {
		return types.ProtocolVersion{}, err
	}
	minor, err := readByte(r)
	if err != nil {
		return types.ProtocolVersion{}, err
	}
	patch, err := readByte(r)
	if err != nil {
		return types.ProtocolVersion{}, err
	}
	return types.ProtocolVersion{Major: major, Minor: minor, Patch: patch}, nil
}

// writeFileSyncEventType writes a FileSyncEventType byte.
func writeFileSyncEventType(w *bufio.Writer, et types.FileSyncEventType) error {
	return writeByte(w, byte(et))
}

// readFileSyncEventType reads a FileSyncEventType byte.
func readFileSyncEventType(r *bufio.Reader) (types.FileSyncEventType, error) {
	b, err := readByte(r)
	return types.FileSyncEventType(b), err
}

// writeCommunicationOrigin writes a CommunicationOrigin byte.
func writeCommunicationOrigin(w *bufio.Writer, co types.CommunicationOrigin) error {
	return writeByte(w, byte(co))
}

// readCommunicationOrigin reads a CommunicationOrigin byte.
func readCommunicationOrigin(r *bufio.Reader) (types.CommunicationOrigin, error) {
	b, err := readByte(r)
	return types.CommunicationOrigin(b), err
}

// writeCompareStrategy writes a CompareStrategy byte.
func writeCompareStrategy(w *bufio.Writer, cs types.CompareStrategy) error {
	return writeByte(w, byte(cs))
}

// readCompareStrategy reads a CompareStrategy byte.
func readCompareStrategy(r *bufio.Reader) (types.CompareStrategy, error) {
	b, err := readByte(r)
	return types.CompareStrategy(b), err
}

// --- Message Structures and Encoding/Decoding ---

// HandshakeRequest represents MSG_HANDSHAKE_REQUEST (0x01).
type HandshakeRequest struct {
	AuthToken     string
	ClientVersion types.ProtocolVersion
	PeerName      string
}

// NewHandshakeRequest creates a new HandshakeRequest.
func NewHandshakeRequest(authToken string, clientVersion types.ProtocolVersion, peerName string) *HandshakeRequest {
	return &HandshakeRequest{
		AuthToken:     authToken,
		ClientVersion: clientVersion,
		PeerName:      peerName,
	}
}

// Encode writes the HandshakeRequest to the writer.
func (m *HandshakeRequest) Encode(w *bufio.Writer) error {
	if err := writeByte(w, byte(types.MSG_HANDSHAKE_REQUEST)); err != nil {
		return err
	}
	if err := writeString(w, m.AuthToken); err != nil {
		return err
	}
	if err := writeProtocolVersion(w, m.ClientVersion); err != nil {
		return err
	}
	if err := writeString(w, m.PeerName); err != nil {
		return err
	}
	return nil
}

// DecodeHandshakeRequest reads a HandshakeRequest from the reader.
// It assumes the message type byte has already been read.
func DecodeHandshakeRequest(r *bufio.Reader) (*HandshakeRequest, error) {
	authToken, err := readString(r)
	if err != nil {
		return nil, fmt.Errorf("failed to read AuthToken: %w", err)
	}
	clientVersion, err := readProtocolVersion(r)
	if err != nil {
		return nil, fmt.Errorf("failed to read ClientVersion: %w", err)
	}
	peerName, err := readString(r)
	if err != nil {
		return nil, fmt.Errorf("failed to read PeerName: %w", err)
	}
	return &HandshakeRequest{
		AuthToken:     authToken,
		ClientVersion: clientVersion,
		PeerName:      peerName,
	}, nil
}

// HandshakeResponse represents MSG_HANDSHAKE_RESPONSE (0x02).
type HandshakeResponse struct {
	Success       bool
	ServerVersion types.ProtocolVersion
	ErrorMessage  string
}

// NewHandshakeResponse creates a new HandshakeResponse.
func NewHandshakeResponse(success bool, serverVersion types.ProtocolVersion, errorMessage string) *HandshakeResponse {
	return &HandshakeResponse{
		Success:       success,
		ServerVersion: serverVersion,
		ErrorMessage:  errorMessage,
	}
}

// Encode writes the HandshakeResponse to the writer.
func (m *HandshakeResponse) Encode(w *bufio.Writer) error {
	if err := writeByte(w, byte(types.MSG_HANDSHAKE_RESPONSE)); err != nil {
		return err
	}
	if err := writeBool(w, m.Success); err != nil {
		return err
	}
	if err := writeProtocolVersion(w, m.ServerVersion); err != nil {
		return err
	}
	if err := writeString(w, m.ErrorMessage); err != nil {
		return err
	}
	return nil
}

// DecodeHandshakeResponse reads a HandshakeResponse from the reader.
// It assumes the message type byte has already been read.
func DecodeHandshakeResponse(r *bufio.Reader) (*HandshakeResponse, error) {
	success, err := readBool(r)
	if err != nil {
		return nil, fmt.Errorf("failed to read Success: %w", err)
	}
	serverVersion, err := readProtocolVersion(r)
	if err != nil {
		return nil, fmt.Errorf("failed to read ServerVersion: %w", err)
	}
	errorMessage, err := readString(r)
	if err != nil {
		return nil, fmt.Errorf("failed to read ErrorMessage: %w", err)
	}
	return &HandshakeResponse{
		Success:       success,
		ServerVersion: serverVersion,
		ErrorMessage:  errorMessage,
	}, nil
}

// ErrorMessage represents MSG_ERROR (0xFE).
type ErrorMessage struct {
	Code    uint16
	Message string
}

// NewErrorMessage creates a new ErrorMessage.
func NewErrorMessage(code uint16, message string) *ErrorMessage {
	return &ErrorMessage{
		Code:    code,
		Message: message,
	}
}

// Encode writes the ErrorMessage to the writer.
func (m *ErrorMessage) Encode(w *bufio.Writer) error {
	if err := writeByte(w, byte(types.MSG_ERROR)); err != nil {
		return err
	}
	if err := writeUint16(w, m.Code); err != nil {
		return err
	}
	if err := writeString(w, m.Message); err != nil {
		return err
	}
	return nil
}

// DecodeErrorMessage reads an ErrorMessage from the reader.
// It assumes the message type byte has already been read.
func DecodeErrorMessage(r *bufio.Reader) (*ErrorMessage, error) {
	code, err := readUint16(r)
	if err != nil {
		return nil, fmt.Errorf("failed to read error code: %w", err)
	}
	message, err := readString(r)
	if err != nil {
		return nil, fmt.Errorf("failed to read error message: %w", err)
	}
	return &ErrorMessage{
		Code:    code,
		Message: message,
	}, nil
}
