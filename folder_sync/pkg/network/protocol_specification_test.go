package network_test

import (
	"bufio"
	"bytes"
	"encoding/binary"
	"testing"
	"time"

	"github.com/real-rm/folder_sync/pkg/network"
	"github.com/real-rm/folder_sync/pkg/types"
)

// TestBinaryProtocolEncoding tests the binary protocol encoding as documented in section 2 of Sync Protocol
func TestBinaryProtocolEncoding(t *testing.T) {
	t.Run("basic data types encoding", func(t *testing.T) {
		var buf bytes.Buffer
		writer := bufio.NewWriter(&buf)

		// Test uint32 encoding (big-endian)
		testUint32 := uint32(0x12345678)
		err := network.WriteUint32(writer, testUint32)
		if err != nil {
			t.<PERSON>rrorf("WriteUint32 failed: %v", err)
		}
		writer.Flush()

		// Verify big-endian encoding
		expected := []byte{0x12, 0x34, 0x56, 0x78}
		if !bytes.Equal(buf.Bytes(), expected) {
			t.<PERSON>("Expected %v, got %v", expected, buf.Bytes())
		}

		// Test reading back
		reader := bufio.NewReader(&buf)
		readUint32, err := network.ReadUint32(reader)
		if err != nil {
			t.Errorf("ReadUint32 failed: %v", err)
		}
		if readUint32 != testUint32 {
			t.Errorf("Expected %d, got %d", testUint32, readUint32)
		}
	})

	t.Run("uint64 encoding", func(t *testing.T) {
		var buf bytes.Buffer
		writer := bufio.NewWriter(&buf)

		// Test uint64 encoding (big-endian)
		testUint64 := uint64(0x123456789ABCDEF0)
		err := network.WriteUint64(writer, testUint64)
		if err != nil {
			t.Errorf("WriteUint64 failed: %v", err)
		}
		writer.Flush()

		// Verify big-endian encoding
		expected := []byte{0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0}
		if !bytes.Equal(buf.Bytes(), expected) {
			t.Errorf("Expected %v, got %v", expected, buf.Bytes())
		}

		// Test reading back
		reader := bufio.NewReader(&buf)
		readUint64, err := network.ReadUint64(reader)
		if err != nil {
			t.Errorf("ReadUint64 failed: %v", err)
		}
		if readUint64 != testUint64 {
			t.Errorf("Expected %d, got %d", testUint64, readUint64)
		}
	})

	t.Run("string encoding with length prefix", func(t *testing.T) {
		var buf bytes.Buffer
		writer := bufio.NewWriter(&buf)

		// Test string encoding (uint16 length + UTF-8 bytes)
		testString := "Hello, 世界!"
		err := network.WriteString(writer, testString)
		if err != nil {
			t.Errorf("WriteString failed: %v", err)
		}
		writer.Flush()

		// Verify length-prefixed encoding
		utf8Bytes := []byte(testString)
		expectedLength := uint16(len(utf8Bytes))
		
		reader := bufio.NewReader(&buf)
		
		// Read length
		var length uint16
		err = binary.Read(reader, binary.BigEndian, &length)
		if err != nil {
			t.Errorf("Failed to read string length: %v", err)
		}
		if length != expectedLength {
			t.Errorf("Expected length %d, got %d", expectedLength, length)
		}

		// Read string bytes
		stringBytes := make([]byte, length)
		_, err = reader.Read(stringBytes)
		if err != nil {
			t.Errorf("Failed to read string bytes: %v", err)
		}
		if string(stringBytes) != testString {
			t.Errorf("Expected '%s', got '%s'", testString, string(stringBytes))
		}
	})

	t.Run("byte array encoding with length prefix", func(t *testing.T) {
		var buf bytes.Buffer
		writer := bufio.NewWriter(&buf)

		// Test byte array encoding (uint32 length + bytes)
		testBytes := []byte{0x01, 0x02, 0x03, 0x04, 0xFF}
		err := network.WriteBytes(writer, testBytes)
		if err != nil {
			t.Errorf("WriteBytes failed: %v", err)
		}
		writer.Flush()

		// Verify length-prefixed encoding
		reader := bufio.NewReader(&buf)
		
		// Read length
		var length uint32
		err = binary.Read(reader, binary.BigEndian, &length)
		if err != nil {
			t.Errorf("Failed to read bytes length: %v", err)
		}
		if length != uint32(len(testBytes)) {
			t.Errorf("Expected length %d, got %d", len(testBytes), length)
		}

		// Read bytes
		readBytes := make([]byte, length)
		_, err = reader.Read(readBytes)
		if err != nil {
			t.Errorf("Failed to read bytes: %v", err)
		}
		if !bytes.Equal(readBytes, testBytes) {
			t.Errorf("Expected %v, got %v", testBytes, readBytes)
		}
	})

	t.Run("time encoding as unix nano", func(t *testing.T) {
		var buf bytes.Buffer
		writer := bufio.NewWriter(&buf)

		// Test time.Time encoding (uint64 Unix nanoseconds)
		testTime := time.Date(2023, 12, 25, 15, 30, 45, 123456789, time.UTC)
		err := network.WriteTime(writer, testTime)
		if err != nil {
			t.Errorf("WriteTime failed: %v", err)
		}
		writer.Flush()

		// Test reading back
		reader := bufio.NewReader(&buf)
		readTime, err := network.ReadTime(reader)
		if err != nil {
			t.Errorf("ReadTime failed: %v", err)
		}
		
		// Compare Unix nanoseconds (should be identical)
		if readTime.UnixNano() != testTime.UnixNano() {
			t.Errorf("Expected %d, got %d", testTime.UnixNano(), readTime.UnixNano())
		}
	})
}

// TestMessageTypeEnums tests the message type enums as documented in section 4 of Sync Protocol
func TestMessageTypeEnums(t *testing.T) {
	tests := []struct {
		name     string
		msgType  types.MessageType
		expected byte
	}{
		{"MSG_HANDSHAKE_REQUEST", types.MSG_HANDSHAKE_REQUEST, 0x01},
		{"MSG_HANDSHAKE_RESPONSE", types.MSG_HANDSHAKE_RESPONSE, 0x02},
		{"MSG_FILE_SYNC_EVENT", types.MSG_FILE_SYNC_EVENT, 0x03},
		{"MSG_PULL_FILE_REQUEST", types.MSG_PULL_FILE_REQUEST, 0x04},
		{"MSG_PULL_FILE_RESPONSE", types.MSG_PULL_FILE_RESPONSE, 0x05},
		{"MSG_SYNC_ACKNOWLEDGE", types.MSG_SYNC_ACKNOWLEDGE, 0x06},
		{"MSG_FILE_REMOVED", types.MSG_FILE_REMOVED, 0x07},
		{"MSG_SAME_CONTENT", types.MSG_SAME_CONTENT, 0x08},
		{"MSG_FILE_CHUNK", types.MSG_FILE_CHUNK, 0x09},
		{"MSG_CHUNK_ACKNOWLEDGE", types.MSG_CHUNK_ACKNOWLEDGE, 0x0A},
		{"MSG_PROXY_WELCOME_REQUEST", types.MSG_PROXY_WELCOME_REQUEST, 0x10},
		{"MSG_PROXY_WELCOME_RESPONSE", types.MSG_PROXY_WELCOME_RESPONSE, 0x11},
		{"MSG_PROXY_TEST_CONNECTION_REQUEST", types.MSG_PROXY_TEST_CONNECTION_REQUEST, 0x12},
		{"MSG_PROXY_TEST_CONNECTION_RESPONSE", types.MSG_PROXY_TEST_CONNECTION_RESPONSE, 0x13},
		{"MSG_PROXY_EVENT_ACKNOWLEDGE", types.MSG_PROXY_EVENT_ACKNOWLEDGE, 0x14},
		{"MSG_ERROR", types.MSG_ERROR, 0xFE},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if byte(tt.msgType) != tt.expected {
				t.Errorf("Expected %s to be 0x%02X, got 0x%02X", tt.name, tt.expected, byte(tt.msgType))
			}
		})
	}
}

// TestFileSyncEventTypeEnums tests the FileSyncEventType enums as documented
func TestFileSyncEventTypeEnums(t *testing.T) {
	tests := []struct {
		name      string
		eventType types.FileSyncEventType
		expected  byte
	}{
		{"CREATE_FILE", types.CREATE_FILE, 0x01},
		{"WRITE_FILE", types.WRITE_FILE, 0x02},
		{"DELETE_FILE", types.DELETE_FILE, 0x03},
		{"RENAME_FILE", types.RENAME_FILE, 0x04},
		{"CHMOD_FILE", types.CHMOD_FILE, 0x05},
		{"CREATE_DIR", types.CREATE_DIR, 0x06},
		{"DELETE_DIR", types.DELETE_DIR, 0x07},
		{"RENAME_DIR", types.RENAME_DIR, 0x08},
		{"CREATE_SYMLINK", types.CREATE_SYMLINK, 0x09},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if byte(tt.eventType) != tt.expected {
				t.Errorf("Expected %s to be 0x%02X, got 0x%02X", tt.name, tt.expected, byte(tt.eventType))
			}
		})
	}
}

// TestCompareStrategyEnums tests the FileCompareStrategy enums as documented
func TestCompareStrategyEnums(t *testing.T) {
	tests := []struct {
		name     string
		strategy types.CompareStrategy
		expected byte
	}{
		{"SIZE_ONLY", types.SIZE_ONLY, 0x01},
		{"FIRST_LAST_BLOCK_SAMPLE", types.FIRST_LAST_BLOCK_SAMPLE, 0x02},
		{"FULL_FILE", types.FULL_FILE, 0x03},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if byte(tt.strategy) != tt.expected {
				t.Errorf("Expected %s to be 0x%02X, got 0x%02X", tt.name, tt.expected, byte(tt.strategy))
			}
		})
	}
}

// TestProtocolVersionEncoding tests the ProtocolVersion encoding as documented
func TestProtocolVersionEncoding(t *testing.T) {
	t.Run("protocol version encoding", func(t *testing.T) {
		var buf bytes.Buffer
		writer := bufio.NewWriter(&buf)

		// Test ProtocolVersion encoding (3 bytes: major.minor.patch)
		version := types.ProtocolVersion{Major: 1, Minor: 2, Patch: 3}
		err := network.WriteProtocolVersion(writer, version)
		if err != nil {
			t.Errorf("WriteProtocolVersion failed: %v", err)
		}
		writer.Flush()

		// Verify 3-byte encoding
		expected := []byte{0x01, 0x02, 0x03}
		if !bytes.Equal(buf.Bytes(), expected) {
			t.Errorf("Expected %v, got %v", expected, buf.Bytes())
		}

		// Test reading back
		reader := bufio.NewReader(&buf)
		readVersion, err := network.ReadProtocolVersion(reader)
		if err != nil {
			t.Errorf("ReadProtocolVersion failed: %v", err)
		}
		if readVersion.Major != version.Major || readVersion.Minor != version.Minor || readVersion.Patch != version.Patch {
			t.Errorf("Expected %+v, got %+v", version, readVersion)
		}
	})
}

// TestHandshakeMessageFlow tests the handshake flow as documented in section 5.1
func TestHandshakeMessageFlow(t *testing.T) {
	t.Run("successful handshake flow", func(t *testing.T) {
		// Test the documented handshake sequence:
		// 1. Client sends MSG_HANDSHAKE_REQUEST
		// 2. Server receives, authenticates, checks version, and sends MSG_HANDSHAKE_RESPONSE
		// 3. If Success is false, connection closes. If true, communication proceeds.

		var buf bytes.Buffer
		writer := bufio.NewWriter(&buf)

		// Step 1: Client sends MSG_HANDSHAKE_REQUEST
		request := types.HandshakeRequest{
			AuthToken:     "test-token-12345678901234567890123456",
			ClientVersion: types.ProtocolVersion{Major: 1, Minor: 0, Patch: 0},
		}

		err := network.WriteHandshakeRequest(writer, request)
		if err != nil {
			t.Errorf("WriteHandshakeRequest failed: %v", err)
		}
		writer.Flush()

		// Verify message starts with correct message type
		reader := bufio.NewReader(&buf)
		msgType, err := reader.ReadByte()
		if err != nil {
			t.Errorf("Failed to read message type: %v", err)
		}
		if msgType != byte(types.MSG_HANDSHAKE_REQUEST) {
			t.Errorf("Expected message type 0x%02X, got 0x%02X", byte(types.MSG_HANDSHAKE_REQUEST), msgType)
		}

		// Step 2: Server responds with MSG_HANDSHAKE_RESPONSE
		buf.Reset()
		writer.Reset(&buf)

		response := types.HandshakeResponse{
			Success:       true,
			ServerVersion: types.ProtocolVersion{Major: 1, Minor: 0, Patch: 0},
			ErrorMessage:  "",
		}

		err = network.WriteHandshakeResponse(writer, response)
		if err != nil {
			t.Errorf("WriteHandshakeResponse failed: %v", err)
		}
		writer.Flush()

		// Verify response message type
		reader.Reset(&buf)
		msgType, err = reader.ReadByte()
		if err != nil {
			t.Errorf("Failed to read response message type: %v", err)
		}
		if msgType != byte(types.MSG_HANDSHAKE_RESPONSE) {
			t.Errorf("Expected message type 0x%02X, got 0x%02X", byte(types.MSG_HANDSHAKE_RESPONSE), msgType)
		}

		t.Log("Handshake message flow test completed successfully")
	})

	t.Run("failed authentication handshake", func(t *testing.T) {
		var buf bytes.Buffer
		writer := bufio.NewWriter(&buf)

		// Test failed authentication response
		response := types.HandshakeResponse{
			Success:       false,
			ServerVersion: types.ProtocolVersion{Major: 1, Minor: 0, Patch: 0},
			ErrorMessage:  "Invalid authentication token",
		}

		err := network.WriteHandshakeResponse(writer, response)
		if err != nil {
			t.Errorf("WriteHandshakeResponse failed: %v", err)
		}
		writer.Flush()

		// In this case, connection should be closed after this response
		t.Log("Failed authentication handshake test completed")
	})
}
