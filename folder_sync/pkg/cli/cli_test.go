package cli_test

import (
	"io"
	"os"
	"strings"
	"testing"

	"github.com/real-rm/folder_sync/pkg/cli"
	"github.com/real-rm/folder_sync/pkg/config" // To access DefaultConfigPath
)

// TestParseCLI_Defaults tests default CLI argument values.
func TestParseCLI_Defaults(t *testing.T) {
	args := cli.ParseCLIWithArgs([]string{}) // No arguments

	if args.ConfigFile != config.DefaultConfigPath {
		t.Errorf("Expected default ConfigFile %q, got %q", config.DefaultConfigPath, args.ConfigFile)
	}
	if args.TestConfig != false {
		t.Errorf("Expected default TestConfig false, got %t", args.TestConfig)
	}
	if args.DebugMode != false {
		t.Errorf("Expected default DebugMode false, got %t", args.DebugMode)
	}
	if args.ShowHelp != false {
		t.Errorf("Expected default ShowHelp false, got %t", args.ShowHelp)
	}
	if args.Daemonize != false {
		t.Errorf("Expected default Daemonize false, got %t", args.Daemonize)
	}
}

// TestParseCLI_CustomConfigFile tests -c flag.
func TestParseCLI_CustomConfigFile(t *testing.T) {
	customPath := "/tmp/my_custom_config.yaml"
	args := cli.ParseCLIWithArgs([]string{"-c", customPath})

	if args.ConfigFile != customPath {
		t.Errorf("Expected ConfigFile %q, got %q", customPath, args.ConfigFile)
	}
}

// TestParseCLI_TestConfigFlag tests -t flag.
func TestParseCLI_TestConfigFlag(t *testing.T) {
	args := cli.ParseCLIWithArgs([]string{"-t"})

	if args.TestConfig != true {
		t.Errorf("Expected TestConfig true, got %t", args.TestConfig)
	}
}

// TestParseCLI_DebugModeFlag tests -v flag.
func TestParseCLI_DebugModeFlag(t *testing.T) {
	args := cli.ParseCLIWithArgs([]string{"-v"})

	if args.DebugMode != true {
		t.Errorf("Expected DebugMode true, got %t", args.DebugMode)
	}
}

// TestParseCLI_ShowHelpFlag tests -h flag.
func TestParseCLI_ShowHelpFlag(t *testing.T) {
	args := cli.ParseCLIWithArgs([]string{"-h"})

	if args.ShowHelp != true {
		t.Errorf("Expected ShowHelp true, got %t", args.ShowHelp)
	}
}

// TestParseCLI_DaemonizeFlag tests -d flag.
func TestParseCLI_DaemonizeFlag(t *testing.T) {
	args := cli.ParseCLIWithArgs([]string{"-d"})

	if args.Daemonize != true {
		t.Errorf("Expected Daemonize true, got %t", args.Daemonize)
	}
}

// TestParseCLI_Combination tests a combination of flags.
func TestParseCLI_Combination(t *testing.T) {
	customPath := "/etc/prod_config.yaml"
	args := cli.ParseCLIWithArgs([]string{"-c", customPath, "-t", "-v"})

	if args.ConfigFile != customPath {
		t.Errorf("Expected ConfigFile %q, got %q", customPath, args.ConfigFile)
	}
	if args.TestConfig != true {
		t.Errorf("Expected TestConfig true, got %t", args.TestConfig)
	}
	if args.DebugMode != true {
		t.Errorf("Expected DebugMode true, got %t", args.DebugMode)
	}
	if args.ShowHelp != false { // Should still be false if not explicitly set
		t.Errorf("Expected ShowHelp false, got %t", args.ShowHelp)
	}
}

// TestPrintHelp tests the output of the PrintHelp function.
func TestPrintHelp(t *testing.T) {
	// Redirect both stdout and stderr to capture output
	oldStdout := os.Stdout
	oldStderr := os.Stderr
	r, w, _ := os.Pipe()
	os.Stdout = w
	os.Stderr = w

	cli.PrintHelp()

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout // Restore stdout
	os.Stderr = oldStderr // Restore stderr

	output := string(out)

	// Check for key phrases in the help output
	if !strings.Contains(output, "Usage:") {
		t.Error("Help output missing 'Usage:'")
	}
	if !strings.Contains(output, "Specify configuration file path") {
		t.Error("Help output missing description for -c flag")
	}
	if !strings.Contains(output, "Test configuration file") {
		t.Error("Help output missing description for -t flag")
	}
	if !strings.Contains(output, "Enable verbose/debug mode") {
		t.Error("Help output missing description for -v flag")
	}
	if !strings.Contains(output, "Display help message") {
		t.Error("Help output missing description for -h flag")
	}
	if !strings.Contains(output, "Daemonize the program") {
		t.Error("Help output missing description for -d flag")
	}
	if !strings.Contains(output, "Examples:") {
		t.Error("Help output missing 'Examples:' section")
	}
}
