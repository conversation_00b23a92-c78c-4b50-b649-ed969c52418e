package cli

import (
	"flag"
	"fmt"
	"os"

	"github.com/real-rm/folder_sync/pkg/config" // Import the config package for DefaultConfigPath
)

// CLIArgs holds the parsed command-line arguments.
type CLIArgs struct {
	ConfigFile string // Path to the configuration file.
	TestConfig bool   // If true, validate config and exit.
	DebugMode  bool   // If true, enable debug logging.
	ShowHelp   bool   // If true, display help message and exit.
	Daemonize  bool   // If true, daemonize the program (handled by systemd integration).
}

// ParseCLI parses command-line arguments and returns a CLIArgs struct.
// This function creates a new FlagSet to avoid conflicts during testing.
func ParseCLI() *CLIArgs {
	return ParseCLIWithArgs(os.Args[1:])
}

// ParseCLIWithArgs parses the provided arguments and returns a CLIArgs struct.
// This function is useful for testing with custom arguments.
func ParseCLIWithArgs(args []string) *CLIArgs {
	cliArgs := &CLIArgs{}

	// Create a new FlagSet to avoid conflicts with global flags
	fs := flag.NewFlagSet("folder_sync", flag.ExitOnError)

	// Define flags
	fs.StringVar(&cliArgs.ConfigFile, "c", config.DefaultConfigPath, "Specify configuration file path")
	fs.BoolVar(&cliArgs.TestConfig, "t", false, "Test configuration file for conflicting or unrecognized parameters; program exits after validation")
	fs.BoolVar(&cliArgs.DebugMode, "v", false, "Enable verbose/debug mode logging (overrides debug setting in config)")
	fs.BoolVar(&cliArgs.ShowHelp, "h", false, "Display help message")
	fs.BoolVar(&cliArgs.Daemonize, "d", false, "Daemonize the program (handled by systemd integration)")

	// Parse the flags
	fs.Parse(args)

	return cliArgs
}

// PrintHelp prints the usage instructions for the CLI.
func PrintHelp() {
	// Create a temporary FlagSet to get the flag descriptions
	fs := flag.NewFlagSet("folder_sync", flag.ExitOnError)
	var configFile string
	var testConfig, debugMode, showHelp, daemonize bool

	fs.StringVar(&configFile, "c", config.DefaultConfigPath, "Specify configuration file path")
	fs.BoolVar(&testConfig, "t", false, "Test configuration file for conflicting or unrecognized parameters; program exits after validation")
	fs.BoolVar(&debugMode, "v", false, "Enable verbose/debug mode logging (overrides debug setting in config)")
	fs.BoolVar(&showHelp, "h", false, "Display help message")
	fs.BoolVar(&daemonize, "d", false, "Daemonize the program (handled by systemd integration)")

	fmt.Printf("Usage: %s [options]\n", os.Args[0])
	fmt.Println("  A Go program designed to synchronize folders between multiple servers.")
	fmt.Println("\nOptions:")
	fs.PrintDefaults()
	fmt.Println("\nExamples:")
	fmt.Printf("  %s -c /etc/my_folder_sync.yaml\n", os.Args[0])
	fmt.Printf("  %s -t\n", os.Args[0])
	fmt.Printf("  %s -v\n", os.Args[0])
}
