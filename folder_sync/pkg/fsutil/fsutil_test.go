package fsutil_test

import (
	"bytes"
	"fmt"
	"io/fs"
	"math/rand"
	"os"
	"path/filepath"
	"strings"
	"sync"    // Added sync for MockLogger
	"syscall" // Re-added for uid/gid
	"testing"
	"time" // Added time for random seed

	"github.com/real-rm/folder_sync/pkg/fsutil"
	"github.com/real-rm/folder_sync/pkg/types"

	"github.com/cespare/xxhash/v2" // For direct hash comparison
)

// MockLogger for testing fsutil package
type MockLogger struct {
	Errors []string
	Infos  []string
	Debugs []string
	Warns  []string
	mu     sync.Mutex
	t      *testing.T // For reporting test failures directly
}

func NewMockLogger(t *testing.T) *MockLogger {
	return &MockLogger{t: t}
}

func (m *MockLogger) Error(format string, args ...interface{}) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.Errors = append(m.Errors, fmt.Sprintf(format, args...))
	m.t.Logf("[MockLogger ERROR] %s", fmt.Sprintf(format, args...))
}
func (m *MockLogger) Info(format string, args ...interface{}) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.Infos = append(m.Infos, fmt.Sprintf(format, args...))
	m.t.Logf("[MockLogger INFO] %s", fmt.Sprintf(format, args...))
}
func (m *MockLogger) Debug(format string, args ...interface{}) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.Debugs = append(m.Debugs, fmt.Sprintf(format, args...))
	m.t.Logf("[MockLogger DEBUG] %s", fmt.Sprintf(format, args...))
}
func (m *MockLogger) Warn(format string, args ...interface{}) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.Warns = append(m.Warns, fmt.Sprintf(format, args...))
	m.t.Logf("[MockLogger WARN] %s", fmt.Sprintf(format, args...))
}

// TestAtomicWriteFile tests the atomic file write functionality.
func TestAtomicWriteFile(t *testing.T) {
	tempDir := t.TempDir()
	filePath := filepath.Join(tempDir, "testfile.txt")
	data := []byte("hello atomic world")
	perm := os.FileMode(0644)

	err := fsutil.AtomicWriteFile(filePath, data, perm)
	if err != nil {
		t.Fatalf("AtomicWriteFile failed: %v", err)
	}

	// Verify file exists
	info, err := os.Stat(filePath)
	if err != nil {
		t.Fatalf("File %q does not exist after write: %v", filePath, err)
	}
	if info.Size() != int64(len(data)) {
		t.Errorf("File size mismatch: got %d, want %d", info.Size(), len(data))
	}
	if info.Mode() != perm {
		// Use &^ (clear bits) to ignore sticky bit, setuid/setgid bits that might be set by OS
		if (info.Mode() & fs.ModePerm) != perm {
			t.Errorf("File permissions mismatch: got %o, want %o (raw: %o)", info.Mode()&fs.ModePerm, perm, info.Mode())
		}
	}

	// Verify content
	readData, err := os.ReadFile(filePath)
	if err != nil {
		t.Fatalf("Failed to read file %q: %v", filePath, err)
	}
	if !bytes.Equal(readData, data) {
		t.Errorf("File content mismatch: got %q, want %q", string(readData), string(data))
	}

	// Verify no temporary files are left
	tempFiles, err := filepath.Glob(filepath.Join(tempDir, "testfile.txt.tmp-*"))
	if err != nil {
		t.Fatalf("Failed to glob for temp files: %v", err)
	}
	if len(tempFiles) > 0 {
		t.Errorf("Temporary files found after successful write: %v", tempFiles)
	}
}

// TestAtomicWriteFile_SmallVsLargeFiles tests that small files don't create temporary files
func TestAtomicWriteFile_SmallVsLargeFiles(t *testing.T) {
	tempDir := t.TempDir()

	t.Run("Small file (no temp file)", func(t *testing.T) {
		filePath := filepath.Join(tempDir, "small_file.txt")
		smallData := []byte("small") // 5 bytes, well under 4KB threshold
		perm := os.FileMode(0644)

		// Monitor directory for temporary files
		initialFiles, err := os.ReadDir(tempDir)
		if err != nil {
			t.Fatalf("Failed to read temp dir: %v", err)
		}

		err = fsutil.AtomicWriteFile(filePath, smallData, perm)
		if err != nil {
			t.Fatalf("AtomicWriteFile failed for small file: %v", err)
		}

		// Check that no temporary files were created
		finalFiles, err := os.ReadDir(tempDir)
		if err != nil {
			t.Fatalf("Failed to read temp dir after write: %v", err)
		}

		// Should only have one more file (the target file)
		if len(finalFiles) != len(initialFiles)+1 {
			t.Errorf("Expected exactly one new file, got %d new files", len(finalFiles)-len(initialFiles))
		}

		// Verify the file was created correctly
		content, err := os.ReadFile(filePath)
		if err != nil {
			t.Fatalf("Failed to read written file: %v", err)
		}
		if !bytes.Equal(content, smallData) {
			t.Errorf("File content mismatch: got %q, want %q", content, smallData)
		}
	})

	t.Run("Large file (uses temp file)", func(t *testing.T) {
		filePath := filepath.Join(tempDir, "large_file.txt")
		largeData := make([]byte, 5000) // 5KB, over 4KB threshold
		for i := range largeData {
			largeData[i] = byte(i % 256)
		}
		perm := os.FileMode(0644)

		err := fsutil.AtomicWriteFile(filePath, largeData, perm)
		if err != nil {
			t.Fatalf("AtomicWriteFile failed for large file: %v", err)
		}

		// Verify the file was created correctly
		content, err := os.ReadFile(filePath)
		if err != nil {
			t.Fatalf("Failed to read written file: %v", err)
		}
		if !bytes.Equal(content, largeData) {
			t.Errorf("File content mismatch for large file")
		}
	})
}

// TestAtomicWriteFile_Overwrite tests overwriting an existing file atomically.
func TestAtomicWriteFile_Overwrite(t *testing.T) {
	tempDir := t.TempDir()
	filePath := filepath.Join(tempDir, "existing.txt")
	initialData := []byte("initial content")
	overwriteData := []byte("new content is here")
	perm := os.FileMode(0644)

	// Create initial file
	err := os.WriteFile(filePath, initialData, perm)
	if err != nil {
		t.Fatalf("Failed to create initial file: %v", err)
	}

	err = fsutil.AtomicWriteFile(filePath, overwriteData, perm)
	if err != nil {
		t.Fatalf("AtomicWriteFile failed during overwrite: %v", err)
	}

	readData, err := os.ReadFile(filePath)
	if err != nil {
		t.Fatalf("Failed to read overwritten file: %v", err)
	}
	if !bytes.Equal(readData, overwriteData) {
		t.Errorf("Overwritten file content mismatch: got %q, want %q", string(readData), string(overwriteData))
	}

	info, err := os.Stat(filePath)
	if err != nil {
		t.Fatalf("Failed to stat overwritten file: %v", err)
	}
	if info.Size() != int64(len(overwriteData)) {
		t.Errorf("Overwritten file size mismatch: got %d, want %d", info.Size(), len(overwriteData))
	}
}

// TestAtomicWriteFile_NoDir tests writing to a non-existent directory (should fail).
func TestAtomicWriteFile_NoDir(t *testing.T) {
	tempDir := t.TempDir()
	filePath := filepath.Join(tempDir, "nonexistentdir", "file.txt")
	data := []byte("data")
	perm := os.FileMode(0644)

	err := fsutil.AtomicWriteFile(filePath, data, perm)
	if err == nil {
		t.Fatal("Expected AtomicWriteFile to fail for non-existent directory, but it succeeded")
	}
	if !strings.Contains(err.Error(), "no such file or directory") && !strings.Contains(err.Error(), "The system cannot find the path specified") {
		t.Errorf("Expected 'no such file or directory' error, got: %v", err)
	}
}

// generateRandomBytes generates a slice of random bytes of a given size.
func generateRandomBytes(size int) []byte {
	b := make([]byte, size)
	rand.Read(b) // Uses crypto/rand by default, which is good for testing hashes
	return b
}

// calculateRawXXHash calculates xxHash using the library directly for verification.
func calculateRawXXHash(data []byte) []byte {
	h := xxhash.New()
	h.Write(data)
	return h.Sum(nil)
}

// TestCalculateXXHashChecksum_FullFile tests FULL_FILE strategy.
func TestCalculateXXHashChecksum_FullFile(t *testing.T) {
	tempDir := t.TempDir()
	filePath := filepath.Join(tempDir, "full_file.txt")

	testCases := []struct {
		name    string
		content []byte
	}{
		{"Empty file", []byte{}},
		{"Small file", []byte("hello world")},
		{"Medium file", generateRandomBytes(1024 * 50)},      // 50KB
		{"Large file", generateRandomBytes(1024 * 1024 * 2)}, // 2MB
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := os.WriteFile(filePath, tc.content, 0644)
			if err != nil {
				t.Fatalf("Failed to write test file: %v", err)
			}
			defer os.Remove(filePath)

			checksum, err := fsutil.CalculateXXHashChecksum(filePath, types.FULL_FILE, 0) // Block size irrelevant here
			if err != nil {
				t.Fatalf("CalculateXXHashChecksum failed: %v", err)
			}

			expectedChecksum := calculateRawXXHash(tc.content)
			if !bytes.Equal(checksum, expectedChecksum) {
				t.Errorf("Checksum mismatch for %s:\nGot: %x\nWant: %x", tc.name, checksum, expectedChecksum)
			}
		})
	}
}

// TestCalculateXXHashChecksum_Sample tests FIRST_LAST_BLOCK_SAMPLE strategy.
func TestCalculateXXHashChecksum_Sample(t *testing.T) {
	tempDir := t.TempDir()
	filePath := filepath.Join(tempDir, "sample_file.txt")
	const blockSize = 4096 // Standard block size

	// Seed random for reproducible tests if needed, but crypto/rand is generally preferred
	rand.Seed(time.Now().UnixNano())

	testCases := []struct {
		name         string
		fileSize     int
		expectedHash func(fileContent []byte, bs int) []byte // Function to compute expected hash
	}{
		{
			"Empty file",
			0,
			func(content []byte, bs int) []byte {
				return calculateRawXXHash([]byte{})
			},
		},
		{
			"File smaller than block size",
			blockSize / 2,
			func(content []byte, bs int) []byte {
				return calculateRawXXHash(content[:min(len(content), bs)])
			},
		},
		{
			"File exactly one block size",
			blockSize,
			func(content []byte, bs int) []byte {
				return calculateRawXXHash(content[:bs])
			},
		},
		{
			"File slightly larger than one block",
			blockSize + 100,
			func(content []byte, bs int) []byte {
				firstBlock := content[:bs]
				lastBlock := content[len(content)-100:]
				combined := append(firstBlock, lastBlock...)
				return calculateRawXXHash(combined)
			},
		},
		{
			"File much larger than one block",
			blockSize*3 + 500, // Three blocks plus 500 bytes
			func(content []byte, bs int) []byte {
				firstBlock := content[:bs]
				lastBlockCalculatedSize := min(len(content)-bs, bs)
				lastBlock := content[len(content)-lastBlockCalculatedSize:]
				combined := append(firstBlock, lastBlock...)
				return calculateRawXXHash(combined)
			},
		},
		{
			"File with size just above 2 blocks",
			blockSize*2 + 1,
			func(content []byte, bs int) []byte {
				firstBlock := content[:bs]
				// Last block size is min(fileSize - blockSize, blockSize) = min(blockSize + 1, blockSize) = blockSize
				lastBlockSize := min(len(content)-bs, bs)
				lastBlock := content[len(content)-lastBlockSize:]
				combined := append(firstBlock, lastBlock...)
				return calculateRawXXHash(combined)
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			content := generateRandomBytes(tc.fileSize)
			err := os.WriteFile(filePath, content, 0644)
			if err != nil {
				t.Fatalf("Failed to write test file: %v", err)
			}
			defer os.Remove(filePath)

			checksum, err := fsutil.CalculateXXHashChecksum(filePath, types.FIRST_LAST_BLOCK_SAMPLE, blockSize)
			if err != nil {
				t.Fatalf("CalculateXXHashChecksum failed: %v", err)
			}

			expectedChecksum := tc.expectedHash(content, blockSize)
			if !bytes.Equal(checksum, expectedChecksum) {
				t.Errorf("Checksum mismatch for %s:\nGot: %x\nWant: %x", tc.name, checksum, expectedChecksum)
			}
		})
	}

	t.Run("Invalid block size", func(t *testing.T) {
		err := os.WriteFile(filePath, []byte("test"), 0644)
		if err != nil {
			t.Fatalf("Failed to write test file: %v", err)
		}
		defer os.Remove(filePath)

		_, err = fsutil.CalculateXXHashChecksum(filePath, types.FIRST_LAST_BLOCK_SAMPLE, 0)
		if err == nil {
			t.Error("Expected error for zero block size, got nil")
		}
		if err != nil && !strings.Contains(err.Error(), "block size must be positive") {
			t.Errorf("Expected 'block size must be positive' error, got: %v", err)
		}
	})
}

// min helper for sample hash calculation
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// TestCalculateXXHashChecksum_SizeOnly tests SIZE_ONLY strategy.
func TestCalculateXXHashChecksum_SizeOnly(t *testing.T) {
	tempDir := t.TempDir()
	filePath := filepath.Join(tempDir, "size_only.txt")
	os.WriteFile(filePath, []byte("some data"), 0644)
	defer os.Remove(filePath)

	checksum, err := fsutil.CalculateXXHashChecksum(filePath, types.SIZE_ONLY, 0)
	if err != nil {
		t.Fatalf("CalculateXXHashChecksum failed: %v", err)
	}
	if checksum != nil {
		t.Errorf("Expected nil checksum for SIZE_ONLY, got %x", checksum)
	}
}

// TestCalculateXXHashChecksum_UnsupportedStrategy tests unsupported strategy.
func TestCalculateXXHashChecksum_UnsupportedStrategy(t *testing.T) {
	tempDir := t.TempDir()
	filePath := filepath.Join(tempDir, "unsupported.txt")
	os.WriteFile(filePath, []byte("data"), 0644)
	defer os.Remove(filePath)

	_, err := fsutil.CalculateXXHashChecksum(filePath, types.CompareStrategy(0xFF), 0)
	if err == nil {
		t.Error("Expected error for unsupported strategy, got nil")
	}
	if err != nil && !strings.Contains(err.Error(), "unsupported compare strategy") {
		t.Errorf("Expected 'unsupported compare strategy' error, got: %v", err)
	}
}

// TestCalculateXXHashChecksum_FileNotFound tests file not found scenario.
func TestCalculateXXHashChecksum_FileNotFound(t *testing.T) {
	nonExistentPath := "/path/to/non/existent/file.txt"
	_, err := fsutil.CalculateXXHashChecksum(nonExistentPath, types.FULL_FILE, 0)
	if err == nil {
		t.Error("Expected error for file not found, got nil")
	}
	if err != nil && !strings.Contains(err.Error(), "file not found for checksum calculation") {
		t.Errorf("Expected 'file not found' error, got: %v", err)
	}
}

// TestGetOSBlockSize tests GetOSBlockSize function.
func TestGetOSBlockSize(t *testing.T) {
	tempDir := t.TempDir()
	blockSize := fsutil.GetOSBlockSize(tempDir)
	if blockSize <= 0 {
		t.Errorf("Expected positive block size, got %d", blockSize)
	}
	// On Linux, typical values are 4096. On some systems it might be different.
	// Just ensure it's a reasonable positive value.
	if blockSize < 512 || blockSize > 65536 {
		t.Logf("Warning: Block size %d seems unusual, but test allows it.", blockSize)
	}
}

// TestEnsureRelativePath tests converting absolute paths to relative.
func TestEnsureRelativePath(t *testing.T) {
	tests := []struct {
		name      string
		absPath   string
		syncRoot  string
		expected  string
		expectErr bool
	}{
		{"File in root", "/sync/file.txt", "/sync", "file.txt", false},
		{"Dir in root", "/sync/subdir/", "/sync", "subdir", false}, // Trailing slash is handled by filepath.Clean
		{"Nested file", "/sync/dir1/dir2/file.txt", "/sync", "dir1/dir2/file.txt", false},
		{"Sync root itself", "/sync", "/sync", "", false},
		{"Path outside sync root", "/other/file.txt", "/sync", "", true},
		{"Path partly matches", "/sync_backup/file.txt", "/sync", "", true},
		{"Windows path (unsupported platform, but test for consistency)", "C:\\sync\\file.txt", "D:\\sync", "", true},
		{"Windows path in root (unsupported platform, but test for consistency)", "C:\\sync\\file.txt", "C:\\sync", "", true},
		{"Windows nested dir (unsupported platform, but test for consistency)", "C:\\sync\\dir1\\dir2\\file.txt", "C:\\sync", "", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Normalize paths for OS compatibility
			absPath := filepath.FromSlash(tt.absPath)
			syncRoot := filepath.FromSlash(tt.syncRoot)
			expected := filepath.FromSlash(tt.expected)

			relPath, err := fsutil.EnsureRelativePath(absPath, syncRoot)

			if tt.expectErr {
				if err == nil {
					t.Fatalf("Expected error for %q, but got none", tt.name)
				}
				if !strings.Contains(err.Error(), "is not within sync root") {
					t.Errorf("Error string mismatch for %q.\nExpected to contain: %q\nActual error: %q", tt.name, "is not within sync root", err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Did not expect error for %q, but got: %v", tt.name, err)
				}
				if relPath != expected {
					t.Errorf("Relative path mismatch for %q:\nGot: %q\nWant: %q", tt.name, relPath, expected)
				}
			}
		})
	}
}

// TestResolveAndSyncSymlink tests symlink handling.
func TestResolveAndSyncSymlink(t *testing.T) {
	tempDir := t.TempDir()
	syncRoot := filepath.Join(tempDir, "sync_root")
	os.MkdirAll(syncRoot, 0755)

	logger := NewMockLogger(t)

	// Create a target file within the sync root
	targetFilePath := filepath.Join(syncRoot, "actual_file.txt")
	os.WriteFile(targetFilePath, []byte("original content"), 0644)

	tests := []struct {
		name          string
		linkPath      string // Relative to syncRoot for the symlink itself
		targetPath    string // Target of the symlink (as it would be read from Readlink)
		isIncoming    bool
		expectError   bool
		expectedError string
		expectWarn    bool
	}{
		{
			name:        "Valid relative symlink within sync root",
			linkPath:    "link_to_file.txt",
			targetPath:  "actual_file.txt",
			isIncoming:  true,
			expectError: false,
		},
		{
			name:        "Valid absolute symlink within sync root",
			linkPath:    "abs_link.txt",
			targetPath:  targetFilePath, // Absolute path to target
			isIncoming:  true,
			expectError: false,
		},
		{
			name:          "Symlink pointing outside sync root (relative)",
			linkPath:      "bad_link_rel.txt",
			targetPath:    "../outside_file.txt", // Points outside tempDir
			isIncoming:    true,
			expectError:   true,
			expectedError: "symlink",
			expectWarn:    true,
		},
		{
			name:          "Symlink pointing outside sync root (absolute)",
			linkPath:      "bad_link_abs.txt",
			targetPath:    "/etc/passwd", // Points to system file (likely outside sync root)
			isIncoming:    true,
			expectError:   true,
			expectedError: "symlink",
			expectWarn:    true,
		},
		{
			name:        "Overwrite existing file with symlink",
			linkPath:    "existing_file.txt",
			targetPath:  "actual_file.txt",
			isIncoming:  true,
			expectError: false,
		},
		{
			name:          "Create symlink in non-existent directory (should fail mkdir)",
			linkPath:      "nonexistent_dir/link.txt",
			targetPath:    "actual_file.txt",
			isIncoming:    true,
			expectError:   true,
			expectedError: "no such file or directory",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			absLinkPath := filepath.Join(syncRoot, tt.linkPath)

			// Clean up before each test
			os.RemoveAll(absLinkPath)
			if tt.name == "Overwrite existing file with symlink" {
				os.WriteFile(absLinkPath, []byte("dummy"), 0644) // Create dummy file
			}
			logger = NewMockLogger(t) // Reset logger for each test

			err := fsutil.ResolveAndSyncSymlink(tt.linkPath, syncRoot, tt.targetPath, tt.isIncoming, logger)

			if tt.expectError {
				if err == nil {
					t.Fatalf("Expected error for %q, but got none", tt.name)
				}
				if !strings.Contains(err.Error(), tt.expectedError) {
					t.Errorf("Error mismatch for %q.\nExpected to contain: %q\nActual: %q", tt.name, tt.expectedError, err.Error())
				}
				if tt.expectWarn && len(logger.Warns) == 0 {
					t.Errorf("Expected warning log for %q, but none found", tt.name)
				}
			} else {
				if err != nil {
					t.Fatalf("Did not expect error for %q, but got: %v", tt.name, err)
				}
				// Verify symlink was created and points to the correct target
				readTarget, err := os.Readlink(absLinkPath)
				if err != nil {
					t.Fatalf("Failed to read symlink %q: %v", absLinkPath, err)
				}
				// On Windows, Readlink returns the original target string, not necessarily an absolute path.
				// On Linux, it often resolves to absolute if relative was outside CWD.
				// For this test, let's compare with the original targetPath provided.
				if readTarget != tt.targetPath {
					t.Errorf("Symlink target mismatch for %q: got %q, want %q", tt.name, readTarget, tt.targetPath)
				}
				if len(logger.Warns) > 0 {
					t.Errorf("Did not expect warnings for %q, but found: %v", tt.name, logger.Warns)
				}
			}
		})
	}
}

// TestApplyFilePermissions tests applying file mode and ownership.
func TestApplyFilePermissions(t *testing.T) {
	tempDir := t.TempDir()
	filePath := filepath.Join(tempDir, "perms_test.txt")
	os.WriteFile(filePath, []byte("test"), 0644)
	defer os.Remove(filePath)

	// Get current user/group IDs
	stat, err := os.Stat(filePath)
	if err != nil {
		t.Fatalf("Failed to stat file: %v", err)
	}
	// For testing, let's use the current file's UID/GID for success cases
	// and dummy values for cases that might trigger permission errors.
	// On Unix-like systems, os.FileStat returns a syscall.Stat_t which has Uid and Gid fields.
	uid := stat.Sys().(*syscall.Stat_t).Uid
	gid := stat.Sys().(*syscall.Stat_t).Gid

	tests := []struct {
		name       string
		fileMode   os.FileMode
		uid        uint32
		gid        uint32
		expectErr  bool
		errContent string // String expected to be contained in the error message
	}{
		{"Change mode only", 0777, uid, gid, false, ""},
		{"Change ownership (same uid/gid)", 0644, uid, gid, false, ""},
		// Attempting to chown to different uid/gid will typically fail unless running as root.
		// We expect os.IsPermission(err) to catch this.
		{"Change ownership (different uid/gid - may fail without root)", 0644, 9999, 9999, false, "failed to apply ownership"},
		// Note: os.Chmod on Unix systems may not validate mode values, so this might not fail
		{"Invalid mode", 0xFFFFFFFF, uid, gid, false, ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset permissions to a known state before each sub-test
			os.Chmod(filePath, 0644)
			os.Chown(filePath, int(uid), int(gid)) // Reset ownership too

			err := fsutil.ApplyFilePermissions(filePath, tt.fileMode, tt.uid, tt.gid)

			if tt.expectErr {
				if err == nil {
					t.Fatalf("Expected error for %q, but got none", tt.name)
				}
				if !strings.Contains(err.Error(), tt.errContent) {
					t.Errorf("Error mismatch for %q.\nExpected to contain: %q\nActual: %q", tt.name, tt.errContent, err.Error())
				}
			} else {
				if err != nil {
					// On Linux, chown will return EPERM if not root or not changing to own user/group.
					// We only fail if it's an unexpected error, not a permission error which is handled.
					if !os.IsPermission(err) && !strings.Contains(err.Error(), "operation not permitted") {
						t.Fatalf("Did not expect error for %q, but got: %v", tt.name, err)
					} else {
						t.Logf("Chown/Chmod permission error expected and ignored: %v", err)
					}
				}

				// Verify mode (ignore sticky/setuid/setgid bits)
				info, _ := os.Stat(filePath)
				expectedMode := tt.fileMode & fs.ModePerm // Only compare permission bits
				if (info.Mode() & fs.ModePerm) != expectedMode {
					t.Errorf("Mode mismatch after apply for %q: got %o, want %o", tt.name, info.Mode()&fs.ModePerm, expectedMode)
				}
				// Verifying ownership (chown) is difficult without root privileges or specific setups.
				// The test focuses on ensuring the function *attempts* the operation and handles expected
				// permission errors gracefully without crashing or returning an unexpected error type.
				// If running as root, this part of the test would pass fully.
			}
		})
	}
}
