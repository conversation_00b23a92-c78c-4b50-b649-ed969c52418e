package fsutil

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"syscall" // For getting block size and chown/chmod
	"time"

	"github.com/cespare/xxhash/v2" // xxHash library
	"github.com/real-rm/folder_sync/pkg/types"
)

// Logger interface for fsutil package
type Logger interface {
	Error(format string, args ...interface{})
	Info(format string, args ...interface{})
	Debug(format string, args ...interface{})
	Warn(format string, args ...interface{})
}

// FSUtil provides file system utility functions.
type FSUtil struct {
	basePath string
}

// NewFSUtil creates a new FSUtil instance with the given base path.
func NewFSUtil(basePath string) *FSUtil {
	return &FSUtil{
		basePath: basePath,
	}
}

// CalculateXXHash calculates the xxHash checksum of a file based on the given strategy.
// This is a wrapper around CalculateXXHashChecksum for compatibility.
func CalculateXXHash(filePath string, strategy types.CompareStrategy) ([]byte, error) {
	return CalculateXXHashChecksum(filePath, strategy, types.DefaultBlockSize)
}

// GetFileStat returns file information for the given path.
func (f *FSUtil) GetFileStat(path string) (os.FileInfo, error) {
	return os.Lstat(path)
}

// GetFileOwnership returns the UID and GID of the file owner.
func (f *FSUtil) GetFileOwnership(path string) (uint32, uint32, error) {
	info, err := os.Stat(path)
	if err != nil {
		return 0, 0, err
	}
	if stat, ok := info.Sys().(*syscall.Stat_t); ok {
		return stat.Uid, stat.Gid, nil
	}
	return 0, 0, nil // Not supported on this OS or not a Unix system
}

// CalculateXXHash calculates the xxHash checksum of a file.
func (f *FSUtil) CalculateXXHash(filePath string, strategy types.CompareStrategy) ([]byte, error) {
	return CalculateXXHashChecksum(filePath, strategy, types.DefaultBlockSize)
}

// ReadFile reads the contents of a file.
func (f *FSUtil) ReadFile(path string) ([]byte, error) {
	return os.ReadFile(path)
}

// ExtractSymlinkTarget returns the target of a symlink.
func (f *FSUtil) ExtractSymlinkTarget(path string) (string, error) {
	return os.Readlink(path)
}

// IsSymlinkWithinSyncRoot checks if a symlink points to a target within the sync root.
// It properly resolves relative symlink targets to absolute paths before checking.
// Returns true if the symlink should be synced, false if it should be ignored.
func (f *FSUtil) IsSymlinkWithinSyncRoot(symlinkPath, symlinkTarget, syncRoot string) bool {
	// If the target is an absolute path, check directly
	if filepath.IsAbs(symlinkTarget) {
		return strings.HasPrefix(filepath.Clean(symlinkTarget), filepath.Clean(syncRoot))
	}

	// For relative targets, resolve to absolute path relative to the symlink's directory
	absTargetPath := filepath.Clean(filepath.Join(filepath.Dir(symlinkPath), symlinkTarget))
	return strings.HasPrefix(absTargetPath, filepath.Clean(syncRoot))
}

// AtomicWrite writes data to a file atomically.
// For small files (less than 4KB), writes directly to avoid temporary file overhead.
// For larger files, uses atomic write with temporary file and rename.
func (f *FSUtil) AtomicWrite(filePath string, data []byte, perm os.FileMode) error {
	return AtomicWriteFile(filePath, data, perm)
}

// CreateDir creates a directory with the given permissions.
func (f *FSUtil) CreateDir(path string, perm os.FileMode) error {
	return os.MkdirAll(path, perm)
}

// Remove removes a file or directory.
func (f *FSUtil) Remove(path string) error {
	return os.RemoveAll(path)
}

// Chmod changes the file mode of a file.
func (f *FSUtil) Chmod(path string, perm os.FileMode) error {
	return os.Chmod(path, perm)
}

// Chown changes the ownership of a file.
func (f *FSUtil) Chown(path string, uid, gid uint32) error {
	return os.Chown(path, int(uid), int(gid))
}

// Chtimes changes the access and modification times of a file.
func (f *FSUtil) Chtimes(path string, atime, mtime time.Time) error {
	return os.Chtimes(path, atime, mtime)
}

// Rename renames a file or directory.
func (f *FSUtil) Rename(oldPath, newPath string) error {
	return os.Rename(oldPath, newPath)
}

// AtomicWriteFile writes data to a file atomically.
// For small files (less than 4KB), writes directly to avoid temporary file overhead.
// For larger files, uses atomic write with temporary file and rename.
// This prevents data corruption in case of crashes during write.
func AtomicWriteFile(path string, data []byte, perm os.FileMode) error {
	// For small files, write directly without temporary file
	if len(data) < types.SmallFileThreshold {
		return directWriteFile(path, data, perm)
	}

	// For larger files, use atomic write with temporary file
	return atomicWriteWithTempFile(path, data, perm)
}

// directWriteFile writes small files directly without using temporary files
func directWriteFile(path string, data []byte, perm os.FileMode) error {
	file, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, perm)
	if err != nil {
		return fmt.Errorf("failed to create file %q: %w", path, err)
	}
	defer file.Close()

	if _, err := file.Write(data); err != nil {
		return fmt.Errorf("failed to write data to file %q: %w", path, err)
	}

	// Ensure all data is written to disk
	if err := file.Sync(); err != nil {
		return fmt.Errorf("failed to sync file %q to disk: %w", path, err)
	}

	return nil
}

// atomicWriteWithTempFile writes larger files using temporary file and rename for atomicity
func atomicWriteWithTempFile(path string, data []byte, perm os.FileMode) error {
	dir := filepath.Dir(path)
	tempFile, err := os.CreateTemp(dir, filepath.Base(path)+".tmp-")
	if err != nil {
		return fmt.Errorf("failed to create temporary file for atomic write: %w", err)
	}
	defer os.Remove(tempFile.Name()) // Ensure temp file is cleaned up on exit (if rename fails)
	defer tempFile.Close()           // Close the temp file descriptor

	if _, err := tempFile.Write(data); err != nil {
		return fmt.Errorf("failed to write data to temporary file %q: %w", tempFile.Name(), err)
	}

	// Ensure all data is written to disk before renaming
	if err := tempFile.Sync(); err != nil {
		return fmt.Errorf("failed to sync temporary file %q to disk: %w", tempFile.Name(), err)
	}

	// Set permissions on the temporary file before renaming
	if err := os.Chmod(tempFile.Name(), perm); err != nil {
		return fmt.Errorf("failed to set permissions %o on temporary file %q: %w", perm, tempFile.Name(), err)
	}

	// Atomically rename the temporary file to the final destination
	if err := os.Rename(tempFile.Name(), path); err != nil {
		return fmt.Errorf("failed to rename temporary file %q to %q: %w", tempFile.Name(), path, err)
	}

	return nil
}

// CalculateXXHashChecksum calculates the xxHash checksum of a file based on the given strategy.
// blockSize is used for FIRST_LAST_BLOCK_SAMPLE strategy.
func CalculateXXHashChecksum(filePath string, strategy types.CompareStrategy, blockSize int) ([]byte, error) {
	switch strategy {
	case types.SIZE_ONLY:
		return nil, nil // No checksum for size-only comparison
	case types.FULL_FILE:
		return calculateFullFileXXHash(filePath)
	case types.FIRST_LAST_BLOCK_SAMPLE:
		return calculateSampleXXHash(filePath, blockSize)
	default:
		return nil, fmt.Errorf("unsupported compare strategy: %v", strategy)
	}
}

// calculateFullFileXXHash calculates the xxHash of the entire file.
func calculateFullFileXXHash(filePath string) ([]byte, error) {
	file, err := os.Open(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, fmt.Errorf("file not found for checksum calculation: %q", filePath)
		}
		return nil, fmt.Errorf("failed to open file %q for checksum: %w", filePath, err)
	}
	defer file.Close()

	h := xxhash.New()
	if _, err := io.Copy(h, file); err != nil {
		return nil, fmt.Errorf("failed to read file %q for checksum calculation: %w", filePath, err)
	}

	return h.Sum(nil), nil
}

// calculateSampleXXHash calculates the xxHash based on the first and last blocks.
func calculateSampleXXHash(filePath string, blockSize int) ([]byte, error) {
	if blockSize <= 0 {
		return nil, fmt.Errorf("block size must be positive for sample checksum")
	}

	file, err := os.Open(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, fmt.Errorf("file not found for checksum calculation: %q", filePath)
		}
		return nil, fmt.Errorf("failed to open file %q for checksum: %w", filePath, err)
	}
	defer file.Close()

	fileInfo, err := file.Stat()
	if err != nil {
		return nil, fmt.Errorf("failed to get file info for %q: %w", filePath, err)
	}
	fileSize := fileInfo.Size()

	h := xxhash.New() // Initialize a new hash for the combined sample

	// Handle empty file
	if fileSize == 0 {
		return h.Sum(nil), nil
	}

	// Read first block
	firstBlock := make([]byte, min(int(fileSize), blockSize))
	if _, err := file.ReadAt(firstBlock, 0); err != nil && err != io.EOF {
		return nil, fmt.Errorf("failed to read first block of %q: %w", filePath, err)
	}
	h.Write(firstBlock)

	// If file is larger than one block, read last block
	if fileSize > int64(blockSize) {
		lastBlockSize := min(int(fileSize)-blockSize, blockSize)
		lastBlock := make([]byte, lastBlockSize)
		// Read from (fileSize - lastBlockSize) to fileSize
		if _, err := file.ReadAt(lastBlock, fileSize-int64(lastBlockSize)); err != nil && err != io.EOF {
			return nil, fmt.Errorf("failed to read last block of %q: %w", filePath, err)
		}
		h.Write(lastBlock)
	}

	return h.Sum(nil), nil
}

// min helper for sample hash calculation
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// GetOSBlockSize attempts to get the optimal I/O block size for the given path.
// Falls back to a default if the syscall fails or is not applicable.
func GetOSBlockSize(path string) int {
	var stat syscall.Statfs_t
	if err := syscall.Statfs(path, &stat); err != nil {
		// Fallback to a common default if syscall fails
		return types.OSBlockSizeFallback
	}
	return int(stat.Bsize)
}

// EnsureRelativePath converts an absolute path to a path relative to the sync root.
// It returns an error if the path is not within the sync root.
func EnsureRelativePath(absPath, syncRoot string) (string, error) {
	// Clean the paths to remove redundant separators and symlinks
	absPath = filepath.Clean(absPath)
	syncRoot = filepath.Clean(syncRoot)

	// Use filepath.Rel to check if the path is within the sync root
	relPath, err := filepath.Rel(syncRoot, absPath)
	if err != nil {
		return "", fmt.Errorf("failed to make path relative: %w", err)
	}

	// Check if the relative path starts with ".." which means it's outside the sync root
	if strings.HasPrefix(relPath, "..") {
		return "", fmt.Errorf("path %q is not within sync root %q", absPath, syncRoot)
	}

	// If the path itself is the sync root, Rel might return ".", convert to empty string
	// or ensure it's normalized for consistency.
	if relPath == "." {
		return "", nil
	}
	return relPath, nil
}

// ResolveAndSyncSymlink reads the target of a symlink and creates a new symlink.
// It returns an error if the symlink points outside the sync folder.
// `linkPath` is the path to the symlink itself (relative to syncRoot).
// `syncRoot` is the absolute path of the sync folder.
// `targetPath` is the target of the symlink as read from `os.Readlink`.
// `isIncoming` indicates if the operation is for an incoming symlink (from peer)
// vs. handling a local symlink for sending to peer.
func ResolveAndSyncSymlink(linkPath, syncRoot, targetPath string, isIncoming bool, logger Logger) error {
	absLinkPath := filepath.Join(syncRoot, linkPath)

	// Check if the target is an absolute path or relative to the symlink's directory.
	// For simplicity, we currently assume targetPath is relative to the symlink's directory
	// if it's not an absolute path.
	if !filepath.IsAbs(targetPath) {
		// Get the absolute path the symlink points to
		absTargetPath := filepath.Clean(filepath.Join(filepath.Dir(absLinkPath), targetPath))

		// IMPORTANT: Validate that the resolved absolute target path is *within* the syncRoot
		if !strings.HasPrefix(absTargetPath, syncRoot) {
			logger.Warn("Symlink %q points outside sync root to %q. Ignoring.", linkPath, absTargetPath)
			return fmt.Errorf("symlink %q points outside sync root to %q; discarding", linkPath, absTargetPath)
		}
	} else {
		// If targetPath is already absolute, directly check if it's within syncRoot.
		if !strings.HasPrefix(filepath.Clean(targetPath), syncRoot) {
			logger.Warn("Symlink %q points outside sync root to %q. Ignoring.", linkPath, targetPath)
			return fmt.Errorf("symlink %q points outside sync root to %q; discarding", linkPath, targetPath)
		}
	}

	// Delete existing symlink or file at the path before creating new one
	if _, err := os.Lstat(absLinkPath); err == nil {
		if err := os.Remove(absLinkPath); err != nil {
			return fmt.Errorf("failed to remove existing symlink/file at %q before creating new symlink: %w", absLinkPath, err)
		}
	}

	// Create the symlink
	if err := os.Symlink(targetPath, absLinkPath); err != nil {
		return fmt.Errorf("failed to create symlink %q -> %q: %w", absLinkPath, targetPath, err)
	}
	return nil
}

// ApplyFilePermissions applies file mode, UID, and GID to a file or directory.
func ApplyFilePermissions(path string, fileMode os.FileMode, uid, gid uint32) error {
	// Apply file mode
	if err := os.Chmod(path, fileMode); err != nil {
		return fmt.Errorf("failed to apply file mode %o to %q: %w", fileMode, path, err)
	}

	// Apply ownership (if not current user/group, and if supported/permitted)
	// On Linux, it requires appropriate permissions.
	if err := os.Chown(path, int(uid), int(gid)); err != nil {
		// Ignore specific errors like operation not permitted (e.g., non-root user trying to chown to different user)
		if !os.IsPermission(err) { // Removed check for syscall.EWINDOWS
			return fmt.Errorf("failed to apply ownership (uid:%d, gid:%d) to %q: %w", uid, gid, path, err)
		}
	}
	return nil
}

// CreateSymlink creates a symbolic link atomically.
// It creates the symlink in a temporary location first, then moves it to the target location.
func CreateSymlink(targetPath, linkPath string) error {
	// Ensure the parent directory exists
	parentDir := filepath.Dir(linkPath)
	if err := os.MkdirAll(parentDir, 0755); err != nil {
		return fmt.Errorf("failed to create parent directory %q: %w", parentDir, err)
	}

	// Create a temporary symlink first for atomic operation
	tempLink := linkPath + ".tmp"

	// Remove any existing temporary file
	os.Remove(tempLink)

	// Create the symlink
	if err := os.Symlink(targetPath, tempLink); err != nil {
		return fmt.Errorf("failed to create symlink %q -> %q: %w", tempLink, targetPath, err)
	}

	// Atomically move the temporary symlink to the final location
	if err := os.Rename(tempLink, linkPath); err != nil {
		os.Remove(tempLink) // Clean up on failure
		return fmt.Errorf("failed to move symlink to final location %q: %w", linkPath, err)
	}

	return nil
}
