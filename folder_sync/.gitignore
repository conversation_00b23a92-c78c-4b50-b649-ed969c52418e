# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Development artifacts
/dev/coverage.out
/dev/coverage.html
/dev/*.log

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build artifacts
/bin/
/build/
/dist/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# macOS system files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows system files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux system files
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Temporary files
*.tmp
*.temp
*.log
*.pid
*.seed
*.pid.lock

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node_modules (if any Node.js tools are used)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Application-specific files
*.db
*.sqlite
*.sqlite3
/data/
/logs/
/tmp/
/temp/

# Configuration files that might contain secrets
config.local.*
*.secret
*.key
*.pem
*.crt

# Backup files
*.bak
*.backup
*.orig

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Folder sync specific
/sync_data/
/peer_data/
/meta_data/
*.sync
