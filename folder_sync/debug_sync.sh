#!/bin/bash

# Simple debug script to test sync functionality

set -e

# Test configuration
TEST_DIR="/tmp/debug-sync-$(date +%s)"
AUTH_TOKEN="test-auth-token-debug-12345678901234567890123456789012"
SERVER1_PORT=17892
SERVER2_PORT=17893

echo "Setting up test at: $TEST_DIR"

# Create directory structure
mkdir -p "$TEST_DIR"/{server1,server2}/{sync,meta,logs}

# Create server1 config (listener mode)
cat > "$TEST_DIR/server1/config.yaml" << EOF
folder:
  sync_path: "$TEST_DIR/server1/sync"
  meta_path: "$TEST_DIR/server1/meta"
  logs_path: "$TEST_DIR/server1/logs"
  ignores:
    - pattern: "/^\\\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "ZSTD"
  readonly: false
  writethrough: false
  concurrent: 10
  block_size: 4096
  initial_sync_timeout_minutes: 10

server:
  name: "server1"
  listen_address: "127.0.0.1:$SERVER1_PORT"
  auth_token: "$AUTH_TOKEN"

monitor:
  socket_path: ""

pairs: []

debug: true
EOF

# Create server2 config (connector mode)
cat > "$TEST_DIR/server2/config.yaml" << EOF
folder:
  sync_path: "$TEST_DIR/server2/sync"
  meta_path: "$TEST_DIR/server2/meta"
  logs_path: "$TEST_DIR/server2/logs"
  ignores:
    - pattern: "/^\\\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "ZSTD"
  readonly: false
  writethrough: false
  concurrent: 10
  block_size: 4096
  initial_sync_timeout_minutes: 10

server:
  name: "server2"
  listen_address: "127.0.0.1:$SERVER2_PORT"
  auth_token: "$AUTH_TOKEN"

monitor:
  socket_path: ""

pairs:
  - name: "server1"
    host: "127.0.0.1"
    direction: "BIDIRECTIONAL_NEWEST_WIN"
    initial_sync_strategy: "SYNC"
    remote_port: $SERVER1_PORT

debug: true
EOF

# Create initial test files
echo "Initial content from server1" > "$TEST_DIR/server1/sync/initial_file1.txt"
echo "Initial content from server2" > "$TEST_DIR/server2/sync/initial_file2.txt"

echo "Starting servers..."

# Start servers
./folder-sync -c "$TEST_DIR/server1/config.yaml" > "$TEST_DIR/server1/logs/server1.log" 2>&1 &
SERVER1_PID=$!

./folder-sync -c "$TEST_DIR/server2/config.yaml" > "$TEST_DIR/server2/logs/server2.log" 2>&1 &
SERVER2_PID=$!

echo "Server1 PID: $SERVER1_PID"
echo "Server2 PID: $SERVER2_PID"

# Wait for startup
sleep 5

echo "Checking if servers are running..."
if kill -0 $SERVER1_PID 2>/dev/null; then
    echo "Server1 is running"
else
    echo "Server1 failed to start"
    cat "$TEST_DIR/server1/logs/server1.log"
fi

if kill -0 $SERVER2_PID 2>/dev/null; then
    echo "Server2 is running"
else
    echo "Server2 failed to start"
    cat "$TEST_DIR/server2/logs/server2.log"
fi

# Wait for initial sync
echo "Waiting for initial sync..."
sleep 15

echo "Checking sync results..."
echo "Server1 sync directory:"
ls -la "$TEST_DIR/server1/sync/"

echo "Server2 sync directory:"
ls -la "$TEST_DIR/server2/sync/"

echo "Server1 logs:"
echo "=============="
cat "$TEST_DIR/server1/logs/server1.log"

echo ""
echo "Server2 logs:"
echo "=============="
cat "$TEST_DIR/server2/logs/server2.log"

# Cleanup
echo "Cleaning up..."
kill $SERVER1_PID 2>/dev/null || true
kill $SERVER2_PID 2>/dev/null || true

echo "Test directory preserved at: $TEST_DIR"
