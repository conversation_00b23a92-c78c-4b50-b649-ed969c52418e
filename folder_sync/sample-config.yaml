# Sample configuration for folder-sync
# This file demonstrates all available configuration options
# Save this as /etc/folder_sync.ini (YAML format despite .ini extension)

folder:
  sync_path: "/opt/folder-sync"  # Path to the folder to synchronize. CHANGE THIS.
  meta_path: "/opt/folder-sync/.meta"  # Path for storing program status and SQLite queues.
  logs_path: "/var/log/folder-sync"  # Path for storing log files.
  ignores:
    # Uncomment and modify patterns as needed
    # - pattern: "\\.tmp$"
    #   type: regexp
    # - pattern: "*.log"
    #   type: extension
    # - pattern: "temp_dir"
    #   type: exact
  compares: SIZE_ONLY  # Options: SIZE_ONLY, FIRST_LAST_BLOCK_SAMPLE, FULL_FILE
  compression: NONE  # Options: NONE, ZSTD
  readonly: false  # Set to true if this server should only receive changes
  writethrough: false  # Set to true if this server acts as a buffer (requires monitor.socket_path)
  concurrent: 10  # Max concurrent file transfers (1-1024)
  block_size: 4096  # Block size for sample checksum (auto-detected from OS if not specified)
  initial_sync_timeout_minutes: 20  # Timeout for initial sync logging progress

server:
  listen_address: "0.0.0.0:8080"  # Address and port to listen on for incoming connections
  auth_token: "CHANGE-THIS-TO-A-SECURE-TOKEN-AT-LEAST-32-CHARS"  # Shared secret token for authentication (minimum 32 chars)

monitor:
  # socket_path: "/var/run/folder-sync.sock"  # Uncomment and configure if using proxy socket for writethrough mode

pairs:
  # Uncomment and configure peer servers as needed
  # - name: "remote-peer-example"
  #   host: "*************"
  #   direction: BIDIRECTIONAL_NEWEST_WIN  # Options: ONE_WAY_TO_PEER, ONE_WAY_FROM_PEER, BIDIRECTIONAL_NEWEST_WIN
  #   initial_sync_strategy: SYNC  # Options: SYNC, NO_INITIAL_SYNC
  #   remote_port: 8080

debug: false  # Enable debug logging
