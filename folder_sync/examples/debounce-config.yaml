folder:
  sync_path: /path/to/sync
  meta_path: /path/to/meta
  logs_path: /path/to/logs
  compares: SIZE_ONLY
  compression: NONE
  readonly: false
  writethrough: false
  concurrent: 10
  block_size: 4096
  initial_sync_timeout_minutes: 20

server:
  name: server1
  listen_address: 0.0.0.0:8080
  auth_token: your-secret-token

monitor:
  socket_path: ""

# Event debounce configuration to reduce unnecessary events and save bandwidth
debounce:
  # Debounce window in milliseconds - events for the same file within this window will be buffered
  window_ms: 250
  
  # Maximum number of events to buffer during debounce window
  buffer_size: 100
  
  # Enable intelligent event consolidation (e.g., CHMOD + CREATE -> CREATE)
  consolidation_enabled: true
  
  # Extended debounce window for temporary files (in milliseconds)
  # Temporary files often generate rapid event sequences that can be safely debounced longer
  temp_file_debounce_ms: 1000

pairs:
  - name: server2
    hostname_or_ip: server2.example.com
    remote_port: 8080
    direction: BIDIRECTIONAL_NEWEST_WIN
    initial_sync_strategy: SYNC

debug: false
