# Configuration for server ca3
folder:
  sync_path: "/mnt/ssd0/test1"
  meta_path: "/opt/folder-sync/meta"
  logs_path: "/var/log/folder-sync"
  ignores:
    - pattern: "/^\./"
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "ZSTD"
  readonly: false
  writethrough: false
  concurrent: 10
  block_size: 4096
  initial_sync_timeout_minutes: 1000

server:
  name: "ca3"  # Name of this server (used for peer identification)
  listen_address: "0.0.0.0:7890"
  auth_token: "1cea5648fe51ac5d0aa44d2f8cd2ef72d2313f5919e9d32bdbc155420da86e77"

monitor:
  socket_path: ""

pairs:
  - name: "ca2"
    hostname_or_ip: "*************"
    direction: "BIDIRECTIONAL_NEWEST_WIN"
    initial_sync: "SYNC"
    remote_port: 7890

debug: true
