package test

import (
	"crypto/md5"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestE2EExternalFolderCopy tests copying an external folder into sync directory during ongoing sync
func TestE2EExternalFolderCopy(t *testing.T) {
	// Skip if running in short mode
	if testing.Short() {
		t.Skip("Skipping E2E external folder copy test in short mode")
	}

	// Setup test environment with no ignore patterns
	testDir := setupTestEnvironmentNoIgnores(t)
	defer cleanupTestEnvironment(testDir)

	// Build the application
	buildApplication(t)

	// Create initial test files to establish ongoing sync
	createInitialTestFiles(t, testDir)

	// Start server1 (listener)
	server1Process := startServer(t, testDir, "server1")
	defer stopServer(server1Process)

	// Wait for server1 to start
	time.Sleep(2 * time.Second)

	// Start server2 (connector)
	server2Process := startServer(t, testDir, "server2")
	defer stopServer(server2Process)

	// Wait for initial sync to complete
	t.Log("Waiting for initial sync to complete...")
	time.Sleep(10 * time.Second)

	// Verify initial sync worked
	verifyInitialSync(t, testDir)

	// Now we're in ongoing sync mode - create and copy external folder
	t.Log("Creating complex external folder structure...")
	externalFolder := createComplexExternalFolder(t, testDir)

	// Copy external folder to server1 sync directory
	t.Log("Copying external folder to server1 sync directory...")
	server1SyncDir := filepath.Join(testDir, "server1", "sync")
	copyExternalFolderToSync(t, externalFolder, server1SyncDir)

	// Wait for sync to propagate - increase time to allow for race condition
	t.Log("Waiting for external folder sync to complete...")
	time.Sleep(30 * time.Second)

	// Check server logs for debugging
	t.Log("Checking server logs for sync events...")
	checkServerLogs(t, testDir)

	// Verify complete synchronization
	t.Log("Verifying complete synchronization...")
	verifyCompleteSync(t, testDir, "rmcfg")

	t.Log("External folder copy test completed successfully!")
}

// setupTestEnvironmentNoIgnores creates test environment without ignore patterns
func setupTestEnvironmentNoIgnores(t *testing.T) string {
	testDir, err := os.MkdirTemp("", "folder-sync-e2e-test-")
	require.NoError(t, err)

	// Create directory structure
	for _, server := range []string{"server1", "server2"} {
		for _, subdir := range []string{"sync", "meta", "logs"} {
			dir := filepath.Join(testDir, server, subdir)
			require.NoError(t, os.MkdirAll(dir, 0755))
		}
	}

	// Create config files without ignore patterns
	createConfigFileNoIgnores(t, testDir, "server1", "127.0.0.1:17890", []string{})
	createConfigFileNoIgnores(t, testDir, "server2", "127.0.0.1:17891", []string{"server1"})

	t.Logf("Test environment created at: %s", testDir)
	return testDir
}

// createConfigFileNoIgnores creates config file without ignore patterns
func createConfigFileNoIgnores(t *testing.T, testDir, serverName, listenAddr string, peers []string) {
	config := fmt.Sprintf(`folder:
  sync_path: "%s"
  meta_path: "%s"
  logs_path: "%s"
  ignores: []
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "ZSTD"
  readonly: false
  writethrough: false
  concurrent: 10
  block_size: 4096
  initial_sync_timeout_minutes: 10

server:
  name: "%s"
  listen_address: "%s"
  auth_token: "test-auth-token-1751213400-very-long-token-for-testing-purposes-12345"

monitor:
  socket_path: ""

pairs:`,
		filepath.Join(testDir, serverName, "sync"),
		filepath.Join(testDir, serverName, "meta"),
		filepath.Join(testDir, serverName, "logs"),
		serverName,
		listenAddr)

	for _, peer := range peers {
		config += fmt.Sprintf(`
  - name: "%s"
    host: "127.0.0.1"
    direction: "BIDIRECTIONAL_NEWEST_WIN"
    initial_sync_strategy: "SYNC"
    remote_port: 17890`, peer)
	}

	config += `

debug: true
`

	configPath := filepath.Join(testDir, serverName, "config.yaml")
	err := os.WriteFile(configPath, []byte(config), 0644)
	require.NoError(t, err)
}

// checkServerLogs examines server logs for sync events and errors
func checkServerLogs(t *testing.T, testDir string) {
	for _, server := range []string{"server1", "server2"} {
		logFile := filepath.Join(testDir, server, "logs", "default.log")
		if _, err := os.Stat(logFile); err == nil {
			content, err := os.ReadFile(logFile)
			if err == nil {
				t.Logf("=== %s logs ===", server)
				lines := strings.Split(string(content), "\n")
				// Show last 20 lines
				start := len(lines) - 20
				if start < 0 {
					start = 0
				}
				for i := start; i < len(lines); i++ {
					if strings.TrimSpace(lines[i]) != "" {
						t.Logf("%s", lines[i])
					}
				}
			}
		}
	}
}

// createComplexExternalFolder creates a complex folder structure similar to the rmcfg example
func createComplexExternalFolder(t *testing.T, testDir string) string {
	externalDir := filepath.Join(testDir, "external")
	rmcfgDir := filepath.Join(externalDir, "rmcfg")

	// Create the main directory
	require.NoError(t, os.MkdirAll(rmcfgDir, 0755))

	// Create subdirectories
	subdirs := []string{
		"built",
		"cronjob",
		"dump/subdir",
		"keys",
		"logs",
		"node_modules/package1/lib",
		"node_modules/package2/dist",
		"tpls",
	}

	for _, subdir := range subdirs {
		require.NoError(t, os.MkdirAll(filepath.Join(rmcfgDir, subdir), 0755))
	}

	// Create files in root directory
	rootFiles := map[string]string{
		".gitignore":        "*.log\n*.tmp\nnode_modules/\n",
		"README.md":         "# RMCFG Project\n\nThis is a test project for folder sync.\n",
		"base.yml":          "version: '3.8'\nservices:\n  app:\n    image: test\n",
		"package.json":      `{"name": "rmcfg", "version": "1.0.0", "dependencies": {"express": "^4.18.0"}}`,
		"package-lock.json": `{"name": "rmcfg", "lockfileVersion": 2, "requires": true}`,
		"setup.coffee":      "console.log 'Setting up application'\n",
		"show.sh":           "#!/bin/bash\necho 'Showing application status'\n",
		"start.sh":          "#!/bin/bash\necho 'Starting application'\nnpm start\n",
		"tasks.txt":         "TODO:\n- Implement feature A\n- Fix bug B\n- Update documentation\n",
	}

	for filename, content := range rootFiles {
		require.NoError(t, os.WriteFile(filepath.Join(rmcfgDir, filename), []byte(content), 0644))
	}

	// Make shell scripts executable
	require.NoError(t, os.Chmod(filepath.Join(rmcfgDir, "show.sh"), 0755))
	require.NoError(t, os.Chmod(filepath.Join(rmcfgDir, "start.sh"), 0755))

	// Create files in subdirectories
	subdirFiles := map[string]string{
		"built/app.js":                       "console.log('Built application');\n",
		"built/styles.css":                   "body { margin: 0; padding: 0; }\n",
		"cronjob/backup.sh":                  "#!/bin/bash\necho 'Running backup'\n",
		"dump/subdir/data.sql":               "CREATE TABLE test (id INT PRIMARY KEY);\n",
		"keys/private.key":                   "-----BEGIN PRIVATE KEY-----\ntest-key-data\n-----END PRIVATE KEY-----\n",
		"keys/public.key":                    "-----BEGIN PUBLIC KEY-----\ntest-public-key\n-----END PUBLIC KEY-----\n",
		"logs/application.log":               "2024-01-01 10:00:00 INFO Application started\n",
		"logs/error.log":                     "2024-01-01 10:01:00 ERROR Test error message\n",
		"node_modules/package1/lib/index.js": "module.exports = { version: '1.0.0' };\n",
		"node_modules/package1/package.json": `{"name": "package1", "version": "1.0.0"}`,
		"node_modules/package2/dist/main.js": "console.log('Package2 main');\n",
		"node_modules/package2/package.json": `{"name": "package2", "version": "2.0.0"}`,
		"tpls/config.tpl":                    "server {\n  listen 80;\n  server_name example.com;\n}\n",
		"tpls/docker.tpl":                    "FROM node:16\nWORKDIR /app\nCOPY . .\n",
	}

	for filepath_rel, content := range subdirFiles {
		fullPath := filepath.Join(rmcfgDir, filepath_rel)
		require.NoError(t, os.MkdirAll(filepath.Dir(fullPath), 0755))
		require.NoError(t, os.WriteFile(fullPath, []byte(content), 0644))
	}

	// Make cronjob script executable
	require.NoError(t, os.Chmod(filepath.Join(rmcfgDir, "cronjob/backup.sh"), 0755))

	t.Logf("Created complex external folder at: %s", rmcfgDir)
	return rmcfgDir
}

// copyExternalFolderToSync copies the external folder to the sync directory
func copyExternalFolderToSync(t *testing.T, externalFolder, syncDir string) {
	// Use cp -r to copy the folder (similar to user's scenario)
	folderName := filepath.Base(externalFolder)
	destPath := filepath.Join(syncDir, folderName)

	cmd := exec.Command("cp", "-r", externalFolder, syncDir)
	output, err := cmd.CombinedOutput()
	if err != nil {
		t.Fatalf("Failed to copy external folder: %v\nOutput: %s", err, output)
	}

	t.Logf("Copied external folder to: %s", destPath)
}

// verifyCompleteSync verifies that all files and directories are synchronized
func verifyCompleteSync(t *testing.T, testDir, folderName string) {
	server1SyncDir := filepath.Join(testDir, "server1", "sync", folderName)
	server2SyncDir := filepath.Join(testDir, "server2", "sync", folderName)

	// Check that both directories exist
	require.DirExists(t, server1SyncDir, "Folder should exist on server1")
	require.DirExists(t, server2SyncDir, "Folder should exist on server2")

	// Get file lists from both servers
	server1Files := getFileList(t, server1SyncDir)
	server2Files := getFileList(t, server2SyncDir)

	// Debug: Print file lists
	t.Logf("Server1 has %d files: %v", len(server1Files), server1Files)
	t.Logf("Server2 has %d files: %v", len(server2Files), server2Files)

	// Find missing files
	var missingOnServer2 []string
	for _, file := range server1Files {
		server2FilePath := filepath.Join(server2SyncDir, file)
		if _, err := os.Stat(server2FilePath); os.IsNotExist(err) {
			missingOnServer2 = append(missingOnServer2, file)
		}
	}
	if len(missingOnServer2) > 0 {
		t.Logf("Files missing on server2: %v", missingOnServer2)
	}

	// Compare file lists
	assert.Equal(t, len(server1Files), len(server2Files), "Number of files should be equal")

	// Check each file exists on both servers and has same content
	for _, file := range server1Files {
		server1FilePath := filepath.Join(server1SyncDir, file)
		server2FilePath := filepath.Join(server2SyncDir, file)

		// Check file/directory exists on server2
		_, err := os.Stat(server2FilePath)
		assert.NoError(t, err, "Path %s should exist on server2", file)

		// Compare file contents (only for regular files)
		if isRegularFile(server1FilePath) && isRegularFile(server2FilePath) {
			server1Hash := getFileHash(t, server1FilePath)
			server2Hash := getFileHash(t, server2FilePath)
			assert.Equal(t, server1Hash, server2Hash, "File %s should have same content on both servers", file)
		}

		// Compare file/directory permissions
		server1Info, err := os.Stat(server1FilePath)
		require.NoError(t, err)
		server2Info, err := os.Stat(server2FilePath)
		require.NoError(t, err)
		assert.Equal(t, server1Info.Mode(), server2Info.Mode(), "Path %s should have same permissions", file)
	}

	// Check for files that exist on server2 but not on server1
	for _, file := range server2Files {
		server1FilePath := filepath.Join(server1SyncDir, file)
		_, err := os.Stat(server1FilePath)
		assert.NoError(t, err, "Path %s should exist on server1", file)
	}

	t.Logf("Verified synchronization of %d files/directories", len(server1Files))
}

// getFileList returns a list of all files and directories relative to the base path
func getFileList(t *testing.T, basePath string) []string {
	var files []string

	err := filepath.Walk(basePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		relPath, err := filepath.Rel(basePath, path)
		if err != nil {
			return err
		}

		if relPath != "." {
			files = append(files, relPath)
		}

		return nil
	})

	require.NoError(t, err)
	return files
}

// getFileHash calculates MD5 hash of a file
func getFileHash(t *testing.T, filePath string) string {
	file, err := os.Open(filePath)
	require.NoError(t, err)
	defer file.Close()

	hash := md5.New()
	_, err = io.Copy(hash, file)
	require.NoError(t, err)

	return fmt.Sprintf("%x", hash.Sum(nil))
}

// isRegularFile checks if a path is a regular file (not directory or symlink)
func isRegularFile(path string) bool {
	info, err := os.Stat(path)
	if err != nil {
		return false
	}
	return info.Mode().IsRegular()
}
