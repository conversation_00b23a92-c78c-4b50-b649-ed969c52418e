#!/bin/bash

# Debug script to check if the issue is with mkdir or with the sync application

set -e

TEST_DIR="/tmp/folder-sync-mkdir-debug-$$"

echo "=== Testing mkdir behavior ==="
echo "Test directory: $TEST_DIR"

# Cleanup function
cleanup() {
    echo "Cleaning up..."
    rm -rf "$TEST_DIR"
}

trap cleanup EXIT

# Create test environment
echo "Setting up test environment..."
mkdir -p "$TEST_DIR"/{server1,server2}/sync

echo ""
echo "=== Test 1: Sequential mkdir ==="
mkdir -p "$TEST_DIR/server1/sync/seq_dir1"
mkdir -p "$TEST_DIR/server2/sync/seq_dir2"
echo "Sequential mkdir completed successfully"

echo ""
echo "=== Test 2: Simultaneous mkdir (background) ==="
echo "Starting simultaneous mkdir..."
mkdir -p "$TEST_DIR/server1/sync/simul_dir1" &
PID1=$!
mkdir -p "$TEST_DIR/server2/sync/simul_dir2" &
PID2=$!

echo "Waiting for background processes..."
echo "PID1: $PID1, PID2: $PID2"

# Wait with timeout
timeout=10
count=0
while [ $count -lt $timeout ]; do
    if ! kill -0 $PID1 2>/dev/null && ! kill -0 $PID2 2>/dev/null; then
        echo "Both processes completed"
        break
    fi
    echo "Waiting... ($count/$timeout)"
    sleep 1
    count=$((count + 1))
done

if [ $count -eq $timeout ]; then
    echo "❌ TIMEOUT: mkdir processes are hanging"
    kill $PID1 $PID2 2>/dev/null || true
    exit 1
else
    echo "✅ SUCCESS: Simultaneous mkdir completed"
fi

echo ""
echo "=== Test 3: Rapid mkdir ==="
for i in {1..5}; do
    mkdir -p "$TEST_DIR/server1/sync/rapid_dir_s1_$i"
    mkdir -p "$TEST_DIR/server2/sync/rapid_dir_s2_$i"
done
echo "Rapid mkdir completed successfully"

echo ""
echo "=== Final directory listing ==="
echo "Server1 directories:"
find "$TEST_DIR/server1/sync" -type d | sort
echo ""
echo "Server2 directories:"
find "$TEST_DIR/server2/sync" -type d | sort

echo ""
echo "✅ All mkdir tests passed - issue is not with mkdir itself"
