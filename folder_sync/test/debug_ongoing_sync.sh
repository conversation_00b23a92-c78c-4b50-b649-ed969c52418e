#!/bin/bash

# Debug script to test ongoing sync functionality
set -e

echo "=== Debug Ongoing Sync Test ==="

# Cleanup function
cleanup() {
    echo "Cleaning up..."
    pkill -f "folder-sync" || true
    rm -rf /tmp/debug-ongoing-sync
    echo "Cleanup complete."
}

# Set trap for cleanup
trap cleanup EXIT

# Create test directories
TEST_DIR="/tmp/debug-ongoing-sync"
mkdir -p "$TEST_DIR"/{server1,server2}/{sync,meta,logs}

# Create config files
cat > "$TEST_DIR/server1/config.yaml" << 'EOF'
folder:
  sync_path: "/tmp/debug-ongoing-sync/server1/sync"
  meta_path: "/tmp/debug-ongoing-sync/server1/meta"
  logs_path: "/tmp/debug-ongoing-sync/server1/logs"
  ignores:
    - pattern: "/^\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "ZSTD"
  readonly: false
  writethrough: false
  concurrent: 10
  block_size: 4096
  initial_sync_timeout_minutes: 10

server:
  name: "server1"
  listen_address: "127.0.0.1:17890"
  auth_token: "test-auth-token-debug-ongoing-12345678901234567890123456789012"

monitor:
  socket_path: ""

pairs: []

debug: true
EOF

cat > "$TEST_DIR/server2/config.yaml" << 'EOF'
folder:
  sync_path: "/tmp/debug-ongoing-sync/server2/sync"
  meta_path: "/tmp/debug-ongoing-sync/server2/meta"
  logs_path: "/tmp/debug-ongoing-sync/server2/logs"
  ignores:
    - pattern: "/^\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "ZSTD"
  readonly: false
  writethrough: false
  concurrent: 10
  block_size: 4096
  initial_sync_timeout_minutes: 10

server:
  name: "server2"
  listen_address: "127.0.0.1:17891"
  auth_token: "test-auth-token-debug-ongoing-12345678901234567890123456789012"

monitor:
  socket_path: ""

pairs:
  - name: "server1"
    host: "127.0.0.1"
    direction: "BIDIRECTIONAL_NEWEST_WIN"
    initial_sync_strategy: "SYNC"
    remote_port: 17890

debug: true
EOF

# Build the application
echo "Building application..."
make build

# Start server1
echo "Starting server1..."
./build/folder-sync -c "$TEST_DIR/server1/config.yaml" > "$TEST_DIR/server1.log" 2>&1 &
SERVER1_PID=$!
sleep 2

# Start server2
echo "Starting server2..."
./build/folder-sync -c "$TEST_DIR/server2/config.yaml" > "$TEST_DIR/server2.log" 2>&1 &
SERVER2_PID=$!
sleep 5

echo "Servers started. Waiting for initial sync..."
sleep 5

# Test 1: Create a file on server1 and check if it syncs to server2
echo "Test 1: Creating file on server1..."
echo "Test content from server1" > "$TEST_DIR/server1/sync/ongoing_test.txt"
echo "File created on server1. Waiting for sync..."
sleep 10

if [ -f "$TEST_DIR/server2/sync/ongoing_test.txt" ]; then
    echo "✅ SUCCESS: File synced from server1 to server2"
    echo "Content: $(cat "$TEST_DIR/server2/sync/ongoing_test.txt")"
else
    echo "❌ FAILED: File did not sync from server1 to server2"
    echo "Files on server2:"
    ls -la "$TEST_DIR/server2/sync/" || echo "No files found"
fi

# Test 2: Create a directory on server1 and check if it syncs to server2
echo ""
echo "Test 2: Creating directory on server1..."
mkdir -p "$TEST_DIR/server1/sync/test_dir"
echo "Directory created. Waiting a moment before creating file..."
sleep 2
echo "Directory content" > "$TEST_DIR/server1/sync/test_dir/dir_file.txt"
echo "File created inside directory. Waiting for sync..."
sleep 10

if [ -d "$TEST_DIR/server2/sync/test_dir" ] && [ -f "$TEST_DIR/server2/sync/test_dir/dir_file.txt" ]; then
    echo "✅ SUCCESS: Directory synced from server1 to server2"
    echo "Content: $(cat "$TEST_DIR/server2/sync/test_dir/dir_file.txt")"
else
    echo "❌ FAILED: Directory did not sync from server1 to server2"
    echo "Directories on server2:"
    find "$TEST_DIR/server2/sync/" -type d || echo "No directories found"
fi

# Show logs for debugging
echo ""
echo "=== Server1 Log (last 20 lines) ==="
tail -20 "$TEST_DIR/server1.log"

echo ""
echo "=== Server2 Log (last 20 lines) ==="
tail -20 "$TEST_DIR/server2.log"

echo ""
echo "Debug test complete."
