#!/bin/bash

# Simple debug script to test concurrent directory creation issue

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
TEST_DIR="/tmp/folder-sync-debug-simple-$$"

echo "=== Simple Concurrent Directory Debug Test ==="
echo "Test directory: $TEST_DIR"

# Cleanup function
cleanup() {
    echo "Cleaning up..."
    if [ -n "$SERVER1_PID" ]; then
        kill $SERVER1_PID 2>/dev/null || true
    fi
    if [ -n "$SERVER2_PID" ]; then
        kill $SERVER2_PID 2>/dev/null || true
    fi
    rm -rf "$TEST_DIR"
}

trap cleanup EXIT

# Create test environment
echo "Setting up test environment..."
mkdir -p "$TEST_DIR"/{server1,server2}/{sync,meta,logs}

# Generate auth token
AUTH_TOKEN="debug-simple-concurrent-dirs-token-12345678901234567890123456789012"

# Create server1 config (listener)
cat > "$TEST_DIR/server1/config.yaml" << EOF
folder:
  sync_path: "$TEST_DIR/server1/sync"
  meta_path: "$TEST_DIR/server1/meta"
  logs_path: "$TEST_DIR/server1/logs"
  ignores:
    - pattern: "/^\\\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "NONE"
  concurrent: 5

server:
  name: "server1"
  listen_address: "127.0.0.1:8080"
  auth_token: "$AUTH_TOKEN"

pairs: []

debug: true
EOF

# Create server2 config (connector)
cat > "$TEST_DIR/server2/config.yaml" << EOF
folder:
  sync_path: "$TEST_DIR/server2/sync"
  meta_path: "$TEST_DIR/server2/meta"
  logs_path: "$TEST_DIR/server2/logs"
  ignores:
    - pattern: "/^\\\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "NONE"
  concurrent: 5

server:
  name: "server2"
  listen_address: "127.0.0.1:8081"
  auth_token: "$AUTH_TOKEN"

pairs:
  - name: "server1"
    host: "127.0.0.1"
    direction: "BIDIRECTIONAL_NEWEST_WIN"
    initial_sync_strategy: "SYNC"
    remote_port: 8080

debug: true
EOF

# Build the application
echo "Building application..."
cd "$PROJECT_ROOT"
make build

# Start server1 (listener)
echo "Starting server1 (listener)..."
"$PROJECT_ROOT/build/folder-sync" -c "$TEST_DIR/server1/config.yaml" > "$TEST_DIR/server1.log" 2>&1 &
SERVER1_PID=$!
echo "Server1 PID: $SERVER1_PID"

# Wait for server1 to start
sleep 3

# Start server2 (connector)
echo "Starting server2 (connector)..."
"$PROJECT_ROOT/build/folder-sync" -c "$TEST_DIR/server2/config.yaml" > "$TEST_DIR/server2.log" 2>&1 &
SERVER2_PID=$!
echo "Server2 PID: $SERVER2_PID"

# Wait for initial connection
echo "Waiting for initial connection..."
sleep 5

echo ""
echo "=== Testing Directory Creation ==="

# Test 1: Create directory on server1
echo "Test 1: Creating directory on server1..."
mkdir -p "$TEST_DIR/server1/sync/concurrent_dir1/subdir"
echo "content1" > "$TEST_DIR/server1/sync/concurrent_dir1/subdir/file1.txt"
echo "Directory created on server1"

# Wait for sync
echo "Waiting for sync (10 seconds)..."
sleep 10

# Check if directory appeared on server2
if [ -d "$TEST_DIR/server2/sync/concurrent_dir1/subdir" ]; then
    echo "✅ SUCCESS: Directory synced to server2"
    if [ -f "$TEST_DIR/server2/sync/concurrent_dir1/subdir/file1.txt" ]; then
        echo "✅ SUCCESS: File inside directory also synced"
    else
        echo "❌ FAILURE: File inside directory not synced"
    fi
else
    echo "❌ FAILURE: Directory did not sync to server2"
fi

echo ""

# Test 2: Create directory on server2
echo "Test 2: Creating directory on server2..."
mkdir -p "$TEST_DIR/server2/sync/concurrent_dir2/subdir"
echo "content2" > "$TEST_DIR/server2/sync/concurrent_dir2/subdir/file2.txt"
echo "Directory created on server2"

# Wait for sync
echo "Waiting for sync (10 seconds)..."
sleep 10

# Check if directory appeared on server1
if [ -d "$TEST_DIR/server1/sync/concurrent_dir2/subdir" ]; then
    echo "✅ SUCCESS: Directory synced to server1"
    if [ -f "$TEST_DIR/server1/sync/concurrent_dir2/subdir/file2.txt" ]; then
        echo "✅ SUCCESS: File inside directory also synced"
    else
        echo "❌ FAILURE: File inside directory not synced"
    fi
else
    echo "❌ FAILURE: Directory did not sync to server1"
fi

echo ""
echo "=== Final Status ==="
echo "Server1 sync directory structure:"
find "$TEST_DIR/server1/sync" -type d | sort
echo ""
echo "Server1 files:"
find "$TEST_DIR/server1/sync" -type f | sort
echo ""
echo "Server2 sync directory structure:"
find "$TEST_DIR/server2/sync" -type d | sort
echo ""
echo "Server2 files:"
find "$TEST_DIR/server2/sync" -type f | sort

echo ""
echo "=== Log Analysis ==="
echo "Server1 log (CREATE_DIR events):"
grep -i "create.*dir\|CREATE_DIR" "$TEST_DIR/server1.log" | tail -10
echo ""
echo "Server2 log (CREATE_DIR events):"
grep -i "create.*dir\|CREATE_DIR" "$TEST_DIR/server2.log" | tail -10

echo ""
echo "Server1 log (CREATE_FILE events):"
grep -i "create.*file\|CREATE_FILE\|Applied CREATE_FILE" "$TEST_DIR/server1.log" | tail -10
echo ""
echo "Server2 log (CREATE_FILE events):"
grep -i "create.*file\|CREATE_FILE\|Applied CREATE_FILE" "$TEST_DIR/server2.log" | tail -10

echo ""
echo "Server1 log (conflict resolution):"
grep -i "conflict\|newer\|mtime\|same.*content" "$TEST_DIR/server1.log" | tail -10
echo ""
echo "Server2 log (conflict resolution):"
grep -i "conflict\|newer\|mtime\|same.*content" "$TEST_DIR/server2.log" | tail -10

echo ""
echo "Server1 log (errors):"
grep -i "error\|failed" "$TEST_DIR/server1.log" | tail -5
echo ""
echo "Server2 log (errors):"
grep -i "error\|failed" "$TEST_DIR/server2.log" | tail -5

echo ""
echo "=== Test Complete ==="
