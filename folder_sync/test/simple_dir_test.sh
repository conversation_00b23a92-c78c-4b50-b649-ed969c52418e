#!/bin/bash

# Simple test to isolate directory synchronization issues

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
TEST_DIR="/tmp/folder-sync-simple-dir-$$"

echo "=== Simple Directory Test ==="
echo "Test directory: $TEST_DIR"

# Cleanup function
cleanup() {
    echo "Cleaning up..."
    if [ -n "$SERVER1_PID" ]; then
        kill $SERVER1_PID 2>/dev/null || true
    fi
    if [ -n "$SERVER2_PID" ]; then
        kill $SERVER2_PID 2>/dev/null || true
    fi
    rm -rf "$TEST_DIR"
}

trap cleanup EXIT

# Create test environment
echo "Setting up test environment..."
mkdir -p "$TEST_DIR"/{server1,server2}/{sync,meta,logs}

# Generate auth token
AUTH_TOKEN="simple-dir-test-token-12345678901234567890123456789012"

# Create server1 config (listener)
cat > "$TEST_DIR/server1/config.yaml" << EOF
folder:
  sync_path: "$TEST_DIR/server1/sync"
  meta_path: "$TEST_DIR/server1/meta"
  logs_path: "$TEST_DIR/server1/logs"
  ignores:
    - pattern: "/^\\\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "NONE"
  concurrent: 5

server:
  name: "server1"
  listen_address: "127.0.0.1:8080"
  auth_token: "$AUTH_TOKEN"

pairs: []

debug: true
EOF

# Create server2 config (connector)
cat > "$TEST_DIR/server2/config.yaml" << EOF
folder:
  sync_path: "$TEST_DIR/server2/sync"
  meta_path: "$TEST_DIR/server2/meta"
  logs_path: "$TEST_DIR/server2/logs"
  ignores:
    - pattern: "/^\\\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "NONE"
  concurrent: 5

server:
  name: "server2"
  listen_address: "127.0.0.1:8081"
  auth_token: "$AUTH_TOKEN"

pairs:
  - name: "server1"
    host: "127.0.0.1"
    direction: "BIDIRECTIONAL_NEWEST_WIN"
    initial_sync_strategy: "SYNC"
    remote_port: 8080

debug: true
EOF

# Build the application
echo "Building application..."
cd "$PROJECT_ROOT"
make build

# Start server1 (listener)
echo "Starting server1 (listener)..."
"$PROJECT_ROOT/build/folder-sync" -c "$TEST_DIR/server1/config.yaml" > "$TEST_DIR/server1.log" 2>&1 &
SERVER1_PID=$!
echo "Server1 PID: $SERVER1_PID"

# Wait for server1 to start
sleep 3

# Start server2 (connector)
echo "Starting server2 (connector)..."
"$PROJECT_ROOT/build/folder-sync" -c "$TEST_DIR/server2/config.yaml" > "$TEST_DIR/server2.log" 2>&1 &
SERVER2_PID=$!
echo "Server2 PID: $SERVER2_PID"

# Wait for initial connection
echo "Waiting for initial connection..."
sleep 5

echo ""
echo "=== Test 1: Create directory on server1 only ==="
mkdir -p "$TEST_DIR/server1/sync/test_dir_s1"
echo "Directory created on server1"
sleep 5

if [ -d "$TEST_DIR/server2/sync/test_dir_s1" ]; then
    echo "✅ SUCCESS: Directory synced to server2"
else
    echo "❌ FAILURE: Directory did not sync to server2"
fi

echo ""
echo "=== Test 2: Create directory on server2 only ==="
mkdir -p "$TEST_DIR/server2/sync/test_dir_s2"
echo "Directory created on server2"
sleep 5

if [ -d "$TEST_DIR/server1/sync/test_dir_s2" ]; then
    echo "✅ SUCCESS: Directory synced to server1"
else
    echo "❌ FAILURE: Directory did not sync to server1"
fi

echo ""
echo "=== Test 3: Create directories with delay ==="
mkdir -p "$TEST_DIR/server1/sync/delayed_dir_s1"
echo "Directory created on server1"
sleep 3
mkdir -p "$TEST_DIR/server2/sync/delayed_dir_s2"
echo "Directory created on server2"
sleep 5

if [ -d "$TEST_DIR/server2/sync/delayed_dir_s1" ] && [ -d "$TEST_DIR/server1/sync/delayed_dir_s2" ]; then
    echo "✅ SUCCESS: Both delayed directories synced"
else
    echo "❌ FAILURE: Delayed directories did not sync properly"
    echo "  server2 has delayed_dir_s1: $([ -d "$TEST_DIR/server2/sync/delayed_dir_s1" ] && echo "YES" || echo "NO")"
    echo "  server1 has delayed_dir_s2: $([ -d "$TEST_DIR/server1/sync/delayed_dir_s2" ] && echo "YES" || echo "NO")"
fi

echo ""
echo "=== Test 4: Create directories simultaneously ==="
mkdir -p "$TEST_DIR/server1/sync/simul_dir_s1" &
mkdir -p "$TEST_DIR/server2/sync/simul_dir_s2" &
wait
echo "Simultaneous directories created"
sleep 8

if [ -d "$TEST_DIR/server2/sync/simul_dir_s1" ] && [ -d "$TEST_DIR/server1/sync/simul_dir_s2" ]; then
    echo "✅ SUCCESS: Both simultaneous directories synced"
else
    echo "❌ FAILURE: Simultaneous directories did not sync properly"
    echo "  server2 has simul_dir_s1: $([ -d "$TEST_DIR/server2/sync/simul_dir_s1" ] && echo "YES" || echo "NO")"
    echo "  server1 has simul_dir_s2: $([ -d "$TEST_DIR/server1/sync/simul_dir_s2" ] && echo "YES" || echo "NO")"
fi

echo ""
echo "=== Final Directory Listing ==="
echo "Server1 directories:"
find "$TEST_DIR/server1/sync" -type d | sort
echo ""
echo "Server2 directories:"
find "$TEST_DIR/server2/sync" -type d | sort

echo ""
echo "=== Log Analysis ==="
echo "Server1 CREATE_DIR events:"
grep "CREATE_DIR\|Created directory" "$TEST_DIR/server1.log" | head -10
echo ""
echo "Server2 CREATE_DIR events:"
grep "CREATE_DIR\|Created directory" "$TEST_DIR/server2.log" | head -10

echo ""
echo "Server1 suppression events:"
grep -i "suppress" "$TEST_DIR/server1.log" | head -5
echo ""
echo "Server2 suppression events:"
grep -i "suppress" "$TEST_DIR/server2.log" | head -5
