folder:
  sync_path: "/tmp/folder-sync-test/server2/sync"
  meta_path: "/tmp/folder-sync-test/server2/meta"
  logs_path: "/tmp/folder-sync-test/server2/logs"
  ignores:
    - pattern: "/^\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "ZSTD"
  readonly: false
  writethrough: false
  concurrent: 10
  block_size: 4096
  initial_sync_timeout_minutes: 10

server:
  name: "server2"
  listen_address: "127.0.0.1:17891"
  auth_token: "test-auth-token-1751213400-very-long-token-for-testing-purposes-12345"

monitor:
  socket_path: ""

pairs:
  - name: "server1"
    host: "127.0.0.1"
    direction: "BIDIRECTIONAL_NEWEST_WIN"
    initial_sync_strategy: "SYNC"
    remote_port: 17890

debug: true
