#!/bin/bash

# Local test script for folder-sync - runs two instances locally

set -e

# Configuration
TEST_DIR="/tmp/folder-sync-test"
CA2_DIR="$TEST_DIR/ca2"
CA3_DIR="$TEST_DIR/ca3"
CA2_PORT=7890
CA3_PORT=7891

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to setup test environment
setup_env() {
    log "Setting up test environment..."
    
    # Clean up any existing test
    cleanup
    
    # Create directories
    mkdir -p "$CA2_DIR"/{sync,meta,logs}
    mkdir -p "$CA3_DIR"/{sync,meta,logs}
    
    # Build the application
    log "Building folder-sync..."
    cd /Users/<USER>/work/folder_sync
    go build -o "$TEST_DIR/folder-sync" .
    
    # Create config files
    create_configs
    
    success "Environment setup complete"
}

# Function to create config files
create_configs() {
    log "Creating configuration files..."

    # Copy and modify existing config files
    cp examples/config-ca2.yaml "$CA2_DIR/config.yaml"
    cp examples/config-ca3.yaml "$CA3_DIR/config.yaml"

    # Update CA2 config
    sed -i '' "s|/mnt/ssd0/test1|$CA2_DIR/sync|g" "$CA2_DIR/config.yaml"
    sed -i '' "s|/opt/folder-sync/meta|$CA2_DIR/meta|g" "$CA2_DIR/config.yaml"
    sed -i '' "s|/var/log/folder-sync|$CA2_DIR/logs|g" "$CA2_DIR/config.yaml"
    sed -i '' "s|0.0.0.0:7890|0.0.0.0:$CA2_PORT|g" "$CA2_DIR/config.yaml"
    sed -i '' "s|192.168.0.103|127.0.0.1|g" "$CA2_DIR/config.yaml"
    sed -i '' "s|remote_port: 7890|remote_port: $CA3_PORT|g" "$CA2_DIR/config.yaml"

    # Update CA3 config
    sed -i '' "s|/mnt/ssd0/test1|$CA3_DIR/sync|g" "$CA3_DIR/config.yaml"
    sed -i '' "s|/opt/folder-sync/meta|$CA3_DIR/meta|g" "$CA3_DIR/config.yaml"
    sed -i '' "s|/var/log/folder-sync|$CA3_DIR/logs|g" "$CA3_DIR/config.yaml"
    sed -i '' "s|0.0.0.0:7890|0.0.0.0:$CA3_PORT|g" "$CA3_DIR/config.yaml"
    sed -i '' "s|192.168.0.102|127.0.0.1|g" "$CA3_DIR/config.yaml"
    sed -i '' "s|remote_port: 7890|remote_port: $CA2_PORT|g" "$CA3_DIR/config.yaml"

    success "Configuration files created"
}

# Function to start services
start_services() {
    log "Starting folder-sync services..."
    
    # Start CA2
    log "Starting CA2 on port $CA2_PORT..."
    "$TEST_DIR/folder-sync" -c "$CA2_DIR/config.yaml" > "$CA2_DIR/logs/output.log" 2>&1 &
    CA2_PID=$!
    echo $CA2_PID > "$CA2_DIR/ca2.pid"

    # Start CA3
    log "Starting CA3 on port $CA3_PORT..."
    "$TEST_DIR/folder-sync" -c "$CA3_DIR/config.yaml" > "$CA3_DIR/logs/output.log" 2>&1 &
    CA3_PID=$!
    echo $CA3_PID > "$CA3_DIR/ca3.pid"
    
    # Wait for services to start
    sleep 3
    
    # Check if services are running
    if kill -0 $CA2_PID 2>/dev/null; then
        success "CA2 started (PID: $CA2_PID)"
    else
        error "CA2 failed to start"
        return 1
    fi
    
    if kill -0 $CA3_PID 2>/dev/null; then
        success "CA3 started (PID: $CA3_PID)"
    else
        error "CA3 failed to start"
        return 1
    fi
    
    # Wait for initial sync to complete
    log "Waiting for initial sync to complete..."
    sleep 5
}

# Function to show logs
show_logs() {
    echo
    log "=== CA2 Logs (last 20 lines) ==="
    tail -20 "$CA2_DIR/logs/output.log" 2>/dev/null || echo "No logs yet"
    
    echo
    log "=== CA3 Logs (last 20 lines) ==="
    tail -20 "$CA3_DIR/logs/output.log" 2>/dev/null || echo "No logs yet"
    echo
}

# Function to test file sync
test_sync() {
    log "Testing file synchronization..."
    
    # Test CA2 -> CA3
    log "Testing CA2 -> CA3 sync..."
    echo "Hello from CA2 at $(date)" > "$CA2_DIR/sync/test-ca2.txt"
    sleep 3
    
    if [ -f "$CA3_DIR/sync/test-ca2.txt" ]; then
        success "File synced from CA2 to CA3"
        log "Content: $(cat "$CA3_DIR/sync/test-ca2.txt")"
    else
        error "File NOT synced from CA2 to CA3"
    fi
    
    # Test CA3 -> CA2
    log "Testing CA3 -> CA2 sync..."
    echo "Hello from CA3 at $(date)" > "$CA3_DIR/sync/test-ca3.txt"
    sleep 3
    
    if [ -f "$CA2_DIR/sync/test-ca3.txt" ]; then
        success "File synced from CA3 to CA2"
        log "Content: $(cat "$CA2_DIR/sync/test-ca3.txt")"
    else
        error "File NOT synced from CA3 to CA2"
    fi
}

# Function to show status
show_status() {
    echo
    log "=== Status Files ==="
    
    log "CA2 status for CA3:"
    cat "$CA2_DIR/meta/ca3_status.json" 2>/dev/null | jq . 2>/dev/null || cat "$CA2_DIR/meta/ca3_status.json" 2>/dev/null || echo "Status file not found"
    
    echo
    log "CA3 status for CA2:"
    cat "$CA3_DIR/meta/ca2_status.json" 2>/dev/null | jq . 2>/dev/null || cat "$CA3_DIR/meta/ca2_status.json" 2>/dev/null || echo "Status file not found"
    
    echo
    log "=== File Listings ==="
    log "Files on CA2:"
    ls -la "$CA2_DIR/sync/" 2>/dev/null || echo "No files"
    
    echo
    log "Files on CA3:"
    ls -la "$CA3_DIR/sync/" 2>/dev/null || echo "No files"
    echo
}

# Function to cleanup
cleanup() {
    log "Cleaning up..."
    
    # Kill processes if running
    if [ -f "$CA2_DIR/ca2.pid" ]; then
        CA2_PID=$(cat "$CA2_DIR/ca2.pid")
        kill $CA2_PID 2>/dev/null || true
        rm -f "$CA2_DIR/ca2.pid"
    fi
    
    if [ -f "$CA3_DIR/ca3.pid" ]; then
        CA3_PID=$(cat "$CA3_DIR/ca3.pid")
        kill $CA3_PID 2>/dev/null || true
        rm -f "$CA3_DIR/ca3.pid"
    fi
    
    # Remove test directory
    rm -rf "$TEST_DIR" 2>/dev/null || true
    
    success "Cleanup complete"
}

# Function to follow logs in real-time
follow_logs() {
    log "Following logs (Ctrl+C to stop)..."
    echo
    
    # Start tail processes in background
    tail -f "$CA2_DIR/logs/output.log" 2>/dev/null | sed 's/^/[CA2] /' &
    TAIL_CA2_PID=$!
    
    tail -f "$CA3_DIR/logs/output.log" 2>/dev/null | sed 's/^/[CA3] /' &
    TAIL_CA3_PID=$!
    
    # Wait for interrupt
    trap "kill $TAIL_CA2_PID $TAIL_CA3_PID 2>/dev/null; exit 0" INT
    wait
}

# Main execution
case "${1:-help}" in
    "setup")
        setup_env
        ;;
        
    "start")
        setup_env
        start_services
        show_logs
        show_status
        ;;
        
    "test")
        test_sync
        show_status
        ;;
        
    "logs")
        show_logs
        ;;
        
    "follow")
        follow_logs
        ;;
        
    "status")
        show_status
        ;;
        
    "stop")
        cleanup
        ;;
        
    "restart")
        cleanup
        setup_env
        start_services
        ;;
        
    "help"|*)
        echo "Usage: $0 {setup|start|test|logs|follow|status|stop|restart}"
        echo
        echo "Commands:"
        echo "  setup   - Setup test environment only"
        echo "  start   - Setup and start both services"
        echo "  test    - Run file sync tests"
        echo "  logs    - Show recent logs from both services"
        echo "  follow  - Follow logs in real-time"
        echo "  status  - Show status files and file listings"
        echo "  stop    - Stop services and cleanup"
        echo "  restart - Stop, cleanup, and start fresh"
        ;;
esac
