folder:
  sync_path: "/tmp/folder-sync-test/server1/sync"
  meta_path: "/tmp/folder-sync-test/server1/meta"
  logs_path: "/tmp/folder-sync-test/server1/logs"
  ignores:
    - pattern: "/^\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "ZSTD"
  readonly: false
  writethrough: false
  concurrent: 10
  block_size: 4096
  initial_sync_timeout_minutes: 10

server:
  name: "server1"
  listen_address: "127.0.0.1:17890"
  auth_token: "test-auth-token-1751213400-very-long-token-for-testing-purposes-12345"

monitor:
  socket_path: ""

pairs: []

debug: true
