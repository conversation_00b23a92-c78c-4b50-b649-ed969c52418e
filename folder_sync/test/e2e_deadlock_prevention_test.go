package test

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestE2EDeadlockPrevention tests various scenarios that could cause deadlocks
// or infinite loops in the folder sync application.
func TestE2EDeadlockPrevention(t *testing.T) {
	// Skip if running in short mode
	if testing.Short() {
		t.Skip("Skipping E2E deadlock prevention test in short mode")
	}

	t.Run("CircularFileOperations", testCircularFileOperations)
	t.Run("SimultaneousFileCreation", testSimultaneousFileCreation)
	t.Run("RapidFileModifications", testRapidFileModifications)
	t.Run("NetworkDisconnectionDuringSync", testNetworkDisconnectionDuringSync)
	t.Run("QueueOverflowScenario", testQueueOverflowScenario)
	t.Run("ConcurrentDirectoryOperations", testConcurrentDirectoryOperations)
	t.Run("LargeFileTransferInterruption", testLargeFileTransferInterruption)
}

// testCircularFileOperations tests scenarios where files are modified back and forth
// between servers, which could cause infinite sync loops.
func testCircularFileOperations(t *testing.T) {
	testDir := setupTestEnvironment(t)
	defer cleanupTestEnvironment(testDir)

	buildApplication(t)

	// Start both servers
	server1Process := startServer(t, testDir, "server1")
	defer stopServer(server1Process)
	server2Process := startServer(t, testDir, "server2")
	defer stopServer(server2Process)

	// Wait for initial connection
	time.Sleep(5 * time.Second)

	// Create a file on server1
	file1Path := filepath.Join(testDir, "server1", "sync", "circular_test.txt")
	require.NoError(t, os.WriteFile(file1Path, []byte("content from server1"), 0644))

	// Wait for sync
	time.Sleep(3 * time.Second)

	// Modify the same file on server2 (should create a conflict scenario)
	file2Path := filepath.Join(testDir, "server2", "sync", "circular_test.txt")
	require.NoError(t, os.WriteFile(file2Path, []byte("content from server2"), 0644))

	// Wait for sync
	time.Sleep(3 * time.Second)

	// Modify again on server1
	require.NoError(t, os.WriteFile(file1Path, []byte("modified content from server1"), 0644))

	// Wait for sync and ensure it doesn't hang
	time.Sleep(5 * time.Second)

	// Verify both files exist and the system didn't deadlock
	assert.FileExists(t, file1Path)
	assert.FileExists(t, file2Path)

	t.Log("Circular file operations test completed without deadlock")
}

// testSimultaneousFileCreation tests creating files with the same name
// on both servers simultaneously.
func testSimultaneousFileCreation(t *testing.T) {
	testDir := setupTestEnvironment(t)
	defer cleanupTestEnvironment(testDir)

	buildApplication(t)

	server1Process := startServer(t, testDir, "server1")
	defer stopServer(server1Process)
	server2Process := startServer(t, testDir, "server2")
	defer stopServer(server2Process)

	time.Sleep(5 * time.Second)

	// Create files with the same name simultaneously
	var wg sync.WaitGroup
	wg.Add(2)

	go func() {
		defer wg.Done()
		file1Path := filepath.Join(testDir, "server1", "sync", "simultaneous.txt")
		os.WriteFile(file1Path, []byte("from server1"), 0644)
	}()

	go func() {
		defer wg.Done()
		file2Path := filepath.Join(testDir, "server2", "sync", "simultaneous.txt")
		os.WriteFile(file2Path, []byte("from server2"), 0644)
	}()

	wg.Wait()

	// Wait for sync to complete without hanging
	time.Sleep(8 * time.Second)

	// Verify the system handled the conflict gracefully
	file1Path := filepath.Join(testDir, "server1", "sync", "simultaneous.txt")
	file2Path := filepath.Join(testDir, "server2", "sync", "simultaneous.txt")

	assert.FileExists(t, file1Path)
	assert.FileExists(t, file2Path)

	t.Log("Simultaneous file creation test completed without deadlock")
}

// testRapidFileModifications tests rapid successive modifications
// that could overwhelm the queue or cause race conditions.
func testRapidFileModifications(t *testing.T) {
	testDir := setupTestEnvironment(t)
	defer cleanupTestEnvironment(testDir)

	buildApplication(t)

	server1Process := startServer(t, testDir, "server1")
	defer stopServer(server1Process)
	server2Process := startServer(t, testDir, "server2")
	defer stopServer(server2Process)

	time.Sleep(5 * time.Second)

	// Rapidly modify a file multiple times
	filePath := filepath.Join(testDir, "server1", "sync", "rapid_test.txt")

	for i := 0; i < 10; i++ {
		content := fmt.Sprintf("rapid modification %d", i)
		require.NoError(t, os.WriteFile(filePath, []byte(content), 0644))
		time.Sleep(100 * time.Millisecond) // Small delay between modifications
	}

	// Wait for all modifications to sync
	time.Sleep(10 * time.Second)

	// Verify the file exists on both servers
	file2Path := filepath.Join(testDir, "server2", "sync", "rapid_test.txt")
	assert.FileExists(t, file2Path)

	t.Log("Rapid file modifications test completed without deadlock")
}

// testNetworkDisconnectionDuringSync tests what happens when network
// connection is lost during active synchronization.
func testNetworkDisconnectionDuringSync(t *testing.T) {
	testDir := setupTestEnvironment(t)
	defer cleanupTestEnvironment(testDir)

	buildApplication(t)

	server1Process := startServer(t, testDir, "server1")
	defer stopServer(server1Process)
	server2Process := startServer(t, testDir, "server2")
	defer stopServer(server2Process)

	time.Sleep(5 * time.Second)

	// Create a file to sync
	filePath := filepath.Join(testDir, "server1", "sync", "network_test.txt")
	require.NoError(t, os.WriteFile(filePath, []byte("test content"), 0644))

	// Wait a moment for sync to start
	time.Sleep(1 * time.Second)

	// Simulate network disconnection by stopping server2
	stopServer(server2Process)

	// Wait a moment
	time.Sleep(2 * time.Second)

	// Restart server2
	server2Process = startServer(t, testDir, "server2")
	defer stopServer(server2Process)

	// Wait for reconnection and sync
	time.Sleep(8 * time.Second)

	// Verify the file was eventually synced
	file2Path := filepath.Join(testDir, "server2", "sync", "network_test.txt")
	assert.FileExists(t, file2Path)

	t.Log("Network disconnection during sync test completed without deadlock")
}

// testQueueOverflowScenario tests creating many files rapidly
// to potentially overflow the queue system.
func testQueueOverflowScenario(t *testing.T) {
	testDir := setupTestEnvironment(t)
	defer cleanupTestEnvironment(testDir)

	buildApplication(t)

	server1Process := startServer(t, testDir, "server1")
	defer stopServer(server1Process)
	server2Process := startServer(t, testDir, "server2")
	defer stopServer(server2Process)

	time.Sleep(5 * time.Second)

	// Create many files rapidly
	syncDir := filepath.Join(testDir, "server1", "sync")
	for i := 0; i < 50; i++ {
		fileName := fmt.Sprintf("overflow_test_%d.txt", i)
		filePath := filepath.Join(syncDir, fileName)
		content := fmt.Sprintf("content for file %d", i)
		require.NoError(t, os.WriteFile(filePath, []byte(content), 0644))
	}

	// Wait for all files to sync (should not hang)
	time.Sleep(15 * time.Second)

	// Verify at least some files were synced
	server2SyncDir := filepath.Join(testDir, "server2", "sync")
	files, err := os.ReadDir(server2SyncDir)
	require.NoError(t, err)

	// Should have synced at least some files
	assert.Greater(t, len(files), 10, "Expected at least some files to be synced")

	t.Log("Queue overflow scenario test completed without deadlock")
}

// testConcurrentDirectoryOperations tests concurrent directory creation/deletion
// which could cause race conditions.
func testConcurrentDirectoryOperations(t *testing.T) {
	testDir := setupTestEnvironment(t)
	defer cleanupTestEnvironment(testDir)

	buildApplication(t)

	server1Process := startServer(t, testDir, "server1")
	defer stopServer(server1Process)
	server2Process := startServer(t, testDir, "server2")
	defer stopServer(server2Process)

	time.Sleep(5 * time.Second)

	// Create directories first, then files after a delay to ensure proper sync order
	dirPath1 := filepath.Join(testDir, "server1", "sync", "concurrent_dir1", "subdir")
	require.NoError(t, os.MkdirAll(dirPath1, 0755))

	// Create second directory with a small delay
	time.Sleep(100 * time.Millisecond)
	dirPath2 := filepath.Join(testDir, "server2", "sync", "concurrent_dir2", "subdir")
	require.NoError(t, os.MkdirAll(dirPath2, 0755))

	// Wait a bit for directory sync to start, then create files
	time.Sleep(500 * time.Millisecond)
	filePath1 := filepath.Join(dirPath1, "file1.txt")
	require.NoError(t, os.WriteFile(filePath1, []byte("content1"), 0644))
	filePath2 := filepath.Join(dirPath2, "file2.txt")
	require.NoError(t, os.WriteFile(filePath2, []byte("content2"), 0644))

	// Wait for sync
	time.Sleep(15 * time.Second)

	// Verify directories were created on both servers
	dir1OnServer2 := filepath.Join(testDir, "server2", "sync", "concurrent_dir1", "subdir")
	dir2OnServer1 := filepath.Join(testDir, "server1", "sync", "concurrent_dir2", "subdir")

	// Debug: List what actually exists
	t.Logf("=== Debug: Checking directory sync ===")
	t.Logf("Server1 sync contents:")
	if entries, err := os.ReadDir(filepath.Join(testDir, "server1", "sync")); err == nil {
		for _, entry := range entries {
			t.Logf("  %s (isDir: %v)", entry.Name(), entry.IsDir())
			if entry.IsDir() {
				// Check subdirectories
				subPath := filepath.Join(testDir, "server1", "sync", entry.Name())
				if subEntries, err := os.ReadDir(subPath); err == nil {
					for _, subEntry := range subEntries {
						t.Logf("    %s/%s (isDir: %v)", entry.Name(), subEntry.Name(), subEntry.IsDir())
					}
				}
			}
		}
	}
	t.Logf("Server2 sync contents:")
	if entries, err := os.ReadDir(filepath.Join(testDir, "server2", "sync")); err == nil {
		for _, entry := range entries {
			t.Logf("  %s (isDir: %v)", entry.Name(), entry.IsDir())
			if entry.IsDir() {
				// Check subdirectories
				subPath := filepath.Join(testDir, "server2", "sync", entry.Name())
				if subEntries, err := os.ReadDir(subPath); err == nil {
					for _, subEntry := range subEntries {
						t.Logf("    %s/%s (isDir: %v)", entry.Name(), subEntry.Name(), subEntry.IsDir())
					}
				}
			}
		}
	}

	assert.DirExists(t, dir1OnServer2)
	assert.DirExists(t, dir2OnServer1)

	t.Log("Concurrent directory operations test completed without deadlock")
}

// testLargeFileTransferInterruption tests interrupting large file transfers
// to ensure proper cleanup and no deadlocks.
func testLargeFileTransferInterruption(t *testing.T) {
	testDir := setupTestEnvironment(t)
	defer cleanupTestEnvironment(testDir)

	buildApplication(t)

	server1Process := startServer(t, testDir, "server1")
	defer stopServer(server1Process)
	server2Process := startServer(t, testDir, "server2")
	defer stopServer(server2Process)

	time.Sleep(5 * time.Second)

	// Create a larger file (1MB)
	filePath := filepath.Join(testDir, "server1", "sync", "large_file.bin")
	largeContent := make([]byte, 1024*1024) // 1MB
	for i := range largeContent {
		largeContent[i] = byte(i % 256)
	}
	require.NoError(t, os.WriteFile(filePath, largeContent, 0644))

	// Wait a moment for transfer to start
	time.Sleep(2 * time.Second)

	// Interrupt by stopping and restarting server2
	stopServer(server2Process)
	time.Sleep(1 * time.Second)
	server2Process = startServer(t, testDir, "server2")
	defer stopServer(server2Process)

	// Wait for transfer to complete
	time.Sleep(10 * time.Second)

	// Verify the file was eventually transferred
	file2Path := filepath.Join(testDir, "server2", "sync", "large_file.bin")
	assert.FileExists(t, file2Path)

	// Verify file size matches
	if assert.FileExists(t, file2Path) {
		info, err := os.Stat(file2Path)
		require.NoError(t, err)
		assert.Equal(t, int64(len(largeContent)), info.Size())
	}

	t.Log("Large file transfer interruption test completed without deadlock")
}
