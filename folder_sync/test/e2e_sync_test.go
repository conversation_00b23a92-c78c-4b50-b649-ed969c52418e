package test

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// E2ESyncTest performs end-to-end testing of folder-sync with initial sync and ongoing sync
func TestE2ESync(t *testing.T) {
	// Skip if running in short mode
	if testing.Short() {
		t.Skip("Skipping E2E test in short mode")
	}

	// Setup test environment
	testDir := setupTestEnvironment(t)
	defer cleanupTestEnvironment(testDir)

	// Build the application
	buildApplication(t)

	// Create initial test files
	createInitialTestFiles(t, testDir)

	// Start server1 (listener)
	server1Process := startServer(t, testDir, "server1")
	defer stopServer(server1Process)

	// Wait for server1 to start
	time.Sleep(2 * time.Second)

	// Start server2 (connector)
	server2Process := startServer(t, testDir, "server2")
	defer stopServer(server2Process)

	// Wait for initial sync to complete
	t.Log("Waiting for initial sync to complete...")
	time.Sleep(10 * time.Second)

	// Verify initial sync worked
	verifyInitialSync(t, testDir)

	// Test ongoing sync: create folder on server1
	t.Log("Testing ongoing sync: creating folder on server1...")
	createTestFolder(t, testDir, "server1")
	time.Sleep(10 * time.Second) // Increased wait time
	verifyFolderSync(t, testDir)

	// Test ongoing sync: create file on server2
	t.Log("Testing ongoing sync: creating file on server2...")
	createTestFile(t, testDir, "server2")
	time.Sleep(10 * time.Second) // Increased wait time
	verifyFileSync(t, testDir)

	t.Log("E2E sync test completed successfully!")
}

// TestE2ETouchFileDeadlockFix tests the specific scenario that was causing deadlock:
// touching a file (which creates CHMOD_FILE + CREATE_FILE events) and ensuring
// proper acknowledgment handling prevents infinite retransmission loops.
//
// This test reproduces the issue where:
// 1. Server A sends CHMOD_FILE event for a non-existent file to Server B
// 2. Server B receives the event but the file doesn't exist locally
// 3. Server B should acknowledge the event (skip it) and continue processing
// 4. Server A should receive the acknowledgment and move to the next event (CREATE_FILE)
// 5. The sync should complete successfully without infinite retransmission
//
// Before the fix, Server B would call RecordPeerAcknowledgement() instead of
// SendAcknowledgementToPeer(), causing Server A to never receive an acknowledgment
// and retry the same message indefinitely, creating a deadlock.
func TestE2ETouchFileDeadlockFix(t *testing.T) {
	// Skip if running in short mode
	if testing.Short() {
		t.Skip("Skipping E2E test in short mode")
	}

	// Setup test environment
	testDir := setupTestEnvironment(t)
	defer cleanupTestEnvironment(testDir)

	// Build the application
	buildApplication(t)

	// Start server1 (listener)
	server1Process := startServer(t, testDir, "server1")
	defer stopServer(server1Process)

	// Wait for server1 to start
	time.Sleep(2 * time.Second)

	// Start server2 (connector)
	server2Process := startServer(t, testDir, "server2")
	defer stopServer(server2Process)

	// Wait for initial connection to establish
	t.Log("Waiting for initial connection to establish...")
	time.Sleep(5 * time.Second)

	// Test the touch scenario that was causing deadlock
	t.Log("Testing touch file scenario (CHMOD_FILE + CREATE_FILE events)...")
	touchFile(t, testDir, "server1", "touched_file.txt")

	// Wait for sync to complete - this should NOT hang indefinitely
	t.Log("Waiting for touch file sync to complete...")
	time.Sleep(8 * time.Second)

	// Verify the file was synced to server2
	verifyTouchFileSync(t, testDir, "touched_file.txt")

	// Test touch on server2 as well to ensure bidirectional works
	t.Log("Testing touch file scenario on server2...")
	touchFile(t, testDir, "server2", "touched_file2.txt")

	// Wait for sync to complete
	t.Log("Waiting for second touch file sync to complete...")
	time.Sleep(8 * time.Second)

	// Verify the file was synced to server1
	verifyTouchFileSyncReverse(t, testDir, "touched_file2.txt")

	t.Log("Touch file deadlock fix test completed successfully!")
}

func setupTestEnvironment(t *testing.T) string {
	testDir, err := os.MkdirTemp("", "folder-sync-e2e-test-")
	require.NoError(t, err)

	// Create directory structure
	for _, server := range []string{"server1", "server2"} {
		for _, subdir := range []string{"sync", "meta", "logs"} {
			err := os.MkdirAll(filepath.Join(testDir, server, subdir), 0755)
			require.NoError(t, err)
		}
	}

	// Generate auth token
	authToken := "test-auth-token-e2e-12345678901234567890123456789012"

	// Create config files
	createConfigFile(t, testDir, "server1", authToken, true)  // listener
	createConfigFile(t, testDir, "server2", authToken, false) // connector

	t.Logf("Test environment created at: %s", testDir)
	return testDir
}

func createConfigFile(t *testing.T, testDir, serverName, authToken string, isListener bool) {
	var config string
	if isListener {
		// Server1 config (listener mode - no pairs)
		config = fmt.Sprintf(`folder:
  sync_path: "%s"
  meta_path: "%s"
  logs_path: "%s"
  ignores:
    - pattern: "/^\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "ZSTD"
  readonly: false
  writethrough: false
  concurrent: 10
  block_size: 4096
  initial_sync_timeout_minutes: 10

server:
  name: "%s"
  listen_address: "127.0.0.1:17890"
  auth_token: "%s"

monitor:
  socket_path: ""

pairs: []

debug: true`,
			filepath.Join(testDir, serverName, "sync"),
			filepath.Join(testDir, serverName, "meta"),
			filepath.Join(testDir, serverName, "logs"),
			serverName,
			authToken)
	} else {
		// Server2 config (connector mode - connects to server1)
		config = fmt.Sprintf(`folder:
  sync_path: "%s"
  meta_path: "%s"
  logs_path: "%s"
  ignores:
    - pattern: "/^\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "ZSTD"
  readonly: false
  writethrough: false
  concurrent: 10
  block_size: 4096
  initial_sync_timeout_minutes: 10

server:
  name: "%s"
  listen_address: "127.0.0.1:17891"
  auth_token: "%s"

monitor:
  socket_path: ""

pairs:
  - name: "server1"
    host: "127.0.0.1"
    direction: "BIDIRECTIONAL_NEWEST_WIN"
    initial_sync_strategy: "SYNC"
    remote_port: 17890

debug: true`,
			filepath.Join(testDir, serverName, "sync"),
			filepath.Join(testDir, serverName, "meta"),
			filepath.Join(testDir, serverName, "logs"),
			serverName,
			authToken)
	}

	configPath := filepath.Join(testDir, serverName, "config.yaml")
	err := os.WriteFile(configPath, []byte(config), 0644)
	require.NoError(t, err)
}

func buildApplication(t *testing.T) {
	cmd := exec.Command("make", "build")
	cmd.Dir = ".." // Go up one level from test/ to project root
	output, err := cmd.CombinedOutput()
	if err != nil {
		t.Fatalf("Failed to build application: %v\nOutput: %s", err, output)
	}
	t.Log("Application built successfully")
}

func createInitialTestFiles(t *testing.T, testDir string) {
	// Create initial files on server1
	server1SyncDir := filepath.Join(testDir, "server1", "sync")
	err := os.WriteFile(filepath.Join(server1SyncDir, "initial_file1.txt"), []byte("Initial content from server1"), 0644)
	require.NoError(t, err)

	err = os.MkdirAll(filepath.Join(server1SyncDir, "initial_subdir"), 0755)
	require.NoError(t, err)

	err = os.WriteFile(filepath.Join(server1SyncDir, "initial_subdir", "nested_file.txt"), []byte("Nested file content"), 0644)
	require.NoError(t, err)

	// Create initial files on server2
	server2SyncDir := filepath.Join(testDir, "server2", "sync")
	err = os.WriteFile(filepath.Join(server2SyncDir, "initial_file2.txt"), []byte("Initial content from server2"), 0644)
	require.NoError(t, err)

	t.Log("Initial test files created")
}

func createLargeTestFile(t *testing.T, testDir string) {
	// Create a large file (3MB) to test chunked transfer
	server1SyncDir := filepath.Join(testDir, "server1", "sync")
	largeFilePath := filepath.Join(server1SyncDir, "large_test_file.bin")

	// Create 3MB of test data (larger than 64KB chunk size)
	const fileSize = 3 * 1024 * 1024 // 3MB
	data := make([]byte, fileSize)

	// Fill with a pattern to make it verifiable
	for i := 0; i < fileSize; i++ {
		data[i] = byte(i % 256)
	}

	err := os.WriteFile(largeFilePath, data, 0644)
	require.NoError(t, err)

	t.Logf("Created large test file: %s (%d bytes)", largeFilePath, fileSize)
}

// TestLargeFileSync tests synchronization of files larger than chunk size
func TestLargeFileSync(t *testing.T) {
	// Skip if running in short mode
	if testing.Short() {
		t.Skip("Skipping large file sync test in short mode")
	}

	// Setup test environment
	testDir := setupTestEnvironment(t)
	defer cleanupTestEnvironment(testDir)

	// Build the application
	buildApplication(t)

	// Create initial test files
	createInitialTestFiles(t, testDir)

	// Start server1 (listener)
	server1Process := startServer(t, testDir, "server1")
	defer stopServer(server1Process)

	// Wait for server1 to start
	time.Sleep(2 * time.Second)

	// Start server2 (connector)
	server2Process := startServer(t, testDir, "server2")
	defer stopServer(server2Process)

	// Wait for initial sync to complete
	t.Log("Waiting for initial sync to complete...")
	time.Sleep(10 * time.Second)

	// Create large file on server1
	createLargeTestFile(t, testDir)

	// Wait for large file sync to complete
	t.Log("Waiting for large file sync to complete...")
	time.Sleep(15 * time.Second) // Large files may take longer

	// Verify large file was synced to server2
	verifyLargeFileSync(t, testDir)

	t.Log("Large file sync test completed successfully")
}

func verifyLargeFileSync(t *testing.T, testDir string) {
	server1FilePath := filepath.Join(testDir, "server1", "sync", "large_test_file.bin")
	server2FilePath := filepath.Join(testDir, "server2", "sync", "large_test_file.bin")

	// Check that file exists on server2
	server2Info, err := os.Stat(server2FilePath)
	require.NoError(t, err, "Large file should exist on server2")

	// Check that file sizes match
	server1Info, err := os.Stat(server1FilePath)
	require.NoError(t, err, "Large file should exist on server1")

	assert.Equal(t, server1Info.Size(), server2Info.Size(), "File sizes should match")
	assert.Greater(t, server2Info.Size(), int64(0), "File should not be empty on server2")

	// Verify file content matches
	server1Data, err := os.ReadFile(server1FilePath)
	require.NoError(t, err, "Should be able to read server1 file")

	server2Data, err := os.ReadFile(server2FilePath)
	require.NoError(t, err, "Should be able to read server2 file")

	assert.Equal(t, server1Data, server2Data, "File contents should match exactly")

	t.Logf("Large file sync verified: %d bytes transferred successfully", server2Info.Size())
}

// TestChmodFileContentSync tests that CHMOD events with content changes are properly synchronized
func TestChmodFileContentSync(t *testing.T) {
	// Skip if running in short mode
	if testing.Short() {
		t.Skip("Skipping CHMOD file content sync test in short mode")
	}

	// Setup test environment
	testDir := setupTestEnvironment(t)
	defer cleanupTestEnvironment(testDir)

	// Build the application
	buildApplication(t)

	// Create initial test files
	createInitialTestFiles(t, testDir)

	// Start server1 (listener)
	server1Process := startServer(t, testDir, "server1")
	defer stopServer(server1Process)

	// Wait for server1 to start
	time.Sleep(2 * time.Second)

	// Start server2 (connector)
	server2Process := startServer(t, testDir, "server2")
	defer stopServer(server2Process)

	// Wait for initial sync to complete
	t.Log("Waiting for initial sync to complete...")
	time.Sleep(10 * time.Second)

	// Create a test file on server1
	server1FilePath := filepath.Join(testDir, "server1", "sync", "chmod_test.txt")
	initialContent := "Initial content for chmod test"
	err := os.WriteFile(server1FilePath, []byte(initialContent), 0644)
	require.NoError(t, err)

	// Wait for file to sync
	time.Sleep(5 * time.Second)

	// Verify file exists on server2
	server2FilePath := filepath.Join(testDir, "server2", "sync", "chmod_test.txt")
	server2Content, err := os.ReadFile(server2FilePath)
	require.NoError(t, err)
	assert.Equal(t, initialContent, string(server2Content), "Initial file content should match")

	// Simulate vim-like editing: modify content and trigger chmod event
	newContent := "Modified content after chmod event"
	err = os.WriteFile(server1FilePath, []byte(newContent), 0644)
	require.NoError(t, err)

	// Trigger a chmod event (simulating what vim might do)
	err = os.Chmod(server1FilePath, 0644)
	require.NoError(t, err)

	// Wait for chmod sync to complete
	t.Log("Waiting for CHMOD file content sync to complete...")
	time.Sleep(8 * time.Second)

	// Verify content was synced to server2
	server2ContentAfter, err := os.ReadFile(server2FilePath)
	require.NoError(t, err)
	assert.Equal(t, newContent, string(server2ContentAfter), "Modified file content should be synced via CHMOD event")

	// Verify file sizes match
	server1Info, err := os.Stat(server1FilePath)
	require.NoError(t, err)
	server2Info, err := os.Stat(server2FilePath)
	require.NoError(t, err)
	assert.Equal(t, server1Info.Size(), server2Info.Size(), "File sizes should match after CHMOD sync")

	t.Log("CHMOD file content sync test completed successfully")
}

func startServer(t *testing.T, testDir, serverName string) *exec.Cmd {
	configPath := filepath.Join(testDir, serverName, "config.yaml")
	cmd := exec.Command("./build/folder-sync", "-c", configPath)
	cmd.Dir = ".." // Run from project root where binary is built

	// Capture output for debugging
	logFile, err := os.Create(filepath.Join(testDir, serverName, fmt.Sprintf("%s.log", serverName)))
	require.NoError(t, err)
	cmd.Stdout = logFile
	cmd.Stderr = logFile

	err = cmd.Start()
	require.NoError(t, err)

	t.Logf("Started %s (PID: %d)", serverName, cmd.Process.Pid)
	return cmd
}

func stopServer(cmd *exec.Cmd) {
	if cmd != nil && cmd.Process != nil {
		cmd.Process.Kill()
		cmd.Wait()
	}
}

func verifyInitialSync(t *testing.T, testDir string) {
	server1SyncDir := filepath.Join(testDir, "server1", "sync")
	server2SyncDir := filepath.Join(testDir, "server2", "sync")

	// Check that server1's files exist on server2
	checkFileExists(t, filepath.Join(server2SyncDir, "initial_file1.txt"), "Initial content from server1")
	checkFileExists(t, filepath.Join(server2SyncDir, "initial_subdir", "nested_file.txt"), "Nested file content")

	// Check that server2's files exist on server1
	checkFileExists(t, filepath.Join(server1SyncDir, "initial_file2.txt"), "Initial content from server2")

	t.Log("Initial sync verification passed")
}

func createTestFolder(t *testing.T, testDir, serverName string) {
	syncDir := filepath.Join(testDir, serverName, "sync")
	folderPath := filepath.Join(syncDir, "test_ongoing_folder")
	err := os.MkdirAll(folderPath, 0755)
	require.NoError(t, err)

	// Wait a moment for inotify to set up watches for the new directory
	time.Sleep(100 * time.Millisecond)

	// Create a file inside the folder
	err = os.WriteFile(filepath.Join(folderPath, "folder_file.txt"), []byte("File inside ongoing test folder"), 0644)
	require.NoError(t, err)

	t.Logf("Created test folder on %s", serverName)
}

func verifyFolderSync(t *testing.T, testDir string) {
	// Check that the folder created on server1 exists on server2
	server2FolderPath := filepath.Join(testDir, "server2", "sync", "test_ongoing_folder")
	assert.DirExists(t, server2FolderPath, "Test folder should exist on server2")

	// Check that the file inside the folder also synced
	checkFileExists(t, filepath.Join(server2FolderPath, "folder_file.txt"), "File inside ongoing test folder")

	t.Log("Folder sync verification passed")
}

func createTestFile(t *testing.T, testDir, serverName string) {
	syncDir := filepath.Join(testDir, serverName, "sync")
	filePath := filepath.Join(syncDir, "test_ongoing_file.txt")
	content := fmt.Sprintf("Ongoing test file created on %s at %s", serverName, time.Now().Format(time.RFC3339))
	err := os.WriteFile(filePath, []byte(content), 0644)
	require.NoError(t, err)

	t.Logf("Created test file on %s", serverName)
}

func verifyFileSync(t *testing.T, testDir string) {
	// Check that the file created on server2 exists on server1
	server1FilePath := filepath.Join(testDir, "server1", "sync", "test_ongoing_file.txt")
	assert.FileExists(t, server1FilePath, "Test file should exist on server1")

	// Verify content contains server2 reference
	content, err := os.ReadFile(server1FilePath)
	require.NoError(t, err)
	assert.Contains(t, string(content), "server2", "File content should indicate it was created on server2")

	t.Log("File sync verification passed")
}

func checkFileExists(t *testing.T, filePath, expectedContent string) {
	assert.FileExists(t, filePath, fmt.Sprintf("File should exist: %s", filePath))

	content, err := os.ReadFile(filePath)
	require.NoError(t, err)
	assert.Equal(t, expectedContent, strings.TrimSpace(string(content)), fmt.Sprintf("File content mismatch: %s", filePath))
}

func cleanupTestEnvironment(testDir string) {
	os.RemoveAll(testDir)
}

// touchFile simulates the 'touch' command that was causing the deadlock.
// This creates an empty file, which generates both CHMOD_FILE and CREATE_FILE events.
func touchFile(t *testing.T, testDir, serverName, fileName string) {
	syncDir := filepath.Join(testDir, serverName, "sync")
	filePath := filepath.Join(syncDir, fileName)

	// Create an empty file (equivalent to 'touch' command)
	file, err := os.Create(filePath)
	require.NoError(t, err)
	file.Close()

	// Set specific permissions to ensure CHMOD_FILE event is generated
	err = os.Chmod(filePath, 0644)
	require.NoError(t, err)

	t.Logf("Touched file %s on %s", fileName, serverName)
}

// verifyTouchFileSync verifies that a file touched on server1 was synced to server2
func verifyTouchFileSync(t *testing.T, testDir, fileName string) {
	server2FilePath := filepath.Join(testDir, "server2", "sync", fileName)
	assert.FileExists(t, server2FilePath, fmt.Sprintf("Touched file %s should exist on server2", fileName))

	// Verify it's an empty file (as created by touch)
	info, err := os.Stat(server2FilePath)
	require.NoError(t, err)
	assert.Equal(t, int64(0), info.Size(), "Touched file should be empty")

	t.Logf("Touch file sync verification passed for %s", fileName)
}

// verifyTouchFileSyncReverse verifies that a file touched on server2 was synced to server1
func verifyTouchFileSyncReverse(t *testing.T, testDir, fileName string) {
	server1FilePath := filepath.Join(testDir, "server1", "sync", fileName)
	assert.FileExists(t, server1FilePath, fmt.Sprintf("Touched file %s should exist on server1", fileName))

	// Verify it's an empty file (as created by touch)
	info, err := os.Stat(server1FilePath)
	require.NoError(t, err)
	assert.Equal(t, int64(0), info.Size(), "Touched file should be empty")

	t.Logf("Touch file reverse sync verification passed for %s", fileName)
}
