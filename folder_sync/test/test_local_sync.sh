#!/bin/bash

# Local test script for folder-sync initial sync issue
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Setting up local folder-sync test environment...${NC}"

# Clean up any existing test directories
rm -rf /tmp/folder-sync-test
mkdir -p /tmp/folder-sync-test

# Create test directories
mkdir -p /tmp/folder-sync-test/server1/{sync,meta,logs}
mkdir -p /tmp/folder-sync-test/server2/{sync,meta,logs}

# Create some test files in server1
echo "Test file 1 content" > /tmp/folder-sync-test/server1/sync/test1.txt
echo "Test file 2 content" > /tmp/folder-sync-test/server2/sync/test2.txt
mkdir -p /tmp/folder-sync-test/server1/sync/subdir
echo "Subdirectory file" > /tmp/folder-sync-test/server1/sync/subdir/nested.txt

# Generate auth token
AUTH_TOKEN="test-auth-token-$(date +%s)"

# Create config for server1 (listener mode)
cat > /tmp/folder-sync-test/server1/config.yaml << EOF
folder:
  sync_path: "/tmp/folder-sync-test/server1/sync"
  meta_path: "/tmp/folder-sync-test/server1/meta"
  logs_path: "/tmp/folder-sync-test/server1/logs"
  ignores:
    - pattern: "/^\\./"
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "ZSTD"
  readonly: false
  writethrough: false
  concurrent: 10
  block_size: 4096
  initial_sync_timeout_minutes: 10

server:
  name: "server1"
  listen_address: "127.0.0.1:17890"
  auth_token: "${AUTH_TOKEN}"

monitor:
  socket_path: ""

pairs: []

debug: true
EOF

# Create config for server2 (connects to server1)
cat > /tmp/folder-sync-test/server2/config.yaml << EOF
folder:
  sync_path: "/tmp/folder-sync-test/server2/sync"
  meta_path: "/tmp/folder-sync-test/server2/meta"
  logs_path: "/tmp/folder-sync-test/server2/logs"
  ignores:
    - pattern: "/^\\./"
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "ZSTD"
  readonly: false
  writethrough: false
  concurrent: 10
  block_size: 4096
  initial_sync_timeout_minutes: 10

server:
  name: "server2"
  listen_address: "127.0.0.1:17891"
  auth_token: "${AUTH_TOKEN}"

monitor:
  socket_path: ""

pairs:
  - name: "server1"
    hostname_or_ip: "127.0.0.1"
    remote_port: 17890
    direction: "BIDIRECTIONAL_NEWEST_WIN"
    initial_sync: "SYNC"

debug: true
EOF

echo -e "${GREEN}Test environment created in /tmp/folder-sync-test${NC}"
echo -e "${YELLOW}Auth token: ${AUTH_TOKEN}${NC}"

# Build the application if needed
if [ ! -f "./folder-sync" ]; then
    echo -e "${YELLOW}Building folder-sync...${NC}"
    go build -o folder-sync ./cmd/folder-sync
fi

echo -e "${GREEN}Setup complete!${NC}"
echo ""
echo "To run the test:"
echo "1. Start server1 (listener): ./folder-sync -config /tmp/folder-sync-test/server1/config.yaml"
echo "2. Start server2 (connector): ./folder-sync -config /tmp/folder-sync-test/server2/config.yaml"
echo ""
echo "Test files:"
echo "- Server1: /tmp/folder-sync-test/server1/sync/"
echo "- Server2: /tmp/folder-sync-test/server2/sync/"
echo ""
echo "Logs:"
echo "- Server1: /tmp/folder-sync-test/server1/logs/"
echo "- Server2: /tmp/folder-sync-test/server2/logs/"
