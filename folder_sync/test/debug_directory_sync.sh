#!/bin/bash

# Debug script to investigate directory synchronization issues

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
TEST_DIR="/tmp/folder-sync-debug-dir-$$"

echo "=== Directory Sync Debug Test ==="
echo "Test directory: $TEST_DIR"

# Cleanup function
cleanup() {
    echo "Cleaning up..."
    if [ -n "$SERVER1_PID" ]; then
        kill $SERVER1_PID 2>/dev/null || true
    fi
    if [ -n "$SERVER2_PID" ]; then
        kill $SERVER2_PID 2>/dev/null || true
    fi
    rm -rf "$TEST_DIR"
}

trap cleanup EXIT

# Create test environment
echo "Setting up test environment..."
mkdir -p "$TEST_DIR"/{server1,server2}/{sync,meta,logs}

# Generate auth token
AUTH_TOKEN="debug-dir-sync-token-12345678901234567890123456789012"

# Create server1 config (listener)
cat > "$TEST_DIR/server1/config.yaml" << EOF
folder:
  sync_path: "$TEST_DIR/server1/sync"
  meta_path: "$TEST_DIR/server1/meta"
  logs_path: "$TEST_DIR/server1/logs"
  ignores:
    - pattern: "/^\\\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "NONE"
  concurrent: 5

server:
  name: "server1"
  listen_address: "127.0.0.1:8080"
  auth_token: "$AUTH_TOKEN"

pairs: []

debug: true
EOF

# Create server2 config (connector)
cat > "$TEST_DIR/server2/config.yaml" << EOF
folder:
  sync_path: "$TEST_DIR/server2/sync"
  meta_path: "$TEST_DIR/server2/meta"
  logs_path: "$TEST_DIR/server2/logs"
  ignores:
    - pattern: "/^\\\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "NONE"
  concurrent: 5

server:
  name: "server2"
  listen_address: "127.0.0.1:8081"
  auth_token: "$AUTH_TOKEN"

pairs:
  - name: "server1"
    host: "127.0.0.1"
    direction: "BIDIRECTIONAL_NEWEST_WIN"
    initial_sync_strategy: "SYNC"
    remote_port: 8080

debug: true
EOF

# Build the application
echo "Building application..."
cd "$PROJECT_ROOT"
make build

# Start server1 (listener)
echo "Starting server1 (listener)..."
"$PROJECT_ROOT/build/folder-sync" -c "$TEST_DIR/server1/config.yaml" > "$TEST_DIR/server1.log" 2>&1 &
SERVER1_PID=$!
echo "Server1 PID: $SERVER1_PID"

# Wait for server1 to start
sleep 3

# Start server2 (connector)
echo "Starting server2 (connector)..."
"$PROJECT_ROOT/build/folder-sync" -c "$TEST_DIR/server2/config.yaml" > "$TEST_DIR/server2.log" 2>&1 &
SERVER2_PID=$!
echo "Server2 PID: $SERVER2_PID"

# Wait for initial connection
echo "Waiting for initial connection..."
sleep 5

echo ""
echo "=== Testing Directory Synchronization ==="

# Test 1: Create directory on server1
echo "Test 1: Creating directory on server1..."
mkdir -p "$TEST_DIR/server1/sync/test_dir1"
echo "test content" > "$TEST_DIR/server1/sync/test_dir1/file1.txt"
echo "Directory created on server1"

# Wait for sync
echo "Waiting for sync (10 seconds)..."
sleep 10

# Check if directory appeared on server2
if [ -d "$TEST_DIR/server2/sync/test_dir1" ]; then
    echo "✅ SUCCESS: Directory synced to server2"
    if [ -f "$TEST_DIR/server2/sync/test_dir1/file1.txt" ]; then
        echo "✅ SUCCESS: File inside directory also synced"
    else
        echo "❌ FAILURE: File inside directory not synced"
    fi
else
    echo "❌ FAILURE: Directory did not sync to server2"
fi

echo ""

# Test 2: Create nested directory on server2
echo "Test 2: Creating nested directory on server2..."
mkdir -p "$TEST_DIR/server2/sync/nested/deep/path"
echo "nested content" > "$TEST_DIR/server2/sync/nested/deep/path/nested_file.txt"
echo "Nested directory created on server2"

# Wait for sync
echo "Waiting for sync (10 seconds)..."
sleep 10

# Check if nested directory appeared on server1
if [ -d "$TEST_DIR/server1/sync/nested/deep/path" ]; then
    echo "✅ SUCCESS: Nested directory synced to server1"
    if [ -f "$TEST_DIR/server1/sync/nested/deep/path/nested_file.txt" ]; then
        echo "✅ SUCCESS: File inside nested directory also synced"
    else
        echo "❌ FAILURE: File inside nested directory not synced"
    fi
else
    echo "❌ FAILURE: Nested directory did not sync to server1"
fi

echo ""

# Test 3: Create directories simultaneously
echo "Test 3: Creating directories simultaneously..."
mkdir -p "$TEST_DIR/server1/sync/simultaneous1" &
mkdir -p "$TEST_DIR/server2/sync/simultaneous2" &
wait

echo "content1" > "$TEST_DIR/server1/sync/simultaneous1/file1.txt" &
echo "content2" > "$TEST_DIR/server2/sync/simultaneous2/file2.txt" &
wait

echo "Simultaneous directories created"

# Wait for sync
echo "Waiting for sync (10 seconds)..."
sleep 10

# Check cross-sync
success=true
if [ -d "$TEST_DIR/server2/sync/simultaneous1" ]; then
    echo "✅ SUCCESS: simultaneous1 synced to server2"
else
    echo "❌ FAILURE: simultaneous1 did not sync to server2"
    success=false
fi

if [ -d "$TEST_DIR/server1/sync/simultaneous2" ]; then
    echo "✅ SUCCESS: simultaneous2 synced to server1"
else
    echo "❌ FAILURE: simultaneous2 did not sync to server1"
    success=false
fi

echo ""
echo "=== Final Status ==="
echo "Server1 sync directory contents:"
find "$TEST_DIR/server1/sync" -type d | sort
echo ""
echo "Server2 sync directory contents:"
find "$TEST_DIR/server2/sync" -type d | sort

echo ""
echo "=== Log Analysis ==="
echo "Server1 log (last 20 lines):"
tail -20 "$TEST_DIR/server1.log"
echo ""
echo "Server2 log (last 20 lines):"
tail -20 "$TEST_DIR/server2.log"

if [ "$success" = true ]; then
    echo ""
    echo "✅ All directory sync tests PASSED"
else
    echo ""
    echo "❌ Some directory sync tests FAILED"
    exit 1
fi
