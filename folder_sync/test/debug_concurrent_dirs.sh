#!/bin/bash

# Debug script to investigate concurrent directory creation issues

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
TEST_DIR="/tmp/folder-sync-debug-concurrent-$$"

echo "=== Concurrent Directory Creation Debug Test ==="
echo "Test directory: $TEST_DIR"

# Cleanup function
cleanup() {
    echo "Cleaning up..."
    if [ -n "$SERVER1_PID" ]; then
        kill $SERVER1_PID 2>/dev/null || true
    fi
    if [ -n "$SERVER2_PID" ]; then
        kill $SERVER2_PID 2>/dev/null || true
    fi
    rm -rf "$TEST_DIR"
}

trap cleanup EXIT

# Create test environment
echo "Setting up test environment..."
mkdir -p "$TEST_DIR"/{server1,server2}/{sync,meta,logs}

# Generate auth token
AUTH_TOKEN="debug-concurrent-dirs-token-12345678901234567890123456789012"

# Create server1 config (listener)
cat > "$TEST_DIR/server1/config.yaml" << EOF
folder:
  sync_path: "$TEST_DIR/server1/sync"
  meta_path: "$TEST_DIR/server1/meta"
  logs_path: "$TEST_DIR/server1/logs"
  ignores:
    - pattern: "/^\\\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "NONE"
  concurrent: 5

server:
  name: "server1"
  listen_address: "127.0.0.1:8080"
  auth_token: "$AUTH_TOKEN"

pairs: []

debug: true
EOF

# Create server2 config (connector)
cat > "$TEST_DIR/server2/config.yaml" << EOF
folder:
  sync_path: "$TEST_DIR/server2/sync"
  meta_path: "$TEST_DIR/server2/meta"
  logs_path: "$TEST_DIR/server2/logs"
  ignores:
    - pattern: "/^\\\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "NONE"
  concurrent: 5

server:
  name: "server2"
  listen_address: "127.0.0.1:8081"
  auth_token: "$AUTH_TOKEN"

pairs:
  - name: "server1"
    host: "127.0.0.1"
    direction: "BIDIRECTIONAL_NEWEST_WIN"
    initial_sync_strategy: "SYNC"
    remote_port: 8080

debug: true
EOF

# Build the application
echo "Building application..."
cd "$PROJECT_ROOT"
make build

# Start server1 (listener)
echo "Starting server1 (listener)..."
"$PROJECT_ROOT/build/folder-sync" -c "$TEST_DIR/server1/config.yaml" > "$TEST_DIR/server1.log" 2>&1 &
SERVER1_PID=$!
echo "Server1 PID: $SERVER1_PID"

# Wait for server1 to start
sleep 3

# Start server2 (connector)
echo "Starting server2 (connector)..."
"$PROJECT_ROOT/build/folder-sync" -c "$TEST_DIR/server2/config.yaml" > "$TEST_DIR/server2.log" 2>&1 &
SERVER2_PID=$!
echo "Server2 PID: $SERVER2_PID"

# Wait for initial connection
echo "Waiting for initial connection..."
sleep 5

echo ""
echo "=== Testing Concurrent Directory Creation ==="

# Create directories simultaneously with detailed timing
echo "Creating directories simultaneously..."
echo "$(date): Starting concurrent directory creation"

# Create directory on server1
mkdir -p "$TEST_DIR/server1/sync/concurrent_dir1/subdir" &
PID1=$!

# Create directory on server2  
mkdir -p "$TEST_DIR/server2/sync/concurrent_dir2/subdir" &
PID2=$!

# Wait for both to complete
wait $PID1
wait $PID2

echo "$(date): Directory creation completed"

# Add files to the directories
echo "Adding files to directories..."
echo "content1" > "$TEST_DIR/server1/sync/concurrent_dir1/subdir/file1.txt" &
echo "content2" > "$TEST_DIR/server2/sync/concurrent_dir2/subdir/file2.txt" &
wait

echo "$(date): Files added to directories"

# Wait for sync with detailed monitoring
echo "Waiting for sync (monitoring every 2 seconds for 20 seconds)..."
for i in {1..10}; do
    echo "$(date): Check $i/10"
    
    # Check server1 -> server2 sync
    if [ -d "$TEST_DIR/server2/sync/concurrent_dir1/subdir" ]; then
        echo "  ✅ concurrent_dir1 exists on server2"
        if [ -f "$TEST_DIR/server2/sync/concurrent_dir1/subdir/file1.txt" ]; then
            echo "  ✅ file1.txt exists on server2"
        else
            echo "  ❌ file1.txt missing on server2"
        fi
    else
        echo "  ❌ concurrent_dir1 missing on server2"
    fi
    
    # Check server2 -> server1 sync
    if [ -d "$TEST_DIR/server1/sync/concurrent_dir2/subdir" ]; then
        echo "  ✅ concurrent_dir2 exists on server1"
        if [ -f "$TEST_DIR/server1/sync/concurrent_dir2/subdir/file2.txt" ]; then
            echo "  ✅ file2.txt exists on server1"
        else
            echo "  ❌ file2.txt missing on server1"
        fi
    else
        echo "  ❌ concurrent_dir2 missing on server1"
    fi
    
    sleep 2
done

echo ""
echo "=== Final Status ==="
echo "Server1 sync directory structure:"
find "$TEST_DIR/server1/sync" -type d | sort
echo ""
echo "Server1 files:"
find "$TEST_DIR/server1/sync" -type f | sort
echo ""
echo "Server2 sync directory structure:"
find "$TEST_DIR/server2/sync" -type d | sort
echo ""
echo "Server2 files:"
find "$TEST_DIR/server2/sync" -type f | sort

echo ""
echo "=== Log Analysis ==="
echo "Server1 log (CREATE_DIR events):"
grep -i "create.*dir\|CREATE_DIR" "$TEST_DIR/server1.log" | tail -10
echo ""
echo "Server2 log (CREATE_DIR events):"
grep -i "create.*dir\|CREATE_DIR" "$TEST_DIR/server2.log" | tail -10

echo ""
echo "Server1 log (suppressed events):"
grep -i "suppressed\|suppress" "$TEST_DIR/server1.log" | tail -10
echo ""
echo "Server2 log (suppressed events):"
grep -i "suppressed\|suppress" "$TEST_DIR/server2.log" | tail -10

echo ""
echo "=== Test Complete ==="
