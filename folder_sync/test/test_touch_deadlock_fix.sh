#!/bin/bash

# Test script to manually reproduce the touch file deadlock scenario
# This script helps verify that the acknowledgment fix prevents infinite retransmission loops

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
TEST_DIR="/tmp/folder-sync-touch-test-$$"

echo "=== Touch File Deadlock Fix Test ==="
echo "Test directory: $TEST_DIR"

# Cleanup function
cleanup() {
    echo "Cleaning up..."
    if [ -n "$SERVER1_PID" ]; then
        kill $SERVER1_PID 2>/dev/null || true
    fi
    if [ -n "$SERVER2_PID" ]; then
        kill $SERVER2_PID 2>/dev/null || true
    fi
    rm -rf "$TEST_DIR"
}

trap cleanup EXIT

# Create test environment
echo "Setting up test environment..."
mkdir -p "$TEST_DIR"/{server1,server2}/{sync,meta,logs}

# Generate auth token
AUTH_TOKEN="test-touch-deadlock-fix-token-12345678901234567890123456789012"

# Create server1 config (listener)
cat > "$TEST_DIR/server1/config.yaml" << EOF
folder:
  sync_path: "$TEST_DIR/server1/sync"
  meta_path: "$TEST_DIR/server1/meta"
  logs_path: "$TEST_DIR/server1/logs"
  ignores:
    - pattern: "/^\\\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "NONE"
  concurrent: 5

server:
  name: "server1"
  listen_address: "127.0.0.1:8080"
  auth_token: "$AUTH_TOKEN"

pairs: []

debug: true
EOF

# Create server2 config (connector)
cat > "$TEST_DIR/server2/config.yaml" << EOF
folder:
  sync_path: "$TEST_DIR/server2/sync"
  meta_path: "$TEST_DIR/server2/meta"
  logs_path: "$TEST_DIR/server2/logs"
  ignores:
    - pattern: "/^\\\\."
      type: "regexp"
  compares: "FIRST_LAST_BLOCK_SAMPLE"
  compression: "NONE"
  concurrent: 5

server:
  name: "server2"
  listen_address: "127.0.0.1:8081"
  auth_token: "$AUTH_TOKEN"

pairs:
  - name: "server1"
    host: "127.0.0.1"
    direction: "BIDIRECTIONAL_NEWEST_WIN"
    initial_sync_strategy: "SYNC"
    remote_port: 8080

debug: true
EOF

# Build the application
echo "Building application..."
cd "$PROJECT_ROOT"
make build

# Start server1 (listener)
echo "Starting server1 (listener)..."
"$PROJECT_ROOT/build/folder-sync" -c "$TEST_DIR/server1/config.yaml" &
SERVER1_PID=$!
echo "Server1 PID: $SERVER1_PID"

# Wait for server1 to start
sleep 3

# Start server2 (connector)
echo "Starting server2 (connector)..."
"$PROJECT_ROOT/build/folder-sync" -c "$TEST_DIR/server2/config.yaml" &
SERVER2_PID=$!
echo "Server2 PID: $SERVER2_PID"

# Wait for initial connection
echo "Waiting for initial connection..."
sleep 5

# Test the touch scenario
echo ""
echo "=== Testing touch file scenario ==="
echo "This should NOT cause a deadlock or infinite retransmission loop"

# Touch a file on server1
echo "Touching file on server1..."
touch "$TEST_DIR/server1/sync/test_touch_file.txt"
echo "File touched: $TEST_DIR/server1/sync/test_touch_file.txt"

# Wait for sync
echo "Waiting for sync to complete (8 seconds)..."
sleep 8

# Check if file appeared on server2
if [ -f "$TEST_DIR/server2/sync/test_touch_file.txt" ]; then
    echo "✅ SUCCESS: File synced to server2"
else
    echo "❌ FAILURE: File did not sync to server2"
    exit 1
fi

# Touch a file on server2
echo ""
echo "Touching file on server2..."
touch "$TEST_DIR/server2/sync/test_touch_file2.txt"
echo "File touched: $TEST_DIR/server2/sync/test_touch_file2.txt"

# Wait for sync
echo "Waiting for sync to complete (8 seconds)..."
sleep 8

# Check if file appeared on server1
if [ -f "$TEST_DIR/server1/sync/test_touch_file2.txt" ]; then
    echo "✅ SUCCESS: File synced to server1"
else
    echo "❌ FAILURE: File did not sync to server1"
    exit 1
fi

echo ""
echo "=== Touch File Deadlock Fix Test PASSED ==="
echo "Both touch operations completed successfully without deadlock"
echo ""
echo "Log files for debugging:"
echo "  Server1: $TEST_DIR/server1/logs/folder-sync.log"
echo "  Server2: $TEST_DIR/server2/logs/folder-sync.log"
echo ""
echo "Test files created:"
echo "  Server1: $TEST_DIR/server1/sync/"
echo "  Server2: $TEST_DIR/server2/sync/"
