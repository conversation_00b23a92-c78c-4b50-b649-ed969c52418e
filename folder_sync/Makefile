# Folder Sync Makefile
# Build and development automation for the folder-sync project

# Variables
BINARY_NAME := folder-sync
MAIN_FILE := main.go
BUILD_DIR := build
INSTALL_DIR := /usr/local/bin
CONFIG_DIR := /etc
SERVICE_DIR := /etc/systemd/system

# Go build flags
GO_BUILD_FLAGS := -ldflags="-s -w"
GO_TEST_FLAGS := -v

# Version information (can be overridden)
VERSION ?= $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# Enhanced build flags with version info
LDFLAGS := -ldflags="-s -w -X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)"

.PHONY: help build test cleantest clean install uninstall dev lint fmt vet deps check coverage run docker

# Default target
all: build

# Help target
help: ## Show this help message
	@echo "Folder Sync - Available Make Targets:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}'
	@echo ""

# Build targets
build: ## Build the binary
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_FILE)
	@echo "Binary built: $(BUILD_DIR)/$(BINARY_NAME)"

build-release: ## Build optimized release binary
	@echo "Building release binary..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=1 GOOS=linux go build $(LDFLAGS) -a -installsuffix cgo -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_FILE)
	@echo "Release binary built: $(BUILD_DIR)/$(BINARY_NAME)"

# Development targets
dev: ## Build and run in development mode
	@echo "Running in development mode..."
	go run $(MAIN_FILE) -v -c folder-sync-dev.ini

run: build ## Build and run the application
	@echo "Running $(BINARY_NAME)..."
	./$(BUILD_DIR)/$(BINARY_NAME) -h

# Testing targets
test: ## Run all tests including E2E
	@echo "Running unit tests..."
	go test $(GO_TEST_FLAGS) ./pkg/...
	@echo "Running E2E tests..."
	go test -v ./test

cleantest: ## Clean test cache and run all tests
	@echo "Cleaning test cache..."
	go clean -testcache
	@echo "Running unit tests..."
	go test $(GO_TEST_FLAGS) ./pkg/...
	@echo "Running E2E tests..."
	go test -v ./test

test-short: ## Run short tests only
	@echo "Running short tests..."
	go test -short ./...

test-coverage: ## Run tests with coverage report
	@echo "Running tests with coverage..."
	go test -v -coverprofile=dev/coverage.out ./pkg/...
	go tool cover -html=dev/coverage.out -o dev/coverage.html
	@echo "Coverage report generated: dev/coverage.html"

test-integration: ## Run integration tests
	@echo "Running integration tests..."
	go test -tags=integration ./...

test-e2e: ## Run end-to-end sync tests
	@echo "Running end-to-end sync tests..."
	./test/test_e2e_sync.sh

test-e2e-go: ## Run Go-based end-to-end tests
	@echo "Running Go-based end-to-end tests..."
	go test -v ./test

coverage: test ## Generate test coverage report
	@echo "Generating coverage report..."
	@mkdir -p dev
	go tool cover -html=dev/coverage.out -o dev/coverage.html
	@echo "Coverage report generated: dev/coverage.html"

benchmark: ## Run benchmarks
	@echo "Running benchmarks..."
	go test -bench=. -benchmem ./...

# Code quality targets
lint: ## Run linter
	@echo "Running linter..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not installed. Install with: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"; \
	fi

fmt: ## Format code
	@echo "Formatting code..."
	go fmt ./...

vet: ## Run go vet
	@echo "Running go vet..."
	go vet ./...

check: fmt vet lint test ## Run all code quality checks

# Dependency management
deps: ## Download dependencies
	@echo "Downloading dependencies..."
	go mod download

# Prepare installation
prepare-install: ## Make install.sh executable
	@echo "Preparing installation script..."
	@chmod +x scripts/install.sh
	@echo "scripts/install.sh is now executable."

deps-update: ## Update dependencies
	@echo "Updating dependencies..."
	go get -u ./...
	go mod tidy

deps-verify: ## Verify dependencies
	@echo "Verifying dependencies..."
	go mod verify

# Installation targets
install: build ## Install the binary system-wide (requires sudo)
	@echo "Installing $(BINARY_NAME) to $(INSTALL_DIR)..."
	@if [ "$$(id -u)" != "0" ]; then \
		echo "Installation requires root privileges. Use: sudo make install"; \
		exit 1; \
	fi
	@if [ ! -f "$(BUILD_DIR)/$(BINARY_NAME)" ]; then \
		echo "Binary not found. Run 'make build' first."; \
		exit 1; \
	fi
	cp $(BUILD_DIR)/$(BINARY_NAME) $(INSTALL_DIR)/
	chmod +x $(INSTALL_DIR)/$(BINARY_NAME)
	@echo "Installation complete. Run 'folder-sync -h' for usage."

install-service: build ## Install as systemd service (requires sudo)
	@echo "Installing systemd service..."
	@if [ "$$(id -u)" != "0" ]; then \
		echo "Service installation requires root privileges. Use: sudo make install-service"; \
		exit 1; \
	fi
	@if [ ! -x "./scripts/install.sh" ]; then \
		echo "Making install.sh executable..."; \
		chmod +x ./scripts/install.sh; \
	fi
	./scripts/install.sh
	@echo "Service installation complete."

uninstall: ## Uninstall the binary and service (requires sudo)
	@echo "Uninstalling $(BINARY_NAME)..."
	@if [ "$$(id -u)" != "0" ]; then \
		echo "Uninstallation requires root privileges. Use: sudo make uninstall"; \
		exit 1; \
	fi
	systemctl stop folder-sync 2>/dev/null || true
	systemctl disable folder-sync 2>/dev/null || true
	rm -f $(SERVICE_DIR)/folder-sync.service
	rm -f $(INSTALL_DIR)/$(BINARY_NAME)
	systemctl daemon-reload
	@echo "Uninstallation complete."

# Validation targets
validate: ## Validate production readiness
	@echo "Validating production readiness..."
	@if [ -f "./scripts/validate-production.sh" ]; then \
		./scripts/validate-production.sh; \
	else \
		echo "scripts/validate-production.sh not found"; \
	fi

config-test: build ## Test configuration file
	@echo "Testing configuration..."
	./$(BUILD_DIR)/$(BINARY_NAME) -t

# Docker targets
docker-build: ## Build Docker image
	@echo "Building Docker image..."
	docker build -t folder-sync:$(VERSION) .

docker-run: docker-build ## Run in Docker container
	@echo "Running Docker container..."
	docker run --rm -it folder-sync:$(VERSION)

# Cleanup targets
clean: ## Clean build artifacts
	@echo "Cleaning build artifacts..."
	rm -rf $(BUILD_DIR)
	rm -f dev/coverage.out dev/coverage.html
	go clean -cache
	@echo "Clean complete."

clean-all: clean ## Clean everything including dependencies
	@echo "Cleaning everything..."
	go clean -modcache
	@echo "Deep clean complete."

# Release targets
release: clean build-release test ## Build release version with tests
	@echo "Release build complete: $(BUILD_DIR)/$(BINARY_NAME)"
	@echo "Version: $(VERSION)"
	@echo "Build Time: $(BUILD_TIME)"
	@echo "Git Commit: $(GIT_COMMIT)"

# Development utilities
watch: ## Watch for changes and rebuild (requires entr)
	@echo "Watching for changes... (requires 'entr' tool)"
	@if command -v entr >/dev/null 2>&1; then \
		find . -name "*.go" | entr -r make build; \
	else \
		echo "entr not installed. Install with your package manager."; \
	fi

# Show project info
info: ## Show project information
	@echo "Project: Folder Sync"
	@echo "Binary: $(BINARY_NAME)"
	@echo "Version: $(VERSION)"
	@echo "Build Time: $(BUILD_TIME)"
	@echo "Git Commit: $(GIT_COMMIT)"
	@echo "Go Version: $$(go version)"
	@echo "Build Dir: $(BUILD_DIR)"
