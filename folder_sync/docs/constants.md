# Centralized Constants

This document describes the centralized constants defined in `pkg/types/constants.go` that are used throughout the folder-sync application.

## File and Network Transfer Constants

### `DefaultChunkSize = 64 KB`
- **Purpose**: Default size for file chunks during network transfer
- **Used in**: 
  - `pkg/client/client.go` - Proxy client chunk size
  - Large file transfers over network connections
- **Rationale**: 64KB provides good balance between memory usage and network efficiency

### `DefaultBlockSize = 64 KB`
- **Purpose**: Default block size for file operations and checksum calculations
- **Used in**:
  - `pkg/config/config.go` - Default configuration value
  - `pkg/fsutil/fsutil.go` - Checksum calculation functions
- **Rationale**: Matches common filesystem block sizes and provides efficient I/O

### `SmallFileThreshold = 4 KB`
- **Purpose**: Size threshold for determining direct vs atomic writes
- **Used in**:
  - `pkg/fsutil/fsutil.go` - `AtomicWriteFile()` function
- **Rationale**: Files smaller than 4KB are written directly without temporary files to avoid overhead

### `DefaultIOBufferSize = 4 KB`
- **Purpose**: Default buffer size for general I/O operations
- **Used in**:
  - `pkg/client/client.go` - Buffered reader implementation
- **Rationale**: Standard page size on most systems, efficient for buffered I/O

### `OSBlockSizeFallback = 4 KB`
- **Purpose**: Fallback block size when OS block size detection fails
- **Used in**:
  - `pkg/fsutil/fsutil.go` - `GetOSBlockSize()` function
- **Rationale**: Safe default when syscall to get filesystem block size fails

## Network Protocol Constants

### `MaxStringSize = 32 KB`
- **Purpose**: Maximum allowed size for strings in network protocol
- **Used in**:
  - `pkg/network/protocol.go` - String serialization/deserialization
- **Rationale**: Prevents memory exhaustion attacks while allowing reasonable string sizes

### `MaxByteArraySize = 100 MB`
- **Purpose**: Maximum allowed size for byte arrays in network protocol
- **Used in**:
  - `pkg/network/protocol.go` - Byte array serialization/deserialization
- **Rationale**: Prevents memory exhaustion while allowing large file transfers

### `MaxMessageQueueSize = 100`
- **Purpose**: Default size for message queues
- **Used in**:
  - `pkg/network/network.go` - Message queue channel buffer size
- **Rationale**: Provides sufficient buffering without excessive memory usage

## Configuration Defaults

### `DefaultConcurrentTransfers = 100`
- **Purpose**: Default number of concurrent file transfers
- **Used in**:
  - `pkg/config/config.go` - Default configuration value
- **Rationale**: Balances performance with resource usage

### `DefaultInitialSyncTimeoutMinutes = 20`
- **Purpose**: Default timeout for initial sync progress logging
- **Used in**:
  - `pkg/config/config.go` - Default configuration value
- **Rationale**: Reasonable timeout for initial sync operations

## Benefits of Centralization

1. **Consistency**: All components use the same values for related operations
2. **Maintainability**: Single location to update chunk sizes and buffer sizes
3. **Documentation**: Clear purpose and rationale for each constant
4. **Type Safety**: Constants are properly typed and validated
5. **Performance Tuning**: Easy to adjust values for different environments

## Migration from Hardcoded Values

The following hardcoded values were replaced with centralized constants:

- `65536` (64KB) → `types.DefaultChunkSize` and `types.DefaultBlockSize`
- `4096` (4KB) → `types.SmallFileThreshold`, `types.DefaultIOBufferSize`, `types.OSBlockSizeFallback`
- `32 * 1024` → `types.MaxStringSize`
- `100 * 1024 * 1024` → `types.MaxByteArraySize`
- `100` → `types.MaxMessageQueueSize`, `types.DefaultConcurrentTransfers`
- `20` → `types.DefaultInitialSyncTimeoutMinutes`

## Usage Guidelines

When adding new functionality that requires buffer sizes, chunk sizes, or similar constants:

1. **Check existing constants first** - Use existing constants if they fit your use case
2. **Add to constants.go** - If you need a new constant, add it to `pkg/types/constants.go`
3. **Document the purpose** - Include clear comments explaining the constant's purpose
4. **Use descriptive names** - Follow the naming convention: `Default*`, `Max*`, `*Threshold`, etc.
5. **Consider configurability** - For user-facing values, consider making them configurable via config file
