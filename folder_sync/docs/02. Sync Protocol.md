# **FOLDER-SYNC-SOCKET-PROTOCOL.md \- Binary Protocol Specification**

This document specifies the custom, efficient binary protocol used by the folder-sync daemon for inter-server communication and for the proxy socket communication with the Go Client Package.

## **1\. General Protocol Principles**

* **Binary Encoding:** All data is encoded in a compact binary format.  
* **Big-Endian Byte Order:** All multi-byte fields (e.g., lengths, timestamps, integers) are transmitted in big-endian byte order.  
* **Length-Prefixed Fields:** Variable-length fields (like strings, byte arrays for file bodies) are prefixed with their length to allow for efficient parsing without delimiters.  
* **Message-Based:** Communication consists of discrete messages, each identified by a MessageType byte.  
* **Reliability:** The underlying TCP/Unix domain socket provides reliable, ordered delivery.  
* **No TLS:** TLS encryption is explicitly *not* supported in this version.  
* **Authentication:** Token-based authentication is performed during the initial handshake for inter-server communication. Proxy socket communication uses simpler welcome/test mechanisms.

## **2\. Data Types and Encoding**

The following fundamental data types are used and encoded as described:

* **byte (1 byte):** Single byte.  
* **uint16 (2 bytes):** Unsigned 16-bit integer.  
* **uint32 (4 bytes):** Unsigned 32-bit integer.  
* **uint64 (8 bytes):** Unsigned 64-bit integer.  
* **bool (1 byte):** 0x00 for false, 0x01 for true.  
* **string (Length-prefixed bytes):** uint16 length of the UTF-8 encoded string, followed by the UTF-8 bytes. Max length 65535 bytes.  
  * Format: \[Length: uint16\] \[String Bytes...\]  
* **time.Time (8 bytes \- Unix Nano):** uint64 representing the Unix nanosecond timestamp (e.g., time.Now().UnixNano()).  
* **\[\]byte (Length-prefixed bytes):** uint32 length of the byte array, followed by the raw bytes. Max length 4GB.  
  * Format: \[Length: uint32\] \[Byte Array Bytes...\]  
* **FileCompareStrategy (1 byte \- enum):** Represents the file comparing strategy.  
  * 0x01: SIZE\_ONLY  
  * 0x02: FIRST\_LAST\_BLOCK\_SAMPLE  
  * 0x03: FULL\_FILE  
* **FileSyncEventType (1 byte \- enum):** Represents the type of file system event.  
  * 0x01: CREATE\_FILE  
  * 0x02: WRITE\_FILE  
  * 0x03: DELETE\_FILE  
  * 0x04: RENAME\_FILE  
  * 0x05: CHMOD\_FILE  
  * 0x06: CREATE\_DIR  
  * 0x07: DELETE\_DIR  
  * 0x08: RENAME\_DIR  
  * 0x09: CREATE\_SYMLINK  
* **CommunicationOrigin (1 byte \- enum):** Indicates the source/context of a FileSyncEvent.  
  * 0x01: LOCAL\_INOTIFY (Event detected by daemon's inotify watch)  
  * 0x02: REMOTE\_PEER (Event received from another folder-sync daemon)  
  * 0x03: PROXY\_CLIENT\_WRITE (Event originated from a Go Client Package writing via the proxy socket)  
* **ProtocolVersion (3 bytes):** Semantic versioning (e.g., 1.2.3). Each component (major, minor, patch) is a single byte.  
  * Format: \[Major: byte\] \[Minor: byte\] \[Patch: byte\]

## **3\. Message Structure**

All messages follow a general structure:  
\[MessageType: byte\] \[Payload...\]  
The MessageType determines the structure and content of the Payload. Each message type is flagged below to indicate its usage in Inter-Server communication, Proxy-Client communication, or both (implicitly by context or specific fields).

## **4\. Message Types and Formats**

### **4.1. Inter-Server Handshake Messages**

Used for initial connection establishment and authentication between folder-sync daemons.

#### **4.1.1. MSG\_HANDSHAKE\_REQUEST (Inter-Server: Client \-\> Server)**

Sent by the client upon initiating an inter-server connection to authenticate and propose its protocol version.

* **MessageType:** 0x01  
* **Payload:**  
  * AuthToken: string (Shared secret token for authentication)  
  * ClientVersion: ProtocolVersion

#### **4.1.2. MSG\_HANDSHAKE\_RESPONSE (Inter-Server: Server \-\> Client)**

Sent by the server in response to a MSG\_HANDSHAKE\_REQUEST. Indicates authentication success/failure and the server's accepted protocol version.

* **MessageType:** 0x02  
* **Payload:**  
  * Success: bool (true if authentication successful, false otherwise)  
  * ServerVersion: ProtocolVersion (The version the server is running or has negotiated)  
  * ErrorMessage: string (If Success is false, a description of the error)

### **4.2. File Synchronization Messages (Inter-Server and Proxy-Client)**

These messages are primarily used for synchronizing file system events and file bodies between folder-sync daemons. MSG\_FILE\_SYNC\_EVENT can also originate from a proxy client.

#### **4.2.1. MSG\_FILE\_SYNC\_EVENT (Inter-Server: Sender \-\> Receiver | Proxy-Client: Client \-\> Daemon)**

Represents a file system change event to be synchronized. This message includes metadata and, for file creations/writes, can either include the file body directly (for small files) or signal that the body will be sent in chunks.

* **MessageType:** 0x03  
* **Payload:**  
  * MessageID: uint64 (Unique ID for this message, correlating to a queue entry in inter-server communication)  
  * Origin: CommunicationOrigin (Indicates the source of this event)  
  * EventType: FileSyncEventType  
  * Path: string (Relative path to the sync folder)  
  * Timestamp: time.Time (mtime of the file/directory)  
  * Size: uint64 (Total size of the file in bytes; 0 for directories/deletions)  
  * FileMode: uint32 (Unix file permissions, e.g., 0o755)  
  * OwnerUID: uint32 (User ID of the file owner)  
  * OwnerGID: uint32 (Group ID of the file owner)  
  * FileCompareStrategy: byte (File compare strategy used by sender)  
  * Checksum: \[\]byte (xxHash checksum of the file content if compare strategy is FIRST\_LAST\_BLOCK\_SAMPLE or FULL\_FILE, or empty for SIZE\_ONLY or if the file body accompanies the message directly.)  
  * SendsChunks: bool (true if FileBody will be sent in subsequent MSG\_FILE\_CHUNK messages; false if FileBody is included directly in this message or is empty)  
  * IsCompressed: bool (True if FileBody is zstd compressed (if SendsChunks is false) or if MSG\_FILE\_CHUNK data will be compressed (if SendsChunks is true))  
  * FileBody: \[\]byte (The content of the file for CREATE/WRITE events *if SendsChunks is false*. Empty for other event types or if SendsChunks is true).

#### **4.2.2. MSG\_PULL\_FILE\_REQUEST (Inter-Server: Receiver \-\> Sender)**

Sent by the receiver to request the full file body (or begin a chunked transfer) for a previously received FileSyncEvent (e.g., if the initial FileSyncEvent only sent metadata, or if the file was updated).

* **MessageType:** 0x04  
* **Payload:**  
  * MessageID: uint64 (The MessageID of the original MSG\_FILE\_SYNC\_EVENT this request is for)  
  * Path: string (Relative path of the file to pull)  
  * Size: uint64 (Size of the file on the receiver side in bytes; 0 for directories/deletions)  
  * FileCompareStrategy: byte (File compare strategy. Must be FIRST\_LAST\_BLOCK\_SAMPLE or FULL\_FILE, or 0 if no file on the receiver side.)  
  * Checksum: \[\]byte (The checksum of the file on the receiver side, or empty for no file.)

#### **4.2.3. MSG\_PULL\_FILE\_RESPONSE (Inter-Server: Sender \-\> Receiver)**

Sent by the original sender in response to a MSG\_PULL\_FILE\_REQUEST. Contains the requested file body or signals the start of a chunked transfer. Timestamp and Size are resent in case the file changed between SYNC event and PULL\_FILE request.

* **MessageType:** 0x05  
* **Payload:**  
  * MessageID: uint64 (The MessageID of the original MSG\_PULL\_FILE\_REQUEST this response is for)  
  * Path: string (Relative path of the file being sent)  
  * Timestamp: time.Time (mtime of the file/directory)  
  * Size: uint64 (Size of the file in bytes; 0 for directories/deletions)  
  * FileMode: uint32 (Unix file permissions, e.g., 0o755)  
  * OwnerUID: uint32 (User ID of the file owner)  
  * OwnerGID: uint32 (Group ID of the file owner)  
  * SendsChunks: bool (true if FileBody will be sent in subsequent MSG\_FILE\_CHUNK messages; false if FileBody is included directly in this message or is empty)  
  * IsCompressed: bool (True if FileBody is zstd compressed (if SendsChunks is false) or if MSG\_FILE\_CHUNK data will be compressed (if SendsChunks is true))  
  * FileBody: \[\]byte (The content of the requested file *if SendsChunks is false*. Empty if SendsChunks is true).  
  * ErrorMessage: string (If FileBody is empty due to an error, a description of the error)

#### **4.2.4. MSG\_SYNC\_ACKNOWLEDGE (Inter-Server: Receiver \-\> Sender)**

Sent by the receiver to acknowledge successful application of a FileSyncEvent, PULL\_FILE\_RESPONSE, or MSG\_SAME\_CONTENT. This signals completion for a specific file/operation for tracking cleanup in writethrough mode.

* **MessageType:** 0x06  
* **Payload:**  
  * MessageID: uint64 (The MessageID of the original message being acknowledged)  
  * Path: string (Relative path of the acknowledged file/directory)  
  * EventType: FileSyncEventType (The type of event being acknowledged, matching the original event's type)  
  * Success: bool (true if applied successfully, false otherwise)  
  * ErrorMessage: string (If Success is false, a description of the error)

#### **4.2.5. MSG\_FILE\_REMOVED (Inter-Server: Sender \-\> Receiver)**

Sent by the original sender to inform the receiver that a file requested via PULL\_FILE\_REQUEST was removed locally before it could be sent.

* **MessageType:** 0x07  
* **Payload:**  
  * MessageID: uint64 (The MessageID of the original MSG\_PULL\_FILE\_REQUEST this message is related to)  
  * Path: string (Relative path of the file that was removed)

#### **4.2.6. MSG\_SAME\_CONTENT (Inter-Server: Sender \-\> Receiver)**

Sent by the sender to indicate that the target file on the receiver already has identical content based on the sender's comparison strategy and checksum. This avoids unnecessary file transfers.

* **MessageType:** 0x08  
* **Payload:**  
  * MessageID: uint64 (The MessageID of the original MSG\_FILE\_SYNC\_EVENT that would have otherwise sent the file body)  
  * Path: string (Relative path of the file with same content)  
  * Timestamp: time.Time (mtime of the file on sender)  
  * Size: uint64 (Size of the file on sender)

#### **4.2.7. MSG\_FILE\_CHUNK (Inter-Server: Sender \-\> Receiver | Proxy-Client: Client \-\> Daemon)**

Sent by a sender (daemon or proxy client) to transmit a portion (chunk) of a file body. This message is part of a multi-message file transfer.

* **MessageType:** 0x09  
* **Payload:**  
  * MessageID: uint64 (The MessageID of the original MSG\_FILE\_SYNC\_EVENT or MSG\_PULL\_FILE\_RESPONSE this chunk belongs to)  
  * Path: string (Relative path of the file being chunked)  
  * ChunkIndex: uint32 (0-based index of the current chunk)  
  * TotalChunks: uint32 (Total number of chunks for the file)  
  * IsCompressed: bool (True if ChunkData is zstd compressed)  
  * ChunkData: \[\]byte (The actual binary data for this chunk)

#### **4.2.8. MSG\_CHUNK\_ACKNOWLEDGE (Inter-Server: Receiver \-\> Sender)**

Sent by the receiver to acknowledge successful receipt and processing of a MSG\_FILE\_CHUNK.

* **MessageType:** 0x0A  
* **Payload:**  
  * MessageID: uint64 (The MessageID of the MSG\_FILE\_CHUNK being acknowledged)  
  * Path: string (Relative path of the file this chunk belongs to)  
  * ChunkIndex: uint32 (Index of the acknowledged chunk)  
  * Success: bool (true if chunk processed successfully, false otherwise)  
  * ErrorMessage: string (If Success is false, a description of the error)

### **4.3. Proxy Socket Communication Messages (Proxy-Client)**

These messages are specifically for communication between the Go Client Package and the folder-sync daemon's proxy socket.

#### **4.3.1. MSG\_PROXY\_WELCOME\_REQUEST (Proxy-Client: Client \-\> Daemon)**

Sent by the Go Client Package upon connecting to the proxy socket to initiate communication.

* **MessageType:** 0x10  
* **Payload:**  
  * ClientMessage: string (A greeting or identification message from the client)

#### **4.3.2. MSG\_PROXY\_WELCOME\_RESPONSE (Proxy-Client: Daemon \-\> Client)**

Sent by the folder-sync daemon in response to a MSG\_PROXY\_WELCOME\_REQUEST.

* **MessageType:** 0x11  
* **Payload:**  
  * Success: bool (true if welcome successful, false otherwise)  
  * DaemonMessage: string (A welcome message or error description from the daemon)

#### **4.3.3. MSG\_PROXY\_TEST\_CONNECTION\_REQUEST (Proxy-Client: Client \-\> Daemon)**

Sent by the Go Client Package to test if the proxy socket connection is active and responsive.

* **MessageType:** 0x12  
* **Payload:** (Empty)

#### **4.3.4. MSG\_PROXY\_TEST\_CONNECTION\_RESPONSE (Proxy-Client: Daemon \-\> Client)**

Sent by the folder-sync daemon in response to a MSG\_PROXY\_TEST\_CONNECTION\_REQUEST.

* **MessageType:** 0x13  
* **Payload:**  
  * Success: bool (true if daemon is responsive, false otherwise)  
  * Message: string (A status message)

#### **4.3.5. MSG\_PROXY\_EVENT\_ACKNOWLEDGE (Proxy-Client: Daemon \-\> Client)**

Sent by the folder-sync daemon to the Go Client Package to acknowledge the receipt and initial processing of a MSG\_FILE\_SYNC\_EVENT or a MSG\_FILE\_CHUNK sent over the proxy socket. This provides immediate feedback to the client regarding its write operation.

* **MessageType:** 0x14  
* **Payload:**  
  * MessageID: uint64 (The MessageID of the MSG\_FILE\_SYNC\_EVENT or MSG\_FILE\_CHUNK that is being acknowledged)  
  * Path: string (The relative path of the file/directory related to the acknowledged event)  
  * Success: bool (true if the event was successfully received and queued/processed by the daemon; false otherwise)  
  * ErrorMessage: string (If Success is false, a description of the error)

### **4.4. General Error Message**

#### **4.4.1. MSG\_ERROR (Any \-\> Any)**

A general error message that can be sent by either client or server when an unrecoverable or unexpected error occurs.

* **MessageType:** 0xFE  
* **Payload:**  
  * Code: uint16 (An error code, TBD)  
  * Message: string (A human-readable error description)

## **5\. Message Sequencing and Flow (Examples)**

### **5.1. Inter-Server Initial Connection & Authentication**

1. Client sends MSG\_HANDSHAKE\_REQUEST.  
2. Server receives, authenticates, checks version, and sends MSG\_HANDSHAKE\_RESPONSE.  
3. If Success is false, connection closes. If true, communication proceeds.

### **5.2. Inter-Server File Creation/Modification (Small File \- Immediate Body)**

1. (Sender Daemon detects local change (via inotify) or receives event from Proxy Client (via MSG\_FILE\_SYNC\_EVENT with Origin: PROXY\_CLIENT\_WRITE))  
2. Sender Daemon sends MSG\_FILE\_SYNC\_EVENT (with SendsChunks: false, FileBody included, FileMode, OwnerUID, OwnerGID, and MessageID) to Receiver Daemon.  
3. Receiver Daemon applies change.  
4. Receiver Daemon sends MSG\_SYNC\_ACKNOWLEDGE (referencing MessageID) to Sender Daemon.

### **5.3. Inter-Server File Creation/Modification (Large File \- Chunked Transfer)**

This flow is used when a file's size dictates chunking.

1. (Sender Daemon detects local change or receives event from Proxy Client)  
2. Sender Daemon sends MSG\_FILE\_SYNC\_EVENT (with SendsChunks: true, empty FileBody, full metadata, FileMode, OwnerUID, OwnerGID, checksum, and MessageID) to Receiver Daemon.  
3. Receiver Daemon processes metadata and prepares to receive chunks.  
4. Sender Daemon begins sending MSG\_FILE\_CHUNK messages (each with MessageID, Path, ChunkIndex, TotalChunks, IsCompressed, and ChunkData).  
5. For each MSG\_FILE\_CHUNK received, Receiver Daemon writes the chunk data and sends MSG\_CHUNK\_ACKNOWLEDGE (referencing MessageID, Path, ChunkIndex) back to the Sender Daemon upon successful receipt and write.  
6. Upon receiving all chunks and successfully rebuilding the file, Receiver Daemon sends a final MSG\_SYNC\_ACKNOWLEDGE (referencing the original MSG\_FILE\_SYNC\_EVENT's MessageID) to Sender Daemon.

### **5.4. Inter-Server File Pull (Large File \- Chunked Transfer)**

This scenario occurs when the receiver explicitly requests a large file body.

1. (Sender Daemon detects change)  
2. Sender Daemon sends MSG\_FILE\_SYNC\_EVENT (with SendsChunks: false, empty FileBody but full metadata, FileMode, OwnerUID, OwnerGID, checksum, and MessageID) to Receiver Daemon.  
3. Receiver Daemon processes metadata and determines it needs the file body (e.g., checksum mismatch or file missing).  
4. Receiver Daemon sends MSG\_PULL\_FILE\_REQUEST (referencing MessageID, and including its current file size and checksum if applicable) to Sender Daemon.  
5. Sender Daemon prepares the file for transfer and sends MSG\_PULL\_FILE\_RESPONSE (with SendsChunks: true, empty FileBody, Timestamp, Size, FileMode, OwnerUID, OwnerGID, and referencing MessageID) to Receiver Daemon.  
6. Sender Daemon begins sending MSG\_FILE\_CHUNK messages (each with MessageID, Path, ChunkIndex, TotalChunks, IsCompressed, and ChunkData).  
7. For each MSG\_FILE\_CHUNK received, Receiver Daemon writes the chunk data and sends MSG\_CHUNK\_ACKNOWLEDGE (referencing MessageID, Path, ChunkIndex) back to the Sender Daemon upon successful receipt and write.  
8. Upon receiving all chunks and successfully rebuilding the file, Receiver Daemon sends a final MSG\_SYNC\_ACKNOWLEDGE (referencing the original MSG\_FILE\_SYNC\_EVENT's MessageID) to Sender Daemon.

### **5.5. Inter-Server File Deletion**

1. (Sender Daemon detects change)  
2. Sender Daemon sends MSG\_FILE\_SYNC\_EVENT (Type: DELETE\_FILE, empty FileBody, and MessageID) to Receiver Daemon.  
3. Receiver Daemon deletes file.  
4. Receiver Daemon sends MSG\_SYNC\_ACKNOWLEDGE (referencing MessageID) to Sender Daemon.

### **5.6. Inter-Server File Removed Before Pull**

1. (Sender Daemon detects change or receives event from Proxy Client)  
2. Sender Daemon sends MSG\_FILE\_SYNC\_EVENT (e.g., Type: WRITE\_FILE, empty FileBody, and MessageID) to Receiver Daemon.  
3. Receiver Daemon sends MSG\_PULL\_FILE\_REQUEST (referencing MessageID).  
4. Before Sender Daemon can read the file for the response, it's deleted locally.  
5. Sender Daemon sends MSG\_FILE\_REMOVED (referencing MessageID) to Receiver Daemon.  
6. Receiver Daemon cleans up any partial state for that file.  
7. Receiver Daemon sends MSG\_SYNC\_ACKNOWLEDGE (referencing MessageID) for the FILE\_REMOVED event to Sender Daemon.

### **5.7. Inter-Server Same Content Found**

1. (Sender Daemon detects change or receives event from Proxy Client)  
2. Sender Daemon sends MSG\_FILE\_SYNC\_EVENT (with metadata, checksum, and MessageID) to Receiver Daemon.  
3. Receiver Daemon compares its local file's checksum/timestamp/size with the received metadata.  
4. If Receiver Daemon determines its file has identical content, it does not request a pull and may respond with MSG\_SYNC\_ACKNOWLEDGE directly.  
5. Alternatively, if the sender, after receiving MSG\_PULL\_FILE\_REQUEST or initially deciding not to send FileBody with MSG\_FILE\_SYNC\_EVENT, determines the receiver already has the exact content (e.g., after an initial checksum exchange), it sends MSG\_SAME\_CONTENT (referencing MessageID) instead of MSG\_PULL\_FILE\_RESPONSE or a full MSG\_FILE\_SYNC\_EVENT with body.  
6. Receiver Daemon acknowledges MSG\_SAME\_CONTENT with MSG\_SYNC\_ACKNOWLEDGE (referencing MessageID).

### **5.8. Proxy Socket Communication Flow (Go Client Package)**

This section outlines the specific flow for the Go Client Package interacting with the folder-sync daemon via the Unix domain proxy socket.

#### **5.8.1. Client Connection and Welcome**

1. Go Client Package establishes a connection to the daemon's proxy socket.  
2. Go Client Package sends MSG\_PROXY\_WELCOME\_REQUEST.  
3. Daemon responds with MSG\_PROXY\_WELCOME\_RESPONSE.  
4. If Success is false, the client should disconnect.

#### **5.8.2. Client Connection Test**

1. Go Client Package (after welcome or at any point to check daemon's responsiveness) sends MSG\_PROXY\_TEST\_CONNECTION\_REQUEST.  
2. Daemon responds with MSG\_PROXY\_TEST\_CONNECTION\_RESPONSE.  
3. Client checks Success field to verify daemon responsiveness.

#### **5.8.3. Client Initiates File System Change (Small File \- Immediate Body)**

1. Go Client Package performs a file system operation (e.g., client.WriteFile for a small file).  
2. Go Client Package constructs an MSG\_FILE\_SYNC\_EVENT with Origin: PROXY\_CLIENT\_WRITE, SendsChunks: false (and relevant file metadata/body, MessageID) and sends it to the daemon via the proxy socket.  
3. Daemon receives and queues the event for processing.  
4. Daemon sends MSG\_PROXY\_EVENT\_ACKNOWLEDGE (referencing the MessageID of the MSG\_FILE\_SYNC\_EVENT) back to the Go Client Package. This acknowledgement indicates the daemon has successfully received the event and initiated internal processing. This mirrors the behavior of Go's os package APIs, which typically return only after the operation has been initiated or completed by the underlying OS.

#### **5.8.4. Client Initiates File System Change (Large File \- Chunked Transfer)**

1. Go Client Package performs a file system operation (e.g., client.WriteFile for a large file).  
2. Go Client Package constructs an MSG\_FILE\_SYNC\_EVENT with Origin: PROXY\_CLIENT\_WRITE, SendsChunks: true (and relevant file metadata, MessageID), and sends it to the daemon via the proxy socket.  
3. Daemon receives and queues the event for processing.  
4. Daemon sends MSG\_PROXY\_EVENT\_ACKNOWLEDGE (referencing the MessageID of the MSG\_FILE\_SYNC\_EVENT) back to the Go Client Package. This acknowledgement indicates the daemon has successfully received the initial event and is ready for chunks.  
5. Go Client Package begins sending MSG\_FILE\_CHUNK messages (each with MessageID, Path, ChunkIndex, TotalChunks, IsCompressed, and ChunkData).  
6. For each MSG\_FILE\_CHUNK sent, the Go Client Package waits for a MSG\_PROXY\_EVENT\_ACKNOWLEDGE (referencing the MessageID and ChunkIndex of the MSG\_FILE\_CHUNK) from the Daemon. This confirms the daemon has successfully received and buffered the chunk.

This document serves as the definitive guide for implementing the binary network protocol within the folder-sync application and its Go Client Package.