# Event Debounce Mechanism

## Overview

The folder-sync application now includes a sophisticated event debounce mechanism to reduce unnecessary file system events and save bandwidth. This is particularly useful for handling rapid event sequences like CHMOD followed by CREATE that occur when using commands like `touch`.

## Features

### 1. Configurable Debounce Window
- Events for the same file within the debounce window are buffered and consolidated
- Default window: 250ms
- Configurable via `debounce.window_ms` in configuration

### 2. Intelligent Event Consolidation
- Detects common event patterns (e.g., CHMOD → CREATE)
- Consolidates related events into single meaningful events
- Reduces bandwidth usage and processing overhead
- Can be enabled/disabled via `debounce.consolidation_enabled`

### 3. Temporary File Detection
- Automatically detects temporary files using common patterns:
  - `.tmp-*` (atomic write temp files)
  - `*~` (backup files)
  - `.#*` (emacs temp files)
  - `#*#` (emacs auto-save files)
  - `.DS_Store` (macOS metadata)
  - `Thumbs.db` (Windows thumbnails)
- Applies extended debounce window for temporary files
- Configurable via `debounce.temp_file_debounce_ms`

### 4. Event Pattern Recognition
- Tracks sequences of events for each file path
- Recognizes patterns like CHMOD → CREATE
- Suppresses redundant events while preserving meaningful ones

## Configuration

Add the following section to your configuration file:

```yaml
debounce:
  window_ms: 250                    # Standard debounce window (ms)
  buffer_size: 100                  # Max events to buffer
  consolidation_enabled: true       # Enable intelligent consolidation
  temp_file_debounce_ms: 1000      # Extended window for temp files (ms)
```

## Benefits

1. **Reduced Bandwidth**: Fewer events sent over the network
2. **Better Performance**: Less processing overhead from redundant events
3. **Cleaner Event Stream**: More meaningful events, less noise
4. **Handles Edge Cases**: Properly handles rapid event sequences from tools like `touch`

## Implementation Details

### Event Buffering
- Events are buffered in memory for the configured debounce window
- A background goroutine periodically flushes expired events
- Events are consolidated before being sent to the main event channel

### Consolidation Rules
1. **CHMOD + CREATE Pattern**: When CHMOD is followed by CREATE for the same file, only the CREATE event is sent (with correct permissions)
2. **Multiple CHMOD Events**: Only the latest CHMOD event is sent
3. **Temporary File CHMOD**: CHMOD events for temporary files are suppressed if followed by CREATE/WRITE events

### Thread Safety
- All debounce operations are thread-safe using mutexes
- Safe concurrent access to the event buffer
- Proper cleanup on shutdown

## Testing

The debounce mechanism includes comprehensive tests:
- Basic buffer functionality
- Event expiration and flushing
- Pattern detection
- Temporary file recognition
- Event consolidation logic
- Integration with inotify watcher

Run tests with:
```bash
go test ./pkg/ingress -run "TestDebounce|TestEventPattern|TestTempFile|TestEventConsolidation" -v
```

## Backward Compatibility

The debounce mechanism is fully backward compatible:
- Default configuration provides reasonable debounce settings
- Can be disabled by setting `window_ms: 0`
- Existing configurations continue to work without modification
