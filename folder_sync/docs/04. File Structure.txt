folder-sync/
├── LICENSE               # MIT License
├── README.md             # Project documentation
├── Makefile              # Build automation
├── go.mod                # Go module definition
├── go.sum                # Go module checksums
├── main.go               # Main application entry point
├── sample-config.yaml    # Example configuration file
├── build/                # Build artifacts
│   └── folder-sync       # Compiled binary
├── docs/                 # Documentation
│   ├── 01. Specification.md
│   ├── 02. Sync Protocol.md
│   ├── 03. Implementation Steps.md
│   ├── 04. File Structure.txt
│   ├── 90. PRODUCTION-CHECKLIST.md
│   └── fixes/            # Bug fix summaries
│       ├── README.md
│       ├── INITIAL_SYNC_FIX_SUMMARY.md
│       ├── META_FILES_FIX_SUMMARY.md
│       └── SYNC_FIX_SUMMARY.md
├── scripts/              # Build and deployment scripts
│   ├── README.md
│   ├── install.sh        # Installation script
│   ├── validate-production.sh
│   ├── test_fix.sh
│   ├── test_initial_sync_fix.sh
│   ├── test_meta_files_fix.sh
│   └── test_sync_issue.sh
├── dev/                  # Development files
│   ├── README.md
│   ├── coverage.out      # Test coverage data
│   ├── coverage.html     # Coverage report
│   ├── diagnose/         # Diagnostic tools
│   │   ├── debug_handshake.sh
│   │   ├── diagnose
│   │   ├── diagnose_sync_issue.sh
│   │   └── test_handshake.go
│   └── prompts/          # Development prompts
│       ├── 01-10 Initial Requirement.txt
│       ├── 01-11  Folder Sync Specification.md
│       ├── 01-12 Updated Specification.md
│       ├── 01-13 Gemini revised specification.md
│       ├── 02-10 Commands.md
│       ├── 03-01 Implementation Commands.md
│       └── 04-01 generate file.md
└── pkg/                  # Internal packages
    ├── cli/              # Command-line interface parsing
    │   ├── cli.go
    │   └── cli_test.go
    ├── client/           # Go client library
    ├── config/           # Configuration parsing and validation
    │   ├── config.go
    │   └── config_test.go
    ├── fsutil/           # File system utilities
    ├── ingress/          # Change monitoring (inotify/socket)
    ├── interfaces/       # Common interfaces
    ├── logger/           # Structured logging
    │   ├── logger.go
    │   └── logger_test.go
    ├── meta/             # Metadata persistence and SQLite queues
    │   ├── meta.go
    │   ├── meta_test.go
    │   ├── queue.go
    │   └── queue_test.go
    ├── network/          # Network communication
    ├── sync/             # Synchronization core
    └── types/            # Core data structures and interfaces
        ├── config_types.go
        ├── config_types_test.go
        ├── enums.go
        ├── enums_test.go
        ├── events.go
        ├── events_test.go
        ├── interfaces.go
        └── interfaces_test.go