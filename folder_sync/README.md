# Folder Sync

A high-performance, production-ready folder synchronization tool written in Go that enables real-time bidirectional synchronization between multiple servers.

## 🚀 Features

- **Real-time Synchronization**: Instant file and directory synchronization using inotify
- **Multiple Sync Strategies**: One-way, bidirectional, and newest-wins conflict resolution
- **Writethrough Mode**: Buffer-based synchronization for high-throughput scenarios
- **Production Ready**: Comprehensive security hardening and monitoring
- **Efficient Protocol**: Custom binary protocol with optional compression
- **Resilient**: Automatic reconnection, queue persistence, and graceful error recovery
- **Proxy Socket Support**: Integration with external applications via Unix domain sockets
- **Go Client Library**: Native Go API for programmatic integration

## 📋 Requirements

- Linux-based operating system
- Go 1.23 or later (for building from source)
- SQLite3 support
- Systemd (for service management)

## 🔧 Installation

### Quick Install (Recommended)

```bash
# Clone the repository
git clone https://github.com/real-rm/folder_sync.git
cd folder_sync

# Build and install (all-in-one)
sudo make install-service

# Or step by step:
make build                    # Build the binary
sudo make install-service     # Install binary and systemd service

# Validate installation
sudo ./scripts/validate-production.sh
```

### Manual Build

```bash
# Build the binary
make build
# Binary will be created in build/folder-sync

# Manual installation
sudo cp build/folder-sync /usr/local/bin/
sudo chmod +x /usr/local/bin/folder-sync

# Or use the install script
chmod +x scripts/install.sh
sudo ./scripts/install.sh
```

## ⚙️ Configuration

The default configuration file is located at `/etc/folder-sync.ini`. Here's a basic example:

```yaml
folder:
  sync_path: "/path/to/sync/directory"  # Path to the folder to synchronize
  meta_path: "/var/lib/folder-sync/meta"  # Path for storing program status and SQLite queues
  logs_path: "/var/log/folder-sync"  # Path for storing log files
  ignores:
    - pattern: "\\.tmp$"
      type: regexp
    - pattern: "*.log"
      type: extension
    - pattern: "temp_dir"
      type: exact
  compares: SIZE_ONLY  # Options: SIZE_ONLY, FIRST_LAST_BLOCK_SAMPLE, FULL_FILE
  compression: NONE  # Options: NONE, ZSTD
  readonly: false  # Set to true if this server should only receive changes
  writethrough: false  # Set to true if this server acts as a buffer (requires monitor.socket_path)
  concurrent: 10  # Max concurrent file transfers (1-1024)
  block_size: 4096  # Block size for sample checksum (auto-detected from OS if not specified)
  initial_sync_timeout_minutes: 20  # Timeout for initial sync logging progress

server:
  listen_address: "0.0.0.0:8080"  # Address and port to listen on for incoming connections
  auth_token: "your-secure-auth-token-here"  # Shared secret token for authentication (minimum 32 chars)

monitor:
  socket_path: "/tmp/folder-sync.sock"  # Optional proxy socket for writethrough mode

pairs:
  - name: "remote-server-1"
    host: "*************"
    direction: ONE_WAY_TO_PEER  # Options: ONE_WAY_TO_PEER, ONE_WAY_FROM_PEER, BIDIRECTIONAL_NEWEST_WIN
    initial_sync_strategy: SYNC  # Options: SYNC, NO_INITIAL_SYNC
    remote_port: 8080
  - name: "remote-server-2"
    host: "*************"
    direction: ONE_WAY_FROM_PEER
    initial_sync_strategy: SYNC
    remote_port: 8080
  - name: "bidirectional-peer"
    host: "*************"
    direction: BIDIRECTIONAL_NEWEST_WIN
    initial_sync_strategy: SYNC
    remote_port: 8080

debug: false  # Enable debug logging
```

### Configuration Options

- **server.listen_address**: Network address to listen on
- **server.auth_token**: Authentication token (minimum 32 characters)
- **folder.sync_path**: Directory to synchronize
- **folder.meta_path**: Metadata storage directory
- **folder.logs_path**: Log files directory
- **folder.compares**: File comparison strategy (SIZE_ONLY, FIRST_LAST_BLOCK_SAMPLE, FULL_FILE)
- **folder.compression**: Compression algorithm (NONE, ZSTD)
- **folder.concurrent**: Number of concurrent transfers (1-1024)
- **folder.block_size**: Block size for sample checksum (auto-detected from OS if not specified)
- **folder.readonly**: If true, this server cannot make outbound changes
- **folder.writethrough**: If true, acts as a buffer, only receives via proxy
- **folder.initial_sync_timeout_minutes**: Timeout for initial sync logging progress
- **folder.ignores**: List of patterns to ignore during sync
- **pairs**: List of peer servers to sync with (name, host, direction, initial_sync_strategy, remote_port)
- **monitor.socket_path**: Optional proxy socket path for writethrough mode
- **debug**: Enable verbose logging

## 🚀 Usage

### Command Line Help

```
Usage: folder-sync [options]
  A Go program designed to synchronize folders between multiple servers.

Options:
  -c string
        Specify configuration file path (default "/etc/folder-sync.ini")
  -d    Daemonize the program (handled by systemd integration)
  -h    Display help message
  -t    Test configuration file for conflicting or unrecognized parameters; program exits after validation
  -v    Enable verbose/debug mode logging (overrides debug setting in config)

Examples:
  folder-sync -c /etc/my_folder_sync.yaml
  folder-sync -t
  folder-sync -v
```

### Service Management

```bash
# Start the service
sudo systemctl start folder-sync

# Check status
sudo systemctl status folder-sync

# View logs
sudo journalctl -u folder-sync -f

# Enable auto-start
sudo systemctl enable folder-sync

# Stop the service
sudo systemctl stop folder-sync

# Restart the service
sudo systemctl restart folder-sync

# Disable auto-start
sudo systemctl disable folder-sync
```

### Command Line Usage Examples

#### Basic Operations

```bash
# Show help and available options
folder-sync -h

# Test configuration file for errors
folder-sync -t

# Test custom configuration file
folder-sync -t -c /path/to/custom-config.yaml

# Run with verbose debug logging
folder-sync -v

# Run with custom config and debug mode
folder-sync -c /etc/prod-sync.yaml -v
```

#### Configuration Testing

```bash
# Validate default configuration
folder-sync -t

# Validate and show detailed output
folder-sync -t -v

# Test configuration before deployment
sudo folder-sync -t -c /etc/folder-sync.ini

# Quick syntax check
folder-sync -t && echo "Config OK" || echo "Config has errors"
```

#### Development and Debugging

```bash
# Run in foreground with debug logging
folder-sync -v

# Test with development config
folder-sync -c ./dev-config.yaml -v

# Run with custom paths for testing
folder-sync -c ./test-configs/sync-test.yaml -v
```

#### Production Deployment

```bash
# Validate production config before starting service
sudo folder-sync -t -c /etc/folder-sync.ini

# Start service after validation
sudo systemctl start folder-sync

# Monitor service startup
sudo journalctl -u folder-sync -f

# Check service health
sudo systemctl status folder-sync
```

### Common Usage Scenarios

#### Scenario 1: Two-Server Bidirectional Sync

```bash
# Server A (*************)
# /etc/folder-sync.ini
folder:
  sync_path: "/data/shared"
  meta_path: "/var/lib/folder-sync/meta"
  logs_path: "/var/log/folder-sync"
  compares: SIZE_ONLY
  compression: NONE
  concurrent: 10
  block_size: 4096

server:
  listen_address: "0.0.0.0:8080"
  auth_token: "secure-shared-token-here"

pairs:
  - name: "server-b"
    host: "*************"
    direction: BIDIRECTIONAL_NEWEST_WIN
    initial_sync_strategy: SYNC
    remote_port: 8080

# Start service
sudo systemctl start folder-sync
```

```bash
# Server B (*************)
# /etc/folder-sync.ini
folder:
  sync_path: "/data/shared"
  meta_path: "/var/lib/folder-sync/meta"
  logs_path: "/var/log/folder-sync"
  compares: SIZE_ONLY
  compression: NONE
  concurrent: 10
  block_size: 4096

server:
  listen_address: "0.0.0.0:8080"
  auth_token: "secure-shared-token-here"

pairs:
  - name: "server-a"
    host: "*************"
    direction: BIDIRECTIONAL_NEWEST_WIN
    initial_sync_strategy: SYNC
    remote_port: 8080

# Start service
sudo systemctl start folder-sync
```

#### Scenario 2: One-Way Backup to Multiple Servers

```bash
# Primary Server (pushes to backups)
pairs:
  - name: "backup1"
    host: "backup1.example.com"
    direction: ONE_WAY_TO_PEER
    initial_sync_strategy: SYNC
    remote_port: 8080
  - name: "backup2"
    host: "backup2.example.com"
    direction: ONE_WAY_TO_PEER
    initial_sync_strategy: SYNC
    remote_port: 8080
  - name: "backup3"
    host: "backup3.example.com"
    direction: ONE_WAY_TO_PEER
    initial_sync_strategy: SYNC
    remote_port: 8080

# Backup servers receive only
pairs:
  - name: "primary"
    host: "primary.example.com"
    direction: ONE_WAY_FROM_PEER
    initial_sync_strategy: SYNC
    remote_port: 8080
```

#### Scenario 3: Writethrough Mode for High-Throughput

```bash
# Application writes to buffer, syncs to multiple destinations
folder:
  writethrough: true

monitor:
  socket_path: "/tmp/app-sync.sock"

pairs:
  - name: "dest1"
    host: "dest1.example.com"
    direction: ONE_WAY_TO_PEER
    initial_sync_strategy: SYNC
    remote_port: 8080
  - name: "dest2"
    host: "dest2.example.com"
    direction: ONE_WAY_TO_PEER
    initial_sync_strategy: SYNC
    remote_port: 8080
  - name: "dest3"
    host: "dest3.example.com"
    direction: ONE_WAY_TO_PEER
    initial_sync_strategy: SYNC
    remote_port: 8080
```

### Go Client Library

```go
package main

import (
    "log"
    "github.com/real-rm/folder_sync/pkg/client"
)

func main() {
    // Initialize client with socket path and sync prefix
    client, err := client.New("/tmp/folder-sync.sock", "/app/data")
    if err != nil {
        log.Fatal("Failed to connect to folder-sync:", err)
    }
    defer client.Close()

    // Test connection
    if err := client.TestConnection(); err != nil {
        log.Fatal("Connection test failed:", err)
    }

    // Write a file (will be synced to all peers)
    data := []byte("Hello, World! This will be synced.")
    err = client.WriteFile("example.txt", data)
    if err != nil {
        log.Fatal("Failed to write file:", err)
    }

    // Create a directory structure
    err = client.MkdirAll("reports/2024/january")
    if err != nil {
        log.Fatal("Failed to create directory:", err)
    }

    // Write a file in the new directory
    report := []byte("Monthly report data...")
    err = client.WriteFile("reports/2024/january/report.txt", report)
    if err != nil {
        log.Fatal("Failed to write report:", err)
    }

    // Remove a file (deletion will be synced)
    err = client.Remove("old-file.txt")
    if err != nil {
        log.Printf("Warning: Failed to remove file: %v", err)
    }

    // Read operations work directly with filesystem
    content, err := client.ReadFile("example.txt")
    if err != nil {
        log.Fatal("Failed to read file:", err)
    }
    log.Printf("File content: %s", content)

    log.Println("All operations completed successfully!")
}
```

#### Advanced Client Usage

```go
package main

import (
    "log"
    "os"
    "path/filepath"
    "github.com/real-rm/folder_sync/pkg/client"
)

func main() {
    // Multiple sync prefixes with different sockets
    configs := []struct {
        socket string
        prefix string
    }{
        {"/tmp/app-sync.sock", "/app/data"},
        {"/tmp/logs-sync.sock", "/app/logs"},
        {"/tmp/config-sync.sock", "/app/config"},
    }

    var clients []*client.Client
    defer func() {
        // Clean up all clients
        for _, c := range clients {
            c.Close()
        }
    }()

    // Initialize multiple clients
    for _, cfg := range configs {
        c, err := client.New(cfg.socket, cfg.prefix)
        if err != nil {
            log.Printf("Failed to connect to %s: %v", cfg.socket, err)
            continue
        }
        clients = append(clients, c)
    }

    // Batch operations
    for i, c := range clients {
        // Create unique content for each sync path
        content := fmt.Sprintf("Data for sync path %d", i)
        filename := fmt.Sprintf("batch-file-%d.txt", i)

        if err := c.WriteFile(filename, []byte(content)); err != nil {
            log.Printf("Failed to write %s: %v", filename, err)
        } else {
            log.Printf("Successfully wrote %s", filename)
        }
    }
}
```

## 🔒 Security

### Production Security Features

- **Authentication**: Token-based authentication between peers
- **Path Validation**: Protection against directory traversal attacks
- **Memory Limits**: Protection against memory exhaustion attacks
- **Connection Limits**: Per-IP and global connection limiting
- **Systemd Hardening**: Comprehensive security restrictions
- **Dedicated User**: Runs as isolated `folder-sync` system user

### Security Recommendations

- Use strong, randomly generated authentication tokens
- Restrict network access with firewall rules
- Consider TLS termination with a reverse proxy
- Regularly rotate authentication tokens
- Monitor logs for security events

## 📊 Monitoring

### Health Checks

```bash
# Check memory usage
sudo journalctl -u folder-sync | grep "Memory usage"

# Monitor connections
sudo journalctl -u folder-sync | grep "connection"

# Check for errors
sudo journalctl -u folder-sync | grep -E "(ERROR|WARN)"
```

### Metrics

The service provides built-in monitoring for:
- Memory usage (alerts at 500MB or 50% of system memory)
- Connection counts and limits
- Sync operation statistics
- Queue depths and processing times

## 🛠️ Development

### Building from Source

```bash
# Install dependencies
go mod download

# Run tests with coverage
make test

# Build optimized binary
make build

# Generate coverage report
make coverage
# Report will be in dev/coverage.html

# Run all quality checks
make check
```

### Project Structure

```
├── main.go              # Application entry point
├── pkg/                 # Core application packages
│   ├── cli/             # Command-line interface
│   ├── client/          # Go client library
│   ├── config/          # Configuration management
│   ├── fsutil/          # File system utilities
│   ├── ingress/         # Change detection (inotify/socket)
│   ├── logger/          # Structured logging
│   ├── meta/            # Metadata and queue management
│   ├── network/         # Network communication
│   ├── sync/            # Core synchronization logic
│   └── types/           # Common data structures
├── docs/                # Documentation
│   ├── fixes/           # Bug fix summaries
│   └── *.md             # Technical documentation
├── scripts/             # Build and deployment scripts
│   ├── install.sh       # Installation script
│   ├── validate-production.sh  # Production validation
│   └── test_*.sh        # Test scripts
├── dev/                 # Development files
│   ├── coverage.out     # Test coverage data
│   ├── diagnose/        # Diagnostic tools
│   └── prompts/         # Development prompts
├── build/               # Build artifacts
└── sample-config.yaml   # Example configuration
```

## 📚 Documentation

- [Specification](docs/01.%20Specification.md) - Detailed technical specification
- [Sync Protocol](docs/02.%20Sync%20Protocol.md) - Network protocol documentation
- [Implementation Steps](docs/03.%20Implementation%20Steps.md) - Development guide
- [Production Checklist](docs/90.%20PRODUCTION-CHECKLIST.md) - Deployment guide
- [Bug Fixes](docs/fixes/) - Historical bug fix summaries and patches

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Write tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Service Won't Start

```bash
# Check service status and logs
sudo systemctl status folder-sync
sudo journalctl -u folder-sync --no-pager

# Validate configuration
sudo folder-sync -t -c /etc/folder-sync.ini

# Check file permissions
ls -la /etc/folder-sync.ini
ls -la /usr/local/bin/folder-sync

# Verify directories exist and are accessible
sudo -u folder-sync ls -la /path/to/sync/directory
```

#### Connection Issues

```bash
# Check if service is listening on correct port
sudo netstat -tlnp | grep :8080
sudo ss -tlnp | grep :8080

# Test connectivity between servers
telnet peer-server 8080
nc -zv peer-server 8080

# Check firewall rules
sudo ufw status
sudo iptables -L | grep 8080

# Verify token authentication
# Ensure all servers use the same token in config
```

#### Sync Not Working

```bash
# Check sync status in logs
sudo journalctl -u folder-sync | grep -E "(sync|error|warn)"

# Verify peer connections
sudo journalctl -u folder-sync | grep "connection"

# Check file permissions in sync directory
ls -la /path/to/sync/directory

# Test with a simple file
echo "test" | sudo -u folder-sync tee /path/to/sync/test.txt
```

#### High Memory Usage

```bash
# Monitor memory usage
sudo journalctl -u folder-sync | grep "Memory usage"

# Check for large files in sync
find /path/to/sync -type f -size +100M

# Review concurrent settings in config
grep concurrent /etc/folder-sync.ini

# Consider adjusting comparison strategy
# Change from "full" to "sample" or "size" in config
```

#### Performance Issues

```bash
# Check system resources
top -p $(pgrep folder-sync)
iostat -x 1 5

# Review sync queue status
sudo journalctl -u folder-sync | grep queue

# Optimize configuration
# - Reduce concurrent transfers
# - Use "sample" instead of "full" comparison
# - Enable compression for text files
# - Add more specific ignore patterns
```

### Debug Mode Usage

```bash
# Run with maximum verbosity
sudo systemctl stop folder-sync
sudo folder-sync -v -c /etc/folder-sync.ini

# Or enable debug in config file
debug: true

# Monitor debug output
sudo journalctl -u folder-sync -f | grep DEBUG
```

### Configuration Validation

```bash
# Test configuration syntax
folder-sync -t

# Test with verbose output
folder-sync -t -v

# Validate specific sections
grep -A 10 "server:" /etc/folder-sync.ini
grep -A 10 "pairs:" /etc/folder-sync.ini

# Check for common mistakes
# - Missing quotes around IP addresses with special chars
# - Incorrect indentation in YAML
# - Invalid sync direction syntax (>, <, or bidirectional)
# - Token length less than 32 characters
```

## 🆘 Support

For issues and questions:
- Check the [troubleshooting guide](docs/90.%20PRODUCTION-CHECKLIST.md#-troubleshooting)
- Review logs: `sudo journalctl -u folder-sync`
- Use debug mode: `folder-sync -v`
- Validate config: `folder-sync -t`
- Open an issue on GitHub

### Getting Help

When reporting issues, please include:

1. **System Information**:
   ```bash
   uname -a
   systemctl --version
   folder-sync -h  # to show version if available
   ```

2. **Configuration** (sanitize sensitive tokens):
   ```bash
   sudo folder-sync -t -v
   ```

3. **Service Status**:
   ```bash
   sudo systemctl status folder-sync
   ```

4. **Recent Logs**:
   ```bash
   sudo journalctl -u folder-sync --since "1 hour ago"
   ```

---

**Status**: ✅ **Production Ready** - All security vulnerabilities addressed and production hardening implemented.
