package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"runtime"
	"strconv"
	"strings"
	stdsync "sync"
	"syscall"
	"time"

	"github.com/real-rm/folder_sync/pkg/config"
	"github.com/real-rm/folder_sync/pkg/fsutil"
	"github.com/real-rm/folder_sync/pkg/ingress"
	"github.com/real-rm/folder_sync/pkg/logger"
	"github.com/real-rm/folder_sync/pkg/meta"
	"github.com/real-rm/folder_sync/pkg/network"
	"github.com/real-rm/folder_sync/pkg/sync"
	"github.com/real-rm/folder_sync/pkg/types" // Import types for config.GetPairConfig
)

// main is the entry point of the folder-sync application.
func main() {
	// --- Command-line Argument Parsing ---
	var configPath string
	var testConfig bool
	var debugMode bool
	var showHelp bool

	flag.StringVar(&configPath, "c", config.DefaultConfigPath, "Path to the configuration file (YAML)")
	flag.BoolVar(&testConfig, "t", false, "Test configuration file for conflicting parameters and exit")
	flag.BoolVar(&debugMode, "v", false, "Enable verbose (debug) logging")
	flag.BoolVar(&showHelp, "h", false, "Display help message")

	flag.Parse()

	if showHelp {
		flag.PrintDefaults()
		os.Exit(0)
	}

	// --- Configuration Loading and Validation ---
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Fatal error: Failed to load configuration from %q: %v\n", configPath, err)
		os.Exit(1)
	}

	// Override debug setting from CLI flag if provided
	if debugMode {
		cfg.Debug = true
	}

	// Initialize a temporary logger for initial setup messages
	setupLogger, err := logger.NewLogger("/tmp/logs", cfg.Debug)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize setup logger: %v\n", err)
		os.Exit(1)
	}

	// Validate configuration
	if err := cfg.Validate(); err != nil {
		setupLogger.Fatal("Configuration validation failed: %v", err)
	}

	if testConfig {
		setupLogger.Info("Configuration test successful. No conflicts or unrecognized parameters found.")
		os.Exit(0)
	}

	// --- Component Initialization ---

	// Initialize main logger for the application (redirects to logs path from config)
	appLogger, err := logger.NewLogger(cfg.Folder.LogsPath, cfg.Debug)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}
	appLogger.Info("Application starting...")
	appLogger.Debug("Configuration loaded: %+v", *cfg)

	// Initialize Meta Manager
	metaManager, err := meta.NewMetaManager(cfg.Folder.MetaPath, appLogger)
	if err != nil {
		appLogger.Fatal("Failed to initialize meta manager: %v", err)
	}
	// Ensure meta manager is closed on shutdown
	defer func() {
		if err := metaManager.Close(); err != nil {
			appLogger.Error("Failed to gracefully close meta manager: %v", err)
		}
	}()

	// Initialize File System Utility
	fsUtility := fsutil.NewFSUtil(cfg.Folder.SyncPath)

	// Create a buffered channel for FileSyncEvents from ingress to sync core
	eventQueue := make(chan types.FileSyncEvent, 1000) // Large enough buffer for burst events

	// Initialize Sync Core
	syncCore := sync.NewSyncCore(cfg, metaManager, fsUtility, eventQueue)

	// Initialize Ingress (Change Monitoring)
	// Create PatternMatcher from config for Ingress
	ignoreMatcher, err := cfg.NewPatternMatcher()
	if err != nil {
		appLogger.Fatal("Failed to create ignore pattern matcher: %v", err)
	}

	ingressComponent, err := ingress.NewIngress(cfg, ignoreMatcher, eventQueue)
	if err != nil {
		appLogger.Fatal("Failed to initialize ingress component: %v", err)
	}

	// Initialize Network Manager
	// The network manager needs to pass connections to the sync core
	// Create wrapper functions to handle interface{} conversion
	addPeerConnectionWrapper := func(conn interface{}) {
		if networkConn, ok := conn.(sync.NetworkConnection); ok {
			syncCore.AddPeerConnection(networkConn)
		} else {
			appLogger.Error("Failed to cast connection to NetworkConnection interface")
		}
	}
	networkManager, err := network.NewNetworkManager(cfg, addPeerConnectionWrapper, syncCore.RemovePeerConnection, syncCore, appLogger)
	if err != nil {
		appLogger.Fatal("Failed to initialize network manager: %v", err)
	}

	// --- Start Goroutines for Main Components ---
	var wg stdsync.WaitGroup
	done := make(chan struct{}) // Channel to signal all components to shut down

	// Start Ingress
	wg.Add(1)
	go func() {
		defer wg.Done()
		defer func() {
			if r := recover(); r != nil {
				appLogger.Error("Ingress component panicked: %v", r)
			}
		}()
		ingressComponent.Start()
	}()

	// Start Sync Core
	wg.Add(1)
	go func() {
		defer wg.Done()
		defer func() {
			if r := recover(); r != nil {
				appLogger.Error("Sync core panicked: %v", r)
			}
		}()
		syncCore.Start()
	}()

	// Wait for SyncCore to be fully initialized before starting NetworkManager
	// This prevents race conditions where connections are established before PeerHandlers are created
	syncCore.WaitForInitialization()

	// Start Network Manager
	wg.Add(1)
	go func() {
		defer wg.Done()
		defer func() {
			if r := recover(); r != nil {
				appLogger.Error("Network manager panicked: %v", r)
			}
		}()
		networkManager.Start()
	}()

	// Goroutine for memory usage monitoring
	wg.Add(1)
	go func() {
		defer wg.Done()
		defer func() {
			if r := recover(); r != nil {
				appLogger.Error("Memory monitor panicked: %v", r)
			}
		}()
		monitorMemoryUsage(appLogger, done)
	}()

	// --- Graceful Shutdown Handling ---
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	select {
	case sig := <-sigChan:
		appLogger.Info("Received signal %s. Initiating graceful shutdown...", sig)
	}

	// Trigger graceful shutdown of all components
	close(done)       // Signal memory monitor to stop
	close(eventQueue) // Close the event queue to stop ingress/sync core loops
	ingressComponent.Close()
	syncCore.Close()
	networkManager.Close()

	// Wait for all goroutines to finish
	appLogger.Info("Waiting for all application goroutines to finish...")
	wg.Wait()
	appLogger.Info("Application shut down gracefully.")
}

// getSystemMemory attempts to get the total system memory in bytes.
func getSystemMemory() uint64 {
	// Try to read from /proc/meminfo on Linux
	if data, err := os.ReadFile("/proc/meminfo"); err == nil {
		lines := strings.Split(string(data), "\n")
		for _, line := range lines {
			if strings.HasPrefix(line, "MemTotal:") {
				fields := strings.Fields(line)
				if len(fields) >= 2 {
					if kb, err := strconv.ParseUint(fields[1], 10, 64); err == nil {
						return kb * 1024 // Convert KB to bytes
					}
				}
				break
			}
		}
	}

	// Fallback: assume 4GB if we can't detect
	return 4 * 1024 * 1024 * 1024
}

// monitorMemoryUsage monitors the application's memory consumption and logs warnings.
func monitorMemoryUsage(log *logger.Logger, done <-chan struct{}) {
	ticker := time.NewTicker(30 * time.Second) // Check every 30 seconds
	defer ticker.Stop()

	const maxMemoryBytes = 500 * 1024 * 1024 // 500MB absolute limit
	const maxMemoryPercent = 0.5             // 50% of system memory

	totalSystemMemoryBytes := getSystemMemory()
	log.Info("Detected system memory: %s", formatBytes(totalSystemMemoryBytes))

	for {
		select {
		case <-ticker.C:
			var m runtime.MemStats
			runtime.ReadMemStats(&m)

			currentMemoryBytes := m.Alloc // Bytes allocated and still in use

			if currentMemoryBytes > maxMemoryBytes || float64(currentMemoryBytes)/float64(totalSystemMemoryBytes) > maxMemoryPercent {
				log.Warn("High memory usage detected: %s (%.2f%% of assumed %s total). Consider optimizing.",
					formatBytes(currentMemoryBytes),
					float64(currentMemoryBytes)/float64(totalSystemMemoryBytes)*100,
					formatBytes(totalSystemMemoryBytes))
			} else {
				log.Debug("Current memory usage: %s (Sys: %s, GC_CPU_Fraction: %.2f)",
					formatBytes(currentMemoryBytes), formatBytes(m.Sys), m.GCCPUFraction)
			}
		case <-done:
			log.Debug("Memory monitor stopping.")
			return
		}
	}
}

// formatBytes converts bytes into a human-readable format.
func formatBytes(b uint64) string {
	const unit = 1024
	if b < unit {
		return fmt.Sprintf("%d B", b)
	}
	div, exp := uint64(unit), 0
	for n := b / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(b)/float64(div), "KMGTPE"[exp])
}
