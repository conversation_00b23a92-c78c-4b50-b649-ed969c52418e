#!/bin/bash

# Test script to identify sync issues
# This script helps identify why files aren't being transferred

echo "=== Folder-Sync Issue Investigation ==="
echo

# Check if log files exist in current directory
if [ -f "192_168_0_103_45158.log" ] && [ -f "192_168_0_102_7890.log" ]; then
    echo "Found log files in current directory. Running analysis..."
    ./diagnose/diagnose_sync_issue.sh "192_168_0_103_45158.log" "192_168_0_102_7890.log"
else
    echo "Log files not found in current directory."
    echo "Please provide the path to your log files or copy them here."
    echo
    echo "Expected files:"
    echo "  - 192_168_0_103_45158.log (receiving server)"
    echo "  - 192_168_0_102_7890.log (sending server)"
    echo
    echo "Usage: $0 [path_to_log1] [path_to_log2]"
    
    if [ $# -eq 2 ]; then
        echo "Using provided log files..."
        ./diagnose/diagnose_sync_issue.sh "$1" "$2"
    fi
fi

echo
echo "=== Quick Checks ==="
echo

# Look for common patterns in the logs that indicate the issue
echo "1. Checking for MSG_SAME_CONTENT responses (files already identical):"
grep -r "MSG_SAME_CONTENT\|same content\|identical" . 2>/dev/null | head -3 || echo "  None found"

echo
echo "2. Checking for conflict resolution messages:"
grep -r "conflict\|newer\|local.*newer\|remote.*newer" . 2>/dev/null | head -3 || echo "  None found"

echo
echo "3. Checking for pull request failures:"
grep -r "pull.*request\|pull.*response\|pull.*failed" . 2>/dev/null | head -3 || echo "  None found"

echo
echo "4. Checking for writethrough mode:"
grep -r "writethrough\|ignoring.*local" . 2>/dev/null | head -3 || echo "  None found"

echo
echo "5. Checking for readonly mode:"
grep -r "readonly\|not.*queueing.*event" . 2>/dev/null | head -3 || echo "  None found"

echo
echo "=== Recommendations ==="
echo
echo "Based on the logs you provided, the most likely causes are:"
echo
echo "1. **Files are identical**: The servers are detecting that files"
echo "   already exist and are the same, so no transfer is needed."
echo
echo "2. **Conflict resolution**: Local files are newer than remote files,"
echo "   so the system is skipping the remote changes."
echo
echo "3. **Configuration issue**: One or both servers might be configured"
echo "   incorrectly (readonly mode, wrong direction, etc.)"
echo
echo "To test this:"
echo "1. Create a NEW file on one server that doesn't exist on the other"
echo "2. Check the full logs for MSG_SAME_CONTENT or conflict messages"
echo "3. Verify both servers have different configuration files"
echo "4. Check that sync directories have different content"
echo
echo "Would you like me to help you check the configuration files or"
echo "create a test to verify file transfer with new files?"
