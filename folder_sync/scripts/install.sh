#!/bin/bash

# This script automates the installation of the folder-sync application.
# It copies the compiled binary, creates a default configuration file,
# and sets up a systemd service for graceful management.

# --- Configuration Variables ---
BINARY_NAME="folder-sync"
BUILD_DIR="build"
SOURCE_BINARY_PATH="$BUILD_DIR/$BINARY_NAME"
BINARY_PATH="/usr/local/bin/$BINARY_NAME"
CONFIG_DIR="/etc"
CONFIG_FILE="$CONFIG_DIR/$BINARY_NAME.ini"
SYSTEMD_UNIT_NAME="$BINARY_NAME.service"
SYSTEMD_UNIT_PATH="/etc/systemd/system/$SYSTEMD_UNIT_NAME"
LOG_DIR_BASE="/var/log" # Base for logs, specific path derived from config
SYNC_FOLDER_BASE="/opt" # Base for sync folder, user should configure this

# --- Functions ---

# Function to display messages to the user
log_info() {
    echo -e "\e[32m[INFO]\e[0m $1"
}

# Function to display warnings to the user
log_warn() {
    echo -e "\e[33m[WARN]\e[0m $1"
}

# Function to display errors and exit
log_error() {
    echo -e "\e[31m[ERROR]\e[0m $1" >&2
    exit 1
}

# Check if the script is run as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root. Please use 'sudo bash install.sh'."
    fi
}

# Function to check for required commands
check_dependencies() {
    log_info "Checking for required system commands..."
    for cmd in systemctl cp mkdir; do
        if ! command -v "$cmd" &> /dev/null; then
            log_error "Required command '$cmd' not found. Please install it."
        fi
    done
    log_info "All required commands found."
}

# --- Main Installation Steps ---

log_info "Starting folder-sync installation script..."
check_root
check_dependencies

# 1. Copy the compiled binary
log_info "Copying '$BINARY_NAME' binary to '$BINARY_PATH'..."
if [ ! -f "$SOURCE_BINARY_PATH" ]; then
    log_error "Compiled binary '$SOURCE_BINARY_PATH' not found. Please run 'make build' first."
fi
cp "$SOURCE_BINARY_PATH" "$BINARY_PATH" || log_error "Failed to copy binary."
chmod +x "$BINARY_PATH" || log_error "Failed to set execute permissions on binary."
log_info "Binary copied and permissions set."

# 2. Generate secure auth token
log_info "Generating secure authentication token..."
SECURE_TOKEN=$(openssl rand -hex 32 2>/dev/null || head -c 64 /dev/urandom | xxd -p -c 64 | tr -d '\n' || echo "PLEASE_REPLACE_WITH_64_CHAR_RANDOM_TOKEN_$(date +%s)")

# 3. Create default configuration file
log_info "Creating default configuration file at '$CONFIG_FILE'..."
if [ -f "$CONFIG_FILE" ]; then
    log_warn "Configuration file '$CONFIG_FILE' already exists. Skipping creation of default. Please review it manually."
else
    # Check if sample config file exists
    SAMPLE_CONFIG_FILE="sample-config.yaml"
    if [ -f "$SAMPLE_CONFIG_FILE" ]; then
        log_info "Using sample configuration file '$SAMPLE_CONFIG_FILE' as template..."
        # Copy sample config and replace placeholders
        cp "$SAMPLE_CONFIG_FILE" "$CONFIG_FILE"

        # Replace placeholder paths with actual values
        sed -i.bak "s|/opt/folder-sync|$SYNC_FOLDER_BASE/folder-sync|g" "$CONFIG_FILE"
        sed -i.bak "s|/var/log/folder-sync|$LOG_DIR_BASE/folder-sync|g" "$CONFIG_FILE"
        sed -i.bak "s|CHANGE-THIS-TO-A-SECURE-TOKEN-AT-LEAST-32-CHARS|$SECURE_TOKEN|g" "$CONFIG_FILE"

        # Remove backup file
        rm -f "$CONFIG_FILE.bak"

        log_info "Default configuration file created at '$CONFIG_FILE' from sample template."
    else
        log_warn "Sample configuration file '$SAMPLE_CONFIG_FILE' not found. Creating basic configuration..."
        # Fallback to inline config generation
        cat <<EOF > "$CONFIG_FILE"
# Default configuration for folder-sync
# This file is in YAML format despite the .ini extension
folder:
  sync_path: "$SYNC_FOLDER_BASE/folder-sync" # Path to the folder to synchronize. CHANGE THIS.
  meta_path: "$SYNC_FOLDER_BASE/folder-sync/.meta" # Path for storing program status and SQLite queues.
  logs_path: "$LOG_DIR_BASE/folder-sync" # Path for storing log files.
  compares: SIZE_ONLY # Options: SIZE_ONLY, FIRST_LAST_BLOCK_SAMPLE, FULL_FILE
  compression: NONE # Options: NONE, ZSTD
  readonly: false # Set to true if this server should only receive changes
  writethrough: false # Set to true if this server acts as a buffer (requires monitor.socket_path)
  concurrent: 10 # Max concurrent file transfers (1-1024)
  block_size: 4096 # Block size for sample checksum (auto-detected from OS if not specified)
  initial_sync_timeout_minutes: 20 # Timeout for initial sync logging progress

server:
  listen_address: "0.0.0.0:8080" # Address and port to listen on for incoming connections
  auth_token: "$SECURE_TOKEN" # Automatically generated secure token (minimum 32 chars)

monitor:
  # socket_path: "/var/run/folder-sync.sock" # Uncomment and configure if using proxy socket for writethrough mode

pairs:
  # - name: "remote-peer-example"
  #   host: "*************"
  #   direction: BIDIRECTIONAL_NEWEST_WIN # ONE_WAY_TO_PEER, ONE_WAY_FROM_PEER, BIDIRECTIONAL_NEWEST_WIN
  #   initial_sync_strategy: SYNC # SYNC, NO_INITIAL_SYNC
  #   remote_port: 8080

debug: false # Enable debug logging
EOF
        log_info "Basic configuration file created at '$CONFIG_FILE'."
    fi

    log_info "Generated secure authentication token: $SECURE_TOKEN"
    log_warn "PLEASE EDIT '$CONFIG_FILE' with your actual sync_path and pair configurations."
    log_warn "The auth_token has been automatically generated and is secure for production use."
    log_warn "Consider creating '$SYNC_FOLDER_BASE/folder-sync' and other paths if they don't exist."
fi

# Create dedicated service user for security
log_info "Creating dedicated service user 'folder-sync'..."
if ! id "folder-sync" &>/dev/null; then
    useradd --system --no-create-home --shell /bin/false folder-sync || log_error "Failed to create folder-sync user."
    log_info "Created system user 'folder-sync'."
else
    log_info "User 'folder-sync' already exists."
fi

# Ensure base directories for sync, meta, and logs exist and are writable by the service user
log_info "Creating necessary directories for logs and sync folder structure..."
mkdir -p "$SYNC_FOLDER_BASE/folder-sync" || log_error "Failed to create sync folder base."
mkdir -p "$SYNC_FOLDER_BASE/folder-sync/.meta" || log_error "Failed to create meta folder."
mkdir -p "$LOG_DIR_BASE/folder-sync" || log_error "Failed to create logs folder."

# Set proper ownership and permissions
chown -R folder-sync:folder-sync "$SYNC_FOLDER_BASE/folder-sync" || log_error "Failed to set ownership of sync folder."
chown -R folder-sync:folder-sync "$LOG_DIR_BASE/folder-sync" || log_error "Failed to set ownership of logs folder."
chmod 750 "$SYNC_FOLDER_BASE/folder-sync" || log_error "Failed to set permissions on sync folder."
chmod 750 "$LOG_DIR_BASE/folder-sync" || log_error "Failed to set permissions on logs folder."
log_info "Directories created and secured."

# 4. Create systemd service unit file
log_info "Creating systemd service unit file at '$SYSTEMD_UNIT_PATH'..."
cat <<EOF > "$SYSTEMD_UNIT_PATH"
[Unit]
Description=Folder Synchronization Service
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=folder-sync
Group=folder-sync
WorkingDirectory=$SYNC_FOLDER_BASE/folder-sync
ExecStart=$BINARY_PATH -c $CONFIG_FILE
Restart=on-failure
RestartSec=5s
StandardOutput=journal
StandardError=journal
LimitNOFILE=65536
# Security hardening
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$SYNC_FOLDER_BASE/folder-sync $LOG_DIR_BASE/folder-sync
PrivateTmp=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
LockPersonality=true
MemoryDenyWriteExecute=true

[Install]
WantedBy=multi-user.target
EOF
log_info "Systemd service unit file created with security hardening."

# 5. Reload systemd, enable and start the service
log_info "Reloading systemd daemon..."
systemctl daemon-reload || log_error "Failed to reload systemd daemon."

log_info "Enabling folder-sync service to start on boot..."
systemctl enable "$SYSTEMD_UNIT_NAME" || log_error "Failed to enable folder-sync service."

log_info "Starting folder-sync service..."
systemctl start "$SYSTEMD_UNIT_NAME" || log_error "Failed to start folder-sync service. Check 'journalctl -u $SYSTEMD_UNIT_NAME' for details."

log_info "Installation complete!"
log_info "To check service status: sudo systemctl status $SYSTEMD_UNIT_NAME"
log_info "To view logs: journalctl -u $SYSTEMD_UNIT_NAME -f"
log_info "Remember to edit your configuration file: '$CONFIG_FILE'"

