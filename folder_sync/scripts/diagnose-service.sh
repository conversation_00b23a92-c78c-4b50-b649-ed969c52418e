#!/bin/bash

# Diagnostic script for folder-sync service issues
# This script helps diagnose and fix common systemd service problems

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BINARY_PATH="/usr/local/bin/folder-sync"
CONFIG_FILE="/etc/folder-sync.ini"
SERVICE_NAME="folder-sync"
SYNC_FOLDER_BASE="/opt"
LOG_DIR_BASE="/var/log"

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

check_service_status() {
    log_info "Checking service status..."
    systemctl status $SERVICE_NAME --no-pager || true
    echo
}

check_service_logs() {
    log_info "Recent service logs:"
    journalctl -u $SERVICE_NAME --no-pager -n 20 || true
    echo
}

check_directories() {
    log_info "Checking directory permissions and ownership..."
    
    local sync_dir="$SYNC_FOLDER_BASE/folder-sync"
    local log_dir="$LOG_DIR_BASE/folder-sync"
    
    echo "Sync directory: $sync_dir"
    if [[ -d "$sync_dir" ]]; then
        ls -la "$sync_dir" || true
        echo "Owner: $(stat -c %U:%G "$sync_dir")"
        echo "Permissions: $(stat -c %a "$sync_dir")"
    else
        log_error "Sync directory does not exist: $sync_dir"
    fi
    echo
    
    echo "Log directory: $log_dir"
    if [[ -d "$log_dir" ]]; then
        ls -la "$log_dir" || true
        echo "Owner: $(stat -c %U:%G "$log_dir")"
        echo "Permissions: $(stat -c %a "$log_dir")"
    else
        log_error "Log directory does not exist: $log_dir"
    fi
    echo
}

check_user() {
    log_info "Checking folder-sync user..."
    if id "folder-sync" &>/dev/null; then
        log_success "User 'folder-sync' exists"
        id folder-sync
    else
        log_error "User 'folder-sync' does not exist"
    fi
    echo
}

check_config() {
    log_info "Checking configuration file..."
    if [[ -f "$CONFIG_FILE" ]]; then
        log_success "Config file exists: $CONFIG_FILE"
        echo "Owner: $(stat -c %U:%G "$CONFIG_FILE")"
        echo "Permissions: $(stat -c %a "$CONFIG_FILE")"
        
        # Test config validation
        if "$BINARY_PATH" -t -c "$CONFIG_FILE" &>/dev/null; then
            log_success "Configuration is valid"
        else
            log_error "Configuration validation failed"
            "$BINARY_PATH" -t -c "$CONFIG_FILE" || true
        fi
    else
        log_error "Config file does not exist: $CONFIG_FILE"
    fi
    echo
}

check_binary() {
    log_info "Checking binary..."
    if [[ -f "$BINARY_PATH" ]]; then
        log_success "Binary exists: $BINARY_PATH"
        echo "Owner: $(stat -c %U:%G "$BINARY_PATH")"
        echo "Permissions: $(stat -c %a "$BINARY_PATH")"
        if [[ -x "$BINARY_PATH" ]]; then
            log_success "Binary is executable"
        else
            log_error "Binary is not executable"
        fi
    else
        log_error "Binary does not exist: $BINARY_PATH"
    fi
    echo
}

fix_permissions() {
    log_info "Attempting to fix permissions..."
    
    # Create directories if they don't exist
    mkdir -p "$SYNC_FOLDER_BASE/folder-sync"
    mkdir -p "$SYNC_FOLDER_BASE/folder-sync/.meta"
    mkdir -p "$LOG_DIR_BASE/folder-sync"
    
    # Set ownership
    chown -R folder-sync:folder-sync "$SYNC_FOLDER_BASE/folder-sync"
    chown -R folder-sync:folder-sync "$LOG_DIR_BASE/folder-sync"
    
    # Set permissions
    chmod 750 "$SYNC_FOLDER_BASE/folder-sync"
    chmod 750 "$LOG_DIR_BASE/folder-sync"
    
    log_success "Permissions fixed"
}

test_user_access() {
    log_info "Testing user access to working directory..."
    local sync_dir="$SYNC_FOLDER_BASE/folder-sync"
    
    if sudo -u folder-sync test -r "$sync_dir" && sudo -u folder-sync test -w "$sync_dir"; then
        log_success "User 'folder-sync' can access working directory"
    else
        log_error "User 'folder-sync' cannot access working directory"
        return 1
    fi
}

main() {
    echo "=== Folder Sync Service Diagnostics ==="
    echo
    
    check_root
    check_service_status
    check_service_logs
    check_user
    check_binary
    check_config
    check_directories
    
    echo "=== Attempting Fixes ==="
    fix_permissions
    
    echo "=== Post-Fix Verification ==="
    test_user_access
    
    echo
    log_info "Restarting service..."
    systemctl restart $SERVICE_NAME
    sleep 2
    
    echo "=== Final Status Check ==="
    systemctl status $SERVICE_NAME --no-pager || true
    
    echo
    log_info "If the service is still failing, check the logs with:"
    echo "sudo journalctl -u $SERVICE_NAME -f"
}

main "$@"
