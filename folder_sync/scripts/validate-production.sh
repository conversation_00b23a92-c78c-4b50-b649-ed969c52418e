#!/bin/bash

# Production validation script for folder-sync
# This script checks if the installation is secure and production-ready

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
CONFIG_FILE="/etc/folder-sync.ini"
BINARY_PATH="/usr/local/bin/folder-sync"
SERVICE_NAME="folder-sync.service"

# Counters
PASSED=0
FAILED=0
WARNINGS=0

# Helper functions
log_pass() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((PASSED++))
}

log_fail() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((FAILED++))
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
    ((WARNINGS++))
}

log_info() {
    echo -e "[INFO] $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_fail "This script must be run as root for complete validation"
        exit 1
    fi
}

# Check binary exists and permissions
check_binary() {
    log_info "Checking binary installation..."
    
    if [[ -f "$BINARY_PATH" ]]; then
        log_pass "Binary exists at $BINARY_PATH"
    else
        log_fail "Binary not found at $BINARY_PATH"
        return
    fi
    
    if [[ -x "$BINARY_PATH" ]]; then
        log_pass "Binary is executable"
    else
        log_fail "Binary is not executable"
    fi
    
    # Check binary ownership
    if [[ $(stat -c %U "$BINARY_PATH") == "root" ]]; then
        log_pass "Binary owned by root"
    else
        log_warn "Binary not owned by root"
    fi
}

# Check service user
check_service_user() {
    log_info "Checking service user..."
    
    if id "folder-sync" &>/dev/null; then
        log_pass "Service user 'folder-sync' exists"
        
        # Check if it's a system user
        if [[ $(id -u folder-sync) -lt 1000 ]]; then
            log_pass "Service user is a system user (UID < 1000)"
        else
            log_warn "Service user has regular user UID (>= 1000)"
        fi
        
        # Check shell
        if [[ $(getent passwd folder-sync | cut -d: -f7) == "/bin/false" ]]; then
            log_pass "Service user has no shell access"
        else
            log_warn "Service user has shell access"
        fi
    else
        log_fail "Service user 'folder-sync' does not exist"
    fi
}

# Check configuration security
check_config() {
    log_info "Checking configuration security..."
    
    if [[ -f "$CONFIG_FILE" ]]; then
        log_pass "Configuration file exists"
    else
        log_fail "Configuration file not found at $CONFIG_FILE"
        return
    fi
    
    # Check file permissions
    local perms=$(stat -c %a "$CONFIG_FILE")
    if [[ "$perms" == "600" ]] || [[ "$perms" == "640" ]]; then
        log_pass "Configuration file has secure permissions ($perms)"
    else
        log_warn "Configuration file permissions ($perms) may be too permissive"
    fi
    
    # Check auth token
    if grep -q "YOUR_VERY_SECRET_AUTH_TOKEN_HERE" "$CONFIG_FILE"; then
        log_fail "Configuration still contains placeholder auth token"
    else
        local token_length=$(grep "auth_token:" "$CONFIG_FILE" | sed 's/.*auth_token: *"\([^"]*\)".*/\1/' | wc -c)
        if [[ $token_length -ge 32 ]]; then
            log_pass "Auth token appears to be sufficiently long"
        else
            log_fail "Auth token is too short (< 32 characters)"
        fi
    fi
    
    # Test configuration validation
    if "$BINARY_PATH" -t -c "$CONFIG_FILE" &>/dev/null; then
        log_pass "Configuration passes validation"
    else
        log_fail "Configuration validation failed"
    fi
}

# Check systemd service
check_systemd_service() {
    log_info "Checking systemd service..."
    
    if systemctl list-unit-files | grep -q "$SERVICE_NAME"; then
        log_pass "Systemd service is installed"
    else
        log_fail "Systemd service not found"
        return
    fi
    
    if systemctl is-enabled "$SERVICE_NAME" &>/dev/null; then
        log_pass "Service is enabled"
    else
        log_warn "Service is not enabled for startup"
    fi
    
    # Check service security settings
    local service_file="/etc/systemd/system/$SERVICE_NAME"
    if [[ -f "$service_file" ]]; then
        if grep -q "User=folder-sync" "$service_file"; then
            log_pass "Service runs as dedicated user"
        else
            log_fail "Service does not run as dedicated user"
        fi
        
        if grep -q "NoNewPrivileges=true" "$service_file"; then
            log_pass "Service has NoNewPrivileges hardening"
        else
            log_warn "Service missing NoNewPrivileges hardening"
        fi
        
        if grep -q "ProtectSystem=strict" "$service_file"; then
            log_pass "Service has ProtectSystem hardening"
        else
            log_warn "Service missing ProtectSystem hardening"
        fi
    fi
}

# Check directory permissions
check_directories() {
    log_info "Checking directory permissions..."
    
    # Check sync directory
    local sync_dir=$(grep "sync_path:" "$CONFIG_FILE" | sed 's/.*sync_path: *"\?\([^"]*\)"\?.*/\1/' | tr -d '"')
    if [[ -d "$sync_dir" ]]; then
        if [[ $(stat -c %U "$sync_dir") == "folder-sync" ]]; then
            log_pass "Sync directory owned by service user"
        else
            log_warn "Sync directory not owned by service user"
        fi
    else
        log_warn "Sync directory does not exist: $sync_dir"
    fi
    
    # Check logs directory
    local logs_dir=$(grep "logs_path:" "$CONFIG_FILE" | sed 's/.*logs_path: *"\?\([^"]*\)"\?.*/\1/' | tr -d '"')
    if [[ -d "$logs_dir" ]]; then
        if [[ $(stat -c %U "$logs_dir") == "folder-sync" ]]; then
            log_pass "Logs directory owned by service user"
        else
            log_warn "Logs directory not owned by service user"
        fi
    else
        log_warn "Logs directory does not exist: $logs_dir"
    fi
}

# Main validation
main() {
    echo "=== Folder Sync Production Validation ==="
    echo
    
    check_root
    check_binary
    check_service_user
    check_config
    check_systemd_service
    check_directories
    
    echo
    echo "=== Validation Summary ==="
    echo -e "${GREEN}Passed: $PASSED${NC}"
    echo -e "${YELLOW}Warnings: $WARNINGS${NC}"
    echo -e "${RED}Failed: $FAILED${NC}"
    
    if [[ $FAILED -eq 0 ]]; then
        echo -e "\n${GREEN}✓ System appears ready for production deployment${NC}"
        if [[ $WARNINGS -gt 0 ]]; then
            echo -e "${YELLOW}⚠ Consider addressing the warnings above${NC}"
        fi
        exit 0
    else
        echo -e "\n${RED}✗ System is NOT ready for production deployment${NC}"
        echo "Please address the failed checks above before deploying."
        exit 1
    fi
}

main "$@"
