# Scripts Directory

This directory contains build, deployment, and testing scripts for the folder-sync project.

## Files

### Installation & Deployment
- **install.sh** - Main installation script for setting up folder-sync as a systemd service
- **validate-production.sh** - Production readiness validation script

### Testing Scripts
- **test_fix.sh** - General bug fix testing
- **test_initial_sync_fix.sh** - Tests for initial synchronization fixes
- **test_meta_files_fix.sh** - Tests for metadata file handling fixes
- **test_sync_issue.sh** - Tests for synchronization issue fixes

## Usage

### Installation
```bash
# Make scripts executable
chmod +x scripts/*.sh

# Install folder-sync system-wide
sudo scripts/install.sh

# Validate production setup
sudo scripts/validate-production.sh
```

### Testing
```bash
# Run specific test suites
./scripts/test_fix.sh
./scripts/test_initial_sync_fix.sh
./scripts/test_meta_files_fix.sh
./scripts/test_sync_issue.sh
```

### Via Makefile
Most scripts can also be run through the Makefile:
```bash
# Install service
sudo make install-service

# Validate production
make validate
```

## Requirements

- Bash shell
- Root privileges (for installation scripts)
- Go toolchain (for test scripts)
- systemd (for service management)

## Notes

- All installation scripts require root privileges
- Test scripts should be run from the project root directory
- Scripts are designed to be idempotent where possible
