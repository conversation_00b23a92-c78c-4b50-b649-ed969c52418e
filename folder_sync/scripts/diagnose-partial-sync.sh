#!/bin/bash

# Diagnostic script for partial sync issues
# This script helps diagnose why only some files/directories are syncing

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Function to check if running in Docker environment
is_docker_env() {
    if command -v docker-compose >/dev/null 2>&1 && [ -f "docker/docker-compose.yml" ]; then
        return 0
    else
        return 1
    fi
}

# Function to run command in Docker or locally
run_cmd() {
    local server=$1
    local cmd=$2
    
    if is_docker_env; then
        docker-compose -f docker/docker-compose.yml exec $server sh -c "$cmd"
    else
        # For local/systemd setup, you'll need to modify this
        log_error "Local/systemd diagnostics not implemented yet. Please run in Docker environment."
        return 1
    fi
}

# Function to get file listing with details
get_detailed_listing() {
    local server=$1
    local path=$2
    
    log_info "Getting detailed listing for $server:$path"
    run_cmd $server "find $path -type f -exec ls -la {} \; 2>/dev/null | sort" || echo "No files found or permission denied"
    echo
    run_cmd $server "find $path -type d -exec ls -ld {} \; 2>/dev/null | sort" || echo "No directories found or permission denied"
    echo
}

# Function to check ignore patterns
check_ignore_patterns() {
    local server=$1
    
    log_info "Checking ignore patterns for $server"
    run_cmd $server "grep -i ignore /app/config.ini || echo 'No ignore patterns configured'"
    echo
}

# Function to check sync status
check_sync_status() {
    local server=$1
    local peer=$2
    
    log_info "Checking sync status: $server -> $peer"
    run_cmd $server "cat /app/meta/${peer}_status.json 2>/dev/null || echo 'Status file not found'"
    echo
}

# Function to analyze missing files
analyze_missing_files() {
    log_info "Analyzing missing files between ca2 and ca3"
    
    # Get file lists
    echo "Getting file lists..."
    run_cmd ca2 "find /app/sync -type f | sort" > /tmp/ca2_files.txt 2>/dev/null || touch /tmp/ca2_files.txt
    run_cmd ca3 "find /app/sync -type f | sort" > /tmp/ca3_files.txt 2>/dev/null || touch /tmp/ca3_files.txt
    
    # Find files only on ca3
    echo "Files only on ca3 (missing from ca2):"
    comm -23 /tmp/ca3_files.txt /tmp/ca2_files.txt | head -20
    echo
    
    # Find files only on ca2
    echo "Files only on ca2 (missing from ca3):"
    comm -13 /tmp/ca3_files.txt /tmp/ca2_files.txt | head -20
    echo
    
    # Get directory lists
    echo "Getting directory lists..."
    run_cmd ca2 "find /app/sync -type d | sort" > /tmp/ca2_dirs.txt 2>/dev/null || touch /tmp/ca2_dirs.txt
    run_cmd ca3 "find /app/sync -type d | sort" > /tmp/ca3_dirs.txt 2>/dev/null || touch /tmp/ca3_dirs.txt
    
    # Find directories only on ca3
    echo "Directories only on ca3 (missing from ca2):"
    comm -23 /tmp/ca3_dirs.txt /tmp/ca2_dirs.txt | head -20
    echo
    
    # Find directories only on ca2
    echo "Directories only on ca2 (missing from ca3):"
    comm -13 /tmp/ca3_dirs.txt /tmp/ca2_dirs.txt | head -20
    echo
    
    # Cleanup
    rm -f /tmp/ca2_files.txt /tmp/ca3_files.txt /tmp/ca2_dirs.txt /tmp/ca3_dirs.txt
}

# Function to check recent logs for sync events
check_sync_logs() {
    local server=$1
    
    log_info "Checking recent sync logs for $server"
    
    echo "--- Error messages ---"
    if is_docker_env; then
        docker-compose -f docker/docker-compose.yml logs $server | grep -i error | tail -10
    fi
    echo
    
    echo "--- Sync events ---"
    if is_docker_env; then
        docker-compose -f docker/docker-compose.yml logs $server | grep -E "(sync|file|event|acknowledge)" | tail -15
    fi
    echo
    
    echo "--- Ignore/filter messages ---"
    if is_docker_env; then
        docker-compose -f docker/docker-compose.yml logs $server | grep -i "ignore\|filter\|skip" | tail -10
    fi
    echo
}

# Function to test ignore pattern matching
test_ignore_patterns() {
    log_info "Testing ignore pattern matching"
    
    # Test some common files that might be ignored
    local test_files=(
        "README.md"
        "package.json"
        "node_modules/test.js"
        "keys/test.key"
        "logs/test.log"
        ".git/config"
        ".gitignore"
    )
    
    for file in "${test_files[@]}"; do
        echo "Testing pattern match for: $file"
        # This would need to be implemented based on the actual ignore pattern logic
        # For now, just show what files exist
        run_cmd ca3 "test -f /app/sync/$file && echo 'EXISTS on ca3' || echo 'NOT FOUND on ca3'"
        run_cmd ca2 "test -f /app/sync/$file && echo 'EXISTS on ca2' || echo 'NOT FOUND on ca2'"
        echo
    done
}

# Main diagnostic function
main() {
    echo "=== Folder Sync Partial Sync Diagnostic ==="
    echo "This script will help diagnose why only some files are syncing"
    echo
    
    if ! is_docker_env; then
        log_error "This script currently only supports Docker environments"
        log_info "Please run from the project root with docker/docker-compose.yml available"
        exit 1
    fi
    
    # Check if services are running
    log_info "Checking if services are running..."
    if ! docker-compose -f docker/docker-compose.yml ps | grep -q "Up"; then
        log_warn "Services don't appear to be running. Starting them..."
        docker-compose -f docker/docker-compose.yml up -d
        sleep 10
    fi
    
    echo "=== 1. Configuration Check ==="
    check_ignore_patterns ca2
    check_ignore_patterns ca3
    
    echo "=== 2. Sync Status Check ==="
    check_sync_status ca2 ca3
    check_sync_status ca3 ca2
    
    echo "=== 3. File Listings ==="
    echo "--- CA2 Files ---"
    get_detailed_listing ca2 /app/sync
    echo "--- CA3 Files ---"
    get_detailed_listing ca3 /app/sync
    
    echo "=== 4. Missing Files Analysis ==="
    analyze_missing_files
    
    echo "=== 5. Recent Logs Analysis ==="
    echo "--- CA2 Logs ---"
    check_sync_logs ca2
    echo "--- CA3 Logs ---"
    check_sync_logs ca3
    
    echo "=== 6. Ignore Pattern Testing ==="
    test_ignore_patterns
    
    echo "=== Diagnostic Complete ==="
    echo
    log_info "If files are missing, check:"
    log_info "1. Ignore patterns in configuration"
    log_info "2. Initial sync completion status"
    log_info "3. Error messages in logs"
    log_info "4. File permissions and accessibility"
    echo
    log_info "To force a resync, you can:"
    log_info "1. Stop services: docker-compose -f docker/docker-compose.yml down"
    log_info "2. Clear metadata: docker volume rm docker_ca2-meta docker_ca3-meta"
    log_info "3. Restart services: docker-compose -f docker/docker-compose.yml up -d"
}

# Run main function
main "$@"
