#!/bin/bash

# Test script for folder-sync Docker setup

set -e

echo "=== Folder Sync Docker Test ==="
echo

# Function to wait for services to be ready
wait_for_service() {
    local service=$1
    local max_attempts=30
    local attempt=1
    
    echo "Waiting for $service to be ready..."
    while [ $attempt -le $max_attempts ]; do
        if docker-compose logs $service 2>/dev/null | grep -q "Server listening on"; then
            echo "$service is ready!"
            return 0
        fi
        echo "Attempt $attempt/$max_attempts: $service not ready yet..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "ERROR: $service failed to start within timeout"
    return 1
}

# Function to show logs
show_logs() {
    echo "=== CA2 Logs ==="
    docker-compose logs ca2 | tail -20
    echo
    echo "=== CA3 Logs ==="
    docker-compose logs ca3 | tail -20
    echo
}

# Function to test file sync
test_file_sync() {
    local direction=$1
    local test_file="test-${direction}-$(date +%s).txt"
    local content="Test content from $direction at $(date)"
    
    echo "Testing sync: $direction"
    
    if [ "$direction" = "ca2-to-ca3" ]; then
        # Create file on ca2
        docker-compose exec ca2 sh -c "echo '$content' > /app/sync/$test_file"
        echo "Created file $test_file on ca2"
        
        # Wait and check if it appears on ca3
        sleep 3
        if docker-compose exec ca3 sh -c "test -f /app/sync/$test_file"; then
            echo "✅ File $test_file successfully synced from ca2 to ca3"
            ca3_content=$(docker-compose exec ca3 sh -c "cat /app/sync/$test_file")
            echo "Content on ca3: $ca3_content"
        else
            echo "❌ File $test_file NOT found on ca3"
            return 1
        fi
    else
        # Create file on ca3
        docker-compose exec ca3 sh -c "echo '$content' > /app/sync/$test_file"
        echo "Created file $test_file on ca3"
        
        # Wait and check if it appears on ca2
        sleep 3
        if docker-compose exec ca2 sh -c "test -f /app/sync/$test_file"; then
            echo "✅ File $test_file successfully synced from ca3 to ca2"
            ca2_content=$(docker-compose exec ca2 sh -c "cat /app/sync/$test_file")
            echo "Content on ca2: $ca2_content"
        else
            echo "❌ File $test_file NOT found on ca2"
            return 1
        fi
    fi
}

# Function to check status files
check_status() {
    echo "=== Status Files ==="
    echo "CA2 status for CA3:"
    docker-compose exec ca2 sh -c "cat /app/meta/ca3_status.json 2>/dev/null || echo 'Status file not found'"
    echo
    echo "CA3 status for CA2:"
    docker-compose exec ca3 sh -c "cat /app/meta/ca2_status.json 2>/dev/null || echo 'Status file not found'"
    echo
}

# Function to list files
list_files() {
    echo "=== File Listings ==="
    echo "Files on CA2:"
    docker-compose exec ca2 sh -c "ls -la /app/sync/ 2>/dev/null || echo 'No files'"
    echo
    echo "Files on CA3:"
    docker-compose exec ca3 sh -c "ls -la /app/sync/ 2>/dev/null || echo 'No files'"
    echo
}

# Main execution
case "${1:-start}" in
    "start")
        echo "Starting folder-sync services..."
        docker-compose up -d --build
        
        # Wait for services to be ready
        wait_for_service ca2
        wait_for_service ca3
        
        echo "Services are ready!"
        echo
        
        # Show initial logs
        show_logs
        
        # Check initial status
        check_status
        ;;
        
    "test")
        echo "Running sync tests..."
        
        # Test ca2 -> ca3
        test_file_sync "ca2-to-ca3"
        echo
        
        # Test ca3 -> ca2  
        test_file_sync "ca3-to-ca2"
        echo
        
        # Show final status
        check_status
        list_files
        ;;
        
    "logs")
        show_logs
        ;;
        
    "status")
        check_status
        list_files
        ;;

    "diagnose")
        echo "=== Diagnostic Information ==="
        echo

        # Check sync status
        echo "--- Sync Status ---"
        check_status
        echo

        # Detailed file listings
        echo "--- Detailed File Listings ---"
        echo "CA2 sync directory:"
        docker-compose exec ca2 sh -c "find /app/sync -type f -exec ls -la {} \; 2>/dev/null | head -20"
        echo
        echo "CA3 sync directory:"
        docker-compose exec ca3 sh -c "find /app/sync -type f -exec ls -la {} \; 2>/dev/null | head -20"
        echo

        # Check ignore patterns
        echo "--- Configuration Check ---"
        echo "CA2 ignore patterns:"
        docker-compose exec ca2 sh -c "grep -i ignore /app/config.ini || echo 'No ignore patterns found'"
        echo
        echo "CA3 ignore patterns:"
        docker-compose exec ca3 sh -c "grep -i ignore /app/config.ini || echo 'No ignore patterns found'"
        echo

        # Check recent logs for errors
        echo "--- Recent Error Logs ---"
        echo "CA2 errors:"
        docker-compose logs ca2 | grep -i error | tail -10
        echo
        echo "CA3 errors:"
        docker-compose logs ca3 | grep -i error | tail -10
        echo

        # Check sync events
        echo "--- Recent Sync Events ---"
        echo "CA2 sync events:"
        docker-compose logs ca2 | grep -i "sync\|file\|event" | tail -15
        echo
        echo "CA3 sync events:"
        docker-compose logs ca3 | grep -i "sync\|file\|event" | tail -15
        ;;

    "stop")
        echo "Stopping services..."
        docker-compose down
        ;;

    "clean")
        echo "Cleaning up..."
        docker-compose down -v
        docker system prune -f
        ;;

    *)
        echo "Usage: $0 {start|test|logs|status|stop|clean|diagnose}"
        echo
        echo "Commands:"
        echo "  start    - Start the services and wait for readiness"
        echo "  test     - Run file sync tests"
        echo "  logs     - Show recent logs from both services"
        echo "  status   - Show status files and file listings"
        echo "  diagnose - Run comprehensive diagnostics"
        echo "  stop     - Stop the services"
        echo "  clean    - Stop services and remove volumes"
        ;;
esac
