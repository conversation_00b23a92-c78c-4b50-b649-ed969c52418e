version: '3.8'

services:
  ca2:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: folder-sync-ca2
    hostname: ca2
    networks:
      - sync-network
    ports:
      - "7890:7890"
    volumes:
      - ./ca2-config.ini:/app/config.ini:ro
      - ca2-sync:/app/sync
      - ca2-meta:/app/meta
      - ca2-logs:/app/logs
    environment:
      - DEBUG=true
    command: ["folder-sync", "-c", "/app/config.ini"]
    restart: unless-stopped

  ca3:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: folder-sync-ca3
    hostname: ca3
    networks:
      - sync-network
    ports:
      - "7891:7890"
    volumes:
      - ./ca3-config.ini:/app/config.ini:ro
      - ca3-sync:/app/sync
      - ca3-meta:/app/meta
      - ca3-logs:/app/logs
    environment:
      - DEBUG=true
    command: ["folder-sync", "-c", "/app/config.ini"]
    restart: unless-stopped
    depends_on:
      - ca2

networks:
  sync-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  ca2-sync:
  ca2-meta:
  ca2-logs:
  ca3-sync:
  ca3-meta:
  ca3-logs:
