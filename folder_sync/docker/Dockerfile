# Multi-stage build for folder-sync
FROM golang:1.21-alpine AS builder

# Install build dependencies
RUN apk add --no-cache gcc musl-dev sqlite-dev

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o folder-sync .

# Final stage
FROM alpine:latest

# Install runtime dependencies
RUN apk add --no-cache sqlite ca-certificates

# Create app user
RUN addgroup -g 1000 appuser && \
    adduser -D -s /bin/sh -u 1000 -G appuser appuser

# Create directories
RUN mkdir -p /app/sync /app/meta /app/logs && \
    chown -R appuser:appuser /app

# Copy binary from builder
COPY --from=builder /app/folder-sync /usr/local/bin/folder-sync
RUN chmod +x /usr/local/bin/folder-sync

# Switch to app user
USER appuser

# Set working directory
WORKDIR /app

# Expose port
EXPOSE 7890

# Default command
CMD ["folder-sync", "-c", "/app/config.ini"]
