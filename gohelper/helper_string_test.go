package gohelper

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsEmail(t *testing.T) {
	tests := []struct {
		name     string
		email    string
		expected bool
	}{
		{
			name:     "Valid email with letters",
			email:    "<EMAIL>",
			expected: true,
		},
		{
			name:     "Valid email with numbers",
			email:    "<EMAIL>",
			expected: true,
		},
		{
			name:     "Valid email with dots",
			email:    "<EMAIL>",
			expected: true,
		},
		{
			name:     "Valid email with hyphens",
			email:    "<EMAIL>",
			expected: true,
		},
		{
			name:     "Valid email with plus sign",
			email:    "<EMAIL>",
			expected: true,
		},
		{
			name:     "Invalid email - missing @",
			email:    "testexample.com",
			expected: false,
		},
		{
			name:     "Invalid email - missing domain",
			email:    "test@",
			expected: false,
		},
		{
			name:     "Invalid email - missing local part",
			email:    "@example.com",
			expected: false,
		},
		{
			name:     "Invalid email - empty string",
			email:    "",
			expected: false,
		},
		{
			name:     "Invalid email - special characters",
			email:    "test!@example.com",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsEmail(tt.email)
			assert.Equal(t, tt.expected, result)
		})
	}
}
