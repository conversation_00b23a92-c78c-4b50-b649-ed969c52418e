<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gohelper

```go
import "github.com/real-rm/gohelper"
```

Package gohelper provides utility functions and helpers for the application.

## Index

- [Constants](<#constants>)
- [func CallOnce\(fn func\(\)\) func\(\)](<#CallOnce>)
- [func CleanupTestEnv\(\)](<#CleanupTestEnv>)
- [func Contains\[T comparable\]\(slice \[\]T, item T\) bool](<#Contains>)
- [func DateToTime\(dateInt int\) time.Time](<#DateToTime>)
- [func GenUUID\(length int\) \(string, error\)](<#GenUUID>)
- [func IsEmail\(eml string\) bool](<#IsEmail>)
- [func SetRmbaseFileCfg\(configPath string\)](<#SetRmbaseFileCfg>)
- [func SetupTestEnv\(opts TestOptions\) error](<#SetupTestEnv>)
- [func StrToTime\(s string\) \(time.Time, error\)](<#StrToTime>)
- [func TestUtilMain\(m \*testing.M, opts ...TestOptions\)](<#TestUtilMain>)
- [func TimeToDateInt\(timestamp time.Time\) int](<#TimeToDateInt>)
- [func TimeToStr\(t time.Time\) string](<#TimeToStr>)
- [type Interval](<#Interval>)
  - [func StartInterval\(intervalSec float64, task func\(\)\) \*Interval](<#StartInterval>)
  - [func \(i \*Interval\) Stop\(\)](<#Interval.Stop>)
- [type TestOptions](<#TestOptions>)
  - [func DefaultTestOptions\(\) TestOptions](<#DefaultTestOptions>)


## Constants

<a name="TimeFormat"></a>

```go
const TimeFormat = "2006-01-02 15:04:05"
```

<a name="CallOnce"></a>
## func [CallOnce](<https://github.com/real-rm/gohelper/blob/main/helper_function.go#L6>)

```go
func CallOnce(fn func()) func()
```

CallOnce only call the function once

<a name="CleanupTestEnv"></a>
## func [CleanupTestEnv](<https://github.com/real-rm/gohelper/blob/main/test_util.go#L124>)

```go
func CleanupTestEnv()
```

CleanupTestEnv cleans up the test environment

<a name="Contains"></a>
## func [Contains](<https://github.com/real-rm/gohelper/blob/main/helpers_array.go#L4>)

```go
func Contains[T comparable](slice []T, item T) bool
```

Contains checks if an item is in a slice

<a name="DateToTime"></a>
## func [DateToTime](<https://github.com/real-rm/gohelper/blob/main/helper_date.go#L24>)

```go
func DateToTime(dateInt int) time.Time
```

DateToTime converts a date integer in the format YYYYMMDD to a time.Time object. For example, 20150101 becomes January 1, 2015 00:00:00. Uses local timezone to match the local filesystem operations.

<a name="GenUUID"></a>
## func [GenUUID](<https://github.com/real-rm/gohelper/blob/main/helper_uuid.go#L12>)

```go
func GenUUID(length int) (string, error)
```

GenUUID generates a random UUID\-like string of specified length If length is less than or equal to 0, returns an empty string

<a name="IsEmail"></a>
## func [IsEmail](<https://github.com/real-rm/gohelper/blob/main/helper_string.go#L9>)

```go
func IsEmail(eml string) bool
```



<a name="SetRmbaseFileCfg"></a>
## func [SetRmbaseFileCfg](<https://github.com/real-rm/gohelper/blob/main/test_util.go#L141>)

```go
func SetRmbaseFileCfg(configPath string)
```

SetRmbaseFileCfg sets the RMBASE\_FILE\_CFG environment variable to the given config path

<a name="SetupTestEnv"></a>
## func [SetupTestEnv](<https://github.com/real-rm/gohelper/blob/main/test_util.go#L34>)

```go
func SetupTestEnv(opts TestOptions) error
```

SetupTestEnv initializes the test environment including config and logging

<a name="StrToTime"></a>
## func [StrToTime](<https://github.com/real-rm/gohelper/blob/main/helper_date.go#L17>)

```go
func StrToTime(s string) (time.Time, error)
```

StrToTime converts a string to a time.Time object. Uses local timezone to match the local filesystem operations.

<a name="TestUtilMain"></a>
## func [TestUtilMain](<https://github.com/real-rm/gohelper/blob/main/test_util.go#L171>)

```go
func TestUtilMain(m *testing.M, opts ...TestOptions)
```

TestUtilMain is a common test main function that can be used across different test files

<a name="TimeToDateInt"></a>
## func [TimeToDateInt](<https://github.com/real-rm/gohelper/blob/main/helper_date.go#L40>)

```go
func TimeToDateInt(timestamp time.Time) int
```

TimeToDateInt converts a time.Time object to a date integer in the format YYYYMMDD. For example, January 1, 2015 becomes 20150101.

<a name="TimeToStr"></a>
## func [TimeToStr](<https://github.com/real-rm/gohelper/blob/main/helper_date.go#L11>)

```go
func TimeToStr(t time.Time) string
```

TimeToStr converts a time.Time object to a string. Uses local timezone to match the local filesystem operations.

<a name="Interval"></a>
## type [Interval](<https://github.com/real-rm/gohelper/blob/main/helper_interval.go#L15-L18>)

define Interval struct

```go
type Interval struct {
    // contains filtered or unexported fields
}
```

<a name="StartInterval"></a>
### func [StartInterval](<https://github.com/real-rm/gohelper/blob/main/helper_interval.go#L21>)

```go
func StartInterval(intervalSec float64, task func()) *Interval
```

StartInterval \- start a timer, execute \`task\` function every \`intervalSec\` seconds \(supports fractional seconds\)

<a name="Interval.Stop"></a>
### func \(\*Interval\) [Stop](<https://github.com/real-rm/gohelper/blob/main/helper_interval.go#L43>)

```go
func (i *Interval) Stop()
```

Stop \- stop the timer

<a name="TestOptions"></a>
## type [TestOptions](<https://github.com/real-rm/gohelper/blob/main/test_util.go#L19-L24>)

TestOptions defines options for test environment setup

```go
type TestOptions struct {
    // UseEnvConfig indicates whether to use existing environment config file
    // If true, will use RMBASE_FILE_CFG environment variable
    // If false, will create temporary config file
    UseEnvConfig bool
}
```

<a name="DefaultTestOptions"></a>
### func [DefaultTestOptions](<https://github.com/real-rm/gohelper/blob/main/test_util.go#L27>)

```go
func DefaultTestOptions() TestOptions
```

DefaultTestOptions returns default test options

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
