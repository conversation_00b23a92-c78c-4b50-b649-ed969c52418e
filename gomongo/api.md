<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gomongo

```go
import "github.com/real-rm/gomongo"
```

## Index

- [func InitMongoDB\(\) error](<#InitMongoDB>)
- [func ListCollections\(dbName string\) \(\[\]string, error\)](<#ListCollections>)
- [func ToBSONDoc\(v interface\{\}\) \(bson.M, error\)](<#ToBSONDoc>)
- [type MongoCollection](<#MongoCollection>)
  - [func Coll\(dbName, collName string\) \*MongoCollection](<#Coll>)
  - [func PreDefColl\(collName string\) \*MongoCollection](<#PreDefColl>)
  - [func \(mc \*MongoCollection\) Aggregate\(ctx context.Context, pipeline interface\{\}, opts ...\*options.AggregateOptions\) \(\*mongo.Cursor, error\)](<#MongoCollection.Aggregate>)
  - [func \(mc \*MongoCollection\) BulkWrite\(ctx context.Context, models \[\]mongo.WriteModel, opts ...\*options.BulkWriteOptions\) \(\*mongo.BulkWriteResult, error\)](<#MongoCollection.BulkWrite>)
  - [func \(mc \*MongoCollection\) CountDocuments\(ctx context.Context, filter interface\{\}, opts ...\*options.CountOptions\) \(int64, error\)](<#MongoCollection.CountDocuments>)
  - [func \(mc \*MongoCollection\) CreateIndex\(ctx context.Context, model mongo.IndexModel, opts ...\*options.CreateIndexesOptions\) \(string, error\)](<#MongoCollection.CreateIndex>)
  - [func \(mc \*MongoCollection\) CreateIndexes\(ctx context.Context, models \[\]mongo.IndexModel, opts ...\*options.CreateIndexesOptions\) \(\[\]string, error\)](<#MongoCollection.CreateIndexes>)
  - [func \(mc \*MongoCollection\) DBName\(\) string](<#MongoCollection.DBName>)
  - [func \(mc \*MongoCollection\) DeleteMany\(ctx context.Context, filter interface\{\}, opts ...\*options.DeleteOptions\) \(\*mongo.DeleteResult, error\)](<#MongoCollection.DeleteMany>)
  - [func \(mc \*MongoCollection\) DeleteOne\(ctx context.Context, filter interface\{\}, opts ...\*options.DeleteOptions\) \(\*mongo.DeleteResult, error\)](<#MongoCollection.DeleteOne>)
  - [func \(mc \*MongoCollection\) Distinct\(ctx context.Context, fieldName string, filter interface\{\}, opts ...\*options.DistinctOptions\) \(\[\]interface\{\}, error\)](<#MongoCollection.Distinct>)
  - [func \(mc \*MongoCollection\) Drop\(ctx context.Context\) error](<#MongoCollection.Drop>)
  - [func \(mc \*MongoCollection\) DropIndex\(ctx context.Context, name string, opts ...\*options.DropIndexesOptions\) error](<#MongoCollection.DropIndex>)
  - [func \(mc \*MongoCollection\) EstimatedDocumentCount\(ctx context.Context, opts ...\*options.EstimatedDocumentCountOptions\) \(int64, error\)](<#MongoCollection.EstimatedDocumentCount>)
  - [func \(mc \*MongoCollection\) Find\(ctx context.Context, filter interface\{\}, opts ...interface\{\}\) \(\*mongo.Cursor, error\)](<#MongoCollection.Find>)
  - [func \(mc \*MongoCollection\) FindOne\(ctx context.Context, filter interface\{\}, opts ...interface\{\}\) \*mongo.SingleResult](<#MongoCollection.FindOne>)
  - [func \(mc \*MongoCollection\) FindOneAndReplace\(ctx context.Context, filter interface\{\}, replacement interface\{\}, opts ...\*options.FindOneAndReplaceOptions\) \*mongo.SingleResult](<#MongoCollection.FindOneAndReplace>)
  - [func \(mc \*MongoCollection\) FindOneAndUpdate\(ctx context.Context, filter interface\{\}, update interface\{\}, opts ...\*options.FindOneAndUpdateOptions\) \*mongo.SingleResult](<#MongoCollection.FindOneAndUpdate>)
  - [func \(mc \*MongoCollection\) FindToArray\(ctx context.Context, filter interface\{\}, opts ...interface\{\}\) \(\[\]bson.M, error\)](<#MongoCollection.FindToArray>)
  - [func \(mc \*MongoCollection\) InsertMany\(ctx context.Context, documents \[\]interface\{\}, opts ...\*options.InsertManyOptions\) \(\*mongo.InsertManyResult, error\)](<#MongoCollection.InsertMany>)
  - [func \(mc \*MongoCollection\) InsertOne\(ctx context.Context, document interface\{\}, opts ...\*options.InsertOneOptions\) \(\*mongo.InsertOneResult, error\)](<#MongoCollection.InsertOne>)
  - [func \(mc \*MongoCollection\) Name\(\) string](<#MongoCollection.Name>)
  - [func \(mc \*MongoCollection\) Rename\(ctx context.Context, newName string\) error](<#MongoCollection.Rename>)
  - [func \(mc \*MongoCollection\) ReplaceOne\(ctx context.Context, filter interface\{\}, replacement interface\{\}, opts ...\*options.ReplaceOptions\) \(\*mongo.UpdateResult, error\)](<#MongoCollection.ReplaceOne>)
  - [func \(mc \*MongoCollection\) UpdateMany\(ctx context.Context, filter interface\{\}, update interface\{\}, opts ...\*options.UpdateOptions\) \(\*mongo.UpdateResult, error\)](<#MongoCollection.UpdateMany>)
  - [func \(mc \*MongoCollection\) UpdateOne\(ctx context.Context, filter interface\{\}, update interface\{\}, opts ...\*options.UpdateOptions\) \(\*mongo.UpdateResult, error\)](<#MongoCollection.UpdateOne>)
  - [func \(mc \*MongoCollection\) Watch\(ctx context.Context, pipeline interface\{\}, opts ...\*options.ChangeStreamOptions\) \(\*mongo.ChangeStream, error\)](<#MongoCollection.Watch>)
- [type QueryOptions](<#QueryOptions>)


<a name="InitMongoDB"></a>
## func [InitMongoDB](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L65>)

```go
func InitMongoDB() error
```

InitMongoDB initializes MongoDB connection using configuration

<a name="ListCollections"></a>
## func [ListCollections](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L679>)

```go
func ListCollections(dbName string) ([]string, error)
```



<a name="ToBSONDoc"></a>
## func [ToBSONDoc](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L657>)

```go
func ToBSONDoc(v interface{}) (bson.M, error)
```

ToBSONDoc converts a struct to a BSON document using struct tags. It marshals and unmarshals the struct to ensure BSON tags are properly applied.

<a name="MongoCollection"></a>
## type [MongoCollection](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L41-L43>)



```go
type MongoCollection struct {
    // contains filtered or unexported fields
}
```

<a name="Coll"></a>
### func [Coll](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L203>)

```go
func Coll(dbName, collName string) *MongoCollection
```

Coll returns a wrapped MongoDB collection

<a name="PreDefColl"></a>
### func [PreDefColl](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L213>)

```go
func PreDefColl(collName string) *MongoCollection
```

PreDefColl returns a wrapped MongoDB collection

<a name="MongoCollection.Aggregate"></a>
### func \(\*MongoCollection\) [Aggregate](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L556>)

```go
func (mc *MongoCollection) Aggregate(ctx context.Context, pipeline interface{}, opts ...*options.AggregateOptions) (*mongo.Cursor, error)
```



<a name="MongoCollection.BulkWrite"></a>
### func \(\*MongoCollection\) [BulkWrite](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L648>)

```go
func (mc *MongoCollection) BulkWrite(ctx context.Context, models []mongo.WriteModel, opts ...*options.BulkWriteOptions) (*mongo.BulkWriteResult, error)
```



<a name="MongoCollection.CountDocuments"></a>
### func \(\*MongoCollection\) [CountDocuments](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L521>)

```go
func (mc *MongoCollection) CountDocuments(ctx context.Context, filter interface{}, opts ...*options.CountOptions) (int64, error)
```



<a name="MongoCollection.CreateIndex"></a>
### func \(\*MongoCollection\) [CreateIndex](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L535>)

```go
func (mc *MongoCollection) CreateIndex(ctx context.Context, model mongo.IndexModel, opts ...*options.CreateIndexesOptions) (string, error)
```



<a name="MongoCollection.CreateIndexes"></a>
### func \(\*MongoCollection\) [CreateIndexes](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L542>)

```go
func (mc *MongoCollection) CreateIndexes(ctx context.Context, models []mongo.IndexModel, opts ...*options.CreateIndexesOptions) ([]string, error)
```



<a name="MongoCollection.DBName"></a>
### func \(\*MongoCollection\) [DBName](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L239>)

```go
func (mc *MongoCollection) DBName() string
```

DBName returns the name of the database that contains this collection

<a name="MongoCollection.DeleteMany"></a>
### func \(\*MongoCollection\) [DeleteMany](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L472>)

```go
func (mc *MongoCollection) DeleteMany(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) (*mongo.DeleteResult, error)
```



<a name="MongoCollection.DeleteOne"></a>
### func \(\*MongoCollection\) [DeleteOne](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L465>)

```go
func (mc *MongoCollection) DeleteOne(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) (*mongo.DeleteResult, error)
```



<a name="MongoCollection.Distinct"></a>
### func \(\*MongoCollection\) [Distinct](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L514>)

```go
func (mc *MongoCollection) Distinct(ctx context.Context, fieldName string, filter interface{}, opts ...*options.DistinctOptions) ([]interface{}, error)
```



<a name="MongoCollection.Drop"></a>
### func \(\*MongoCollection\) [Drop](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L641>)

```go
func (mc *MongoCollection) Drop(ctx context.Context) error
```



<a name="MongoCollection.DropIndex"></a>
### func \(\*MongoCollection\) [DropIndex](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L549>)

```go
func (mc *MongoCollection) DropIndex(ctx context.Context, name string, opts ...*options.DropIndexesOptions) error
```



<a name="MongoCollection.EstimatedDocumentCount"></a>
### func \(\*MongoCollection\) [EstimatedDocumentCount](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L528>)

```go
func (mc *MongoCollection) EstimatedDocumentCount(ctx context.Context, opts ...*options.EstimatedDocumentCountOptions) (int64, error)
```



<a name="MongoCollection.Find"></a>
### func \(\*MongoCollection\) [Find](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L422>)

```go
func (mc *MongoCollection) Find(ctx context.Context, filter interface{}, opts ...interface{}) (*mongo.Cursor, error)
```



<a name="MongoCollection.FindOne"></a>
### func \(\*MongoCollection\) [FindOne](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L445>)

```go
func (mc *MongoCollection) FindOne(ctx context.Context, filter interface{}, opts ...interface{}) *mongo.SingleResult
```



<a name="MongoCollection.FindOneAndReplace"></a>
### func \(\*MongoCollection\) [FindOneAndReplace](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L507>)

```go
func (mc *MongoCollection) FindOneAndReplace(ctx context.Context, filter interface{}, replacement interface{}, opts ...*options.FindOneAndReplaceOptions) *mongo.SingleResult
```



<a name="MongoCollection.FindOneAndUpdate"></a>
### func \(\*MongoCollection\) [FindOneAndUpdate](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L479>)

```go
func (mc *MongoCollection) FindOneAndUpdate(ctx context.Context, filter interface{}, update interface{}, opts ...*options.FindOneAndUpdateOptions) *mongo.SingleResult
```



<a name="MongoCollection.FindToArray"></a>
### func \(\*MongoCollection\) [FindToArray](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L586>)

```go
func (mc *MongoCollection) FindToArray(ctx context.Context, filter interface{}, opts ...interface{}) ([]bson.M, error)
```

FindToArray executes a find operation and returns the results as a slice of bson.M

<a name="MongoCollection.InsertMany"></a>
### func \(\*MongoCollection\) [InsertMany](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L625>)

```go
func (mc *MongoCollection) InsertMany(ctx context.Context, documents []interface{}, opts ...*options.InsertManyOptions) (*mongo.InsertManyResult, error)
```



<a name="MongoCollection.InsertOne"></a>
### func \(\*MongoCollection\) [InsertOne](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L368>)

```go
func (mc *MongoCollection) InsertOne(ctx context.Context, document interface{}, opts ...*options.InsertOneOptions) (*mongo.InsertOneResult, error)
```

Collection operation wrappers with timing and timestamps

<a name="MongoCollection.Name"></a>
### func \(\*MongoCollection\) [Name](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L234>)

```go
func (mc *MongoCollection) Name() string
```

Name returns the name of the collection

<a name="MongoCollection.Rename"></a>
### func \(\*MongoCollection\) [Rename](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L570>)

```go
func (mc *MongoCollection) Rename(ctx context.Context, newName string) error
```



<a name="MongoCollection.ReplaceOne"></a>
### func \(\*MongoCollection\) [ReplaceOne](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L500>)

```go
func (mc *MongoCollection) ReplaceOne(ctx context.Context, filter interface{}, replacement interface{}, opts ...*options.ReplaceOptions) (*mongo.UpdateResult, error)
```



<a name="MongoCollection.UpdateMany"></a>
### func \(\*MongoCollection\) [UpdateMany](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L401>)

```go
func (mc *MongoCollection) UpdateMany(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error)
```



<a name="MongoCollection.UpdateOne"></a>
### func \(\*MongoCollection\) [UpdateOne](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L380>)

```go
func (mc *MongoCollection) UpdateOne(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error)
```



<a name="MongoCollection.Watch"></a>
### func \(\*MongoCollection\) [Watch](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L563>)

```go
func (mc *MongoCollection) Watch(ctx context.Context, pipeline interface{}, opts ...*options.ChangeStreamOptions) (*mongo.ChangeStream, error)
```



<a name="QueryOptions"></a>
## type [QueryOptions](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L45-L51>)



```go
type QueryOptions struct {
    Projection interface{}
    Sort       interface{}
    Limit      int64
    Skip       int64
}
```

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
