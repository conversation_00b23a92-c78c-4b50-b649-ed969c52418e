package gomongo

import (
	"context"
	"crypto/tls"
	"fmt"
	"strings"
	"time"

	goconfig "github.com/real-rm/goconfig"
	golog "github.com/real-rm/golog"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/x/mongo/driver/connstring"
)

var (
	dbConfigs    map[string]dbConfig
	preDefColls  map[string]collConfig
	verboseLevel int
)

type dbConfig struct {
	URI      string `toml:"uri"`
	client   *mongo.Client
	database string
}

type collConfig struct {
	CollName string                 `toml:"collName"`
	DBName   string                 `toml:"dbName"`
	Options  map[string]interface{} `toml:"options"`
}

type mongoDatabase struct {
	db *mongo.Database
}

type MongoCollection struct {
	coll *mongo.Collection
}

type QueryOptions struct {
	Projection interface{}
	Sort       interface{}
	Limit      int64
	Skip       int64
	// Add other options as needed
}

type mongoLogSink struct{}

func init() {
	dbConfigs = make(map[string]dbConfig)
	preDefColls = make(map[string]collConfig)
}

func allowsInvalidCerts(uri string) bool {
	return strings.Contains(uri, "tlsAllowInvalidCertificates=true")
}

// InitMongoDB initializes MongoDB connection using configuration
func InitMongoDB() error {
	// Read MongoDB configs
	if dbsConfig := goconfig.Config("dbs"); dbsConfig != nil {
		if dbSettings, ok := dbsConfig.(map[string]interface{}); ok {
			for dbKey, dbValue := range dbSettings {
				if dbKey == "verbose" {
					if verbosity, ok := dbValue.(int64); ok {
						verboseLevel = int(verbosity)
					}
				} else {
					if dbConfigMap, ok := dbValue.(map[string]interface{}); ok {
						if connectionURI, ok := dbConfigMap["uri"].(string); ok {
							cs, err := connstring.Parse(connectionURI)
							if err != nil {
								golog.Error("failed to parse MongoDB URI", "dbName", dbKey, "error", err)
								return err
							}
							if cs == nil || cs.Database == "" {
								err := fmt.Errorf("MongoDB URI is missing a database name for dbKey: %s", dbKey)
								golog.Error(err.Error(), "dbName", dbKey)
								return err
							}
							dbConfigs[dbKey] = dbConfig{
								URI:      connectionURI,
								database: cs.Database,
							}
						}
					}
				}
			}
		}
	}

	// Read predefined collections
	if collsConfig := goconfig.Config("preDefColls"); collsConfig != nil {
		if collSettings, ok := collsConfig.(map[string]interface{}); ok {
			for collKey, collValue := range collSettings {
				options := make(map[string]interface{})
				if cfg, ok := collValue.(map[string]interface{}); ok {
					collName, ok := cfg["collName"].(string)
					if !ok {
						return fmt.Errorf("invalid collName type in config for collection %s", collKey)
					}

					dbName, ok := cfg["dbName"].(string)
					if !ok {
						return fmt.Errorf("invalid dbName type in config for collection %s", collKey)
					}
					if cfg["options"] != nil {
						options, ok = cfg["options"].(map[string]interface{})
						if !ok {
							return fmt.Errorf("invalid options type in config for collection %s", collKey)
						}
					}
					preDefColls[collKey] = collConfig{
						CollName: collName,
						DBName:   dbName,
						Options:  options,
					}
				}
			}
		}
	}

	// Connect to MongoDB
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	for dbName, cfg := range dbConfigs {
		clientOptions := options.Client().ApplyURI(cfg.URI).
			SetMaxPoolSize(20).
			SetConnectTimeout(300 * time.Second)

		// Only skip verification if explicitly allowed
		if allowsInvalidCerts(cfg.URI) {
			clientOptions.SetTLSConfig(&tls.Config{
				InsecureSkipVerify: true,
			})
			golog.Warn("skipping TLS verification for MongoDB", "dbName", dbName)
		}

		loggerOptions := options.Logger().
			SetComponentLevel(options.LogComponentCommand, options.LogLevel(verboseLevel)).
			SetSink(&mongoLogSink{})
		clientOptions.SetLoggerOptions(loggerOptions)

		client, err := mongo.Connect(ctx, clientOptions)
		if err != nil {
			golog.Error("failed to connect to MongoDB", "dbName", dbName, "error", err)
			return err
		}

		// Ping to verify connection
		err = client.Ping(ctx, nil)
		if err != nil {
			golog.Error("failed to ping MongoDB", "dbName", dbName, "error", err)
			if client != nil {
				if disconnectErr := client.Disconnect(ctx); disconnectErr != nil {
					golog.Error("failed to disconnect after ping error", "dbName", dbName, "error", disconnectErr)
				}
			}
			return err
		}

		// Update the map entry with the client
		cfg.client = client
		dbConfigs[dbName] = cfg

		// Log successful connection
		golog.Info("Connected to MongoDB database", "database", dbName)
	}

	// Log predefined collections
	for collName, cfg := range preDefColls {
		golog.Info("Registered predefined collection",
			"collection", collName,
			"database", cfg.DBName,
			"collName", cfg.CollName,
		)
		if err := Database(cfg.DBName).CreateCollection(context.Background(), cfg.CollName, nil); err != nil {
			golog.Error("failed to create collection", "error", err)
		} else {
			golog.Info("predefined collection created", "database", cfg.DBName, "collection", cfg.CollName)
		}
	}

	return nil
}

// Database returns a wrapped MongoDB database
func Database(dbName string) *mongoDatabase {
	cfg := dbConfigs[dbName]
	if cfg.client == nil || cfg.database == "" {
		golog.Error("Database configuration not properly initialized", "dbName", dbName)
		return nil
	}
	return &mongoDatabase{db: cfg.client.Database(cfg.database)}
}

// Coll returns a wrapped MongoDB collection
func Coll(dbName, collName string) *MongoCollection {
	cfg := dbConfigs[dbName]
	if cfg.client == nil || cfg.database == "" {
		golog.Error("Database configuration not properly initialized", "dbName", dbName)
		return nil
	}
	return &MongoCollection{coll: cfg.client.Database(cfg.database).Collection(collName)}
}

// PreDefColl returns a wrapped MongoDB collection
func PreDefColl(collName string) *MongoCollection {
	cfgPreDef := preDefColls[collName]
	if cfgPreDef.DBName == "" || cfgPreDef.CollName == "" {
		golog.Error("Database configuration not properly initialized", "dbName", cfgPreDef.DBName)
		return nil
	}
	db := Database(cfgPreDef.DBName)
	if db == nil {
		golog.Error("Database configuration not properly initialized", "dbName", cfgPreDef.DBName)
		return nil
	}
	return &MongoCollection{coll: db.db.Collection(cfgPreDef.CollName)}

}

// Coll returns a collection from the database
func (md *mongoDatabase) Coll(collName string) *MongoCollection {
	return &MongoCollection{coll: md.db.Collection(collName)}
}

// Name returns the name of the collection
func (mc *MongoCollection) Name() string {
	return mc.coll.Name()
}

// DBName returns the name of the database that contains this collection
func (mc *MongoCollection) DBName() string {
	return mc.coll.Database().Name()
}

// Database returns the underlying mongo.Database instance
func (mc *MongoCollection) Database() *mongo.Database {
	return mc.coll.Database()
}

// Ping checks the connection to the MongoDB server
func (mc *MongoCollection) Ping(ctx context.Context) error {
	return mc.coll.Database().Client().Ping(ctx, nil)
}

// GetClient returns the underlying mongo.Client instance
func (mc *MongoCollection) GetClient() *mongo.Client {
	return mc.coll.Database().Client()
}

// Helper function to add timestamps
func addTimestamps(doc interface{}, isUpdate bool, noModifyMt bool) (interface{}, error) {
	now := time.Now()

	// Convert to mutable map
	m, ok := doc.(bson.M)
	if !ok {
		// When the document is not already a bson.M (e.g. it's a struct or other type),
		// we need to convert it to bson.M to be able to modify it.
		// ToBSONDoc handles the conversion while respecting BSON tags
		var err error
		m, err = ToBSONDoc(doc)
		if err != nil {
			golog.Error("failed to convert document to BSON", "error", err)
			return nil, err
		}
	}

	if isUpdate {
		// For updates, use $setOnInsert for _ts and $set for _mt
		update := bson.M{
			"$setOnInsert": bson.M{"_ts": now},
		}

		// Only add _mt if noModifyMt is false
		if !noModifyMt {
			update["$set"] = bson.M{"_mt": now}
		}
		// Merge the original update with our timestamp fields
		for op, fields := range m {
			if op[0] == '$' { // If it's an operator like $set, $inc etc
				if existingFields, ok := update[op]; ok {
					// Merge with existing operator fields
					if existing, ok := existingFields.(bson.M); ok {
						if newFields, ok := fields.(bson.M); ok {
							for k, v := range newFields {
								existing[k] = v
							}
						}
					}
				} else {
					// Add new operator
					update[op] = fields
				}
			} else {
				// If no operators, wrap the entire update in $set
				if setFields, ok := update["$set"].(bson.M); ok {
					setFields[op] = fields
				}
			}
		}
		return update, nil
	} else {
		// TODO: replace will change _ts
		// For inserts, directly add timestamps
		m["_ts"] = now
		m["_mt"] = now
		return m, nil
	}
}

// Helper to log slow operations
func logSlowOperation(duration time.Duration, op string, collName string, args ...interface{}) {
	if duration > 2*time.Second {
		golog.Warnf("Slow MongoDB %s operation (%.2fs) on %s: %v", op, duration.Seconds(), collName, args)
	}
	if verboseLevel > 2 {
		golog.Debugf("MongoDB %s operation (%.2fs) on %s: %v", op, duration.Seconds(), collName, args)
	}
}

// Helper function to extract metadata/options from operations
func extractMetadata(operation interface{}) (interface{}, map[string]interface{}) {
	metadata := make(map[string]interface{})

	if m, ok := operation.(bson.M); ok {
		// List of metadata fields to extract
		metadataFields := []string{
			"noModifyMt",
			// Add other metadata fields here as needed
		}

		// Check if we have any metadata fields
		hasMetadata := false
		for _, field := range metadataFields {
			if value, exists := m[field]; exists {
				metadata[field] = value
				hasMetadata = true
			}
		}

		if hasMetadata {
			// Create a new map without the metadata fields
			cleanOperation := bson.M{}
			for k, v := range m {
				if !contains(metadataFields, k) {
					cleanOperation[k] = v
				}
			}
			return cleanOperation, metadata
		}
	}
	return operation, metadata
}

// Helper function to check if a string slice contains a value
func contains(slice []string, str string) bool {
	for _, v := range slice {
		if v == str {
			return true
		}
	}
	return false
}

// Helper function to extract QueryOptions from the provided options
func applyOptions(opts []interface{}) *QueryOptions {
	for _, opt := range opts {
		if qo, ok := opt.(QueryOptions); ok {
			return &qo
		}
	}
	return nil
}

// Collection operation wrappers with timing and timestamps
func (mc *MongoCollection) InsertOne(ctx context.Context, document interface{}, opts ...*options.InsertOneOptions) (*mongo.InsertOneResult, error) {
	start := time.Now()
	document, err := addTimestamps(document, false, false)
	if err != nil {
		golog.Error("failed to add timestamps to insertOne", "error", err)
		return nil, err
	}
	result, err := mc.coll.InsertOne(ctx, document, opts...)
	logSlowOperation(time.Since(start), "InsertOne", mc.coll.Name(), document)
	return result, err
}

func (mc *MongoCollection) UpdateOne(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error) {
	start := time.Now()
	cleanUpdate, metadata := extractMetadata(update)
	noModifyMt := false
	if value, exists := metadata["noModifyMt"]; exists {
		if boolValue, ok := value.(bool); ok {
			noModifyMt = boolValue
		} else {
			golog.Error("invalid type for noModifyMt, expected bool")
		}
	}
	cleanUpdate, err := addTimestamps(cleanUpdate, true, noModifyMt)
	if err != nil {
		golog.Error("failed to add timestamps to updateOne", "error", err)
		return nil, err
	}
	result, err := mc.coll.UpdateOne(ctx, filter, cleanUpdate, opts...)
	logSlowOperation(time.Since(start), "UpdateOne", mc.coll.Name(), "filter=", filter, "update=", cleanUpdate)
	return result, err
}

func (mc *MongoCollection) UpdateMany(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error) {
	start := time.Now()
	cleanUpdate, metadata := extractMetadata(update)
	noModifyMt := false
	if value, exists := metadata["noModifyMt"]; exists {
		if boolValue, ok := value.(bool); ok {
			noModifyMt = boolValue
		} else {
			golog.Error("invalid type for noModifyMt, expected bool")
		}
	}
	cleanUpdate, err := addTimestamps(cleanUpdate, true, noModifyMt)
	if err != nil {
		golog.Error("failed to add timestamps to updateMany", "error", err)
		return nil, err
	}
	result, err := mc.coll.UpdateMany(ctx, filter, cleanUpdate, opts...)
	logSlowOperation(time.Since(start), "UpdateMany", mc.coll.Name(), "filter=", filter, "update=", cleanUpdate)
	return result, err
}

func (mc *MongoCollection) Find(ctx context.Context, filter interface{}, opts ...interface{}) (*mongo.Cursor, error) {
	start := time.Now()
	queryOptions := applyOptions(opts)
	findOptions := options.Find()
	if queryOptions != nil {
		if queryOptions.Projection != nil {
			findOptions.SetProjection(queryOptions.Projection)
		}
		if queryOptions.Sort != nil {
			findOptions.SetSort(queryOptions.Sort)
		}
		if queryOptions.Limit > 0 {
			findOptions.SetLimit(queryOptions.Limit)
		}
		if queryOptions.Skip > 0 {
			findOptions.SetSkip(queryOptions.Skip)
		}
	}
	result, err := mc.coll.Find(ctx, filter, findOptions)
	logSlowOperation(time.Since(start), "Find", mc.coll.Name(), filter)
	return result, err
}

func (mc *MongoCollection) FindOne(ctx context.Context, filter interface{}, opts ...interface{}) *mongo.SingleResult {
	start := time.Now()
	queryOptions := applyOptions(opts)
	findOneOptions := options.FindOne()
	if queryOptions != nil {
		if queryOptions.Projection != nil {
			findOneOptions.SetProjection(queryOptions.Projection)
		}
		if queryOptions.Sort != nil {
			findOneOptions.SetSort(queryOptions.Sort)
		}
		if queryOptions.Skip > 0 {
			findOneOptions.SetSkip(queryOptions.Skip)
		}
	}
	result := mc.coll.FindOne(ctx, filter, findOneOptions)
	logSlowOperation(time.Since(start), "FindOne", mc.coll.Name(), filter)
	return result
}

func (mc *MongoCollection) DeleteOne(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) (*mongo.DeleteResult, error) {
	start := time.Now()
	result, err := mc.coll.DeleteOne(ctx, filter, opts...)
	logSlowOperation(time.Since(start), "DeleteOne", mc.coll.Name(), filter)
	return result, err
}

func (mc *MongoCollection) DeleteMany(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) (*mongo.DeleteResult, error) {
	start := time.Now()
	result, err := mc.coll.DeleteMany(ctx, filter, opts...)
	logSlowOperation(time.Since(start), "DeleteMany", mc.coll.Name(), filter)
	return result, err
}

func (mc *MongoCollection) FindOneAndUpdate(ctx context.Context, filter interface{}, update interface{}, opts ...*options.FindOneAndUpdateOptions) *mongo.SingleResult {
	start := time.Now()
	cleanUpdate, metadata := extractMetadata(update)
	noModifyMt := false
	if value, exists := metadata["noModifyMt"]; exists {
		if boolValue, ok := value.(bool); ok {
			noModifyMt = boolValue
		} else {
			golog.Error("invalid type for noModifyMt, expected bool")
		}
	}
	cleanUpdate, err := addTimestamps(cleanUpdate, true, noModifyMt)
	if err != nil {
		golog.Error("failed to add timestamps to FindOneAndUpdate", "error", err)
		return nil
	}
	result := mc.coll.FindOneAndUpdate(ctx, filter, cleanUpdate, opts...)
	logSlowOperation(time.Since(start), "FindOneAndUpdate", mc.coll.Name(), "filter=", filter, "update=", cleanUpdate)
	return result
}

func (mc *MongoCollection) ReplaceOne(ctx context.Context, filter interface{}, replacement interface{}, opts ...*options.ReplaceOptions) (*mongo.UpdateResult, error) {
	start := time.Now()
	result, err := mc.coll.ReplaceOne(ctx, filter, replacement, opts...)
	logSlowOperation(time.Since(start), "ReplaceOne", mc.coll.Name(), filter)
	return result, err
}

func (mc *MongoCollection) FindOneAndReplace(ctx context.Context, filter interface{}, replacement interface{}, opts ...*options.FindOneAndReplaceOptions) *mongo.SingleResult {
	start := time.Now()
	result := mc.coll.FindOneAndReplace(ctx, filter, replacement, opts...)
	logSlowOperation(time.Since(start), "FindOneAndReplace", mc.coll.Name(), filter)
	return result
}

func (mc *MongoCollection) Distinct(ctx context.Context, fieldName string, filter interface{}, opts ...*options.DistinctOptions) ([]interface{}, error) {
	start := time.Now()
	result, err := mc.coll.Distinct(ctx, fieldName, filter, opts...)
	logSlowOperation(time.Since(start), "Distinct", mc.coll.Name(), "field=", fieldName, "filter=", filter)
	return result, err
}

func (mc *MongoCollection) CountDocuments(ctx context.Context, filter interface{}, opts ...*options.CountOptions) (int64, error) {
	start := time.Now()
	result, err := mc.coll.CountDocuments(ctx, filter, opts...)
	logSlowOperation(time.Since(start), "CountDocuments", mc.coll.Name(), filter)
	return result, err
}

func (mc *MongoCollection) EstimatedDocumentCount(ctx context.Context, opts ...*options.EstimatedDocumentCountOptions) (int64, error) {
	start := time.Now()
	result, err := mc.coll.EstimatedDocumentCount(ctx, opts...)
	logSlowOperation(time.Since(start), "EstimatedDocumentCount", mc.coll.Name())
	return result, err
}

func (mc *MongoCollection) CreateIndex(ctx context.Context, model mongo.IndexModel, opts ...*options.CreateIndexesOptions) (string, error) {
	start := time.Now()
	result, err := mc.coll.Indexes().CreateOne(ctx, model, opts...)
	logSlowOperation(time.Since(start), "CreateIndex", mc.coll.Name(), model)
	return result, err
}

func (mc *MongoCollection) CreateIndexes(ctx context.Context, models []mongo.IndexModel, opts ...*options.CreateIndexesOptions) ([]string, error) {
	start := time.Now()
	result, err := mc.coll.Indexes().CreateMany(ctx, models, opts...)
	logSlowOperation(time.Since(start), "CreateIndexes", mc.coll.Name(), models)
	return result, err
}

func (mc *MongoCollection) DropIndex(ctx context.Context, name string, opts ...*options.DropIndexesOptions) error {
	start := time.Now()
	_, err := mc.coll.Indexes().DropOne(ctx, name, opts...)
	logSlowOperation(time.Since(start), "DropIndex", mc.coll.Name(), name)
	return err
}

func (mc *MongoCollection) Aggregate(ctx context.Context, pipeline interface{}, opts ...*options.AggregateOptions) (*mongo.Cursor, error) {
	start := time.Now()
	result, err := mc.coll.Aggregate(ctx, pipeline, opts...)
	logSlowOperation(time.Since(start), "Aggregate", mc.coll.Name(), pipeline)
	return result, err
}

func (mc *MongoCollection) Watch(ctx context.Context, pipeline interface{}, opts ...*options.ChangeStreamOptions) (*mongo.ChangeStream, error) {
	start := time.Now()
	result, err := mc.coll.Watch(ctx, pipeline, opts...)
	logSlowOperation(time.Since(start), "Watch", mc.coll.Name(), pipeline)
	return result, err
}

func (mc *MongoCollection) Rename(ctx context.Context, newName string) error {
	start := time.Now()
	renameCmd := bson.D{
		{Key: "renameCollection", Value: fmt.Sprintf("%s.%s", mc.coll.Database().Name(), mc.coll.Name())},
		{Key: "to", Value: fmt.Sprintf("%s.%s", mc.coll.Database().Name(), newName)},
	}
	err := mc.coll.Database().Client().Database("admin").RunCommand(ctx, renameCmd).Err()
	if err != nil {
		golog.Error("failed to rename collection", "error", err)
		return err
	}
	logSlowOperation(time.Since(start), "Rename", mc.coll.Name(), "newName=", newName)
	return nil
}

// FindToArray executes a find operation and returns the results as a slice of bson.M
func (mc *MongoCollection) FindToArray(ctx context.Context, filter interface{}, opts ...interface{}) ([]bson.M, error) {
	start := time.Now()
	queryOptions := applyOptions(opts)
	findOptions := options.Find()
	if queryOptions != nil {
		if queryOptions.Projection != nil {
			findOptions.SetProjection(queryOptions.Projection)
		}
		if queryOptions.Sort != nil {
			findOptions.SetSort(queryOptions.Sort)
		}
		if queryOptions.Limit > 0 {
			findOptions.SetLimit(queryOptions.Limit)
		}
		if queryOptions.Skip > 0 {
			findOptions.SetSkip(queryOptions.Skip)
		}
	}

	cursor, err := mc.coll.Find(ctx, filter, findOptions)
	if err != nil {
		golog.Error("failed to find in FindToArray", "error", err)
		return nil, err
	}
	// Ensure cursor is closed after function returns to prevent memory leaks
	defer func() {
		if closeErr := cursor.Close(ctx); closeErr != nil {
			golog.Error("error closing cursor", "error", closeErr)
		}
	}()

	var results []bson.M
	// cursor.All() is Go's implementation of MongoDB's cursor.toArray()
	// It iterates through all documents and loads them into the results slice
	err = cursor.All(ctx, &results)
	logSlowOperation(time.Since(start), "FindToArray", mc.coll.Name(), filter)
	return results, err
}

func (mc *MongoCollection) InsertMany(ctx context.Context, documents []interface{}, opts ...*options.InsertManyOptions) (*mongo.InsertManyResult, error) {
	start := time.Now()
	// Add timestamps to each document
	for i := range documents {
		var err error
		documents[i], err = addTimestamps(documents[i], false, false)
		if err != nil {
			golog.Error("failed to add timestamps to InsertMany", "error", err)
			return nil, err
		}
	}
	result, err := mc.coll.InsertMany(ctx, documents, opts...)
	logSlowOperation(time.Since(start), "InsertMany", mc.coll.Name(), documents)
	return result, err
}

func (mc *MongoCollection) Drop(ctx context.Context) error {
	start := time.Now()
	err := mc.coll.Drop(ctx)
	logSlowOperation(time.Since(start), "Drop", mc.coll.Name())
	return err
}

func (mc *MongoCollection) BulkWrite(ctx context.Context, models []mongo.WriteModel, opts ...*options.BulkWriteOptions) (*mongo.BulkWriteResult, error) {
	start := time.Now()
	result, err := mc.coll.BulkWrite(ctx, models, opts...)
	logSlowOperation(time.Since(start), "BulkWrite", mc.coll.Name(), models)
	return result, err
}

// ToBSONDoc converts a struct to a BSON document using struct tags.
// It marshals and unmarshals the struct to ensure BSON tags are properly applied.
func ToBSONDoc(v interface{}) (bson.M, error) {
	data, err := bson.Marshal(v)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal document: %v", err)
	}

	var updateDoc bson.M
	if err := bson.Unmarshal(data, &updateDoc); err != nil {
		return nil, fmt.Errorf("failed to unmarshal document: %v", err)
	}

	return updateDoc, nil
}

// https://pkg.go.dev/go.mongodb.org/mongo-driver/v2/mongo/options#LogSink
func (sink *mongoLogSink) Info(level int, message string, keysAndValues ...interface{}) {
	golog.Verbose(message, keysAndValues...)
}
func (sink *mongoLogSink) Error(err error, message string, keysAndValues ...interface{}) {
	golog.Error(message, append(keysAndValues, "error", err)...)
}

func ListCollections(dbName string) ([]string, error) {
	db := Database(dbName)
	if db == nil {
		return nil, fmt.Errorf("database not found")
	}
	collections, err := db.ListCollectionNames(context.Background(), bson.M{})
	if err != nil {
		return nil, err
	}
	return collections, nil
}

// ListCollectionNames returns a list of collection names in the database
func (md *mongoDatabase) ListCollectionNames(ctx context.Context, filter interface{}, opts ...*options.ListCollectionsOptions) ([]string, error) {
	if md == nil || md.db == nil {
		return nil, fmt.Errorf("database is not initialized")
	}
	if err := Database(md.db.Name()); err != nil {
		return nil, fmt.Errorf("database not found")
	}
	return md.db.ListCollectionNames(ctx, filter, opts...)
}

func (md *mongoDatabase) CreateCollection(ctx context.Context, collectionName string, opts ...*options.CreateCollectionOptions) error {
	if md == nil || md.db == nil {
		return fmt.Errorf("database is not initialized")
	}
	if err := Database(md.db.Name()); err != nil {
		return fmt.Errorf("database not found")
	}
	collections, err := md.ListCollectionNames(ctx, bson.M{})
	if err != nil {
		return err
	}
	if !contains(collections, collectionName) {
		return md.db.CreateCollection(ctx, collectionName, opts...)
	}
	return nil
}
