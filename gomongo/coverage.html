
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>gomongo: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">github.com/real-rm/gomongo/mongodb.go (85.6%)</option>
				
				<option value="file1">github.com/real-rm/gomongo/transaction_example.go (0.0%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package gomongo

import (
        "context"
        "crypto/tls"
        "fmt"
        "strings"
        "time"

        goconfig "github.com/real-rm/goconfig"
        golog "github.com/real-rm/golog"
        "go.mongodb.org/mongo-driver/bson"
        "go.mongodb.org/mongo-driver/mongo"
        "go.mongodb.org/mongo-driver/mongo/options"
        "go.mongodb.org/mongo-driver/mongo/readconcern"
        "go.mongodb.org/mongo-driver/mongo/writeconcern"
        "go.mongodb.org/mongo-driver/x/mongo/driver/connstring"
)

var (
        dbConfigs    map[string]dbConfig
        preDefColls  map[string]collConfig
        verboseLevel int
)

type dbConfig struct {
        URI      string `toml:"uri"`
        client   *mongo.Client
        database string
}

type collConfig struct {
        CollName string                 `toml:"collName"`
        DBName   string                 `toml:"dbName"`
        Options  map[string]interface{} `toml:"options"`
}

type mongoDatabase struct {
        db *mongo.Database
}

type MongoCollection struct {
        coll *mongo.Collection
}

type QueryOptions struct {
        Projection interface{}
        Sort       interface{}
        Limit      int64
        Skip       int64
        // Add other options as needed
}

type mongoLogSink struct{}

// MongoSession represents a MongoDB session for transaction management
type MongoSession struct {
        session mongo.Session
        ctx     context.Context
}

// MongoTransaction represents a MongoDB transaction
type MongoTransaction struct {
        session   mongo.Session
        ctx       context.Context
        started   bool
        committed bool
        aborted   bool
}

// TransactionOptions defines options for starting a transaction
type TransactionOptions struct {
        ReadConcern  *readconcern.ReadConcern
        WriteConcern *writeconcern.WriteConcern
}

func init() <span class="cov8" title="1">{
        dbConfigs = make(map[string]dbConfig)
        preDefColls = make(map[string]collConfig)
}</span>

func allowsInvalidCerts(uri string) bool <span class="cov8" title="1">{
        return strings.Contains(uri, "tlsAllowInvalidCertificates=true")
}</span>

// InitMongoDB initializes MongoDB connection using configuration
func InitMongoDB() error <span class="cov8" title="1">{
        // Check if golog is initialized by trying to log a debug message
        // If golog is not initialized, this will panic
        defer func() </span><span class="cov8" title="1">{
                if r := recover(); r != nil </span><span class="cov0" title="0">{
                        panic(fmt.Sprintf("golog is not initialized, please call golog.InitLog() first: %v", r))</span>
                }
        }()
        <span class="cov8" title="1">golog.Debug("Checking golog initialization")

        // Read MongoDB configs
        if dbsConfig := goconfig.Config("dbs"); dbsConfig != nil </span><span class="cov8" title="1">{
                if dbSettings, ok := dbsConfig.(map[string]interface{}); ok </span><span class="cov8" title="1">{
                        for dbKey, dbValue := range dbSettings </span><span class="cov8" title="1">{
                                if dbKey == "verbose" </span><span class="cov8" title="1">{
                                        if verbosity, ok := dbValue.(int64); ok </span><span class="cov8" title="1">{
                                                verboseLevel = int(verbosity)
                                        }</span>
                                } else<span class="cov8" title="1"> {
                                        if dbConfigMap, ok := dbValue.(map[string]interface{}); ok </span><span class="cov8" title="1">{
                                                if connectionURI, ok := dbConfigMap["uri"].(string); ok </span><span class="cov8" title="1">{
                                                        cs, err := connstring.Parse(connectionURI)
                                                        if err != nil </span><span class="cov0" title="0">{
                                                                golog.Error("failed to parse MongoDB URI", "dbName", dbKey, "error", err)
                                                                return err
                                                        }</span>
                                                        <span class="cov8" title="1">if cs == nil || cs.Database == "" </span><span class="cov0" title="0">{
                                                                err := fmt.Errorf("MongoDB URI is missing a database name for dbKey: %s", dbKey)
                                                                golog.Error(err.Error(), "dbName", dbKey)
                                                                return err
                                                        }</span>
                                                        <span class="cov8" title="1">dbConfigs[dbKey] = dbConfig{
                                                                URI:      connectionURI,
                                                                database: cs.Database,
                                                        }</span>
                                                }
                                        }
                                }
                        }
                }
        }

        // Read predefined collections
        <span class="cov8" title="1">if collsConfig := goconfig.Config("preDefColls"); collsConfig != nil </span><span class="cov8" title="1">{
                if collSettings, ok := collsConfig.(map[string]interface{}); ok </span><span class="cov8" title="1">{
                        for collKey, collValue := range collSettings </span><span class="cov8" title="1">{
                                options := make(map[string]interface{})
                                if cfg, ok := collValue.(map[string]interface{}); ok </span><span class="cov8" title="1">{
                                        collName, ok := cfg["collName"].(string)
                                        if !ok </span><span class="cov0" title="0">{
                                                return fmt.Errorf("invalid collName type in config for collection %s", collKey)
                                        }</span>

                                        <span class="cov8" title="1">dbName, ok := cfg["dbName"].(string)
                                        if !ok </span><span class="cov0" title="0">{
                                                return fmt.Errorf("invalid dbName type in config for collection %s", collKey)
                                        }</span>
                                        <span class="cov8" title="1">if cfg["options"] != nil </span><span class="cov8" title="1">{
                                                options, ok = cfg["options"].(map[string]interface{})
                                                if !ok </span><span class="cov0" title="0">{
                                                        return fmt.Errorf("invalid options type in config for collection %s", collKey)
                                                }</span>
                                        }
                                        <span class="cov8" title="1">preDefColls[collKey] = collConfig{
                                                CollName: collName,
                                                DBName:   dbName,
                                                Options:  options,
                                        }</span>
                                }
                        }
                }
        }

        // Connect to MongoDB
        <span class="cov8" title="1">ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
        defer cancel()

        for dbName, cfg := range dbConfigs </span><span class="cov8" title="1">{
                clientOptions := options.Client().ApplyURI(cfg.URI).
                        SetMaxPoolSize(20).
                        SetConnectTimeout(300 * time.Second)

                // Only skip verification if explicitly allowed
                if allowsInvalidCerts(cfg.URI) </span><span class="cov0" title="0">{
                        clientOptions.SetTLSConfig(&amp;tls.Config{
                                InsecureSkipVerify: true,
                        })
                        golog.Warn("skipping TLS verification for MongoDB", "dbName", dbName)
                }</span>

                <span class="cov8" title="1">loggerOptions := options.Logger().
                        SetComponentLevel(options.LogComponentCommand, options.LogLevel(verboseLevel)).
                        SetSink(&amp;mongoLogSink{})
                clientOptions.SetLoggerOptions(loggerOptions)

                client, err := mongo.Connect(ctx, clientOptions)
                if err != nil </span><span class="cov0" title="0">{
                        golog.Error("failed to connect to MongoDB", "dbName", dbName, "error", err)
                        return err
                }</span>

                // Ping to verify connection
                <span class="cov8" title="1">err = client.Ping(ctx, nil)
                if err != nil </span><span class="cov0" title="0">{
                        golog.Error("failed to ping MongoDB", "dbName", dbName, "error", err)
                        if client != nil </span><span class="cov0" title="0">{
                                if disconnectErr := client.Disconnect(ctx); disconnectErr != nil </span><span class="cov0" title="0">{
                                        golog.Error("failed to disconnect after ping error", "dbName", dbName, "error", disconnectErr)
                                }</span>
                        }
                        <span class="cov0" title="0">return err</span>
                }

                // Update the map entry with the client
                <span class="cov8" title="1">cfg.client = client
                dbConfigs[dbName] = cfg

                // Log successful connection
                golog.Info("Connected to MongoDB database", "database", dbName)</span>
        }

        // Log predefined collections
        <span class="cov8" title="1">for collName, cfg := range preDefColls </span><span class="cov8" title="1">{
                golog.Info("Registered predefined collection",
                        "collection", collName,
                        "database", cfg.DBName,
                        "collName", cfg.CollName,
                )
                if err := Database(cfg.DBName).CreateCollection(context.Background(), cfg.CollName, nil); err != nil </span><span class="cov0" title="0">{
                        golog.Error("failed to create collection", "error", err)
                }</span> else<span class="cov8" title="1"> {
                        golog.Info("predefined collection created", "database", cfg.DBName, "collection", cfg.CollName)
                }</span>
        }

        <span class="cov8" title="1">return nil</span>
}

// Database returns a wrapped MongoDB database
func Database(dbName string) *mongoDatabase <span class="cov8" title="1">{
        cfg := dbConfigs[dbName]
        if cfg.client == nil || cfg.database == "" </span><span class="cov8" title="1">{
                golog.Error("Database configuration not properly initialized", "dbName", dbName)
                return nil
        }</span>
        <span class="cov8" title="1">return &amp;mongoDatabase{db: cfg.client.Database(cfg.database)}</span>
}

// Coll returns a wrapped MongoDB collection
func Coll(dbName, collName string) *MongoCollection <span class="cov8" title="1">{
        if dbName == "" || collName == "" </span><span class="cov8" title="1">{
                golog.Error("Database or collection name is empty", "dbName", dbName, "collName", collName)
                return nil
        }</span>
        <span class="cov8" title="1">cfg := dbConfigs[dbName]
        if cfg.client == nil || cfg.database == "" </span><span class="cov8" title="1">{
                golog.Error("Database configuration not properly initialized", "dbName", dbName)
                return nil
        }</span>
        <span class="cov8" title="1">return &amp;MongoCollection{coll: cfg.client.Database(cfg.database).Collection(collName)}</span>
}

// PreDefColl returns a wrapped MongoDB collection
func PreDefColl(collName string) *MongoCollection <span class="cov8" title="1">{
        cfgPreDef := preDefColls[collName]
        if cfgPreDef.DBName == "" || cfgPreDef.CollName == "" </span><span class="cov8" title="1">{
                golog.Error("Database configuration not properly initialized", "dbName", cfgPreDef.DBName)
                return nil
        }</span>
        <span class="cov8" title="1">db := Database(cfgPreDef.DBName)
        if db == nil </span><span class="cov0" title="0">{
                golog.Error("Database configuration not properly initialized", "dbName", cfgPreDef.DBName)
                return nil
        }</span>
        <span class="cov8" title="1">return &amp;MongoCollection{coll: db.db.Collection(cfgPreDef.CollName)}</span>

}

// Coll returns a collection from the database
func (md *mongoDatabase) Coll(collName string) *MongoCollection <span class="cov0" title="0">{
        return &amp;MongoCollection{coll: md.db.Collection(collName)}
}</span>

// Name returns the name of the collection
func (mc *MongoCollection) Name() string <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return ""
        }</span>
        <span class="cov8" title="1">return mc.coll.Name()</span>
}

// DBName returns the name of the database that contains this collection
func (mc *MongoCollection) DBName() string <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return ""
        }</span>
        <span class="cov8" title="1">return mc.coll.Database().Name()</span>
}

// Database returns the underlying mongo.Database instance
func (mc *MongoCollection) Database() *mongo.Database <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil
        }</span>
        <span class="cov8" title="1">return mc.coll.Database()</span>
}

// Ping checks the connection to the MongoDB server
func (mc *MongoCollection) Ping(ctx context.Context) error <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov8" title="1">return mc.coll.Database().Client().Ping(ctx, nil)</span>
}

// GetClient returns the underlying mongo.Client instance
func (mc *MongoCollection) GetClient() *mongo.Client <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil
        }</span>
        <span class="cov8" title="1">return mc.coll.Database().Client()</span>
}

// Helper function to add timestamps
func addTimestamps(doc interface{}, isUpdate bool, noModifyMt bool) (interface{}, error) <span class="cov8" title="1">{
        now := time.Now()

        // Convert to mutable map
        m, ok := doc.(bson.M)
        if !ok </span><span class="cov8" title="1">{
                // When the document is not already a bson.M (e.g. it's a struct or other type),
                // we need to convert it to bson.M to be able to modify it.
                // ToBSONDoc handles the conversion while respecting BSON tags
                var err error
                m, err = ToBSONDoc(doc)
                if err != nil </span><span class="cov8" title="1">{
                        golog.Error("failed to convert document to BSON", "error", err)
                        return nil, err
                }</span>
        }

        <span class="cov8" title="1">if isUpdate </span><span class="cov8" title="1">{
                // For updates, use $setOnInsert for _ts and $set for _mt
                update := bson.M{
                        "$setOnInsert": bson.M{"_ts": now},
                }

                // Only add _mt if noModifyMt is false
                if !noModifyMt </span><span class="cov8" title="1">{
                        update["$set"] = bson.M{"_mt": now}
                }</span>
                // Merge the original update with our timestamp fields
                <span class="cov8" title="1">for op, fields := range m </span><span class="cov8" title="1">{
                        if op != "" &amp;&amp; op[0] == '$' </span><span class="cov8" title="1">{ // If it's an operator like $set, $inc etc
                                if existingFields, ok := update[op]; ok </span><span class="cov8" title="1">{
                                        // Merge with existing operator fields
                                        if existing, ok := existingFields.(bson.M); ok </span><span class="cov8" title="1">{
                                                if newFields, ok := fields.(bson.M); ok </span><span class="cov8" title="1">{
                                                        for k, v := range newFields </span><span class="cov8" title="1">{
                                                                existing[k] = v
                                                        }</span>
                                                }
                                        }
                                } else<span class="cov8" title="1"> {
                                        // Add new operator
                                        update[op] = fields
                                }</span>
                        } else<span class="cov8" title="1"> {
                                // If no operators, wrap the entire update in $set
                                if setFields, ok := update["$set"].(bson.M); ok </span><span class="cov8" title="1">{
                                        setFields[op] = fields
                                }</span> else<span class="cov8" title="1"> if inputSet, ok := m["$set"].(bson.M); ok </span><span class="cov8" title="1">{
                                        // 如果 $set 只在输入有，当前 update 没有，则复制输入 $set 并合并
                                        newSet := bson.M{}
                                        for k, v := range inputSet </span><span class="cov8" title="1">{
                                                newSet[k] = v
                                        }</span>
                                        <span class="cov8" title="1">newSet[op] = fields
                                        update["$set"] = newSet</span>
                                }
                        }
                }
                <span class="cov8" title="1">return update, nil</span>
        } else<span class="cov8" title="1"> {
                // For inserts, directly add timestamps
                m["_ts"] = now
                m["_mt"] = now
                return m, nil
        }</span>
}

// Helper to log slow operations
func logSlowOperation(duration time.Duration, op string, collName string, args ...interface{}) <span class="cov8" title="1">{
        if duration &gt; 2*time.Second </span><span class="cov8" title="1">{
                golog.Warnf("Slow MongoDB %s operation (%.2fs) on %s: %v", op, duration.Seconds(), collName, args)
        }</span>
        <span class="cov8" title="1">if verboseLevel &gt; 2 </span><span class="cov8" title="1">{
                golog.Debugf("MongoDB %s operation (%.2fs) on %s: %v", op, duration.Seconds(), collName, args)
        }</span>
}

// Helper function to extract metadata/options from operations
func extractMetadata(operation interface{}) (interface{}, map[string]interface{}) <span class="cov8" title="1">{
        metadata := make(map[string]interface{})

        if m, ok := operation.(bson.M); ok </span><span class="cov8" title="1">{
                // List of metadata fields to extract
                metadataFields := []string{
                        "noModifyMt",
                        // Add other metadata fields here as needed
                }

                // Check if we have any metadata fields
                hasMetadata := false
                for _, field := range metadataFields </span><span class="cov8" title="1">{
                        if value, exists := m[field]; exists </span><span class="cov8" title="1">{
                                metadata[field] = value
                                hasMetadata = true
                        }</span>
                }

                <span class="cov8" title="1">if hasMetadata </span><span class="cov8" title="1">{
                        // Create a new map without the metadata fields
                        cleanOperation := bson.M{}
                        for k, v := range m </span><span class="cov8" title="1">{
                                if !contains(metadataFields, k) </span><span class="cov8" title="1">{
                                        cleanOperation[k] = v
                                }</span>
                        }
                        <span class="cov8" title="1">return cleanOperation, metadata</span>
                }
        }
        <span class="cov8" title="1">return operation, metadata</span>
}

// Helper function to check if a string slice contains a value
func contains(slice []string, str string) bool <span class="cov8" title="1">{
        for _, v := range slice </span><span class="cov8" title="1">{
                if v == str </span><span class="cov8" title="1">{
                        return true
                }</span>
        }
        <span class="cov8" title="1">return false</span>
}

// Helper function to extract QueryOptions from the provided options
func applyOptions(opts []interface{}) *QueryOptions <span class="cov8" title="1">{
        for _, opt := range opts </span><span class="cov8" title="1">{
                if qo, ok := opt.(QueryOptions); ok </span><span class="cov8" title="1">{
                        return &amp;qo
                }</span>
        }
        <span class="cov8" title="1">return nil</span>
}

// Collection operation wrappers with timing and timestamps
func (mc *MongoCollection) InsertOne(ctx context.Context, document interface{}, opts ...*options.InsertOneOptions) (*mongo.InsertOneResult, error) <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov8" title="1">start := time.Now()
        document, err := addTimestamps(document, false, false)
        if err != nil </span><span class="cov8" title="1">{
                golog.Error("failed to add timestamps to insertOne", "error", err)
                return nil, err
        }</span>
        <span class="cov8" title="1">result, err := mc.coll.InsertOne(ctx, document, opts...)
        logSlowOperation(time.Since(start), "InsertOne", mc.coll.Name(), document)
        return result, err</span>
}

func (mc *MongoCollection) UpdateOne(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error) <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov8" title="1">start := time.Now()
        cleanUpdate, metadata := extractMetadata(update)
        noModifyMt := false
        if value, exists := metadata["noModifyMt"]; exists </span><span class="cov8" title="1">{
                if boolValue, ok := value.(bool); ok </span><span class="cov8" title="1">{
                        noModifyMt = boolValue
                }</span> else<span class="cov0" title="0"> {
                        golog.Error("invalid type for noModifyMt, expected bool")
                }</span>
        }
        <span class="cov8" title="1">cleanUpdate, err := addTimestamps(cleanUpdate, true, noModifyMt)
        if err != nil </span><span class="cov8" title="1">{
                golog.Error("failed to add timestamps to updateOne", "error", err)
                return nil, err
        }</span>
        <span class="cov8" title="1">result, err := mc.coll.UpdateOne(ctx, filter, cleanUpdate, opts...)
        logSlowOperation(time.Since(start), "UpdateOne", mc.coll.Name(), "filter=", filter, "update=", cleanUpdate)
        return result, err</span>
}

func (mc *MongoCollection) UpdateMany(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error) <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov8" title="1">start := time.Now()
        cleanUpdate, metadata := extractMetadata(update)
        noModifyMt := false
        if value, exists := metadata["noModifyMt"]; exists </span><span class="cov8" title="1">{
                if boolValue, ok := value.(bool); ok </span><span class="cov8" title="1">{
                        noModifyMt = boolValue
                }</span> else<span class="cov0" title="0"> {
                        golog.Error("invalid type for noModifyMt, expected bool")
                }</span>
        }
        <span class="cov8" title="1">cleanUpdate, err := addTimestamps(cleanUpdate, true, noModifyMt)
        if err != nil </span><span class="cov8" title="1">{
                golog.Error("failed to add timestamps to updateMany", "error", err)
                return nil, err
        }</span>
        <span class="cov8" title="1">result, err := mc.coll.UpdateMany(ctx, filter, cleanUpdate, opts...)
        logSlowOperation(time.Since(start), "UpdateMany", mc.coll.Name(), "filter=", filter, "update=", cleanUpdate)
        return result, err</span>
}

func (mc *MongoCollection) Find(ctx context.Context, filter interface{}, opts ...interface{}) (*mongo.Cursor, error) <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov8" title="1">start := time.Now()
        queryOptions := applyOptions(opts)
        findOptions := options.Find()
        if queryOptions != nil </span><span class="cov8" title="1">{
                if queryOptions.Projection != nil </span><span class="cov8" title="1">{
                        findOptions.SetProjection(queryOptions.Projection)
                }</span>
                <span class="cov8" title="1">if queryOptions.Sort != nil </span><span class="cov8" title="1">{
                        findOptions.SetSort(queryOptions.Sort)
                }</span>
                <span class="cov8" title="1">if queryOptions.Limit &gt; 0 </span><span class="cov0" title="0">{
                        findOptions.SetLimit(queryOptions.Limit)
                }</span>
                <span class="cov8" title="1">if queryOptions.Skip &gt; 0 </span><span class="cov0" title="0">{
                        findOptions.SetSkip(queryOptions.Skip)
                }</span>
        }
        <span class="cov8" title="1">result, err := mc.coll.Find(ctx, filter, findOptions)
        logSlowOperation(time.Since(start), "Find", mc.coll.Name(), filter)
        return result, err</span>
}

func (mc *MongoCollection) FindOne(ctx context.Context, filter interface{}, opts ...interface{}) *mongo.SingleResult <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil
        }</span>
        <span class="cov8" title="1">start := time.Now()
        queryOptions := applyOptions(opts)
        findOneOptions := options.FindOne()
        if queryOptions != nil </span><span class="cov0" title="0">{
                if queryOptions.Projection != nil </span><span class="cov0" title="0">{
                        findOneOptions.SetProjection(queryOptions.Projection)
                }</span>
                <span class="cov0" title="0">if queryOptions.Sort != nil </span><span class="cov0" title="0">{
                        findOneOptions.SetSort(queryOptions.Sort)
                }</span>
                <span class="cov0" title="0">if queryOptions.Skip &gt; 0 </span><span class="cov0" title="0">{
                        findOneOptions.SetSkip(queryOptions.Skip)
                }</span>
        }
        <span class="cov8" title="1">result := mc.coll.FindOne(ctx, filter, findOneOptions)
        logSlowOperation(time.Since(start), "FindOne", mc.coll.Name(), filter)
        return result</span>
}

func (mc *MongoCollection) DeleteOne(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) (*mongo.DeleteResult, error) <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov8" title="1">start := time.Now()
        result, err := mc.coll.DeleteOne(ctx, filter, opts...)
        logSlowOperation(time.Since(start), "DeleteOne", mc.coll.Name(), filter)
        return result, err</span>
}

func (mc *MongoCollection) DeleteMany(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) (*mongo.DeleteResult, error) <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov8" title="1">start := time.Now()
        result, err := mc.coll.DeleteMany(ctx, filter, opts...)
        logSlowOperation(time.Since(start), "DeleteMany", mc.coll.Name(), filter)
        return result, err</span>
}

func (mc *MongoCollection) FindOneAndUpdate(ctx context.Context, filter interface{}, update interface{}, opts ...*options.FindOneAndUpdateOptions) *mongo.SingleResult <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil
        }</span>
        <span class="cov8" title="1">start := time.Now()
        cleanUpdate, metadata := extractMetadata(update)
        noModifyMt := false
        if value, exists := metadata["noModifyMt"]; exists </span><span class="cov8" title="1">{
                if boolValue, ok := value.(bool); ok </span><span class="cov8" title="1">{
                        noModifyMt = boolValue
                }</span> else<span class="cov0" title="0"> {
                        golog.Error("invalid type for noModifyMt, expected bool")
                }</span>
        }
        <span class="cov8" title="1">cleanUpdate, err := addTimestamps(cleanUpdate, true, noModifyMt)
        if err != nil </span><span class="cov8" title="1">{
                golog.Error("failed to add timestamps to FindOneAndUpdate", "error", err)
                return nil
        }</span>
        <span class="cov8" title="1">result := mc.coll.FindOneAndUpdate(ctx, filter, cleanUpdate, opts...)
        logSlowOperation(time.Since(start), "FindOneAndUpdate", mc.coll.Name(), "filter=", filter, "update=", cleanUpdate)
        return result</span>
}

func (mc *MongoCollection) ReplaceOne(ctx context.Context, filter interface{}, replacement interface{}, opts ...*options.ReplaceOptions) (*mongo.UpdateResult, error) <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov8" title="1">start := time.Now()
        result, err := mc.coll.ReplaceOne(ctx, filter, replacement, opts...)
        logSlowOperation(time.Since(start), "ReplaceOne", mc.coll.Name(), filter)
        return result, err</span>
}

func (mc *MongoCollection) FindOneAndReplace(ctx context.Context, filter interface{}, replacement interface{}, opts ...*options.FindOneAndReplaceOptions) *mongo.SingleResult <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil
        }</span>
        <span class="cov8" title="1">start := time.Now()
        result := mc.coll.FindOneAndReplace(ctx, filter, replacement, opts...)
        logSlowOperation(time.Since(start), "FindOneAndReplace", mc.coll.Name(), filter)
        return result</span>
}

func (mc *MongoCollection) Distinct(ctx context.Context, fieldName string, filter interface{}, opts ...*options.DistinctOptions) ([]interface{}, error) <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov8" title="1">start := time.Now()
        result, err := mc.coll.Distinct(ctx, fieldName, filter, opts...)
        logSlowOperation(time.Since(start), "Distinct", mc.coll.Name(), "field=", fieldName, "filter=", filter)
        return result, err</span>
}

func (mc *MongoCollection) CountDocuments(ctx context.Context, filter interface{}, opts ...*options.CountOptions) (int64, error) <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return 0, fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov8" title="1">start := time.Now()
        result, err := mc.coll.CountDocuments(ctx, filter, opts...)
        logSlowOperation(time.Since(start), "CountDocuments", mc.coll.Name(), filter)
        return result, err</span>
}

func (mc *MongoCollection) EstimatedDocumentCount(ctx context.Context, opts ...*options.EstimatedDocumentCountOptions) (int64, error) <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return 0, fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov0" title="0">start := time.Now()
        result, err := mc.coll.EstimatedDocumentCount(ctx, opts...)
        logSlowOperation(time.Since(start), "EstimatedDocumentCount", mc.coll.Name())
        return result, err</span>
}

func (mc *MongoCollection) CreateIndex(ctx context.Context, model mongo.IndexModel, opts ...*options.CreateIndexesOptions) (string, error) <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return "", fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov0" title="0">start := time.Now()
        result, err := mc.coll.Indexes().CreateOne(ctx, model, opts...)
        logSlowOperation(time.Since(start), "CreateIndex", mc.coll.Name(), model)
        return result, err</span>
}

func (mc *MongoCollection) CreateIndexes(ctx context.Context, models []mongo.IndexModel, opts ...*options.CreateIndexesOptions) ([]string, error) <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov0" title="0">start := time.Now()
        result, err := mc.coll.Indexes().CreateMany(ctx, models, opts...)
        logSlowOperation(time.Since(start), "CreateIndexes", mc.coll.Name(), models)
        return result, err</span>
}

func (mc *MongoCollection) DropIndex(ctx context.Context, name string, opts ...*options.DropIndexesOptions) error <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov0" title="0">start := time.Now()
        _, err := mc.coll.Indexes().DropOne(ctx, name, opts...)
        logSlowOperation(time.Since(start), "DropIndex", mc.coll.Name(), name)
        return err</span>
}

func (mc *MongoCollection) Aggregate(ctx context.Context, pipeline interface{}, opts ...*options.AggregateOptions) (*mongo.Cursor, error) <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov8" title="1">start := time.Now()
        result, err := mc.coll.Aggregate(ctx, pipeline, opts...)
        logSlowOperation(time.Since(start), "Aggregate", mc.coll.Name(), pipeline)
        return result, err</span>
}

func (mc *MongoCollection) Watch(ctx context.Context, pipeline interface{}, opts ...*options.ChangeStreamOptions) (*mongo.ChangeStream, error) <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov8" title="1">start := time.Now()
        result, err := mc.coll.Watch(ctx, pipeline, opts...)
        logSlowOperation(time.Since(start), "Watch", mc.coll.Name(), pipeline)
        return result, err</span>
}

func (mc *MongoCollection) Rename(ctx context.Context, newName string) error <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov8" title="1">start := time.Now()
        renameCmd := bson.D{
                {Key: "renameCollection", Value: fmt.Sprintf("%s.%s", mc.coll.Database().Name(), mc.coll.Name())},
                {Key: "to", Value: fmt.Sprintf("%s.%s", mc.coll.Database().Name(), newName)},
        }
        err := mc.coll.Database().Client().Database("admin").RunCommand(ctx, renameCmd).Err()
        if err != nil </span><span class="cov8" title="1">{
                golog.Error("failed to rename collection", "error", err)
                return err
        }</span>
        <span class="cov8" title="1">logSlowOperation(time.Since(start), "Rename", mc.coll.Name(), "newName=", newName)
        return nil</span>
}

// FindToArray executes a find operation and returns the results as a slice of bson.M
func (mc *MongoCollection) FindToArray(ctx context.Context, filter interface{}, opts ...interface{}) ([]bson.M, error) <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov8" title="1">start := time.Now()
        queryOptions := applyOptions(opts)
        findOptions := options.Find()
        if queryOptions != nil </span><span class="cov8" title="1">{
                if queryOptions.Projection != nil </span><span class="cov8" title="1">{
                        findOptions.SetProjection(queryOptions.Projection)
                }</span>
                <span class="cov8" title="1">if queryOptions.Sort != nil </span><span class="cov8" title="1">{
                        findOptions.SetSort(queryOptions.Sort)
                }</span>
                <span class="cov8" title="1">if queryOptions.Limit &gt; 0 </span><span class="cov0" title="0">{
                        findOptions.SetLimit(queryOptions.Limit)
                }</span>
                <span class="cov8" title="1">if queryOptions.Skip &gt; 0 </span><span class="cov0" title="0">{
                        findOptions.SetSkip(queryOptions.Skip)
                }</span>
        }

        <span class="cov8" title="1">cursor, err := mc.coll.Find(ctx, filter, findOptions)
        if err != nil </span><span class="cov8" title="1">{
                golog.Error("failed to find in FindToArray", "error", err)
                return nil, err
        }</span>
        // Ensure cursor is closed after function returns to prevent memory leaks
        <span class="cov8" title="1">defer func() </span><span class="cov8" title="1">{
                if closeErr := cursor.Close(ctx); closeErr != nil </span><span class="cov0" title="0">{
                        golog.Error("error closing cursor", "error", closeErr)
                }</span>
        }()

        <span class="cov8" title="1">var results []bson.M
        // cursor.All() is Go's implementation of MongoDB's cursor.toArray()
        // It iterates through all documents and loads them into the results slice
        err = cursor.All(ctx, &amp;results)
        logSlowOperation(time.Since(start), "FindToArray", mc.coll.Name(), filter)
        return results, err</span>
}

func (mc *MongoCollection) InsertMany(ctx context.Context, documents []interface{}, opts ...*options.InsertManyOptions) (*mongo.InsertManyResult, error) <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov8" title="1">start := time.Now()
        // Add timestamps to each document
        for i := range documents </span><span class="cov8" title="1">{
                var err error
                documents[i], err = addTimestamps(documents[i], false, false)
                if err != nil </span><span class="cov0" title="0">{
                        golog.Error("failed to add timestamps to InsertMany", "error", err)
                        return nil, err
                }</span>
        }
        <span class="cov8" title="1">result, err := mc.coll.InsertMany(ctx, documents, opts...)
        logSlowOperation(time.Since(start), "InsertMany", mc.coll.Name(), documents)
        return result, err</span>
}

func (mc *MongoCollection) Drop(ctx context.Context) error <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov8" title="1">start := time.Now()
        err := mc.coll.Drop(ctx)
        logSlowOperation(time.Since(start), "Drop", mc.coll.Name())
        return err</span>
}

func (mc *MongoCollection) BulkWrite(ctx context.Context, models []mongo.WriteModel, opts ...*options.BulkWriteOptions) (*mongo.BulkWriteResult, error) <span class="cov8" title="1">{
        if mc == nil || mc.coll == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("collection is not initialized")
        }</span>
        <span class="cov8" title="1">start := time.Now()
        result, err := mc.coll.BulkWrite(ctx, models, opts...)
        logSlowOperation(time.Since(start), "BulkWrite", mc.coll.Name(), models)
        return result, err</span>
}

// ToBSONDoc converts a struct to a BSON document using struct tags.
// It marshals and unmarshals the struct to ensure BSON tags are properly applied.
func ToBSONDoc(v interface{}) (bson.M, error) <span class="cov8" title="1">{
        data, err := bson.Marshal(v)
        if err != nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("failed to marshal document: %v", err)
        }</span>

        <span class="cov8" title="1">var updateDoc bson.M
        if err := bson.Unmarshal(data, &amp;updateDoc); err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to unmarshal document: %v", err)
        }</span>

        <span class="cov8" title="1">return updateDoc, nil</span>
}

// https://pkg.go.dev/go.mongodb.org/mongo-driver/v2/mongo/options#LogSink
func (sink *mongoLogSink) Info(level int, message string, keysAndValues ...interface{}) <span class="cov8" title="1">{
        golog.Verbose(message, keysAndValues...)
}</span>
func (sink *mongoLogSink) Error(err error, message string, keysAndValues ...interface{}) <span class="cov8" title="1">{
        golog.Error(message, append(keysAndValues, "error", err)...)
}</span>

func ListCollections(dbName string) ([]string, error) <span class="cov8" title="1">{
        db := Database(dbName)
        if db == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("database not found")
        }</span>
        <span class="cov8" title="1">collections, err := db.ListCollectionNames(context.Background(), bson.M{})
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov8" title="1">return collections, nil</span>
}

// ListCollectionNames returns a list of collection names in the database
func (md *mongoDatabase) ListCollectionNames(ctx context.Context, filter interface{}, opts ...*options.ListCollectionsOptions) ([]string, error) <span class="cov8" title="1">{
        if md == nil || md.db == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("database is not initialized")
        }</span>
        <span class="cov8" title="1">if err := Database(md.db.Name()); err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("database not found")
        }</span>
        <span class="cov8" title="1">return md.db.ListCollectionNames(ctx, filter, opts...)</span>
}

func (md *mongoDatabase) CreateCollection(ctx context.Context, collectionName string, opts ...*options.CreateCollectionOptions) error <span class="cov8" title="1">{
        if md == nil || md.db == nil </span><span class="cov8" title="1">{
                return fmt.Errorf("database is not initialized")
        }</span>
        <span class="cov8" title="1">if err := Database(md.db.Name()); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("database not found")
        }</span>
        <span class="cov8" title="1">collections, err := md.ListCollectionNames(ctx, bson.M{})
        if err != nil </span><span class="cov8" title="1">{
                return err
        }</span>
        <span class="cov8" title="1">if !contains(collections, collectionName) </span><span class="cov8" title="1">{
                return md.db.CreateCollection(ctx, collectionName, opts...)
        }</span>
        <span class="cov8" title="1">return nil</span>
}

// StartSession starts a new MongoDB session
func StartSession(dbName string) (*MongoSession, error) <span class="cov8" title="1">{
        cfg := dbConfigs[dbName]
        if cfg.client == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("database configuration not properly initialized for dbName: %s", dbName)
        }</span>

        <span class="cov8" title="1">session, err := cfg.client.StartSession()
        if err != nil </span><span class="cov0" title="0">{
                golog.Error("failed to start session", "dbName", dbName, "error", err)
                return nil, err
        }</span>

        <span class="cov8" title="1">ctx := context.Background()
        return &amp;MongoSession{
                session: session,
                ctx:     ctx,
        }, nil</span>
}

// StartSessionWithContext starts a new MongoDB session with a specific context
func StartSessionWithContext(ctx context.Context, dbName string) (*MongoSession, error) <span class="cov8" title="1">{
        if ctx == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("context cannot be nil")
        }</span>
        <span class="cov8" title="1">if dbName == "" </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("database name cannot be empty")
        }</span>
        <span class="cov8" title="1">cfg := dbConfigs[dbName]
        if cfg.client == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("database configuration not properly initialized for dbName: %s", dbName)
        }</span>

        <span class="cov8" title="1">session, err := cfg.client.StartSession()
        if err != nil </span><span class="cov0" title="0">{
                golog.Error("failed to start session", "dbName", dbName, "error", err)
                return nil, err
        }</span>

        <span class="cov8" title="1">return &amp;MongoSession{
                session: session,
                ctx:     ctx,
        }, nil</span>
}

// StartTransaction starts a new transaction within the session
func (ms *MongoSession) StartTransaction(opts ...*TransactionOptions) (*MongoTransaction, error) <span class="cov8" title="1">{
        if ms == nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("session is nil")
        }</span>
        <span class="cov8" title="1">if ms.session == nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("session is not initialized")
        }</span>

        <span class="cov8" title="1">var sessionOpts *options.TransactionOptions
        if len(opts) &gt; 0 &amp;&amp; opts[0] != nil </span><span class="cov8" title="1">{
                sessionOpts = options.Transaction()
                if opts[0].ReadConcern != nil </span><span class="cov8" title="1">{
                        sessionOpts.SetReadConcern(opts[0].ReadConcern)
                }</span>
                <span class="cov8" title="1">if opts[0].WriteConcern != nil </span><span class="cov8" title="1">{
                        sessionOpts.SetWriteConcern(opts[0].WriteConcern)
                }</span>
        }

        <span class="cov8" title="1">err := ms.session.StartTransaction(sessionOpts)
        if err != nil </span><span class="cov0" title="0">{
                golog.Error("failed to start transaction", "error", err)
                return nil, err
        }</span>

        <span class="cov8" title="1">return &amp;MongoTransaction{
                session:   ms.session,
                ctx:       ms.ctx,
                started:   true,
                committed: false,
                aborted:   false,
        }, nil</span>
}

// CommitTransaction commits the transaction
func (mt *MongoTransaction) CommitTransaction() error <span class="cov8" title="1">{
        if mt == nil || mt.session == nil </span><span class="cov8" title="1">{
                return fmt.Errorf("transaction is not initialized")
        }</span>
        <span class="cov8" title="1">if !mt.started </span><span class="cov8" title="1">{
                return fmt.Errorf("transaction not started")
        }</span>
        <span class="cov8" title="1">if mt.committed </span><span class="cov8" title="1">{
                return fmt.Errorf("transaction already committed")
        }</span>
        <span class="cov8" title="1">if mt.aborted </span><span class="cov8" title="1">{
                return fmt.Errorf("transaction already aborted")
        }</span>

        <span class="cov8" title="1">err := mt.session.CommitTransaction(mt.ctx)
        if err != nil </span><span class="cov0" title="0">{
                golog.Error("failed to commit transaction", "error", err)
                return err
        }</span>

        <span class="cov8" title="1">mt.committed = true
        golog.Debug("transaction committed successfully")
        return nil</span>
}

// AbortTransaction aborts the transaction
func (mt *MongoTransaction) AbortTransaction() error <span class="cov8" title="1">{
        if mt == nil || mt.session == nil </span><span class="cov8" title="1">{
                return fmt.Errorf("transaction is not initialized")
        }</span>
        <span class="cov8" title="1">if !mt.started </span><span class="cov8" title="1">{
                return fmt.Errorf("transaction not started")
        }</span>
        <span class="cov8" title="1">if mt.committed </span><span class="cov8" title="1">{
                return fmt.Errorf("transaction already committed")
        }</span>
        <span class="cov8" title="1">if mt.aborted </span><span class="cov8" title="1">{
                return fmt.Errorf("transaction already aborted")
        }</span>

        <span class="cov8" title="1">err := mt.session.AbortTransaction(mt.ctx)
        if err != nil </span><span class="cov0" title="0">{
                golog.Error("failed to abort transaction", "error", err)
                return err
        }</span>

        <span class="cov8" title="1">mt.aborted = true
        golog.Debug("transaction aborted successfully")
        return nil</span>
}

// EndSession ends the session
func (ms *MongoSession) EndSession() <span class="cov8" title="1">{
        if ms != nil &amp;&amp; ms.session != nil </span><span class="cov8" title="1">{
                ms.session.EndSession(ms.ctx)
        }</span>
}

// WithTransaction executes a function within a transaction
func (ms *MongoSession) WithTransaction(fn func(mongo.SessionContext) error, opts ...*TransactionOptions) error <span class="cov8" title="1">{
        if ms == nil </span><span class="cov8" title="1">{
                return fmt.Errorf("session is nil")
        }</span>
        <span class="cov8" title="1">if ms.session == nil </span><span class="cov0" title="0">{
                return fmt.Errorf("session is not initialized")
        }</span>

        <span class="cov8" title="1">var sessionOpts *options.TransactionOptions
        if len(opts) &gt; 0 &amp;&amp; opts[0] != nil </span><span class="cov8" title="1">{
                sessionOpts = options.Transaction()
                if opts[0].ReadConcern != nil </span><span class="cov8" title="1">{
                        sessionOpts.SetReadConcern(opts[0].ReadConcern)
                }</span>
                <span class="cov8" title="1">if opts[0].WriteConcern != nil </span><span class="cov8" title="1">{
                        sessionOpts.SetWriteConcern(opts[0].WriteConcern)
                }</span>
        }

        // Start transaction manually
        <span class="cov8" title="1">err := ms.session.StartTransaction(sessionOpts)
        if err != nil </span><span class="cov0" title="0">{
                golog.Error("failed to start transaction", "error", err)
                return err
        }</span>

        // Execute the function
        <span class="cov8" title="1">sessCtx := mongo.NewSessionContext(ms.ctx, ms.session)
        err = fn(sessCtx)
        if err != nil </span><span class="cov8" title="1">{
                // Abort transaction on error
                if abortErr := ms.session.AbortTransaction(ms.ctx); abortErr != nil </span><span class="cov0" title="0">{
                        golog.Error("failed to abort transaction", "error", abortErr)
                }</span>
                <span class="cov8" title="1">return err</span>
        }

        // Commit transaction
        <span class="cov8" title="1">err = ms.session.CommitTransaction(ms.ctx)
        if err != nil </span><span class="cov0" title="0">{
                golog.Error("failed to commit transaction", "error", err)
                return err
        }</span>

        <span class="cov8" title="1">golog.Debug("transaction executed successfully")
        return nil</span>
}

// GetSessionContext returns the session context for use in operations
func (ms *MongoSession) GetSessionContext() mongo.SessionContext <span class="cov8" title="1">{
        if ms == nil || ms.session == nil </span><span class="cov8" title="1">{
                return nil
        }</span>
        <span class="cov8" title="1">return mongo.NewSessionContext(ms.ctx, ms.session)</span>
}

// GetTransactionContext returns the transaction context for use in operations
func (mt *MongoTransaction) GetSessionContext() mongo.SessionContext <span class="cov8" title="1">{
        if mt == nil || mt.session == nil </span><span class="cov8" title="1">{
                return nil
        }</span>
        <span class="cov8" title="1">return mongo.NewSessionContext(mt.ctx, mt.session)</span>
}
</pre>
		
		<pre class="file" id="file1" style="display: none">package gomongo

import (
        "context"
        "fmt"
        "time"

        "go.mongodb.org/mongo-driver/bson"
        "go.mongodb.org/mongo-driver/mongo"
        "go.mongodb.org/mongo-driver/mongo/readconcern"
        "go.mongodb.org/mongo-driver/mongo/writeconcern"
)

// ExampleTransaction demonstrates basic transaction usage
func ExampleTransaction() error <span class="cov0" title="0">{
        // Start a session
        session, err := StartSession("your_db_name")
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to start session: %v", err)
        }</span>
        <span class="cov0" title="0">defer session.EndSession()

        // Start a transaction
        txn, err := session.StartTransaction()
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to start transaction: %v", err)
        }</span>

        // Get session context for operations
        <span class="cov0" title="0">sessCtx := txn.GetSessionContext()

        // Perform operations within transaction
        usersColl := Coll("your_db_name", "users")
        accountsColl := Coll("your_db_name", "accounts")

        // Insert a user
        userResult, err := usersColl.InsertOne(sessCtx, bson.M{
                "name":  "John Doe",
                "email": "<EMAIL>",
        })
        if err != nil </span><span class="cov0" title="0">{
                // Abort transaction on error
                if abortErr := txn.AbortTransaction(); abortErr != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to abort transaction: %v", abortErr)
                }</span>
                <span class="cov0" title="0">return fmt.Errorf("failed to insert user: %v", err)</span>
        }

        // Insert an account for the user
        <span class="cov0" title="0">accountResult, err := accountsColl.InsertOne(sessCtx, bson.M{
                "userId":  userResult.InsertedID,
                "balance": 1000,
                "type":    "savings",
        })
        if err != nil </span><span class="cov0" title="0">{
                // Abort transaction on error
                if abortErr := txn.AbortTransaction(); abortErr != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to abort transaction: %v", abortErr)
                }</span>
                <span class="cov0" title="0">return fmt.Errorf("failed to insert account: %v", err)</span>
        }

        // Commit the transaction
        <span class="cov0" title="0">err = txn.CommitTransaction()
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to commit transaction: %v", err)
        }</span>

        <span class="cov0" title="0">fmt.Printf("Transaction completed successfully. User ID: %v, Account ID: %v\n",
                userResult.InsertedID, accountResult.InsertedID)
        return nil</span>
}

// ExampleWithTransaction demonstrates using WithTransaction helper
func ExampleWithTransaction() error <span class="cov0" title="0">{
        // Start a session
        session, err := StartSession("your_db_name")
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to start session: %v", err)
        }</span>
        <span class="cov0" title="0">defer session.EndSession()

        // Define transaction options
        txnOpts := &amp;TransactionOptions{
                ReadConcern:  readconcern.Snapshot(),
                WriteConcern: writeconcern.Majority(),
        }

        // Execute transaction using WithTransaction helper
        err = session.WithTransaction(func(sessCtx mongo.SessionContext) error </span><span class="cov0" title="0">{
                usersColl := Coll("your_db_name", "users")
                accountsColl := Coll("your_db_name", "accounts")

                // Insert a user
                userResult, err := usersColl.InsertOne(sessCtx, bson.M{
                        "name":  "Jane Doe",
                        "email": "<EMAIL>",
                })
                if err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to insert user: %v", err)
                }</span>

                // Insert an account for the user
                <span class="cov0" title="0">_, err = accountsColl.InsertOne(sessCtx, bson.M{
                        "userId":  userResult.InsertedID,
                        "balance": 2000,
                        "type":    "checking",
                })
                if err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to insert account: %v", err)
                }</span>

                <span class="cov0" title="0">return nil</span>
        }, txnOpts)

        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("transaction failed: %v", err)
        }</span>

        <span class="cov0" title="0">fmt.Println("Transaction completed successfully using WithTransaction helper")
        return nil</span>
}

// ExampleTransferMoney demonstrates a money transfer transaction
func ExampleTransferMoney(fromUserId, toUserId interface{}, amount float64) error <span class="cov0" title="0">{
        session, err := StartSession("your_db_name")
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to start session: %v", err)
        }</span>
        <span class="cov0" title="0">defer session.EndSession()

        err = session.WithTransaction(func(sessCtx mongo.SessionContext) error </span><span class="cov0" title="0">{
                accountsColl := Coll("your_db_name", "accounts")

                // Deduct from source account
                result, err := accountsColl.UpdateOne(
                        sessCtx,
                        bson.M{"userId": fromUserId},
                        bson.M{"$inc": bson.M{"balance": -amount}},
                )
                if err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to deduct from source account: %v", err)
                }</span>
                <span class="cov0" title="0">if result.ModifiedCount == 0 </span><span class="cov0" title="0">{
                        return fmt.Errorf("source account not found or insufficient balance")
                }</span>

                // Add to destination account
                <span class="cov0" title="0">result, err = accountsColl.UpdateOne(
                        sessCtx,
                        bson.M{"userId": toUserId},
                        bson.M{"$inc": bson.M{"balance": amount}},
                )
                if err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to add to destination account: %v", err)
                }</span>
                <span class="cov0" title="0">if result.ModifiedCount == 0 </span><span class="cov0" title="0">{
                        return fmt.Errorf("destination account not found")
                }</span>

                // Log the transfer
                <span class="cov0" title="0">transfersColl := Coll("your_db_name", "transfers")
                _, err = transfersColl.InsertOne(sessCtx, bson.M{
                        "fromUserId": fromUserId,
                        "toUserId":   toUserId,
                        "amount":     amount,
                        "timestamp":  time.Now(),
                })
                if err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to log transfer: %v", err)
                }</span>

                <span class="cov0" title="0">return nil</span>
        })

        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("money transfer failed: %v", err)
        }</span>

        <span class="cov0" title="0">fmt.Printf("Money transfer completed successfully: %.2f from %v to %v\n",
                amount, fromUserId, toUserId)
        return nil</span>
}

// ExampleContextWithTimeout demonstrates using context with timeout
func ExampleContextWithTimeout() error <span class="cov0" title="0">{
        // Create context with timeout
        ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
        defer cancel()

        // Start session with context
        session, err := StartSessionWithContext(ctx, "your_db_name")
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to start session: %v", err)
        }</span>
        <span class="cov0" title="0">defer session.EndSession()

        // Execute transaction with timeout
        err = session.WithTransaction(func(sessCtx mongo.SessionContext) error </span><span class="cov0" title="0">{
                // Your transaction operations here
                usersColl := Coll("your_db_name", "users")
                _, err := usersColl.InsertOne(sessCtx, bson.M{
                        "name":  "Timeout Test User",
                        "email": "<EMAIL>",
                })
                return err
        }</span>)

        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("transaction with timeout failed: %v", err)
        }</span>

        <span class="cov0" title="0">fmt.Println("Transaction with timeout completed successfully")
        return nil</span>
}
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
