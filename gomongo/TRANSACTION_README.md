# MongoDB Transaction Support

This document describes the transaction functionality added to the gomongo package, providing ACID compliance for multi-document operations.

## Overview

MongoDB transactions allow you to group multiple operations into a single atomic unit. All operations within a transaction either succeed together or fail together, ensuring data consistency.

## Features

- **Session Management**: Create and manage MongoDB sessions
- **Transaction Control**: Start, commit, and abort transactions
- **Helper Functions**: Convenient `WithTransaction` helper for automatic transaction management
- **Context Support**: Full context support for timeouts and cancellation
- **Error Handling**: Comprehensive error handling with automatic rollback
- **Logging**: Integrated logging for transaction operations

## Types

### MongoSession
Represents a MongoDB session for transaction management.

```go
type MongoSession struct {
    session mongo.Session
    ctx     context.Context
}
```

### MongoTransaction
Represents a MongoDB transaction with state tracking.

```go
type MongoTransaction struct {
    session    mongo.Session
    ctx        context.Context
    started    bool
    committed  bool
    aborted    bool
}
```

### TransactionOptions
Defines options for starting a transaction.

```go
type TransactionOptions struct {
    ReadConcern   *readconcern.ReadConcern
    WriteConcern  *writeconcern.WriteConcern
}
```

## API Reference

### Session Management

#### StartSession
Creates a new MongoDB session.

```go
func StartSession(dbName string) (*MongoSession, error)
```

**Parameters:**
- `dbName`: Name of the database configuration

**Returns:**
- `*MongoSession`: Session instance
- `error`: Error if session creation fails

#### StartSessionWithContext
Creates a new MongoDB session with a specific context.

```go
func StartSessionWithContext(ctx context.Context, dbName string) (*MongoSession, error)
```

**Parameters:**
- `ctx`: Context for the session
- `dbName`: Name of the database configuration

**Returns:**
- `*MongoSession`: Session instance
- `error`: Error if session creation fails

#### EndSession
Ends the session and releases resources.

```go
func (ms *MongoSession) EndSession()
```

### Transaction Management

#### StartTransaction
Starts a new transaction within the session.

```go
func (ms *MongoSession) StartTransaction(opts ...*TransactionOptions) (*MongoTransaction, error)
```

**Parameters:**
- `opts`: Optional transaction options (read concern, write concern)

**Returns:**
- `*MongoTransaction`: Transaction instance
- `error`: Error if transaction start fails

#### CommitTransaction
Commits the transaction.

```go
func (mt *MongoTransaction) CommitTransaction() error
```

**Returns:**
- `error`: Error if commit fails

#### AbortTransaction
Aborts the transaction.

```go
func (mt *MongoTransaction) AbortTransaction() error
```

**Returns:**
- `error`: Error if abort fails

#### WithTransaction
Executes a function within a transaction with automatic commit/abort handling.

```go
func (ms *MongoSession) WithTransaction(fn func(mongo.SessionContext) error, opts ...*TransactionOptions) error
```

**Parameters:**
- `fn`: Function to execute within the transaction
- `opts`: Optional transaction options

**Returns:**
- `error`: Error if transaction fails

### Context Management

#### GetSessionContext
Returns the session context for use in operations.

```go
func (ms *MongoSession) GetSessionContext() mongo.SessionContext
```

#### GetTransactionContext
Returns the transaction context for use in operations.

```go
func (mt *MongoTransaction) GetSessionContext() mongo.SessionContext
```

## Usage Examples

### Basic Transaction

```go
// Start a session
session, err := gomongo.StartSession("your_db_name")
if err != nil {
    return err
}
defer session.EndSession()

// Start a transaction
txn, err := session.StartTransaction()
if err != nil {
    return err
}

// Get session context for operations
sessCtx := txn.GetSessionContext()

// Perform operations within transaction
usersColl := gomongo.Coll("your_db_name", "users")
accountsColl := gomongo.Coll("your_db_name", "accounts")

// Insert a user
userResult, err := usersColl.InsertOne(sessCtx, bson.M{
    "name":  "John Doe",
    "email": "<EMAIL>",
})
if err != nil {
    // Abort transaction on error
    txn.AbortTransaction()
    return err
}

// Insert an account for the user
_, err = accountsColl.InsertOne(sessCtx, bson.M{
    "userId": userResult.InsertedID,
    "balance": 1000,
    "type":   "savings",
})
if err != nil {
    // Abort transaction on error
    txn.AbortTransaction()
    return err
}

// Commit the transaction
err = txn.CommitTransaction()
if err != nil {
    return err
}
```

### Using WithTransaction Helper

```go
// Start a session
session, err := gomongo.StartSession("your_db_name")
if err != nil {
    return err
}
defer session.EndSession()

// Define transaction options
txnOpts := &gomongo.TransactionOptions{
    ReadConcern:  readconcern.Snapshot(),
    WriteConcern: writeconcern.Majority(),
}

// Execute transaction using WithTransaction helper
err = session.WithTransaction(func(sessCtx mongo.SessionContext) error {
    usersColl := gomongo.Coll("your_db_name", "users")
    accountsColl := gomongo.Coll("your_db_name", "accounts")

    // Insert a user
    userResult, err := usersColl.InsertOne(sessCtx, bson.M{
        "name":  "Jane Doe",
        "email": "<EMAIL>",
    })
    if err != nil {
        return err
    }

    // Insert an account for the user
    _, err = accountsColl.InsertOne(sessCtx, bson.M{
        "userId": userResult.InsertedID,
        "balance": 2000,
        "type":   "checking",
    })
    return err
}, txnOpts)

if err != nil {
    return err
}
```

### Money Transfer Example

```go
func TransferMoney(fromUserId, toUserId interface{}, amount float64) error {
    session, err := gomongo.StartSession("your_db_name")
    if err != nil {
        return err
    }
    defer session.EndSession()

    return session.WithTransaction(func(sessCtx mongo.SessionContext) error {
        accountsColl := gomongo.Coll("your_db_name", "accounts")

        // Deduct from source account
        result, err := accountsColl.UpdateOne(
            sessCtx,
            bson.M{"userId": fromUserId},
            bson.M{"$inc": bson.M{"balance": -amount}},
        )
        if err != nil || result.ModifiedCount == 0 {
            return fmt.Errorf("failed to deduct from source account")
        }

        // Add to destination account
        result, err = accountsColl.UpdateOne(
            sessCtx,
            bson.M{"userId": toUserId},
            bson.M{"$inc": bson.M{"balance": amount}},
        )
        if err != nil || result.ModifiedCount == 0 {
            return fmt.Errorf("failed to add to destination account")
        }

        // Log the transfer
        transfersColl := gomongo.Coll("your_db_name", "transfers")
        _, err = transfersColl.InsertOne(sessCtx, bson.M{
            "fromUserId": fromUserId,
            "toUserId":   toUserId,
            "amount":     amount,
            "timestamp":  time.Now(),
        })
        return err
    })
}
```

### Context with Timeout

```go
// Create context with timeout
ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
defer cancel()

// Start session with context
session, err := gomongo.StartSessionWithContext(ctx, "your_db_name")
if err != nil {
    return err
}
defer session.EndSession()

// Execute transaction with timeout
err = session.WithTransaction(func(sessCtx mongo.SessionContext) error {
    // Your transaction operations here
    usersColl := gomongo.Coll("your_db_name", "users")
    _, err := usersColl.InsertOne(sessCtx, bson.M{
        "name":  "Timeout Test User",
        "email": "<EMAIL>",
    })
    return err
})
```

## Best Practices

### 1. Always Use defer for Session Cleanup
```go
session, err := gomongo.StartSession("your_db_name")
if err != nil {
    return err
}
defer session.EndSession() // Always defer session cleanup
```

### 2. Use WithTransaction for Simple Cases
The `WithTransaction` helper automatically handles commit/abort logic, making it safer and easier to use for most cases.

### 3. Handle Errors Properly
Always check for errors and abort transactions when operations fail:

```go
if err != nil {
    if abortErr := txn.AbortTransaction(); abortErr != nil {
        // Log abort error but return original error
        golog.Error("failed to abort transaction", "error", abortErr)
    }
    return err
}
```

### 4. Use Appropriate Read/Write Concerns
Choose read and write concerns based on your consistency requirements:

```go
txnOpts := &gomongo.TransactionOptions{
    ReadConcern:  readconcern.Snapshot(),    // For consistent reads
    WriteConcern: writeconcern.Majority(),   // For durability
}
```

### 5. Keep Transactions Short
Long-running transactions can impact performance and increase the risk of conflicts. Keep transaction operations focused and efficient.

### 6. Use Context for Timeouts
Always use context with appropriate timeouts to prevent hanging operations:

```go
ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
defer cancel()
session, err := gomongo.StartSessionWithContext(ctx, "your_db_name")
```

## Error Handling

The transaction system provides comprehensive error handling:

- **Session Creation Errors**: Return descriptive errors for configuration issues
- **Transaction Start Errors**: Handle cases where transactions cannot be started
- **Operation Errors**: Automatically abort transactions on operation failures
- **Commit/Abort Errors**: Log and handle transaction finalization errors

## Logging

All transaction operations are logged with appropriate levels:

- **Debug**: Successful operations and commits
- **Info**: Session creation and transaction start
- **Warn**: Transaction aborts and retries
- **Error**: Operation failures and system errors

## Limitations

1. **MongoDB Version**: Transactions require MongoDB 4.0+ for replica sets and 4.2+ for sharded clusters
2. **Collection Requirements**: Collections must exist before starting transactions
3. **Operation Restrictions**: Some operations (like creating/dropping collections) cannot be performed within transactions
4. **Size Limits**: Transactions have size limits (16MB for the total size of all operations)

## Troubleshooting

### Common Issues

1. **"Transaction numbers are only allowed on a replica set member or mongos"**
   - Ensure you're connected to a replica set or mongos
   - Check MongoDB version compatibility

2. **"Transaction aborted"**
   - Check for conflicting operations
   - Verify collection existence
   - Review operation size limits

3. **"Session expired"**
   - Sessions have time limits
   - Use shorter timeouts or refresh sessions

### Debug Mode

Enable verbose logging to debug transaction issues:

```go
// In your configuration
[dbs]
verbose = 3
```

This will log detailed information about transaction operations, including timing and operation details. 