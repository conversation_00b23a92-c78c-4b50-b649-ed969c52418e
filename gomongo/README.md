# go-mongo

A Go language MongoDB API wrapper that provides a simple and efficient way to interact with MongoDB databases.

## Features

- Simple and intuitive API for MongoDB operations
- Automatic timestamp management for documents
- Configurable database connections
- Predefined collection support
- Comprehensive CRUD operations
- Support for MongoDB aggregation and change streams
- Built-in logging and performance monitoring

## Installation

```bash
go get github.com/real-rm/gomongo
```

## Configuration

The package uses a configuration system that supports TOML format. You need to configure your MongoDB connections and predefined collections in your config file:

```toml
[dbs]
verbose = 1  # Logging verbosity level
[dbs.mydb]
uri = "mongodb://localhost:27017/mydatabase"


```

## Usage

### Initialization

```go
import "github.com/real-rm/gomongo"

func main() {
    err := gomongo.InitMongoDB()
    if err != nil {
        log.Fatal(err)
    }
}
```

### Basic Operations

#### Get a Collection

```go
// Get a collection directly
collection := gomongo.Coll("mydb", "users")

// Or get a database first
db := gomongo.Database("mydb")
collection := db.Coll("users")
```

#### Insert Documents

```go
ctx := context.Background()
doc := bson.M{"name": "John", "age": 30}
result, err := collection.InsertOne(ctx, doc)
```

#### Find Documents

```go
// Find with options
opts := []interface{}{
    gomongo.QueryOptions{
        Projection: bson.M{"name": 1},
        Sort:      bson.M{"age": -1},
        Limit:     10,
        Skip:      0,
    },
}
cursor, err := collection.Find(ctx, bson.M{"age": bson.M{"$gt": 18}}, opts...)
```

#### Update Documents

```go
filter := bson.M{"name": "John"}
update := bson.M{"$set": bson.M{"age": 31}}
result, err := collection.UpdateOne(ctx, filter, update)
```

#### Delete Documents

```go
filter := bson.M{"name": "John"}
result, err := collection.DeleteOne(ctx, filter)
```

## API Reference

### Collection Methods

- `InsertOne(ctx context.Context, document interface{}, opts ...*options.InsertOneOptions)`
- `InsertMany(ctx context.Context, documents []interface{}, opts ...*options.InsertManyOptions)`
- `Find(ctx context.Context, filter interface{}, opts ...interface{})`
- `FindOne(ctx context.Context, filter interface{}, opts ...interface{})`
- `UpdateOne(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions)`
- `UpdateMany(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions)`
- `DeleteOne(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions)`
- `DeleteMany(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions)`
- `FindOneAndUpdate(ctx context.Context, filter interface{}, update interface{}, opts ...*options.FindOneAndUpdateOptions)`
- `ReplaceOne(ctx context.Context, filter interface{}, replacement interface{}, opts ...*options.ReplaceOptions)`
- `FindOneAndReplace(ctx context.Context, filter interface{}, replacement interface{}, opts ...*options.FindOneAndReplaceOptions)`
- `Distinct(ctx context.Context, fieldName string, filter interface{}, opts ...*options.DistinctOptions)`
- `CountDocuments(ctx context.Context, filter interface{}, opts ...*options.CountOptions)`
- `EstimatedDocumentCount(ctx context.Context, opts ...*options.EstimatedDocumentCountOptions)`
- `CreateIndex(ctx context.Context, model mongo.IndexModel, opts ...*options.CreateIndexesOptions)`
- `CreateIndexes(ctx context.Context, models []mongo.IndexModel, opts ...*options.CreateIndexesOptions)`
- `DropIndex(ctx context.Context, name string, opts ...*options.DropIndexesOptions)`
- `Aggregate(ctx context.Context, pipeline interface{}, opts ...*options.AggregateOptions)`
- `Watch(ctx context.Context, pipeline interface{}, opts ...*options.ChangeStreamOptions)`
- `Rename(ctx context.Context, newName string)`
- `FindToArray(ctx context.Context, filter interface{}, opts ...interface{})`
- `Drop(ctx context.Context)`
- `BulkWrite(ctx context.Context, models []mongo.WriteModel, opts ...*options.BulkWriteOptions)`

### Query Options

```go
type QueryOptions struct {
    Projection interface{}  // Fields to return
    Sort       interface{}  // Sort order
    Limit      int64       // Maximum number of documents to return
    Skip       int64       // Number of documents to skip
}
```

## Automatic Timestamps

The package automatically adds timestamps to documents:
- `_ts`: Creation timestamp
- `_mt`: Last modification timestamp

## Error Handling

All operations return errors that should be checked:

```go
result, err := collection.InsertOne(ctx, doc)
if err != nil {
    log.Printf("Error inserting document: %v", err)
    return
}
```

## Performance Monitoring

The package includes built-in performance monitoring that logs slow operations. The verbosity level can be configured in the database configuration.

## Dependencies

- go.mongodb.org/mongo-driver/mongo
- github.com/real-rm/goconfig
- github.com/real-rm/golog
