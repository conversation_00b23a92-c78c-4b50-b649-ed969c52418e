package gomongo

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	goconfig "github.com/real-rm/goconfig"
	golog "github.com/real-rm/golog"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func setupTestMongoDB(t *testing.T) (cleanup func()) {
	// Save original env var
	originalCfg := os.Getenv("RMBASE_FILE_CFG")

	// Get the absolute path to local.test.ini
	// First get the absolute path of the current test file directory
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}

	// Then construct the path to config file
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}

	t.Logf("Using config file: %s", configPath)

	// Verify config file exists
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		t.Fatalf("Config file not found at %s: %v", configPath, err)
	}

	// Set environment variable to point to test config
	if err := os.Setenv("RMBASE_FILE_CFG", configPath); err != nil {
		t.Fatalf("Failed to set RMBASE_FILE_CFG: %v", err)
	}

	// Add verification of the environment variable
	currentValue := os.Getenv("RMBASE_FILE_CFG")
	t.Logf("RMBASE_FILE_CFG is now set to: %s", currentValue)

	if err := goconfig.LoadConfig(); err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	if err := golog.InitLog(); err != nil {
		t.Fatalf("Failed to initialize logging: %v", err)
	}

	if err := InitMongoDB(); err != nil {
		t.Fatalf("Failed to InitMongoDB: %v", err)
	}

	// Return cleanup function
	return func() {
		if err := os.Setenv("RMBASE_FILE_CFG", originalCfg); err != nil {
			t.Fatalf("Failed to restore RMBASE_FILE_CFG: %v", err)
		}
	}
}

func TestInitMongoDB(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()
}

func cleanupCollection(t *testing.T, coll *MongoCollection) {
	if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
		t.Errorf("Failed to cleanup collection: %v", err)
	}
}

// getCleanCollection returns a clean collection and handles cleanup after test
func getCleanCollection(t *testing.T) *MongoCollection {
	coll := Coll("tmp", "goapp_test")
	cleanupCollection(t, coll)
	t.Cleanup(func() {
		cleanupCollection(t, coll)
	})
	return coll
}

func TestMongoOperations(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("test operations", func(t *testing.T) {
		// Test InsertOne
		t.Run("InsertOne", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()
			doc := bson.M{"test": "value"}

			result, err := coll.InsertOne(ctx, doc)
			if err != nil {
				t.Fatalf("InsertOne failed: %v", err)
			}
			if result.InsertedID == nil {
				t.Fatal("InsertOne didn't return an InsertedID")
			}

			// Verify the document was inserted with timestamps
			var found bson.M
			err = coll.FindOne(ctx, bson.M{"_id": result.InsertedID}).Decode(&found)
			if err != nil {
				t.Errorf("Failed to find inserted document: %v", err)
			}
			if found["test"] != "value" {
				t.Errorf("Wrong value in found document: got %v, want %v", found["test"], "value")
			}
			if found["_ts"] == nil {
				t.Error("_ts field missing from inserted document")
			}
			if found["_mt"] == nil {
				t.Error("_mt field missing from inserted document")
			}
		})

		// Test UpdateOne with noModifyMt
		t.Run("UpdateOne with noModifyMt", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()

			// Insert initial document
			doc := bson.M{
				"_id":   "test123",
				"field": "initial_value",
			}
			_, err := coll.InsertOne(ctx, doc)
			if err != nil {
				t.Fatalf("Failed to insert test document: %v", err)
			}

			// Get the document after insertion to capture initial _mt
			var initial bson.M
			err = coll.FindOne(ctx, bson.M{"_id": "test123"}).Decode(&initial)
			if err != nil {
				t.Fatalf("Failed to find initial document: %v", err)
			}
			initialMt := initial["_mt"]

			// Update with noModifyMt flag
			update := bson.M{
				"noModifyMt": true,
				"$set": bson.M{
					"field": "new_value",
				},
			}

			result, err := coll.UpdateOne(ctx, bson.M{"_id": "test123"}, update)
			if err != nil {
				t.Fatalf("UpdateOne failed: %v", err)
			}
			if result.ModifiedCount != 1 {
				t.Errorf("UpdateOne modified %d documents, want 1", result.ModifiedCount)
			}

			// Verify the update
			var updated bson.M
			err = coll.FindOne(ctx, bson.M{"_id": "test123"}).Decode(&updated)
			if err != nil {
				t.Fatalf("Failed to find updated document: %v", err)
			}

			if updated["field"] != "new_value" {
				t.Errorf("Field not updated, got %v, want %v", updated["field"], "new_value")
			}

			// Verify _mt wasn't modified due to noModifyMt flag
			if updated["_mt"] != initialMt {
				t.Error("_mt field was modified despite noModifyMt flag")
			}
		})

		// Test UpdateOne without noModifyMt
		t.Run("UpdateOne without noModifyMt", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()

			// Test Case 1: noModifyMt not set
			t.Run("noModifyMt not set", func(t *testing.T) {
				// Insert initial document
				doc := bson.M{
					"_id":   "test_default",
					"field": "initial_value",
				}
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}

				// Get the document after insertion to capture initial _mt
				var initial bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "test_default"}).Decode(&initial)
				if err != nil {
					t.Fatalf("Failed to find initial document: %v", err)
				}
				initialMt := initial["_mt"]

				// Small delay to ensure _mt would be different
				time.Sleep(10 * time.Millisecond)

				// Update without noModifyMt flag
				update := bson.M{
					"$set": bson.M{
						"field": "new_value_default",
					},
				}

				result, err := coll.UpdateOne(ctx, bson.M{"_id": "test_default"}, update)
				if err != nil {
					t.Fatalf("UpdateOne failed: %v", err)
				}
				if result.ModifiedCount != 1 {
					t.Errorf("UpdateOne modified %d documents, want 1", result.ModifiedCount)
				}

				// Verify the update
				var updated bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "test_default"}).Decode(&updated)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				if updated["field"] != "new_value_default" {
					t.Errorf("Field not updated, got %v, want %v", updated["field"], "new_value_default")
				}

				// Verify _mt was modified when noModifyMt is not set
				if updated["_mt"] == initialMt {
					t.Error("_mt field was not modified when noModifyMt is not set")
				}
			})

			// Test Case 2: noModifyMt set to false
			t.Run("noModifyMt set to false", func(t *testing.T) {
				// Insert initial document
				doc := bson.M{
					"_id":   "test_false",
					"field": "initial_value",
				}
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}

				// Get the document after insertion to capture initial _mt
				var initial bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "test_false"}).Decode(&initial)
				if err != nil {
					t.Fatalf("Failed to find initial document: %v", err)
				}
				initialMt := initial["_mt"]

				// Small delay to ensure _mt would be different
				time.Sleep(10 * time.Millisecond)

				// Update with noModifyMt explicitly set to false
				update := bson.M{
					"noModifyMt": false,
					"$set": bson.M{
						"field": "new_value_false",
					},
				}

				result, err := coll.UpdateOne(ctx, bson.M{"_id": "test_false"}, update)
				if err != nil {
					t.Fatalf("UpdateOne failed: %v", err)
				}
				if result.ModifiedCount != 1 {
					t.Errorf("UpdateOne modified %d documents, want 1", result.ModifiedCount)
				}

				// Verify the update
				var updated bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "test_false"}).Decode(&updated)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				if updated["field"] != "new_value_false" {
					t.Errorf("Field not updated, got %v, want %v", updated["field"], "new_value_false")
				}

				// Verify _mt was modified when noModifyMt is false
				if updated["_mt"] == initialMt {
					t.Error("_mt field was not modified when noModifyMt is set to false")
				}
			})
		})

		// Test Find with projection and sort
		t.Run("Find with projection and sort", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()

			// Insert test documents
			docs := []interface{}{
				bson.M{"_id": "1", "field": "value2", "extra": "should_not_see"},
				bson.M{"_id": "2", "field": "value1", "extra": "should_not_see"},
			}

			for _, doc := range docs {
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}
			}

			options := QueryOptions{
				Projection: bson.D{{Key: "field", Value: 1}},
				Sort:       bson.D{{Key: "field", Value: 1}},
			}

			cursor, err := coll.Find(ctx, bson.M{}, options)
			if err != nil {
				t.Fatalf("Find failed: %v", err)
			}
			defer func() {
				if err := cursor.Close(ctx); err != nil {
					t.Errorf("Failed to close cursor: %v", err)
				}
			}()

			var results []bson.M
			if err = cursor.All(ctx, &results); err != nil {
				t.Fatalf("Failed to decode results: %v", err)
			}

			if len(results) != 2 {
				t.Errorf("Got %d results, want 2", len(results))
			}

			// Verify sorting and projection
			if results[0]["field"] != "value1" || results[1]["field"] != "value2" {
				t.Error("Results not properly sorted")
			}
			for _, result := range results {
				if result["extra"] != nil {
					t.Error("Projection failed - extra field present")
				}
			}
		})

		// Test UpdateMany with noModifyMt
		t.Run("UpdateMany with noModifyMt", func(t *testing.T) {
			coll := getCleanCollection(t)
			ctx := context.Background()

			// Insert test documents
			docs := []interface{}{
				bson.M{"_id": "update_many_1", "category": "test", "field": "initial_value"},
				bson.M{"_id": "update_many_2", "category": "test", "field": "initial_value"},
				bson.M{"_id": "update_many_3", "category": "other", "field": "initial_value"},
			}

			_, err := coll.InsertMany(ctx, docs)
			if err != nil {
				t.Fatalf("Failed to insert test documents: %v", err)
			}

			// Get initial _mt values for comparison
			var initialDoc1 bson.M
			err = coll.FindOne(ctx, bson.M{"_id": "update_many_1"}).Decode(&initialDoc1)
			if err != nil {
				t.Fatalf("Failed to find initial document: %v", err)
			}
			initialMt1 := initialDoc1["_mt"]

			var initialDoc2 bson.M
			err = coll.FindOne(ctx, bson.M{"_id": "update_many_2"}).Decode(&initialDoc2)
			if err != nil {
				t.Fatalf("Failed to find initial document: %v", err)
			}
			initialMt2 := initialDoc2["_mt"]

			// Small delay to ensure _mt would be different
			time.Sleep(10 * time.Millisecond)

			// Test Case 1: noModifyMt = true
			t.Run("noModifyMt set to true", func(t *testing.T) {
				// Update with noModifyMt flag
				update := bson.M{
					"noModifyMt": true,
					"$set": bson.M{
						"field": "new_value_true",
					},
				}

				result, err := coll.UpdateMany(ctx, bson.M{"category": "test"}, update)
				if err != nil {
					t.Fatalf("UpdateMany failed: %v", err)
				}
				if result.ModifiedCount != 2 {
					t.Errorf("UpdateMany modified %d documents, want 2", result.ModifiedCount)
				}

				// Verify the updates
				var updated1 bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "update_many_1"}).Decode(&updated1)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				if updated1["field"] != "new_value_true" {
					t.Errorf("Field not updated, got %v, want %v", updated1["field"], "new_value_true")
				}

				// Verify _mt wasn't modified due to noModifyMt flag
				if updated1["_mt"] != initialMt1 {
					t.Error("_mt field was modified despite noModifyMt flag")
				}

				var updated2 bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "update_many_2"}).Decode(&updated2)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				if updated2["field"] != "new_value_true" {
					t.Errorf("Field not updated, got %v, want %v", updated2["field"], "new_value_true")
				}

				// Verify _mt wasn't modified due to noModifyMt flag
				if updated2["_mt"] != initialMt2 {
					t.Error("_mt field was modified despite noModifyMt flag")
				}
			})

			// Reset for next tests
			coll = getCleanCollection(t)

			// Re-insert the test documents
			_, err = coll.InsertMany(ctx, docs)
			if err != nil {
				t.Fatalf("Failed to insert test documents: %v", err)
			}

			// Get fresh initial _mt values
			err = coll.FindOne(ctx, bson.M{"_id": "update_many_1"}).Decode(&initialDoc1)
			if err != nil {
				t.Fatalf("Failed to find initial document: %v", err)
			}
			initialMt1 = initialDoc1["_mt"]

			err = coll.FindOne(ctx, bson.M{"_id": "update_many_2"}).Decode(&initialDoc2)
			if err != nil {
				t.Fatalf("Failed to find initial document: %v", err)
			}
			initialMt2 = initialDoc2["_mt"]

			// Small delay to ensure _mt would be different
			time.Sleep(10 * time.Millisecond)

			// Test Case 2: noModifyMt = false
			t.Run("noModifyMt set to false", func(t *testing.T) {
				// Update with noModifyMt explicitly set to false
				update := bson.M{
					"noModifyMt": false,
					"$set": bson.M{
						"field": "new_value_false",
					},
				}

				result, err := coll.UpdateMany(ctx, bson.M{"category": "test"}, update)
				if err != nil {
					t.Fatalf("UpdateMany failed: %v", err)
				}
				if result.ModifiedCount != 2 {
					t.Errorf("UpdateMany modified %d documents, want 2", result.ModifiedCount)
				}

				// Verify the updates
				var updated1 bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "update_many_1"}).Decode(&updated1)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				if updated1["field"] != "new_value_false" {
					t.Errorf("Field not updated, got %v, want %v", updated1["field"], "new_value_false")
				}

				// Verify _mt was modified when noModifyMt is false
				if updated1["_mt"] == initialMt1 {
					t.Error("_mt field was not modified when noModifyMt is set to false")
				}

				var updated2 bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "update_many_2"}).Decode(&updated2)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				if updated2["field"] != "new_value_false" {
					t.Errorf("Field not updated, got %v, want %v", updated2["field"], "new_value_false")
				}

				// Verify _mt was modified when noModifyMt is false
				if updated2["_mt"] == initialMt2 {
					t.Error("_mt field was not modified when noModifyMt is set to false")
				}
			})

			// Reset for next tests
			coll = getCleanCollection(t)

			// Re-insert the test documents
			_, err = coll.InsertMany(ctx, docs)
			if err != nil {
				t.Fatalf("Failed to insert test documents: %v", err)
			}

			// Get fresh initial _mt values
			err = coll.FindOne(ctx, bson.M{"_id": "update_many_1"}).Decode(&initialDoc1)
			if err != nil {
				t.Fatalf("Failed to find initial document: %v", err)
			}
			initialMt1 = initialDoc1["_mt"]

			err = coll.FindOne(ctx, bson.M{"_id": "update_many_2"}).Decode(&initialDoc2)
			if err != nil {
				t.Fatalf("Failed to find initial document: %v", err)
			}
			initialMt2 = initialDoc2["_mt"]

			// Small delay to ensure _mt would be different
			time.Sleep(10 * time.Millisecond)

			// Test Case 3: noModifyMt not set
			t.Run("noModifyMt not set", func(t *testing.T) {
				// Update without noModifyMt flag
				update := bson.M{
					"$set": bson.M{
						"field": "new_value_default",
					},
				}

				result, err := coll.UpdateMany(ctx, bson.M{"category": "test"}, update)
				if err != nil {
					t.Fatalf("UpdateMany failed: %v", err)
				}
				if result.ModifiedCount != 2 {
					t.Errorf("UpdateMany modified %d documents, want 2", result.ModifiedCount)
				}

				// Verify the updates
				var updated1 bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "update_many_1"}).Decode(&updated1)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				if updated1["field"] != "new_value_default" {
					t.Errorf("Field not updated, got %v, want %v", updated1["field"], "new_value_default")
				}

				// Verify _mt was modified when noModifyMt is not set
				if updated1["_mt"] == initialMt1 {
					t.Error("_mt field was not modified when noModifyMt is not set")
				}

				var updated2 bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "update_many_2"}).Decode(&updated2)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				if updated2["field"] != "new_value_default" {
					t.Errorf("Field not updated, got %v, want %v", updated2["field"], "new_value_default")
				}

				// Verify _mt was modified when noModifyMt is not set
				if updated2["_mt"] == initialMt2 {
					t.Error("_mt field was not modified when noModifyMt is not set")
				}
			})
		})

		// Test FindOneAndUpdate with noModifyMt
		t.Run("FindOneAndUpdate with noModifyMt", func(t *testing.T) {
			ctx := context.Background()

			// Test Case 1: noModifyMt = true
			t.Run("noModifyMt set to true", func(t *testing.T) {
				coll := getCleanCollection(t)

				// Insert initial document
				doc := bson.M{
					"_id":   "findupdate_true",
					"field": "initial_value",
				}
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}

				// Get the document after insertion to capture initial _mt
				var initial bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "findupdate_true"}).Decode(&initial)
				if err != nil {
					t.Fatalf("Failed to find initial document: %v", err)
				}
				initialMt := initial["_mt"]

				// Small delay to ensure _mt would be different
				time.Sleep(10 * time.Millisecond)

				// Update with noModifyMt flag
				update := bson.M{
					"noModifyMt": true,
					"$set": bson.M{
						"field": "new_value_true",
					},
				}

				// Using ReturnDocument: After to get the updated document
				opts := options.FindOneAndUpdate().SetReturnDocument(options.After)
				result := coll.FindOneAndUpdate(ctx, bson.M{"_id": "findupdate_true"}, update, opts)

				var updated bson.M
				err = result.Decode(&updated)
				if err != nil {
					t.Fatalf("FindOneAndUpdate failed: %v", err)
				}

				if updated["field"] != "new_value_true" {
					t.Errorf("Field not updated, got %v, want %v", updated["field"], "new_value_true")
				}

				// Verify _mt wasn't modified due to noModifyMt flag
				if updated["_mt"] != initialMt {
					t.Error("_mt field was modified despite noModifyMt flag")
				}
			})

			// Test Case 2: noModifyMt = false
			t.Run("noModifyMt set to false", func(t *testing.T) {
				coll := getCleanCollection(t)

				// Insert initial document
				doc := bson.M{
					"_id":   "findupdate_false",
					"field": "initial_value",
				}
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}

				// Get the document after insertion to capture initial _mt
				var initial bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "findupdate_false"}).Decode(&initial)
				if err != nil {
					t.Fatalf("Failed to find initial document: %v", err)
				}
				initialMt := initial["_mt"]

				// Small delay to ensure _mt would be different
				time.Sleep(10 * time.Millisecond)

				// Update with noModifyMt explicitly set to false
				update := bson.M{
					"noModifyMt": false,
					"$set": bson.M{
						"field": "new_value_false",
					},
				}

				// Using ReturnDocument: After to get the updated document
				opts := options.FindOneAndUpdate().SetReturnDocument(options.After)
				result := coll.FindOneAndUpdate(ctx, bson.M{"_id": "findupdate_false"}, update, opts)

				var updated bson.M
				err = result.Decode(&updated)
				if err != nil {
					t.Fatalf("FindOneAndUpdate failed: %v", err)
				}

				if updated["field"] != "new_value_false" {
					t.Errorf("Field not updated, got %v, want %v", updated["field"], "new_value_false")
				}

				// Verify _mt was modified when noModifyMt is false
				if updated["_mt"] == initialMt {
					t.Error("_mt field was not modified when noModifyMt is set to false")
				}
			})

			// Test Case 3: noModifyMt not set
			t.Run("noModifyMt not set", func(t *testing.T) {
				coll := getCleanCollection(t)

				// Insert initial document
				doc := bson.M{
					"_id":   "findupdate_default",
					"field": "initial_value",
				}
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}

				// Get the document after insertion to capture initial _mt
				var initial bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "findupdate_default"}).Decode(&initial)
				if err != nil {
					t.Fatalf("Failed to find initial document: %v", err)
				}
				initialMt := initial["_mt"]

				// Small delay to ensure _mt would be different
				time.Sleep(10 * time.Millisecond)

				// Update without noModifyMt flag
				update := bson.M{
					"$set": bson.M{
						"field": "new_value_default",
					},
				}

				// Using ReturnDocument: After to get the updated document
				opts := options.FindOneAndUpdate().SetReturnDocument(options.After)
				result := coll.FindOneAndUpdate(ctx, bson.M{"_id": "findupdate_default"}, update, opts)

				var updated bson.M
				err = result.Decode(&updated)
				if err != nil {
					t.Fatalf("FindOneAndUpdate failed: %v", err)
				}

				if updated["field"] != "new_value_default" {
					t.Errorf("Field not updated, got %v, want %v", updated["field"], "new_value_default")
				}

				// Verify _mt was modified when noModifyMt is not set
				if updated["_mt"] == initialMt {
					t.Error("_mt field was not modified when noModifyMt is not set")
				}
			})
		})

		// Test FindToArray
		t.Run("FindToArray", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()

			// Insert test documents
			docs := []interface{}{
				bson.M{"_id": "1", "field": "value2", "status": "active", "extra": "should_not_see"},
				bson.M{"_id": "2", "field": "value1", "status": "active", "extra": "should_not_see"},
				bson.M{"_id": "3", "field": "value3", "status": "inactive", "extra": "should_not_see"},
			}

			for _, doc := range docs {
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}
			}

			// Test FindToArray with QueryOptions
			options := QueryOptions{
				Projection: bson.D{
					{Key: "field", Value: 1},
					{Key: "_ts", Value: 1},
					{Key: "_mt", Value: 1},
				},
				Sort: bson.D{{Key: "field", Value: 1}},
			}

			// Query for only active status documents
			query := bson.M{"status": "active"}
			results, err := coll.FindToArray(ctx, query, options)
			if err != nil {
				t.Fatalf("FindToArray failed: %v", err)
			}

			// Verify results
			if len(results) != 2 {
				t.Errorf("Got %d results, want 2", len(results))
			}

			// Verify sorting and projection
			if results[0]["field"] != "value1" || results[1]["field"] != "value2" {
				t.Error("Results not properly sorted")
			}

			// Verify projection worked
			for _, result := range results {
				if result["extra"] != nil {
					t.Error("Projection failed - extra field present")
				}
				if result["status"] != nil {
					t.Error("Projection failed - status field present")
				}
				// Verify _ts and _mt fields exist
				if result["_ts"] == nil {
					t.Error("_ts field missing from document")
				}
				if result["_mt"] == nil {
					t.Error("_mt field missing from document")
				}
			}
		})

		// Test Find with timeout
		t.Run("FindWithTimeout", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()

			// Insert test documents with larger data to make query slower
			for i := 0; i < 1000; i++ {
				doc := bson.M{
					"_id":  fmt.Sprintf("timeout_test_%d", i),
					"data": strings.Repeat("test_data_", 10000), // Much larger data to slow down query
					"num":  i,
				}
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}
			}

			// Create a context with short timeout
			ctxTimeout, cancel := context.WithTimeout(ctx, 1*time.Millisecond)
			defer cancel()

			// More complex query with sort and projection to make it slower
			opts := QueryOptions{
				Sort: bson.D{
					{Key: "data", Value: 1},
					{Key: "num", Value: -1},
				},
				Projection: bson.D{
					{Key: "data", Value: 1},
					{Key: "num", Value: 1},
				},
			}

			_, err := coll.Find(ctxTimeout, bson.M{}, opts)

			// Verify timeout error
			if err == nil {
				t.Error("Expected timeout error, got nil")
			} else if !strings.Contains(err.Error(), "deadline exceeded") {
				t.Errorf("Expected deadline exceeded error, got: %v", err)
			}
		})

		// Test InsertMany
		t.Run("InsertMany", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()
			docs := []interface{}{
				bson.M{"test": "value1"},
				bson.M{"test": "value2"},
			}

			result, err := coll.InsertMany(ctx, docs)
			if err != nil {
				t.Fatalf("InsertMany failed: %v", err)
			}
			if len(result.InsertedIDs) != len(docs) {
				t.Fatalf("InsertMany inserted %d documents, want %d", len(result.InsertedIDs), len(docs))
			}

			// Verify the documents were inserted with timestamps
			for _, id := range result.InsertedIDs {
				var found bson.M
				err = coll.FindOne(ctx, bson.M{"_id": id}).Decode(&found)
				if err != nil {
					t.Errorf("Failed to find inserted document: %v", err)
				}
				if found["test"] == nil {
					t.Errorf("Wrong value in found document: got %v, want %v", found["test"], "value1 or value2")
				}
				if found["_ts"] == nil {
					t.Error("_ts field missing from inserted document")
				}
				if found["_mt"] == nil {
					t.Error("_mt field missing from inserted document")
				}
			}
		})

		// Test Drop
		t.Run("Drop", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()

			// Insert a test document to ensure the collection exists
			doc := bson.M{"test": "value"}
			_, err := coll.InsertOne(ctx, doc)
			if err != nil {
				t.Fatalf("InsertOne failed: %v", err)
			}

			// Drop the collection
			err = coll.Drop(ctx)
			if err != nil {
				t.Fatalf("Drop failed: %v", err)
			}

			// Attempt to find a document in the dropped collection, should return an error
			err = coll.FindOne(ctx, bson.M{"test": "value"}).Decode(bson.M{})
			if err == nil || !strings.Contains(err.Error(), "mongo: no documents in result") {
				t.Errorf("Expected 'mongo: no documents in result' error after drop, got: %v", err)
			}
		})

		// Test BulkWrite
		t.Run("BulkWrite", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()

			// Define write models
			models := []mongo.WriteModel{
				mongo.NewInsertOneModel().SetDocument(bson.M{"_id": "bulk_insert_1", "field": "value1"}),
				mongo.NewUpdateOneModel().SetFilter(bson.M{"_id": "bulk_insert_1"}).SetUpdate(bson.M{"$set": bson.M{"field": "updated_value1"}}),
				mongo.NewDeleteOneModel().SetFilter(bson.M{"_id": "bulk_insert_1"}),
				mongo.NewInsertOneModel().SetDocument(bson.M{"_id": "bulk_insert_2", "field": "value2"}),
			}

			// Perform BulkWrite
			result, err := coll.BulkWrite(ctx, models)
			if err != nil {
				t.Fatalf("BulkWrite failed: %v", err)
			}

			// Verify results
			if result.InsertedCount != 2 {
				t.Errorf("BulkWrite inserted %d documents, want 2", result.InsertedCount)
			}
			if result.ModifiedCount != 1 {
				t.Errorf("BulkWrite modified %d documents, want 1", result.ModifiedCount)
			}
			if result.DeletedCount != 1 {
				t.Errorf("BulkWrite deleted %d documents, want 1", result.DeletedCount)
			}

			// Verify the document was inserted and updated
			var found bson.M
			err = coll.FindOne(ctx, bson.M{"_id": "bulk_insert_2"}).Decode(&found)
			if err != nil {
				t.Fatalf("Failed to find inserted document: %v", err)
			}
			if found["field"] != "value2" {
				t.Errorf("Wrong value in found document: got %v, want %v", found["field"], "value2")
			}
		})

		// Test Rename
		t.Run("Rename", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()

			// Insert a document before renaming
			testDoc := bson.M{"test_field": "test_value"}
			_, err := coll.InsertOne(ctx, testDoc)
			if err != nil {
				t.Fatalf("InsertOne failed: %v", err)
			}

			// Rename the collection
			err = coll.Rename(ctx, "goapp_test_renamed")
			if err != nil {
				t.Fatalf("Rename failed: %v", err)
			}

			// Verify the new collection exists and has the document
			newColl := Coll("tmp", "goapp_test_renamed")
			var newResult bson.M
			err = newColl.FindOne(ctx, bson.M{}).Decode(&newResult)
			if err != nil {
				t.Fatalf("Failed to find document in renamed collection: %v", err)
			}

			if newResult["test_field"] != "test_value" {
				t.Errorf("Wrong value in found document: got %v, want %v", newResult["test_field"], "test_value")
			}

			// Clean up
			_ = newColl.Drop(ctx)
		})

		// Test FindToArray with filtering
		t.Run("FindToArray with filtering", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()

			// Insert test documents
			docs := []interface{}{
				bson.M{"_id": "1", "field": "value1", "category": "A"},
				bson.M{"_id": "2", "field": "value2", "category": "A"},
				bson.M{"_id": "3", "field": "value3", "category": "B"},
			}

			for _, doc := range docs {
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}
			}

			// Test FindToArray with filter and QueryOptions
			options := QueryOptions{
				Projection: bson.D{
					{Key: "field", Value: 1},
					{Key: "category", Value: 1},
				},
				Sort: bson.D{{Key: "field", Value: 1}},
			}

			// Filter to get only category A documents
			filter := bson.M{"category": "A"}
			results, err := coll.FindToArray(ctx, filter, options)
			if err != nil {
				t.Fatalf("FindToArray failed: %v", err)
			}

			// Verify results count
			if len(results) != 2 {
				t.Errorf("Got %d results, want 2", len(results))
			}

			// Verify filtering and sorting
			if results[0]["field"] != "value1" || results[1]["field"] != "value2" {
				t.Error("Results not properly sorted")
			}

			// Verify all results are from category A
			for _, result := range results {
				if result["category"] != "A" {
					t.Errorf("Got document with category %v, want 'A'", result["category"])
				}
			}
		})

		// Test Name
		t.Run("Name", func(t *testing.T) {
			coll := getCleanCollection(t)

			// Test the collection name
			expectedName := "goapp_test"
			actualName := coll.Name()
			if actualName != expectedName {
				t.Errorf("Collection name mismatch, got %s, want %s", actualName, expectedName)
			}
		})

		// Test DBName
		t.Run("DBName", func(t *testing.T) {
			coll := getCleanCollection(t)

			// Test the database name
			expectedDbName := dbConfigs["tmp"].database
			actualDbName := coll.DBName()
			if actualDbName != expectedDbName {
				t.Errorf("Database name mismatch, got %s, want %s", actualDbName, expectedDbName)
			}
		})
	})
}

func TestCreateCollection(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("Create new collection", func(t *testing.T) {
		db := Database("tmp")
		if db == nil {
			t.Fatal("Failed to get database")
		}

		// Test creating a new collection
		collectionName := "test_create_collection"
		err := db.CreateCollection(context.Background(), collectionName, nil)
		if err != nil {
			t.Fatalf("Failed to create collection: %v", err)
		}

		// Verify collection exists
		collections, err := db.ListCollectionNames(context.Background(), bson.M{})
		if err != nil {
			t.Fatalf("Failed to list collections: %v", err)
		}
		if !contains(collections, collectionName) {
			t.Errorf("Collection %s not found in database", collectionName)
		}

		// Test creating existing collection (should not error)
		err = db.CreateCollection(context.Background(), collectionName, nil)
		if err != nil {
			t.Errorf("Creating existing collection should not error: %v", err)
		}

		// Cleanup
		coll := Coll("tmp", collectionName)
		if err := coll.Drop(context.Background()); err != nil {
			t.Errorf("Failed to drop collection: %v", err)
		}
	})
}

func TestPreDefColl(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("Get predefined collection", func(t *testing.T) {
		// Test with valid collection name
		coll := PreDefColl("mailLog")
		if coll == nil {
			t.Fatal("Failed to get predefined collection")
		}

		// Test with invalid collection name
		invalidColl := PreDefColl("non_existent_coll")
		if invalidColl != nil {
			t.Error("Expected nil for non-existent collection")
		}

		// Test collection operations
		doc := bson.M{"test": "value", "timestamp": time.Now()}
		_, err := coll.InsertOne(context.Background(), doc)
		if err != nil {
			t.Fatalf("Failed to insert document: %v", err)
		}

		// Verify document was inserted
		var result bson.M
		err = coll.FindOne(context.Background(), bson.M{"test": "value"}).Decode(&result)
		if err != nil {
			t.Fatalf("Failed to find document: %v", err)
		}
		if result["test"] != "value" {
			t.Errorf("Wrong value in found document: got %v, want %v", result["test"], "value")
		}

		// Cleanup
		if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
			t.Errorf("Failed to cleanup collection: %v", err)
		}

		if err := coll.Drop(context.Background()); err != nil {
			t.Errorf("Failed to drop collection: %v", err)
		}
	})
}
