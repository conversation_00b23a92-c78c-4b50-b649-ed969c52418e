package middleware

import (
	"strings"
	"errors"
	"fmt"
	
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	goconfig "github.com/real-rm/goconfig"
)

var secretKey string

// GetSecretKey returns the JWT secret key
func GetSecretKey() string {
	return secretKey
}

// InitJWTSecret initializes the JWT secret key from config
func InitJWTSecret() error {
	var err error
	secretKey, err = goconfig.ConfigString("auth.jwtSecret")
	if err != nil {
		return fmt.Errorf("failed to get auth.jwtSecret: %v", err)
	}
	if secretKey == "" {
		return fmt.Errorf("auth.jwtSecret is empty")
	}
	secretKeyMasked := strings.Repeat("*", len(secretKey))
	fmt.Printf("auth secretKey initialized: %s\n", secretKeyMasked)
	// fmt.Printf("auth secretKey initialized: %s\n", secretKey)
	return nil
}

// force require login
func RequireLogin(c *gin.Context) (string, error) {
	val, exists := c.Get("authValid")
	if !exists || val != true {
		return "", errors.New("login required")
	}

	userID, ok := c.Get("userID")
	if !ok {
		return "", errors.New("invalid token data")
	}

	return userID.(string), nil
}

// JWTAuthMiddleware is a middleware that validates JWT tokens in the Authorization header
func JWTAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Set("authValid", false)
			c.Set("userID", nil)
			c.Set("jwt_claims", nil)
			c.Next()
			return
		}

		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.Set("authValid", false)
			c.Set("userID", nil)
			c.Set("jwt_claims", nil)
			c.Next()
			return
		}

		tokenString := parts[1]

		if secretKey == "" {
			c.Set("authValid", false)
			c.Set("userID", nil)
			c.Set("jwt_claims", nil)
			c.Next()
			return
		}

		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}
			return []byte(secretKey), nil
		})

		if err != nil || !token.Valid {
			c.Set("authValid", false)
			c.Set("userID", nil)
			c.Set("jwt_claims", nil)
			c.Next()
			return
		}

		if claims, ok := token.Claims.(jwt.MapClaims); ok {
			c.Set("jwt_claims", claims)
			if sub, ok := claims["sub"].(string); ok {
				c.Set("userID", sub)
			} else {
				c.Set("userID", nil)
			}
		} else {
			c.Set("jwt_claims", nil)
			c.Set("userID", nil)
		}
		c.Set("authValid", true)
		c.Next()
	}
} 