package controller

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	gonanoid "github.com/matoous/go-nanoid/v2"
	"github.com/real-rm/goauth/middleware"
	usermodel "github.com/real-rm/goauth/model"
	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func setupTest(t *testing.T) *usermodel.User {
	// Save original env var
	originalCfg := os.Getenv("RMBASE_FILE_CFG")

	// Get the absolute path to local.test.ini
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}

	configPath, err := filepath.Abs(filepath.Join(currentDir, "../local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}

	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		t.Fatalf("Config file not found at %s: %v", configPath, err)
	}

	if err := os.Setenv("RMBASE_FILE_CFG", configPath); err != nil {
		t.Fatalf("Failed to set RMBASE_FILE_CFG: %v", err)
	}

	if err := goconfig.LoadConfig(); err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	if err := golog.InitLog(); err != nil {
		t.Fatalf("Failed to initialize logging: %v", err)
	}

	if err := gomongo.InitMongoDB(); err != nil {
		t.Fatalf("Failed to InitMongoDB: %v", err)
	}

	if err := middleware.InitJWTSecret(); err != nil {
		t.Fatalf("Failed to initialize JWT secret: %v", err)
	}

	// Create a test user
	userCollection := gomongo.Coll("data", "user")
	loginCollection := gomongo.Coll("data", "login")

	// Clean up previous test data
	_, _ = userCollection.DeleteMany(context.Background(), bson.M{})
	_, _ = loginCollection.DeleteMany(context.Background(), bson.M{})

	objectID := primitive.NewObjectID()
	uidHex := objectID.Hex()

	mt, _ := time.Parse(time.RFC3339Nano, "2025-05-24T10:54:16.600Z")
	ts, _ := time.Parse(time.RFC3339Nano, "2012-08-04T13:46:24.000Z")

	_, err = userCollection.InsertOne(context.Background(), bson.M{
		"_id":      objectID,
		"_mt":      mt,
		"cnty":     nil,
		"eml":      "<EMAIL>",
		"fn":       "Charles ",
		"id":       "RM_26",
		"ln":       "Yangwang",
		"mbl":      "6476666666",
		"nm_en":    "Charles  Yangwang",
		"nm_zh":    "Charles  Yangwang",
		"openid":   nil,
		"rmb":      0,
		"stars":    1.5,
		"verified": "c817d4e0-de3a-11e1-b140-37eaac90788d",
		"zip":      "L6M6G4",
		"ts":       ts,
		"roles":    []string{"user"},
	})
	if err != nil {
		t.Fatalf("Failed to insert test user: %v", err)
	}

	nanoidWunikey, err := gonanoid.New(21)
	if err != nil {
		t.Fatalf("Failed to generate nanoid for wunikey: %v", err)
	}
	wunikey := fmt.Sprintf("%s.%d", nanoidWunikey, time.Now().Add(time.Hour).UnixMilli())

	nanoidUnikey, err := gonanoid.New(21)
	if err != nil {
		t.Fatalf("Failed to generate nanoid for unikey: %v", err)
	}
	unikey := fmt.Sprintf("%s.%d", nanoidUnikey, time.Now().Add(time.Hour).UnixMilli())

	_, err = loginCollection.InsertOne(context.Background(), bson.M{
		"_id":    fmt.Sprintf("test_login_%s", uidHex),
		"uid":    objectID,
		"method": "sha512",
		"secret": "*.*.*",
		"passwd": "3e7377777cb11d92dfed7df0a19140c8c433af697340f84ee1e4c354547546ab6b3260b7772808d908e9a7196cbc0de32b4be71783a38fb2fd6a86e6a1690dd5",
		"failed": 0,
		"sid": bson.M{
			"realmaster@com@@app": "1459087485255ruJg4qU0O6HP2dvAipKC8atVWjs_",
		},
		"his": []bson.M{
			{"dt": int64(1459046059650), "ip": "************", "dm": "realmaster.com"},
			{"dt": int64(1459087485847), "ip": "************", "dm": "realmaster.com"},
		},
		"unikey":  unikey,
		"wunikey": []string{wunikey},
	})
	if err != nil {
		t.Fatalf("Failed to insert test login data: %v", err)
	}

	createdTestUser := &usermodel.User{
		UID:          uidHex,
		Roles:        []string{"user"},
		RefreshToken: unikey,
		Wunikey:      []string{wunikey},
	}

	t.Cleanup(func() {
		// Clean up test data
		_, _ = userCollection.DeleteOne(context.Background(), bson.M{"_id": objectID})
		_, _ = loginCollection.DeleteOne(context.Background(), bson.M{"uid": objectID})
		if err := os.Setenv("RMBASE_FILE_CFG", originalCfg); err != nil {
			t.Fatalf("Failed to restore RMBASE_FILE_CFG: %v", err)
		}
	})

	return createdTestUser
}

func TestGenerateTokens(t *testing.T) {
	testUser := setupTest(t)

	// Generate tokens for web
	jwtToken, jwtExpires, refreshToken, rftExpires, err := generateTokens(testUser, true, testUser.Wunikey[0], true)

	assert.NoError(t, err)
	assert.NotEmpty(t, jwtToken)
	assert.True(t, jwtExpires > time.Now().Unix())
	assert.NotEmpty(t, refreshToken)
	assert.True(t, rftExpires > time.Now().UnixMilli())
}

func TestConvertJWT(t *testing.T) {
	testUser := setupTest(t)

	gin.SetMode(gin.TestMode)

	// Create a new router
	router := gin.New()
	router.POST("/convertjwt", convertJWT)

	// Create a new request
	body := WkPostbody{
		WK: testUser.Wunikey[0],
	}
	jsonBody, _ := json.Marshal(body)
	req, _ := http.NewRequest(http.MethodPost, "/convertjwt", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	// Create a new recorder
	w := httptest.NewRecorder()

	// Perform the request
	router.ServeHTTP(w, req)

	// Check the response
	assert.Equal(t, http.StatusOK, w.Code)

	var response gin.H
	json.Unmarshal(w.Body.Bytes(), &response)

	assert.Equal(t, float64(1), response["ok"])
	assert.NotEmpty(t, response["jwt"])
	assert.NotEmpty(t, response["rft"])
}

func TestRefreshTokenReq(t *testing.T) {
	testUser := setupTest(t)

	gin.SetMode(gin.TestMode)

	// Create a new router
	router := gin.New()
	router.POST("/refreshtoken", refreshTokenReq)

	// Get the original refresh token
	originalRefreshToken := testUser.Wunikey[0]

	// Create a new request
	body := RefreshTokenRequest{
		RefreshToken: originalRefreshToken,
		IsWeb:        true,
	}
	jsonBody, _ := json.Marshal(body)
	req, _ := http.NewRequest(http.MethodPost, "/refreshtoken", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	// Create a new recorder
	w := httptest.NewRecorder()

	// Perform the request
	router.ServeHTTP(w, req)

	// Check the response
	assert.Equal(t, http.StatusOK, w.Code)

	var response gin.H
	json.Unmarshal(w.Body.Bytes(), &response)

	assert.Equal(t, float64(1), response["ok"])
	assert.NotEmpty(t, response["jwt"])
	assert.NotEmpty(t, response["rft"])

	newRefreshToken := response["rft"].(string)

	// Verify old refresh token is removed and new one is present
	user, err := usermodel.FindUserByRefreshToken(originalRefreshToken, true)
	assert.Error(t, err) // Should not find the old token
	assert.Nil(t, user)

	user, err = usermodel.FindUserByRefreshToken(newRefreshToken, true)
	assert.NoError(t, err)
	assert.NotNil(t, user)
	assert.Equal(t, testUser.UID, user.UID)
	time.Sleep(500 * time.Millisecond) // Add a small delay to ensure data is written
}
