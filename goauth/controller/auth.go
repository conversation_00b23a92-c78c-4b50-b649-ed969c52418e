package controller

import (
	// "context"
	// "encoding/json"
	// "net/http"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/real-rm/goauth/middleware"
	"github.com/real-rm/goauth/model"
	golog "github.com/real-rm/golog"

	// "github.com/real-rm/gomongo"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	gonanoid "github.com/matoous/go-nanoid/v2"
	// "go.mongodb.org/mongo-driver/bson"
)

type TokenType string

const (
	AccessToken     TokenType = "access_token"
	RefreshToken    TokenType = "refresh_token"
	DefaultAudience           = "realmaster-api"
	DefaultIssuer             = "realmaster-app"
)

type Claims struct {
	UserID    string   `json:"sub"`
	Roles     []string `json:"roles"`
	ExpiresAt int64    `json:"exp"`
}

// <PERSON><PERSON> implements the jwt.Claims interface
func (c *Claims) Valid() error {
	// Check if token is expired
	if time.Now().Unix() > c.ExpiresAt {
		return fmt.Errorf("token is expired")
	}
	return nil
}

func (c *Claims) GetAudience() (jwt.ClaimStrings, error) {
	return nil, nil
}
func (c *Claims) GetIssuer() (string, error) {
	return "", nil
}
func (c *Claims) GetSubject() (string, error) {
	return c.UserID, nil
}
func (c *Claims) GetExpirationTime() (*jwt.NumericDate, error) {
	return jwt.NewNumericDate(time.Unix(c.ExpiresAt, 0)), nil
}
func (c *Claims) GetNotBefore() (*jwt.NumericDate, error) {
	return nil, nil
}
func (c *Claims) GetIssuedAt() (*jwt.NumericDate, error) {
	return nil, nil
}

// RefreshTokenRequest represents the refresh token request body
type RefreshTokenRequest struct {
	RefreshToken string `json:"rft" binding:"required"`
	IsWeb        bool   `json:"isweb"` // Flag to indicate if this is a web refresh token
}

// WkPostbody represents the request body for JWT conversion
type WkPostbody struct {
	WK string `json:"wk"` // Web key
	K  string `json:"k"`  // App key
}

// CacheItem represents a cached response with expiration
type CacheItem struct {
	Response gin.H
	Expires  int64
}

// TokenCache manages the cache for token responses
type TokenCache struct {
	items    map[string]CacheItem
	keyLocks map[string]*sync.Mutex
	mu       sync.Mutex // protects keyLocks map
}

// NewTokenCache creates a new token cache
func NewTokenCache() *TokenCache {
	return &TokenCache{
		items:    make(map[string]CacheItem),
		keyLocks: make(map[string]*sync.Mutex),
	}
}

// getLockForKey returns a mutex for the given key
func (c *TokenCache) getLockForKey(key string) *sync.Mutex {
	c.mu.Lock()
	defer c.mu.Unlock()

	if m, ok := c.keyLocks[key]; ok {
		return m
	}

	// Create new lock
	m := &sync.Mutex{}
	c.keyLocks[key] = m
	return m
}

// Get retrieves a cached response if it exists and is not expired
func (c *TokenCache) Get(key string) (gin.H, bool) {
	// Get lock for this key
	keyLock := c.getLockForKey(key)
	keyLock.Lock()
	defer keyLock.Unlock()

	item, exists := c.items[key]
	if !exists {
		return nil, false
	}

	// Check if item is expired
	if time.Now().Unix() > item.Expires {
		delete(c.items, key)
		return nil, false
	}

	return item.Response, true
}

// Set stores a response in the cache with expiration
func (c *TokenCache) Set(key string, response gin.H) {
	// Get lock for this key
	keyLock := c.getLockForKey(key)
	keyLock.Lock()
	defer keyLock.Unlock()

	// Clean expired items
	now := time.Now().Unix()
	for k, v := range c.items {
		if now > v.Expires {
			delete(c.items, k)
		}
	}

	// Set new item with 5 second expiration
	c.items[key] = CacheItem{
		Response: response,
		Expires:  now + 5,
	}
}

// Global cache instance
var tokenCache = NewTokenCache()

// refreshToken handles token refresh requests
func refreshTokenReq(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		golog.Error(fmt.Sprintf("Error binding JSON: %v", err))
		c.JSON(400, gin.H{"error": "invalid request body"})
		return
	}

	// Check cache first
	cacheKey := fmt.Sprintf("refresh_%s_%v", req.RefreshToken, req.IsWeb)
	if cached, exists := tokenCache.Get(cacheKey); exists {
		c.JSON(200, cached)
		return
	}

	// Parse and validate the refresh token format
	parts := strings.Split(req.RefreshToken, ".")
	if len(parts) != 2 {
		c.JSON(401, gin.H{"error": "invalid refresh token format"})
		return
	}

	// Parse and validate expiration timestamp (milliseconds)
	expirationMs, err := strconv.ParseInt(parts[1], 10, 64)
	if err != nil {
		golog.Error(fmt.Sprintf("Failed to parse refresh token timestamp: %v", err))
		c.JSON(400, gin.H{"error": "invalid refresh token format"})
		return
	}

	// Check if token has expired
	if time.Now().UnixMilli() >= expirationMs {
		c.JSON(401, gin.H{"error": "refresh token expired"})
		return
	}

	// Find user by refresh token
	user, err := usermodel.FindUserByRefreshToken(req.RefreshToken, req.IsWeb)
	if err != nil {
		golog.Error(fmt.Sprintf("Error finding user by refresh token: %v", err))
		c.JSON(401, gin.H{"error": "invalid credentials"})
		return
	}

	// Generate new tokens with new refresh token
	jwtToken, jwtExpires, refreshToken, rftExpires, err := generateTokens(user, req.IsWeb, req.RefreshToken, true)
	if err != nil {
		c.JSON(500, gin.H{"error": fmt.Sprintf("failed to generate tokens: %v", err)})
		return
	}

	// Prepare response
	response := gin.H{
		"ok":     1,
		"jwt":    jwtToken,
		"jwtexp": jwtExpires,
		"rft":    refreshToken,
		"rftexp": rftExpires,
	}

	// Cache the response
	tokenCache.Set(cacheKey, response)

	// Return response
	c.JSON(200, response)
}

// register handles user registration requests
func register(c *gin.Context) {
	c.JSON(403, gin.H{
		"ok": 0,
	})
}

// login handles user login requests
func login(c *gin.Context) {
	c.JSON(403, gin.H{
		"ok": 0,
	})
}

// generateTokens generates JWT and refresh tokens for a user
func generateTokens(user *usermodel.User, isWeb bool, oldKey string, shouldGenerateNewKey bool) (string, int64, string, int64, error) {
	// Create access token
	now := time.Now()
	nowUnix := now.Unix()
	// similar to cmate.sid, expires very fast
	jwtExpires := now.Add(10 * time.Minute).Unix()
	accessClaims := &Claims{
		UserID:    user.UID,
		Roles:     user.Roles,
		ExpiresAt: jwtExpires,
	}
	accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims)
	jwtToken, err := accessToken.SignedString([]byte(middleware.GetSecretKey()))
	if err != nil {
		return "", nowUnix, "", nowUnix, fmt.Errorf("failed to sign access token: %v", err)
	}

	// Calculate refresh token expiration (half year in seconds)
	halfYearInSeconds := int64(365 / 2 * 24 * 60 * 60) // 180 days in seconds
	nowMs := now.UnixMilli()
	rftExpires := nowMs + halfYearInSeconds*1000

	var refreshToken string
	if shouldGenerateNewKey {
		// Generate new refresh token with NanoID and timestamp
		nanoID, err := gonanoid.New(21) // 21 characters for good uniqueness
		if err != nil {
			return "", nowUnix, "", nowUnix, fmt.Errorf("failed to generate nano id: %v", err)
		}
		refreshToken = fmt.Sprintf("%s.%d", nanoID, rftExpires)
	} else {
		// Use the provided key as refresh token
		refreshToken = oldKey
	}

	// Store refresh token in MongoDB based on type
	if isWeb {
		// For web, we need to remove the old key and add the new one
		err = usermodel.UpdateWebRefreshToken(user.UID, oldKey, refreshToken)
	} else {
		// For app, just update the refresh token
		err = usermodel.UpdateRefreshToken(user.UID, refreshToken)
	}
	if err != nil {
		return "", nowUnix, "", nowUnix, fmt.Errorf("failed to store refresh token: %v", err)
	}

	// Verify the generated token
	token2, err := jwt.Parse(jwtToken, func(token *jwt.Token) (interface{}, error) {
		return []byte(middleware.GetSecretKey()), nil
	}, jwt.WithValidMethods([]string{jwt.SigningMethodHS256.Alg()}))
	golog.Verbose(fmt.Sprintf("Parsed JWT Token: %+v", token2))
	return jwtToken, jwtExpires, refreshToken, rftExpires, nil
}

// convertJWT handles JWT conversion requests
func convertJWT(c *gin.Context) {
	var newWkPostbody WkPostbody
	if err := c.BindJSON(&newWkPostbody); err != nil {
		c.JSON(400, gin.H{
			"ok":  0,
			"err": "invalid request body",
		})
		return
	}

	// Check cache first
	cacheKey := fmt.Sprintf("convert_%s_%s", newWkPostbody.WK, newWkPostbody.K)
	if cached, exists := tokenCache.Get(cacheKey); exists {
		c.JSON(200, cached)
		return
	}

	golog.Info(fmt.Sprintf("Convert JWT request body: %+v", newWkPostbody))
	// Try web key first, then app key
	var user *usermodel.User
	var err error
	isWeb := false
	var oldKey string

	if newWkPostbody.WK != "" {
		user, err = usermodel.FindUserByWUnionK(newWkPostbody.WK)
		isWeb = true
		oldKey = newWkPostbody.WK
	} else if newWkPostbody.K != "" {
		user, err = usermodel.FindUserByUnionK(newWkPostbody.K)
		oldKey = newWkPostbody.K
	} else {
		golog.Error(fmt.Sprintf("missing wk or k in request body: %v", newWkPostbody))
		c.JSON(400, gin.H{"error": "invalid request body"})
		return
	}

	if err != nil {
		golog.Error(fmt.Sprintf("Error finding user by key: %v", err))
		c.JSON(401, gin.H{
			"ok":  0,
			"err": "invalid credentials",
		})
		return
	}

	// Generate tokens with new refresh token
	jwtToken, jwtExpires, refreshToken, rftExpires, err := generateTokens(user, isWeb, oldKey, true)
	if err != nil {
		c.JSON(500, gin.H{
			"ok":  0,
			"err": err.Error(),
		})
		return
	}

	// Prepare response
	response := gin.H{
		"ok":     1,
		"jwt":    jwtToken,
		"jwtexp": jwtExpires,
		"rft":    refreshToken,
		"rftexp": rftExpires,
	}

	// Cache the response
	tokenCache.Set(cacheKey, response)

	// Return response
	c.JSON(200, response)
}

// 在appweb后端直接POST请求到本服务器，不需要redirect
// convertJWTAndRedirect handles JWT conversion and redirection requests
// func convertJWTAndRedirect(c *gin.Context) {
// 	// Get wk from cookie
// 	wk, err := c.Cookie("wk")
// 	if err != nil {
// 		redirectURL := c.Query("redirect")
// 		if redirectURL == "" {
// 			redirectURL = "/"
// 		}
// 		c.Redirect(http.StatusFound, redirectURL)
// 		return
// 	}

// 	// Get redirect URL
// 	redirectURL := c.Query("redirect")
// 	if redirectURL == "" {
// 		redirectURL = "/"
// 	}

// 	// Find user by wk
// 	user, err := usermodel.FindUserByWUnionK(wk)
// 	if err != nil {
// 		c.Redirect(http.StatusFound, redirectURL)
// 		return
// 	}

// 	// Generate tokens
// 	jwtToken, refreshToken, err := generateTokens(user)
// 	if err != nil {
// 		c.Redirect(http.StatusFound, redirectURL)
// 		return
// 	}

// 	// Set cookies
// 	c.SetCookie("cmate.jwt", jwtToken, int(24*time.Hour.Seconds()), "/", "", false, true)
// 	c.SetCookie("cmate.refreshtoken", refreshToken, int(7*24*time.Hour.Seconds()), "/", "", false, true)

// 	// Redirect
// 	c.Redirect(http.StatusFound, redirectURL)
// }

// auth controller endpoint
func Auth(g *gin.RouterGroup) {
	g.POST("/refreshtoken", refreshTokenReq)
	// g.POST("/register", register)
	// g.POST("/login", login)
	// g.GET("/convertjwt", convertJWTAndRedirect)
	g.POST("/convertjwt", convertJWT)
}
