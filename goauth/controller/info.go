package controller

import (
	// "net/http"
	"github.com/gin-gonic/gin"
	// "github.com/real-rm/goauth/middleware"
	"net/http"
	"fmt"
	"github.com/real-rm/goauth/model"
	golog "github.com/real-rm/golog"
	"os"
	"time"
	goconfig "github.com/real-rm/goconfig"
	"crypto/sha256"
	"encoding/hex"
)

// hashHostname returns a SHA256 hash of the hostname
func hashHostname(hostname string) string {
	hash := sha256.Sum256([]byte(hostname))
	return hex.EncodeToString(hash[:8]) // Return first 8 characters of hash
}

// formatUserCount formats user count for display
func formatUserCount(count int64) string {
	if count > 1000 {
		return ">1000"
	}
	if count > 100 {
		return ">100"
	}
	return "under 100"
}

// ping if service is running
// func ping(c *gin.Context) {	
// 	userID, err := middleware.RequireLogin(c)
// 	if err != nil {
// 		c.J<PERSON>(401, gin.H{"error": "unauthorized"})
// 		return
// 	}
// 	c.J<PERSON>(http.StatusOK, gin.H{
// 		"message": "pong"+string(userID),
// 	})
// }

// TODO: daily report of how many user registered today, need check auth user roles
// func report(c *gin.Context) {	
// 	c.JSON(http.StatusOK, gin.H{
// 		"message": "under construction",
// 	})
// }

// healthz checks MongoDB connectivity and user count /info/healthz
// ⚠️ 注意事项
// livenessProbe 失败会导致容器被 自动重启。
// 如果你的服务启动较慢，记得设置 initialDelaySeconds 足够大。
// 如果你同时用了 readinessProbe 和 livenessProbe：
// readinessProbe 失败 → 只是从服务发现中摘除
// livenessProbe 失败 → 容器直接重启
func healthz(c *gin.Context) {
	// Log authValid, userID, and jwt_claims from gin.Context
	authValid, _ := c.Get("authValid")
	userID, _ := c.Get("userID")
	jwtClaims, _ := c.Get("jwt_claims")
	golog.Info(fmt.Sprintf("authValid: %v, userID: %v, jwt_claims: %v", authValid, userID, jwtClaims))
	
	// Get hostname and hash it
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "unknown"
	}
	hostnameHash := hashHostname(hostname)
	
	// Get version from config
	version, err := goconfig.ConfigString("app.version")
	if err != nil {
		version = "unknown"
	}
	
	// Get current timestamp
	ts := time.Now().Format(time.RFC3339)
	
	// Check database
	count, err := usermodel.CountUsers()
	
	// Prepare response
	response := gin.H{
		"hostname": hostnameHash,
		"ts":       ts,
		"version":  version,
		"detail": gin.H{
			"database": gin.H{},
		},
	}
	
	if err != nil {
		response["status"] = "unhealthy"
		response["detail"].(gin.H)["database"] = gin.H{"error": err.Error()}
		c.JSON(http.StatusInternalServerError, response)
		return
	}
	
	if count == 0 {
		response["status"] = "alive"
		response["detail"].(gin.H)["database"] = gin.H{"status": "alive but 0 user"}
		c.JSON(http.StatusOK, response)
		return
	}
	
	response["status"] = "alive"
	response["detail"].(gin.H)["database"] = gin.H{"status": "alive", "user_count": formatUserCount(count)}
	c.JSON(http.StatusOK, response)
}

// auth controller endpoint
func Info(g *gin.RouterGroup) {
	g.GET("/healthz", healthz)
	// g.POST("/ping", ping)
	// g.POST("/report", report)
}