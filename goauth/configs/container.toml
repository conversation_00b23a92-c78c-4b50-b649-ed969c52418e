[dbs]
verbose = 3
[dbs.tmp]
uri = "mongodb://d1:d1@198.19.249.3:27017/vow?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"
[dbs.data]
uri = "mongodb://d1:d1@198.19.249.3:27017/chomeca?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"
[dbs.listing]
uri = "mongodb://d1:d1@198.19.249.3:27017/vow?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"


[golog]
dir = "logs"
level = "info"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "json"
standard_output = true

[auth]
jwtSecret = "b9lzajneH2MiTrUeRzaSyCzCBgaRfib29lzajna"

[app]
version = "6.6.2"