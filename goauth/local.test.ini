[auth]
jwtSecret = "test-secret"

[dbs]
verbose = 3
[dbs.tmp]
uri = "**********************************************************************************************************************************************"
[dbs.data]
uri = "**********************************************************************************************************************************************"
[dbs.listing]
uri = "**********************************************************************************************************************************************"


[golog]
dir = "logs"
level = "info"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"
standard_output = true

[app]
version = "6.6.2"