# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out

# Output of the go coverage tool, specifically when used with LiteIDE
*.cov

# Dependency directories (vendor/)
vendor/

# Go workspace file
go.work
go.work.sum

# Go module files (if you want to ignore local changes)
# go.mod
# go.sum

# Build directories
bin/
build/
dist/

# IDE/editor directories and files
.idea/
.vscode/
*.swp

# OS generated files
.DS_Store
Thumbs.db

# Test binary, coverage, and profiling files
*.coverprofile
*.profile
*.log

# Specstory folder
.specstory/


log/
./log/*
.gemini/
./model/logs/
./controller/logs/