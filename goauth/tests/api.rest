### ping server/check alive
GET http://localhost:8099/info/healthz
#Authorization: Bearer eyJhbGciiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************.bdGrz6PQYnsQ4oqoH5QBDV0YjXTfn5tcSEkjyc7nKoo
Content-Type: application/json


### get report (404)
POST http://localhost:8099/info/report
Content-Type: application/json



### convert with error post.body
GET http://localhost:8099/auth/convertjwt
Content-Type: text/html; charset=utf-8

### 可以用k/wk 来获取jwt,jwtexp,rft,rftexp
POST http://localhost:8099/auth/convertjwt
Content-Type: application/json; charset=utf-8

{
  "wk": "VxhPF7DVqi30Qwt0Q6Suu"
}


### refresh with rft
POST http://localhost:8099/auth/refreshtoken
Content-Type: application/json
{
  rft:''
}

### !!NOT Implemented
POST http://localhost:8099/auth/login
Content-Type: application/json

### !!NOT Implemented
POST http://localhost:8099/auth/register
Content-Type: application/json
