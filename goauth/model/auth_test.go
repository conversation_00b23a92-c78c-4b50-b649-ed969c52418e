package usermodel

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func setupTest(t *testing.T) *User {
	// Save original env var
	originalCfg := os.Getenv("RMBASE_FILE_CFG")

	// Get the absolute path to local.test.ini
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}

	configPath, err := filepath.Abs(filepath.Join(currentDir, "../local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}

	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		t.Fatalf("Config file not found at %s: %v", configPath, err)
	}

	if err := os.Setenv("RMBASE_FILE_CFG", configPath); err != nil {
		t.Fatalf("Failed to set RMBASE_FILE_CFG: %v", err)
	}

	if err := goconfig.LoadConfig(); err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	if err := golog.InitLog(); err != nil {
		t.Fatalf("Failed to initialize logging: %v", err)
	}

	if err := gomongo.InitMongoDB(); err != nil {
		t.Fatalf("Failed to InitMongoDB: %v", err)
	}

	// Create a test user
	userCollection := gomongo.Coll("data", "user")
	loginCollection := gomongo.Coll("data", "login")

	// Clean up previous test data
	t.Logf("Cleaning up previous test data!")
	_, _ = userCollection.DeleteMany(context.Background(), bson.M{})
	_, _ = loginCollection.DeleteMany(context.Background(), bson.M{})
	time.Sleep(100 * time.Millisecond) // Add a small delay to ensure data is written

	objectID := primitive.NewObjectID()
	uidHex := objectID.Hex()

	mt, _ := time.Parse(time.RFC3339Nano, "2025-05-24T10:54:16.600Z")
	ts, _ := time.Parse(time.RFC3339Nano, "2012-08-04T13:46:24.000Z")

	_, err = userCollection.InsertOne(context.Background(), bson.M{
		"_id":      objectID,
		"_mt":      mt,
		"cnty":     nil,
		"eml":      "<EMAIL>",
		"fn":       "Charles ",
		"id":       "RM_26",
		"ln":       "Yangwang",
		"mbl":      "6476666666",
		"nm_en":    "Charles  Yangwang",
		"nm_zh":    "Charles  Yangwang",
		"openid":   nil,
		"rmb":      0,
		"stars":    1.5,
		"verified": "c817d4e0-de3a-11e1-b140-37eaac90788d",
		"zip":      "L6M6G4",
		"ts":       ts,
		"roles":    []string{"user"},
	})
	if err != nil {
		t.Fatalf("Failed to insert test user: %v", err)
	}

	wunikey := "VSjxbEc3YSkCHaDmedanH.1767276868753"
	unikey := "YX8_EtYuArAhaEr8agBaE.1767234786131"

	_, err = loginCollection.InsertOne(context.Background(), bson.M{
		"_id":    fmt.Sprintf("chrlesfakeemail@hotmail.com_%s", uidHex),
		"uid":    objectID,
		"method": "sha512",
		"secret": "*.*.*",
		"passwd": "3e7377777cb11d92dfed7df0a19140c8c433af697340f84ee1e4c354547546ab6b3260b7772808d908e9a7196cbc0de32b4be71783a38fb2fd6a86e6a1690dd5",
		"failed": 0,
		"sid": bson.M{
			"realmaster@com@@app": "1459087485255ruJg4qU0O6HP2dvAipKC8atVWjs_",
		},
		"his": []bson.M{
			{"dt": int64(1459046059650), "ip": "************", "dm": "realmaster.com"},
			{"dt": int64(1459087485847), "ip": "************", "dm": "realmaster.com"},
		},
		"unikey":  unikey,
		"wunikey": []string{wunikey},
	})
	if err != nil {
		t.Fatalf("Failed to insert test login data: %v", err)
	}

	time.Sleep(100 * time.Millisecond) // Add a small delay to ensure data is written

	createdTestUser := &User{
		UID:          uidHex,
		Roles:        []string{"user"},
		RefreshToken: unikey,
		Wunikey:      []string{wunikey},
	}

	t.Cleanup(func() {
		// Clean up test data
		_, _ = userCollection.DeleteOne(context.Background(), bson.M{"_id": objectID})
		_, _ = loginCollection.DeleteOne(context.Background(), bson.M{"_id": fmt.Sprintf("chrlesfakeemail@hotmail.com_%s", uidHex)})
		if err := os.Setenv("RMBASE_FILE_CFG", originalCfg); err != nil {
			t.Fatalf("Failed to restore RMBASE_FILE_CFG: %v", err)
		}
	})
	return createdTestUser
}

// NOTE: go test 有bug，=== RUN   TestCountUsers
// 2025-07-03T13:11:59.347-04:00
// 但是 === RUN   TestRefreshTokenReq
// 2025-07-03T13:11:59.573-04:00
// 这个测试在上一个结束之前就执行了，所以导致测试失败
func TestCountUsers(t *testing.T) {
	time.Sleep(1000 * time.Millisecond) // Add a small delay to ensure data is written
	_ = setupTest(t)

	count, err := CountUsers()
	assert.NoError(t, err)
	assert.Equal(t, int64(1), count)

	// Add another user and check count
	userCollection := gomongo.Coll("data", "user")
	_, err = userCollection.InsertOne(context.Background(), bson.M{
		"_id":   primitive.NewObjectID(),
		"id":    "unique_id_for_test_user_2", // Add a unique 'id' field
		"roles": []string{"admin"},
	})
	assert.NoError(t, err)

	count, err = CountUsers()
	assert.NoError(t, err)
	assert.Equal(t, int64(2), count)
}

func TestFindUserByWUnionK(t *testing.T) {
	testUser := setupTest(t)

	userCollection := gomongo.Coll("data", "login")
	cursor, err := userCollection.Find(context.Background(), bson.M{})
	if err != nil {
		t.Fatalf("Failed to find users: %v", err)
	}
	defer cursor.Close(context.Background())

	var users []bson.M
	if err = cursor.All(context.Background(), &users); err != nil {
		t.Fatalf("Failed to decode users: %v", err)
	}
	t.Logf("All login records in DB: %+v", users)

	user, err := FindUserByWUnionK(testUser.Wunikey[0])
	t.Logf("Searching for wunikey: %s", testUser.Wunikey[0])
	if err != nil {
		t.Logf("Error finding user by wunikey: %v", err)
	}
	assert.NoError(t, err)
	assert.NotNil(t, user)
	assert.Equal(t, testUser.UID, user.UID)
	assert.Contains(t, user.Roles, "user")

	// Test with non-existent key
	user, err = FindUserByWUnionK("nonexistent")
	t.Logf("Searching for non-existent wunikey: %s", "nonexistent")
	if err != nil {
		t.Logf("Error finding non-existent user by wunikey: %v", err)
	}
	assert.Error(t, err)
	assert.Nil(t, user)
}

func TestUpdateRefreshToken(t *testing.T) {
	testUser := setupTest(t)

	newKey := "new_test_unikey"
	err := UpdateRefreshToken(testUser.UID, newKey)
	assert.NoError(t, err)

	// Verify the refresh token was updated by directly querying the database
	loginCollection := gomongo.Coll("data", "login")
	var result struct {
		Unikey string `bson:"unikey"`
	}
	objectID, err := primitive.ObjectIDFromHex(testUser.UID)
	assert.NoError(t, err)
	err = loginCollection.FindOne(context.Background(), bson.M{"uid": objectID}).Decode(&result)
	assert.NoError(t, err)
	assert.Equal(t, newKey, result.Unikey)
}

func TestUpdateWebRefreshToken(t *testing.T) {
	testUser := setupTest(t)

	oldKey := testUser.Wunikey[0]
	newKey := "new_test_wunikey"

	err := UpdateWebRefreshToken(testUser.UID, oldKey, newKey)
	assert.NoError(t, err)

	// Verify the web refresh token was updated by directly querying the database
	loginCollection := gomongo.Coll("data", "login")
	var result struct {
		Wunikey []string `bson:"wunikey"`
	}
	objectID, err := primitive.ObjectIDFromHex(testUser.UID)
	assert.NoError(t, err)
	err = loginCollection.FindOne(context.Background(), bson.M{"uid": objectID}).Decode(&result)
	assert.NoError(t, err)
	assert.Contains(t, result.Wunikey, newKey)
	assert.NotContains(t, result.Wunikey, oldKey)
}

func TestFindUserByUnionK(t *testing.T) {
	testUser := setupTest(t)

	user, err := FindUserByUnionK(testUser.RefreshToken)
	assert.NoError(t, err)
	assert.NotNil(t, user)
	assert.Equal(t, testUser.UID, user.UID)
	assert.Contains(t, user.Roles, "user")

	// Test with non-existent key
	user, err = FindUserByUnionK("nonexistent")
	assert.Error(t, err)
	assert.Nil(t, user)
}

func TestFindUserByRefreshToken(t *testing.T) {
	testUser := setupTest(t)

	// Test with web refresh token
	user, err := FindUserByRefreshToken(testUser.Wunikey[0], true)
	assert.NoError(t, err)
	assert.NotNil(t, user)
	assert.Equal(t, testUser.UID, user.UID)
	assert.Contains(t, user.Roles, "user")

	// Test with app refresh token
	user, err = FindUserByRefreshToken(testUser.RefreshToken, false)
	assert.NoError(t, err)
	assert.NotNil(t, user)
	assert.Equal(t, testUser.UID, user.UID)
	assert.Contains(t, user.Roles, "user")

	// Test with non-existent token
	user, err = FindUserByRefreshToken("nonexistent", true)
	assert.Error(t, err)
	assert.Nil(t, user)
}
