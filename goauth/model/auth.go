package usermodel

import (
	"context"
	"fmt"

	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	// DefaultMaxWebRefreshTokens is the default maximum number of web refresh tokens per user
	DefaultMaxWebRefreshTokens = 2
)

// User represents a user in the system
type User struct {
	UID          string   `bson:"uid"`
	Wu<PERSON>ey      []string `bson:"wunikey,omitempty"` // Array of web unikeys
	Email        string   `bson:"eml,omitempty"`
	Roles        []string `bson:"roles,omitempty"`
	RefreshToken string   `bson:"rft,omitempty"` // App refresh token
}

// FindUserByWUnionK finds a user by their wk (unikey) the unikey field
// UpdateWebRefreshToken will then reset the key
// @input wk(string) wk of req.cookie.wk
func FindUserByWUnionK(wk string) (*User, error) {
	var userData struct {
		Roles []string `bson:"roles"`
	}

	loginCollection := gomongo.Coll("data", "login")
	userCollection := gomongo.Coll("data", "user")

	// Find the user in login collection
	var user User
	err := loginCollection.FindOne(context.Background(), bson.M{"wunikey": wk}).Decode(&user)
	if err != nil {
		return nil, err
	}

	// Find user roles in user collection
	objectID, err := primitive.ObjectIDFromHex(user.UID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %v", err)
	}
	// Find user roles in user collection using ObjectId
	err = userCollection.FindOne(context.Background(), bson.M{"_id": objectID}).Decode(&userData)
	if err != nil {
		return nil, err
	}

	// Set the roles from user collection
	user.Roles = userData.Roles

	return &user, nil
}

// UpdateRefreshToken updates the refresh token for a user
func UpdateRefreshToken(uid string, newKey string) error {
	collection := gomongo.Coll("data", "login")

	objectUid, err := primitive.ObjectIDFromHex(uid)
	if err != nil {
		return err
	}
	_, err = collection.UpdateOne(
		context.Background(),
		bson.M{"uid": objectUid},
		bson.M{"$set": bson.M{"unikey": newKey}},
	)
	return err
}

// UpdateWebRefreshToken updates the web refresh token for a user
// It removes the old key and adds the new key to maintain the maximum number of tokens
func UpdateWebRefreshToken(uid string, oldKey string, newKey string) error {
	collection := gomongo.Coll("data", "login")

	objectUid, err := primitive.ObjectIDFromHex(uid)
	if err != nil {
		return err
	}

	// First, remove the old key
	_, err = collection.UpdateOne(
		context.Background(),
		bson.M{"uid": objectUid},
		bson.M{"$pull": bson.M{"wunikey": oldKey}},
	)
	if err != nil {
		return err
	}

	// Then add the new key to the beginning of the array
	_, err = collection.UpdateOne(
		context.Background(),
		bson.M{"uid": objectUid},
		bson.M{
			"$push": bson.M{
				"wunikey": bson.M{
					"$each":     []string{newKey},
					"$position": 0,
					"$slice":    DefaultMaxWebRefreshTokens,
				},
			},
		},
	)
	return err
}

// FindUserByUnionK finds a user by their app key (k)
func FindUserByUnionK(k string) (*User, error) {
	var userData struct {
		Roles []string `bson:"roles"`
	}

	loginCollection := gomongo.Coll("data", "login")
	userCollection := gomongo.Coll("data", "user")

	// Find the user in login collection
	var user User
	err := loginCollection.FindOne(context.Background(), bson.M{"unikey": k}).Decode(&user)
	if err != nil {
		return nil, err
	}

	// Find user roles in user collection
	objectID, err := primitive.ObjectIDFromHex(user.UID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %v", err)
	}

	// Find user roles in user collection using ObjectId
	err = userCollection.FindOne(context.Background(), bson.M{"_id": objectID}).Decode(&userData)
	if err != nil {
		return nil, err
	}

	// Set the roles from user collection
	user.Roles = userData.Roles

	return &user, nil
}

// FindUserByIDAndRefreshToken finds a user by their ID and refresh token
// func FindUserByIDAndRefreshToken(uid string, refreshToken string, isWeb bool) (*User, error) {
// 	collection := gomongo.Coll("data", "login")

// 	objectUid, err := primitive.ObjectIDFromHex(uid)
// 	if err != nil {
// 		return nil, fmt.Errorf("invalid user ID format: %v", err)
// 	}

// 	// Build query based on token type
// 	query := bson.M{
// 		"uid": objectUid,
// 	}
// 	if isWeb {
// 		query["wunikey"] = refreshToken
// 	} else {
// 		query["rft"] = refreshToken
// 	}

// 	var user User
// 	err = collection.FindOne(context.Background(), query).Decode(&user)
// 	if err != nil {
// 		return nil, err
// 	}

// 	// Find user roles in user collection
// 	userCollection := gomongo.Coll("data", "user")
// 	err = userCollection.FindOne(context.Background(), bson.M{"_id": objectUid}).Decode(&userData)
// 	if err != nil {
// 		return nil, err
// 	}

// 	// Set the roles from user collection
// 	user.Roles = userData.Roles

// 	return &user, nil
// }

// FindUserByRefreshToken finds a user by their refresh token
func FindUserByRefreshToken(refreshToken string, isWeb bool) (*User, error) {
	var userData struct {
		Roles []string `bson:"roles"`
	}

	collection := gomongo.Coll("data", "login")

	// Build query based on token type
	query := bson.M{}
	if isWeb {
		query["wunikey"] = refreshToken
	} else {
		query["unikey"] = refreshToken
	}

	var user User
	err := collection.FindOne(context.Background(), query).Decode(&user)
	if err != nil {
		return nil, err
	}

	// Find user roles in user collection
	objectUid, err := primitive.ObjectIDFromHex(user.UID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %v", err)
	}

	userCollection := gomongo.Coll("data", "user")
	err = userCollection.FindOne(context.Background(), bson.M{"_id": objectUid}).Decode(&userData)
	if err != nil {
		return nil, err
	}

	// Set the roles from user collection
	user.Roles = userData.Roles

	return &user, nil
}

// CountUsers returns the number of users in the user collection
func CountUsers() (int64, error) {
	userCollection := gomongo.Coll("data", "user")
	count, err := userCollection.CountDocuments(context.Background(), bson.M{})
	return count, err
}

// func Authenticator() func(c *gin.Context) (interface{}, error) {
//   return func(c *gin.Context) (interface{}, error) {
//     var loginVals login
//     if err := c.ShouldBind(&loginVals); err != nil {
//       return "", jwt.ErrMissingLoginValues
//     }
//     userID := loginVals.Username
//     password := loginVals.Password

//     if (userID == "admin" && password == "admin") || (userID == "test" && password == "test") {
//       return &User{
//         UserName:  userID,
//         LastName:  "Bo-Yi",
//         FirstName: "Wu",
//       }, nil
//     }
//     return nil, jwt.ErrFailedAuthentication
//   }
// }
