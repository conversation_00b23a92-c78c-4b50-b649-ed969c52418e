package main

import (
  "fmt"
  // "strings"
  
  "github.com/real-rm/goauth/controller"
  "github.com/real-rm/goauth/middleware"
  "github.com/real-rm/gomongo"
  goconfig "github.com/real-rm/goconfig"
  golog "github.com/real-rm/golog"

  "github.com/gin-gonic/gin"
)

func main() {
  router := gin.Default()
  // redirect 404 pages
  // router.NoRoute(func(c *gin.Context) {
	// 	path := c.Request.URL.Path
	// 	method := c.Request.Method
	// 	fmt.Println(path)
	// 	fmt.Println(method)
	// 	if strings.HasPrefix(path, "/auth") {
	// 		fmt.Println("ok")
	// 	}
  //   // https://gin-gonic.com/en/docs/examples/redirects/
  //   c.Redirect(http.StatusFound, "/info/test/404.html")
	// })
  // gin.DebugPrintRouteFunc = func(httpMethod, absolutePath, handlerName string, nuHandlers int) {
  //   log.Printf("endpoint %v %v %v %v\n", httpMethod, absolutePath, handlerName, nuHandlers)
  // }

  err := goconfig.LoadConfig()
  if err != nil {
    panic(fmt.Sprint(err))
  }
  logerr := golog.InitLog()
  if logerr != nil {
    panic(fmt.Sprint(logerr))
  }

  // Initialize JWT secret
  if err := middleware.InitJWTSecret(); err != nil {
    panic(fmt.Sprintf("Failed to initialize JWT secret: %v", err))
  }
  // using router groups/controller easy to track, this path is synced with nginx
  // auth group for handling auth requests
  {
    authGroup := router.Group("/auth")
    authGroup.Use(middleware.JWTAuthMiddleware()) // Add JWT middleware to info group
    controller.Auth(authGroup)
  }
  // info group for handling info/server status requests
  {
    infoGroup := router.Group("/info")
    infoGroup.Use(middleware.JWTAuthMiddleware())
    // infoGroup.Static("/test", "./src/static") //http.Dir("/your_directory")
    // Add JWT middleware to info group
    controller.Info(infoGroup)
  }

  dbName := goconfig.Config("dbs.data")
  fmt.Printf("Database connection: %s\n", dbName)

  dberr := gomongo.InitMongoDB()
  if dberr != nil {
    panic(fmt.Sprint(dberr))
  }
  golog.Verbose("Starting auth service", "port", "8099")
  router.Run(":8099") // listen and serve on 0.0.0.0:8099 (for windows "localhost:8080")
}
