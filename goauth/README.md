# goauth
authentication service for go, including login/regiser/jwt_util etc...

backend uses go gin as http server.

to dun the server:
```bash
go mod tidy
go run main.go
```

## How it Works

The `goauth` service provides a complete authentication solution, including JWT-based authentication, token refresh, and conversion of legacy keys. Here's a high-level overview of the authentication flow:

1.  **Client Request:** A client sends a request to a protected endpoint with a JWT in the `Authorization` header.
2.  **JWT Middleware:** The `JWTAuthMiddleware` intercepts the request and validates the JWT. If the token is valid, the user's information is extracted and added to the request context.
3.  **Token Conversion:** The `/auth/convertjwt` endpoint allows legacy clients to exchange their old API keys (`wk` or `k`) for a new JWT and refresh token.
4.  **Token Refresh:** When a JWT expires, the client can use a refresh token to obtain a new JWT by calling the `/auth/refreshtoken` endpoint.

### Authentication Flow Diagram

```mermaid
sequenceDiagram
    participant Client
    participant Server
    participant Database

    Client->>Server: POST /auth/convertjwt (with wk/k)
    Server->>Database: Find user by wk/k
    Database-->>Server: User details
    Server->>Server: Generate JWT and Refresh Token
    Server-->>Client: JWT, Refresh Token

    Client->>Server: GET /protected-resource (with JWT)
    Server->>Server: Validate JWT
    alt JWT is valid
        Server-->>Client: Resource
    else JWT is expired
        Client->>Server: POST /auth/refreshtoken (with Refresh Token)
        Server->>Database: Find user by Refresh Token
        Database-->>Server: User details
        Server->>Server: Generate new JWT and Refresh Token
        Server-->>Client: New JWT, New Refresh Token
        Client->>Server: GET /protected-resource (with new JWT)
        Server-->>Client: Resource
    end
```

# TODO: todo lists
- [x] +goconfig,golog,gomongo 的使用，区分src/log/doc/configs folder structures
- [x] make this package and handle jwt utils
- [x] 想办法部署，需要测试不同部署方法
- [ ] move rent_report login/register/auth to this package
- [ ] 增加测试
- [ ] 增加github actions
- [ ] 增加readme和api docs

## Project Structure
```
.
├── Dockerfile
├── DockerfileArm
├── go.mod
├── go.sum
├── main.go
├── README.md
├── start.sh
├── configs
│   ├── container.toml
│   └── rain.toml
├── controller
│   ├── auth.go
│   └── info.go
├── docs
│   ├── gogin.md
│   └── API
│       └── ...
├── logs
│   ├── error.log
│   ├── info.log
│   └── verbose.log
├── middleware
│   └── jwt.go
├── model
│   └── auth.go
├── src
│   └── static
│       ├── 404.html
│       └── hello.html
├── tests
│   └── api.rest
└── views
```

## 注意！！！
config下的文件仅用于参考，其作用域仅限测试。不可直接用于production！！
production 文件位于 https://github.com/real-rm/rmprodcfg

假如遇到问题gcc: error: unrecognized command-line option '-m64'
```
export CGO_ENABLED=0
```
