
### 需求 [go_jwt_web]

## 反馈
1. 使用go后端接受redirect请求，处理用户请求的req.header.cookie.k, 签发jwt
2. 签发使用setCookie
3. 处理refrestoken请求

(# Sample Data)

## 需求提出人:   Fred
## 修改人:       Rain

## 提出日期
1. 2025-04-22 提出反馈

## 原因
1. 以前未提供

## 影响范围
1. 第一步修改web, 只处理web请求
2. web里的登录注册，访问敏感数据
3. 用户登出，jwt过期，用户修改密码，旧jwt过期
4. 分析不同用户角色下的使用场景影响

## 是否需要补充UT
- [X] 是
- [ ] 否
如选"是"，说明需要补充的测试用例范围：
1. test for api requests
2. test for web user experience

## 测试验证方案/测试方法 （需要测试与修改相关的功能，防止改一个bug，改出另外的bug）
1. 描述如何验证此修复是否成功
2. 列出需要执行的测试场景
3. 说明如何进行回归测试


## 解决办法/方案 （涉及到编码coding）
1. 添加auth模块，暂时不处理登录注册的请求
2. 本次修改主要涉及refreshtoken(post) 和 convertjwt(get/post)处理请求
3. 除了go修改，还需要appweb修改，把所有没有jwt的请求如果是get，then redirect to /auth/convertjwt. if ispost 请求， then post to convertjwt(post), 返回json response {cmate.jwt:'xxxxx'}
4. 具体处理细节：


## 需求确认日期
1. 2025-04-22

## 测试结果/


## online-step/上线步骤
1. 准备go服务器，按照goauth说明部署
2. 更新appweb，按照测试方法测试web


## 准备pod 文件 (Podman_container_k3s/run_go_container.md)
1. podman build
2. podman push
3. 准备goauthk3s.ini
```
[dbs]
verbose = 3
[dbs.tmp]
uri = "mongodb://d1:d1@***********:28017/listing?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"
[dbs.data]
uri = "mongodb://d1:d1@***********:28017/data?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"
[dbs.listing]
uri = "mongodb://d1:d1@***********:28017/listing?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"


[golog]
dir = "logs"
level = "info"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "json"
standard_output = true


[auth]
jwtSecret = "123456"

[app]
version = "6.6.2"
```



## k3s 配置
### 1. 在ca6,7,8 编辑sudo vi /etc/rancher/k3s/config.yaml
### 2. 安装k3s, 假如没有wireguard/internal ip, 需要配置wg(Podman_container_k3s/k3s_overWireguard.md)
```
# Use the internal WireGuard IP for all node-to-node traffic
node-ip: ***********

# Tell K3s what its public-facing IP is
node-external-ip: **************

# Bind the K3s API server only to the internal IP for security
bind-address: ***********

# Advertise the internal IP to other nodes joining the cluster
advertise-address: ***********

# Ensure Flannel (pod networking) uses the WireGuard interface
flannel-iface: wg0

# --- ADD THIS LINE ---
# Tell kube-proxy to ONLY open NodePorts on the internal network
kube-proxy-arg: "nodeport-addresses=***********/24"
```
### 3. install k3s with out traefik
```
curl -sfL https://get.k3s.io | sh - --disable-traefik
```

### 4. install k3s clients
```
1.  **获取主节点的 IP 地址**: 在主节点上运行 `ip addr` 或 `hostname -I`。
2.  **获取主节点的 Token**:
    ```bash
    sudo cat /var/lib/rancher/k3s/server/node-token
    ```

然后，在您要添加的 **Agent (工作节点)** 上运行以下命令，将 `<主节点IP>` 和 `<主节点Token>` 替换为实际值：
注意 Only https:// URLs are supported for K3S_URL

```bash
# 将YOUR_SERVER_IP和YOUR_NODE_TOKEN替换为实际值
curl -sfL https://get.k3s.io | K3S_URL=https://<主节点IP>:6443 K3S_TOKEN=<主节点Token> sh - --disable-traefik
```


### 5. 配置config
```
kubectl delete configmap goauth-config
kubectl create configmap goauth-config --from-file=./goauthk3s.ini
kubectl get configmap goauth-config -o yaml
```
### 6 . 配置github token
```
kubectl create secret docker-registry ghcr-secret \
  --docker-server=ghcr.io \
  --docker-username=your_github_username \
  --docker-password=your_personal_access_token \
  --docker-email=<EMAIL>
```
### 7. 拷贝配置文件Podman_container_k3s/configs/goauth-deployment.yaml，Podman_container_k3s/configs/goauth-service.yaml
### 8. 执行pod
```
kubectl apply -f ./goauth-deployment.yaml
kubectl apply -f ./goauth-service.yaml
```

### 9. 修改appweb/local.ini, 请求会发给这个internal ip
```
[jwt]
host = "***********"
port = 30099
path = "/auth/convertjwt"
rftpath = "/auth/refreshtoken"
protocol = "http"
```
### 10. restart server，如果没有请求转发，需要systemctl --stop appweb, ./start.sh 重启服务

### 11. 没问题后1个月删掉wk array, 在db内存在几百个的情况

