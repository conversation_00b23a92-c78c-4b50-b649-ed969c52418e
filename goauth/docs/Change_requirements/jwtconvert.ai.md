# 参考文件： jwtconvert.md

## 修改方法
1. 修改convertJWTAndRedirect function， 使他接受2个参数，验证1个参数wk，这个参数在cookie里，是wk，格式为string, eg:"rjs2wnDNZVxTy4RlKQbla"。另一个参数是redirecturl
2. 修改FindUserByWk，从mongodb.login.find({unikey:wk})(mongodb 使用gomongo，参考 docs/API/gomongo)
3. 如果出现任何错误，直接redirect到redirecturl
4. 如果找到db的记录，给用户签发jwttoken和refreshtoken，写入到setcookie('cmate.jwt',jwttoken),setcookie('cmate.refreshtoken',refreshtoken)并且redirect到redirecturl, 并且从mongodb删除unikey

注意：
1.从mongodb删除unikey不是删除document，而是unset掉unikey field
2.签发的refreshtoken要记录到mongodb.login对应的用户下（按照用户的uid）
3. jwt payload格式为stringify(json)，例如:'{"uid":"681d10cb5e11f05fb10932b8","eml":"<EMAIL>","roles":["realtor","vip"]}'

new requirements:
1. now modify refreshToken function so that refreshToken is consumend and a new jwt token is signed;
2. this refresh_token is inside post body;

#new request2
1. now modify convertJWT, similar to convertJWTAndRedirect, but this is a POST
2. convertJWT returns {ok:0,err} instead of redirects
3. convertJWT do not need redirect url