package gomail

import (
	"fmt"
	"os"
	"regexp"
	"strings"

	goconfig "github.com/real-rm/goconfig"
	golog "github.com/real-rm/golog"
)

const (
	RM_MAIL               = "rmMail"
	GMAIL                 = "gmail"
	SES_ENGINE            = "SES"
	SEND_MAIL             = "sendMail"
	MOCK_MAIL             = "mockmail"
	DEFAULT_FROM_NAME     = "RealMaster"
	GMAIL_HOST            = "smtp.gmail.com"
	GMAIL_PORT            = 587
	DEFAULT_SENDMAIL_PATH = "/usr/sbin/sendmail"
)

var (
	ENGINES            []string
	HOST_NAME          string
	EMAIL_ENGINE_MAP   map[string]map[string]interface{}
	VALID_EMAIL_REGEXP = regexp.MustCompile(`^[a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,}$`)
	rubyPattern        = regexp.MustCompile(`^ruby\d+`)
	liaoPattern        = regexp.MustCompile(`^liao\.ca$`)
	mailEngineList     []map[string]interface{}
)

func init() {
	ENGINES = []string{RM_MAIL, GMAIL, SES_ENGINE, SEND_MAIL, MOCK_MAIL}
	hostname, err := os.Hostname()
	if err != nil {
		panic(err)
	}
	re := regexp.MustCompile(`[^-_a-zA-Z0-9]`)
	HOST_NAME = re.ReplaceAllString(hostname, "-")
}

// getDefaultFromEmails gets default from email addresses for all engines
func getDefaultFromEmails() (defaultFrom, SESFrom, SESHFrom, gmailFrom, sendmailFrom, rmMailFrom string) {
	defaultFrom = DefaultFromEmail
	email, err := goconfig.ConfigString("contact.defaultEmail")
	if err != nil {
		golog.Warn("defaultEmail not found in config")
		defaultFrom = DefaultFromEmail
	}
	if email != "" {
		defaultFrom = email
		SESFrom = email
		SESHFrom = email
		gmailFrom = email
		sendmailFrom = email
		rmMailFrom = email
	}

	sesDefaultEmail, err := goconfig.ConfigString("mailEngine.ses.defaultEmail")
	if err == nil && sesDefaultEmail != "" {
		SESFrom = sesDefaultEmail
	}
	sesHDefaultEmail, err := goconfig.ConfigString("mailEngine.sesH.defaultEmail")
	if err == nil && sesHDefaultEmail != "" {
		SESHFrom = sesHDefaultEmail
	}
	gmailDefaultEmail, err := goconfig.ConfigString("mailEngine.gmail.defaultEmail")
	if err == nil && gmailDefaultEmail != "" {
		gmailFrom = gmailDefaultEmail
	}
	sendmailDefaultEmail, err := goconfig.ConfigString("mailEngine.sendmail.defaultEmail")
	if err == nil && sendmailDefaultEmail != "" {
		sendmailFrom = sendmailDefaultEmail
	}
	rmMailDefaultEmail, err := goconfig.ConfigString("mailEngine.rmMail.defaultEmail")
	if err == nil && rmMailDefaultEmail != "" {
		rmMailFrom = rmMailDefaultEmail
	}
	return
}

// initMailEngineList initializes the mail engine list from config
func initMailEngineList() {
	raw := goconfig.Config("mailEngineList")
	mailEngineListIface, ok := raw.([]interface{})
	if !ok {
		golog.Warn("mailEngineList not configured correctly, fallback to empty list")
		return
	}
	mailEngineListRaw := mailEngineListIface
	mailEngineList = make([]map[string]interface{}, len(mailEngineListRaw))
	for i, v := range mailEngineListRaw {
		mailEngineList[i] = v.(map[string]interface{})
	}
}

// initEmailEngineMap initializes the email engine map with regex patterns
func initEmailEngineMap(defaultFrom, SESFrom, SESHFrom, gmailFrom, sendmailFrom, rmMailFrom string) {
	EMAIL_ENGINE_MAP = map[string]map[string]interface{}{
		"sendmail": {
			"email": sendmailFrom,
			"regex": regexp.MustCompile(`.`),
		},
		"SES": {
			"email": SESFrom,
			"regex": regexp.MustCompile(`realmaster\.ca`),
		},
		"SESH": {
			"email": SESHFrom,
			"regex": regexp.MustCompile(`realmaster\.ca`),
		},
		"gmail": {
			"email": gmailFrom,
			"regex": regexp.MustCompile(`info@realmaster\.com`),
		},
		"rmMail": {
			"email": rmMailFrom,
			"regex": regexp.MustCompile(`realmaster\.cc|avionhome\.com|4salebc\.ca`),
		},
		"mockMail": {
			"email": defaultFrom,
			"regex": regexp.MustCompile(`.`),
		},
	}
}

// getEngineName returns the engine name from configuration
func getEngineName() string {
	engineName, err := goconfig.ConfigString("mailEngine.mailEngine")
	if err != nil {
		return "sendmail"
	}
	return engineName
}

// getDomainName returns the domain name from the email
func getDomainName(domain string) string {
	parts := strings.Split(domain, ".")
	if len(parts) < 2 {
		return domain
	}
	return strings.ToLower(parts[len(parts)-2])
}

// getAdminEmail returns the admin email address from configuration
func getAdminEmail() string {
	if email, err := goconfig.ConfigString("contact.alertEmail"); err == nil && email != "" {
		return email
	}
	if email, err := goconfig.ConfigString("contact.defaultEmail"); err == nil && email != "" {
		return email
	}
	return "<EMAIL>"
}

// getReplyToEmail returns the reply to email address from configuration
func getReplyToEmail() string {
	if replyToEmail, err := goconfig.ConfigString("contact.defaultRTEmail"); err == nil && replyToEmail != "" {
		return replyToEmail
	}
	return "<EMAIL>"
}

// isValidEmail checks if the email is valid
func isValidEmail(email string) bool {
	return VALID_EMAIL_REGEXP.MatchString(email)
}

// isIgnoredToAddr determines whether an email address should be ignored based on specific criteria
func isIgnoredToAddr(to string) bool {
	if !isValidEmail(to) {
		return true
	}
	parts := strings.SplitN(to, "@", 2)
	if len(parts) != 2 {
		return true
	}
	name := parts[0]
	domain := parts[1]
	if len(name) < 1 {
		return true
	}
	if len(name) < 2 {
		golog.Warn("Warning: email name is too short:", "email", to)
	}
	domainName := getDomainName(domain)
	if domainName == "" {
		return true
	}
	if domainName != "qq" && len(domainName) < 2 {
		return true
	}
	if rubyPattern.MatchString(name) && liaoPattern.MatchString(domain) {
		return true
	}
	invalidEmails := map[string]bool{
		"<EMAIL>": true,
		"<EMAIL>": true,
		"<EMAIL>": true,
	}
	return invalidEmails[strings.ToLower(to)]
}

// ServiceConfig holds the extracted service config
type ServiceConfig struct {
	Service string
	User    string
	Pass    string
	Host    string
	Port    int
}

// getServiceConfig extracts SMTP service config from the input config
func getServiceConfig() (*ServiceConfig, error) {
	serviceRaw, err := goconfig.ConfigString("mailEngine.smtp.service")
	if err != nil {
		return nil, fmt.Errorf("service not specified in mailEngine.smtp")
	}

	service := fmt.Sprintf("%v", serviceRaw)
	serviceKey := strings.ToLower(service)
	rawCfg := goconfig.Config("mailEngine." + serviceKey)
	if rawCfg == nil {
		return nil, fmt.Errorf("mailEngine.%s config not found", serviceKey)
	}

	engineCfg, ok := rawCfg.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("mailEngine.%s config is not a map", serviceKey)
	}
	authCfg, ok := engineCfg["auth"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("auth configuration missing for service %s", service)
	}
	user, userOk := authCfg["user"].(string)
	pass, passOk := authCfg["pass"].(string)
	if !userOk || !passOk {
		return nil, fmt.Errorf("auth configuration incomplete for service %s", service)
	}
	var host string
	var port int
	var hostOk bool
	var portOk bool

	if serviceKey == "gmail" {
		host = GMAIL_HOST
		port = GMAIL_PORT
	} else {
		host, hostOk = authCfg["host"].(string)
		port, portOk = authCfg["port"].(int)
		if !hostOk || !portOk {
			return nil, fmt.Errorf("auth configuration incomplete for service %s", service)
		}
	}

	if host == "" || port == 0 {
		return nil, fmt.Errorf("host or port is not set for service %s", service)
	}

	configOut := &ServiceConfig{
		Service: service,
		User:    user,
		Pass:    pass,
		Host:    host,
		Port:    port,
	}

	return configOut, nil
}

// replaceParam replaces the parameters in the mail
func replaceParam(mail *EmailMessage, data map[string]string) *EmailMessage {
	if mail.rpd {
		return mail
	}

	if mail.Subject != "" {
		mail.Subject = templateReplacing(mail.Subject, data)
	}

	if mail.Text != "" {
		mail.Text = templateReplacing(mail.Text, data)
	}

	if mail.HTML != "" {
		mail.HTML = templateReplacing(mail.HTML, data)
	}

	mail.rpd = true

	if mail.ReplyTo == "" {
		mail.ReplyTo = mail.From
	}

	return mail
}

// templateReplacing replaces the template in the string
func templateReplacing(src string, user map[string]string) string {
	// src = "asdjal sd lajsd {{ fn|default }} asdasd {{ln }}  {{ nm }}"
	// src = "<p>h<b>ellow</b>orld {{fn}} {{ ln }}</p>"
	// src = "Test Dear {{ nm | 用户}}  "
	// user = {fn:'xxxx',ln:'zzzz'}
	re := regexp.MustCompile(`{{\s*(\w+)\s*(\|\s*([\p{Han}\w]+)\s*)?}}`)
	for {
		match := re.FindStringSubmatchIndex(src)
		if match == nil {
			break
		}

		fullMatch := src[match[0]:match[1]]
		key := src[match[2]:match[3]]

		var replacement string
		if val, ok := user[key]; ok && val != "" {
			replacement = val
		} else if match[6] != -1 {
			replacement = src[match[6]:match[7]] // default value
		} else {
			replacement = ""
		}

		src = strings.Replace(src, fullMatch, replacement, 1)
	}
	return src
}
