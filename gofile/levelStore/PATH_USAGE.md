# Path Generation Usage Guide

This document explains how to use the simplified path generation functions in the `levelStore` package.

## Overview

The package provides two main ways to get file paths:

1. **Combined format**: `"/L1/L2"` - Returns path with leading slash (backward compatible)
2. **Separate format**: `"L1", "L2"` - Returns L1 and L2 components separately

## Functions

### 1. GetFullFilePathForProp (Backward Compatible)

Returns the complete path in `/L1/L2` format with leading slash.

```go
path, err := levelStore.GetFullFilePathForProp(propTs, board, sid)
// Returns: "/1223/abc12"
```

**Use cases:**
- File system operations requiring absolute paths
- Backward compatibility with existing code
- Directory creation with `mkdir -p`

### 2. GetPathComponents

Returns a `PathResult` struct containing all path components.

```go
components, err := levelStore.GetPathComponents(propTs, board, sid)
// Returns: &PathResult{
//     Combined: "1223/abc12",  // No leading slash
//     L1: "1223",
//     L2: "abc12"
// }
```

**Use cases:**
- When you need both combined and separate values
- Building different path formats
- Complex path manipulation

### 3. GetL1L2Separate

Returns L1 and L2 as separate string values.

```go
l1, l2, err := levelStore.GetL1L2Separate(propTs, board, sid)
// Returns: l1="1223", l2="abc12"
```

**Use cases:**
- Database queries by L1 directory
- Cache key generation
- Statistics grouping
- When you only need the individual components

## Usage Examples

### Example 1: File Path Construction

```go
// Method 1: Using backward compatible format
path, err := levelStore.GetFullFilePathForProp(propTs, board, sid)
fileName := fmt.Sprintf("%s_%s.jpg", sid, "base62hash")
fullPath := fmt.Sprintf("%s/%s", path, fileName)
// Result: "/1223/abc12/W4054894_xyz123.jpg"

// Method 2: Using components
components, err := levelStore.GetPathComponents(propTs, board, sid)
fileName := fmt.Sprintf("%s_%s.jpg", sid, "base62hash")
relativePath := fmt.Sprintf("%s/%s", components.Combined, fileName)
// Result: "1223/abc12/W4054894_xyz123.jpg"
```

### Example 2: Database Operations

```go
// Query files by L1 directory
l1, l2, err := levelStore.GetL1L2Separate(propTs, board, sid)
query := fmt.Sprintf("SELECT * FROM files WHERE l1 = '%s'", l1)

// Update statistics for specific L1/L2
updateQuery := fmt.Sprintf("UPDATE stats SET count = count + 1 WHERE l1 = '%s' AND l2 = '%s'", l1, l2)
```

### Example 3: Cache Key Generation

```go
l1, l2, err := levelStore.GetL1L2Separate(propTs, board, sid)
cacheKey := fmt.Sprintf("%s:%s:%s", board, l1, l2)
// Result: "TRB:1223:abc12"
```

### Example 4: URL Building

```go
components, err := levelStore.GetPathComponents(propTs, board, sid)
fileName := fmt.Sprintf("%s_%s.webp", sid, "base62hash")
cdnURL := fmt.Sprintf("https://cdn.example.com/%s/%s", components.Combined, fileName)
// Result: "https://cdn.example.com/1223/abc12/W4054894_xyz123.webp"
```

### Example 5: Directory Operations

```go
// Create directories using backward compatible format
path, err := levelStore.GetFullFilePathForProp(propTs, board, sid)
err = os.MkdirAll("/storage" + path, 0755)

// Create directories using separate values
l1, l2, err := levelStore.GetL1L2Separate(propTs, board, sid)
err = os.MkdirAll(fmt.Sprintf("/storage/%s/%s", l1, l2), 0755)
```

## Key Differences

| Function | Return Format | Leading Slash | Use Case |
|----------|---------------|---------------|----------|
| `GetFullFilePathForProp` | `"/L1/L2"` | ✅ Yes | Backward compatibility, absolute paths |
| `GetPathComponents` | `PathResult{Combined: "L1/L2", L1: "L1", L2: "L2"}` | ❌ No | Flexible usage, multiple formats |
| `GetL1L2Separate` | `l1, l2 strings` | ❌ N/A | Individual components only |

## Migration Guide

If you're migrating from the old complex format system:

```go
// Old way (removed)
// result, err := GetFullFilePathWithFormat(propTs, board, sid, PathFormatL1Only)

// New way
l1, l2, err := levelStore.GetL1L2Separate(propTs, board, sid)
// Use l1 for L1-only operations
// Use l2 for L2-only operations
```

## Error Handling

All functions return the same types of errors:
- Empty board name
- Invalid board name (not in `goconfig.SOURCE_L2_SIZE`)
- Invalid timestamp
- Invalid date (before 1970-01-01)
- Empty sid

```go
components, err := levelStore.GetPathComponents(propTs, board, sid)
if err != nil {
    log.Printf("Path generation failed: %v", err)
    return
}
```
