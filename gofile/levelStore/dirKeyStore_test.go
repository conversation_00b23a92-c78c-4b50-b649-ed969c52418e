package levelStore

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	gohelper "github.com/real-rm/gohelper"
	gomongo "github.com/real-rm/gomongo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	testColl            *gomongo.MongoCollection
	originalGetImageDir = GetImageDir
)

// SetupTestProcess sets up the test environment
func SetupTestProcess(t *testing.T) (*gomongo.MongoCollection, func(*gomongo.MongoCollection)) {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "..", "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}

	gohelper.SetRmbaseFileCfg(configPath)
	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}
	coll := gomongo.Coll("tmp", "processStatus")
	testColl = coll

	if coll == nil {
		t.Error("Failed to create test collection.", "name", "processStatus")
	}
	// Return collection and cleanup function
	return coll, func(coll *gomongo.MongoCollection) {
		if coll == nil {
			t.Log("Collection is nil, skipping cleanup")
			return
		}
		if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
			t.Logf("Failed to cleanup collection: %v", err)
		}
	}
}

func TestNewDirKeyStore(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	tests := []struct {
		name           string
		prefix         string
		coll           interface{}
		updateInterval time.Duration
		wantErr        bool
	}{
		{
			name:           "valid parameters",
			prefix:         "TRB",
			coll:           testColl,
			updateInterval: 5 * time.Minute,
			wantErr:        false,
		},
		{
			name:           "nil collection",
			prefix:         "TRB",
			coll:           nil,
			updateInterval: 5 * time.Minute,
			wantErr:        true,
		},
		{
			name:           "invalid collection type",
			prefix:         "TRB",
			coll:           "invalid",
			updateInterval: 5 * time.Minute,
			wantErr:        true,
		},
		{
			name:           "zero update interval",
			prefix:         "TRB",
			coll:           testColl,
			updateInterval: 0,
			wantErr:        false, // Should use default interval
		},
		{
			name:           "negative update interval",
			prefix:         "TRB",
			coll:           testColl,
			updateInterval: -1 * time.Minute,
			wantErr:        false, // Should use default interval
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store, err := NewDirKeyStore(tt.prefix, tt.coll, tt.updateInterval)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, store)
				return
			}
			assert.NoError(t, err)
			assert.NotNil(t, store)
			defer store.Close()

			// Verify that store was created with correct parameters
			assert.Equal(t, tt.prefix, store.prefix)
		})
	}
}

func TestDirKeyStore_AddDirStats(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	tests := []struct {
		name           string
		l1             string
		l2             string
		entityAmount   int
		fileAmount     int
		expectedEntity int
		expectedFile   int
	}{
		{
			name:           "add positive stats",
			l1:             "750",
			l2:             "abc12",
			entityAmount:   10,
			fileAmount:     5,
			expectedEntity: 10,
			expectedFile:   5,
		},
		{
			name:           "add negative stats",
			l1:             "750",
			l2:             "abc12",
			entityAmount:   -5,
			fileAmount:     -2,
			expectedEntity: 5, // 10 + (-5)
			expectedFile:   3, // 5 + (-2)
		},
		{
			name:           "add zero stats",
			l1:             "750",
			l2:             "abc12",
			entityAmount:   0,
			fileAmount:     0,
			expectedEntity: 5, // Previous value remains
			expectedFile:   3, // Previous value remains
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store.AddDirStats(tt.l1, tt.l2, tt.entityAmount, tt.fileAmount)
			key := tt.l1 + "-" + tt.l2
			stats, exists := store.dirMap[key]
			assert.True(t, exists)
			assert.Equal(t, tt.expectedEntity, stats.EntityAmount)
			assert.Equal(t, tt.expectedFile, stats.FileAmount)
		})
	}
}

func TestDirKeyStore_SaveChangedCurrent(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	// Add test data
	store.AddDirStats("750", "abc12", 10, 5)
	store.AddDirStats("750", "def34", 20, 10)
	store.AddDirStats("751", "ghi56", 30, 15)

	// Save to database
	err = store.saveStatsToDb(map[string]map[string]DirStats{
		"750": {
			"abc12": {EntityAmount: 10, FileAmount: 5},
			"def34": {EntityAmount: 20, FileAmount: 10},
		},
		"751": {
			"ghi56": {EntityAmount: 30, FileAmount: 15},
		},
	})
	assert.NoError(t, err)

	// Verify data in database
	ctx := context.Background()
	var result struct {
		L2Stats       map[string]DirStats `bson:"l2Stats"`
		TotalFiles    int                 `bson:"totalFiles"`
		TotalEntities int                 `bson:"totalEntities"`
	}

	// Check L1 750
	err = coll.FindOne(ctx, bson.D{
		{Key: "_id", Value: bson.D{
			{Key: "board", Value: "TRB"},
			{Key: "l1", Value: "750"},
		}},
	}).Decode(&result)
	assert.NoError(t, err)
	assert.Equal(t, 15, result.TotalFiles)    // 5 + 10
	assert.Equal(t, 30, result.TotalEntities) // 10 + 20
	assert.Equal(t, 5, result.L2Stats["abc12"].FileAmount)
	assert.Equal(t, 10, result.L2Stats["def34"].FileAmount)
	assert.Equal(t, 10, result.L2Stats["abc12"].EntityAmount)
	assert.Equal(t, 20, result.L2Stats["def34"].EntityAmount)

	// Check L1 751
	err = coll.FindOne(ctx, bson.D{
		{Key: "_id", Value: bson.D{
			{Key: "board", Value: "TRB"},
			{Key: "l1", Value: "751"},
		}},
	}).Decode(&result)
	assert.NoError(t, err)
	assert.Equal(t, 15, result.TotalFiles)
	assert.Equal(t, 30, result.TotalEntities)
	assert.Equal(t, 15, result.L2Stats["ghi56"].FileAmount)
	assert.Equal(t, 30, result.L2Stats["ghi56"].EntityAmount)
}

func TestDirKeyStore_AddTmpDirMap(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	tests := []struct {
		name     string
		l1       string
		dirArray []string
	}{
		{
			name:     "add valid array",
			l1:       "750",
			dirArray: []string{"abc12", "def34", "ghi56"},
		},
		{
			name:     "add empty array",
			l1:       "751",
			dirArray: []string{},
		},
		{
			name:     "add nil array",
			l1:       "752",
			dirArray: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store.AddTmpDirMap(tt.l1, tt.dirArray)
			value, exists := store.tmpDirMap.Load(tt.l1)
			assert.True(t, exists)
			if tt.dirArray != nil {
				assert.Equal(t, tt.dirArray, value)
			} else {
				assert.Nil(t, value)
			}
		})
	}
}

func TestDirKeyStore_CleanTmpDirMap(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	// Calculate current and old L1 values
	now := time.Now()
	currentOnD := gohelper.TimeToDateInt(now)
	currentL1 := CalculateL1(currentOnD, "TRB")

	// Calculate old date (DEFAULT_L1_DIFF_LIMIT + 1 weeks ago)
	oldDate := now.AddDate(0, 0, -(DEFAULT_L1_DIFF_LIMIT+1)*7)
	oldOnD := gohelper.TimeToDateInt(oldDate)
	oldL1 := CalculateL1(oldOnD, "TRB")

	// Add test data
	store.AddTmpDirMap(currentL1, []string{"abc12"})
	store.AddTmpDirMap(oldL1, []string{"def34"})

	// Clean old entries
	store.CleanTmpDirMap()

	// Check if old entry was removed
	_, ok := store.tmpDirMap.Load(oldL1)
	assert.False(t, ok)

	// Check if current entry remains
	value, ok := store.tmpDirMap.Load(currentL1)
	assert.True(t, ok)
	assert.NotNil(t, value)
}

func TestDirKeyStore_FetchDirArray(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	ctx := context.Background()

	// Insert test data
	_, err = coll.UpdateOne(ctx,
		bson.D{
			{Key: "_id", Value: bson.D{
				{Key: "board", Value: "TRB"},
				{Key: "l1", Value: "1200"},
			}},
		},
		bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "dirArray", Value: []string{"abc12", "def34"}},
			}},
		},
		options.Update().SetUpsert(true))
	assert.NoError(t, err)

	tests := []struct {
		name     string
		board    string
		propTsD  int
		wantErr  bool
		wantSize int
	}{
		{
			name:     "fetch from database",
			board:    "TRB",
			propTsD:  20240101,
			wantErr:  false,
			wantSize: 2,
		},
		{
			name:     "fetch from cache",
			board:    "TRB",
			propTsD:  20240101,
			wantErr:  false,
			wantSize: 2,
		},
		{
			name:     "invalid board",
			board:    "INVALID",
			propTsD:  20240101,
			wantErr:  true,
			wantSize: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dirArray, err := store.FetchDirArray(ctx, tt.board, tt.propTsD)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, dirArray)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantSize, len(dirArray))
			}
		})
	}
}

func TestDirKeyStore_SaveDirArrayInDB(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	ctx := context.Background()

	// Insert test data first
	_, err = coll.UpdateOne(ctx,
		bson.D{
			{Key: "_id", Value: bson.D{
				{Key: "board", Value: "TRB"},
				{Key: "l1", Value: "1200"},
			}},
		},
		bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "dirArray", Value: []string{"abc12", "def34"}},
			}},
		},
		options.Update().SetUpsert(true))
	assert.NoError(t, err)

	tests := []struct {
		name        string
		board       string
		propTsD     int
		force       bool
		wantErr     bool
		wantNewSize int
	}{
		{
			name:        "save new array",
			board:       "TRB",
			propTsD:     20240101,
			force:       false,
			wantErr:     false,
			wantNewSize: 2,
		},
		{
			name:        "force update existing array",
			board:       "TRB",
			propTsD:     20240101,
			force:       true,
			wantErr:     false,
			wantNewSize: 512,
		},
		{
			name:        "invalid board",
			board:       "INVALID",
			propTsD:     20240101,
			force:       false,
			wantErr:     true,
			wantNewSize: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dirArray, err := store.SaveDirArrayInDB(ctx, tt.board, tt.propTsD, tt.force)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, dirArray)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantNewSize, len(dirArray))
			}
		})
	}
}

// Helper to create L1 directories for all tempDirs and L1s
func createL1Dirs(t *testing.T, tempDirs []string, l1s []string) {
	for _, dir := range tempDirs {
		for _, l1 := range l1s {
			l1Dir := filepath.Join(dir, l1)
			err := os.MkdirAll(l1Dir, 0755)
			require.NoError(t, err)
		}
	}
}

func mockGetImageDir(board string, tempDirs []string) []string {
	if board == "TRB" {
		return tempDirs
	}
	return originalGetImageDir(board)
}

func TestSaveCurrentInFileWithData(t *testing.T) {
	// Create temporary directories for testing
	tempDirs := make([]string, 3)
	for i := range tempDirs {
		dir, err := os.MkdirTemp("", fmt.Sprintf("gofile_test_%d_*", i))
		require.NoError(t, err)
		tempDirs[i] = dir
	}
	defer func() {
		// Restore permissions for all subdirectories
		for _, dir := range tempDirs {
			err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
				if err != nil {
					return err
				}
				if info.IsDir() {
					if err := os.Chmod(path, 0755); err != nil {
						t.Logf("Failed to restore permissions for %s: %v", path, err)
					}
				}
				return nil
			})
			if err != nil {
				t.Errorf("Failed to restore permissions for %s: %v", dir, err)
			}
			// Now try to remove the directory
			if err := os.RemoveAll(dir); err != nil {
				t.Errorf("Failed to remove temporary directory: %v", err)
			}
		}
	}()

	// Create test store with proper initialization
	store := &DirKeyStore{
		prefix:     "TRB",
		sourceRoot: tempDirs[0],
		dirMap:     make(map[string]DirStats),
	}

	// Mock GetImageDir to return our test directories
	GetImageDir = func(board string) []string { return mockGetImageDir(board, tempDirs) }
	defer func() { GetImageDir = originalGetImageDir }()

	tests := []struct {
		name    string
		l1Map   map[string]map[string]DirStats
		setup   func()                        // Setup function to create initial files
		cleanup func()                        // Cleanup function to restore permissions
		verify  func(t *testing.T, l1 string) // Verification function
		wantErr bool
	}{
		{
			name: "save new stats to new files",
			l1Map: map[string]map[string]DirStats{
				"1200": {
					"abc12": {EntityAmount: 5, FileAmount: 3},
					"def34": {EntityAmount: 2, FileAmount: 1},
				},
			},
			setup: func() {
				createL1Dirs(t, tempDirs, []string{"1200"})
			},
			verify: func(t *testing.T, l1 string) {
				// Verify files exist in all directories
				for _, dir := range tempDirs {
					metaPath := filepath.Join(dir, l1, "dir_stats.json")
					data, err := os.ReadFile(metaPath)
					require.NoError(t, err)

					var meta DirMetaInfo
					err = json.Unmarshal(data, &meta)
					require.NoError(t, err)

					// Verify stats
					assert.Equal(t, 7, meta.TotalEntities)
					assert.Equal(t, 4, meta.TotalFiles)
					assert.Equal(t, DirStats{EntityAmount: 5, FileAmount: 3}, meta.L2Stats["abc12"])
					assert.Equal(t, DirStats{EntityAmount: 2, FileAmount: 1}, meta.L2Stats["def34"])
				}
			},
		},
		{
			name: "accumulate stats to existing files",
			l1Map: map[string]map[string]DirStats{
				"1200": {
					"abc12": {EntityAmount: 3, FileAmount: 2},
					"def34": {EntityAmount: 1, FileAmount: 1},
				},
			},
			setup: func() {
				createL1Dirs(t, tempDirs, []string{"1200"})
				// Create initial files with existing stats in all directories
				for _, dir := range tempDirs {
					l1Dir := filepath.Join(dir, "1200")
					existingMeta := DirMetaInfo{
						TotalEntities: 7,
						TotalFiles:    4,
						L2Stats: map[string]DirStats{
							"abc12": {EntityAmount: 5, FileAmount: 3},
							"def34": {EntityAmount: 2, FileAmount: 1},
						},
						LastUpdated: time.Now(),
					}
					data, err := json.MarshalIndent(existingMeta, "", "  ")
					require.NoError(t, err)
					err = os.WriteFile(filepath.Join(l1Dir, "dir_stats.json"), data, 0644)
					require.NoError(t, err)
				}
			},
			verify: func(t *testing.T, l1 string) {
				// Verify files in all directories
				for _, dir := range tempDirs {
					metaPath := filepath.Join(dir, l1, "dir_stats.json")
					data, err := os.ReadFile(metaPath)
					require.NoError(t, err)

					var meta DirMetaInfo
					err = json.Unmarshal(data, &meta)
					require.NoError(t, err)

					// Verify accumulated stats
					assert.Equal(t, 11, meta.TotalEntities)                                          // 5+3 + 2+1 = 11
					assert.Equal(t, 7, meta.TotalFiles)                                              // 3+2 + 1+1 = 7
					assert.Equal(t, DirStats{EntityAmount: 8, FileAmount: 5}, meta.L2Stats["abc12"]) // 5+3, 3+2
					assert.Equal(t, DirStats{EntityAmount: 3, FileAmount: 2}, meta.L2Stats["def34"]) // 2+1, 1+1
				}
			},
		},
		{
			name: "handle invalid directory",
			l1Map: map[string]map[string]DirStats{
				"1200": {
					"abc12": {EntityAmount: 5, FileAmount: 3},
				},
			},
			setup: func() {
				createL1Dirs(t, tempDirs, []string{"1200"})
				// Make the second directory read-only
				err := os.Chmod(tempDirs[1], 0444)
				require.NoError(t, err)
				// Verify we can't write to the directory
				testFile := filepath.Join(tempDirs[1], "test.txt")
				err = os.WriteFile(testFile, []byte("test"), 0644)
				require.Error(t, err) // This should fail
			},
			cleanup: func() {
				// Restore directory permissions
				if err := os.Chmod(tempDirs[1], 0755); err != nil {
					t.Logf("Failed to restore directory permissions: %v", err)
				}
			},
			wantErr: true,
		},
		{
			name: "handle multiple L1 directories",
			l1Map: map[string]map[string]DirStats{
				"1200": {
					"abc12": {EntityAmount: 5, FileAmount: 3},
				},
				"1201": {
					"def34": {EntityAmount: 2, FileAmount: 1},
				},
			},
			setup: func() {
				// Clean up any existing files
				for _, dir := range tempDirs {
					for _, l1 := range []string{"1200", "1201"} {
						metaPath := filepath.Join(dir, l1, "dir_stats.json")
						if _, err := os.Stat(metaPath); err == nil {
							if err := os.Remove(metaPath); err != nil {
								t.Errorf("Failed to remove meta file %s: %v", metaPath, err)
							}
						}
					}
				}
				// Create L1 directories
				createL1Dirs(t, tempDirs, []string{"1200", "1201"})
				// Add a dummy file to each L1 directory to ensure calculateDirSize works
				for _, dir := range tempDirs {
					for _, l1 := range []string{"1200", "1201"} {
						dummyFile := filepath.Join(dir, l1, "dummy.txt")
						err := os.WriteFile(dummyFile, []byte("dummy"), 0644)
						require.NoError(t, err)
						// Debug log to verify dummy file exists
						if info, err := os.Stat(dummyFile); err != nil {
							t.Logf("Failed to stat dummy file %s: %v", dummyFile, err)
						} else {
							t.Logf("Dummy file exists: %s (mode: %v)", dummyFile, info.Mode())
						}
					}
				}
				// Reset store state
				store.dirMap = make(map[string]DirStats)
				// Add test data to store
				for l1, l2Stats := range map[string]map[string]DirStats{
					"1200": {"abc12": {EntityAmount: 5, FileAmount: 3}},
					"1201": {"def34": {EntityAmount: 2, FileAmount: 1}},
				} {
					for l2, stats := range l2Stats {
						store.AddDirStats(l1, l2, stats.EntityAmount, stats.FileAmount)
					}
				}
				// Debug: Print directory structure
				for _, dir := range tempDirs {
					t.Logf("Created directory: %s", dir)
					for _, l1 := range []string{"1200", "1201"} {
						l1Path := filepath.Join(dir, l1)
						if info, err := os.Stat(l1Path); err != nil {
							t.Logf("Failed to stat directory %s: %v", l1Path, err)
						} else {
							t.Logf("Directory exists: %s (mode: %v)", l1Path, info.Mode())
						}
					}
				}
			},
			verify: func(t *testing.T, l1 string) {
				// Check each directory
				for _, dir := range tempDirs {
					metaPath := filepath.Join(dir, l1, "dir_stats.json")
					// Debug: Check if directory exists
					if info, err := os.Stat(filepath.Dir(metaPath)); err != nil {
						t.Logf("Directory does not exist: %s", filepath.Dir(metaPath))
					} else {
						t.Logf("Directory exists: %s (mode: %v)", filepath.Dir(metaPath), info.Mode())
					}
					// Debug: Check if file exists
					if info, err := os.Stat(metaPath); err != nil {
						t.Logf("File does not exist: %s", metaPath)
					} else {
						t.Logf("File exists: %s (mode: %v)", metaPath, info.Mode())
					}
					data, err := os.ReadFile(metaPath)
					if err != nil {
						t.Fatalf("Failed to read meta file for %s: %v", l1, err)
					}
					var meta DirMetaInfo
					err = json.Unmarshal(data, &meta)
					require.NoError(t, err)
					switch l1 {
					case "1200":
						assert.Equal(t, 5, meta.TotalEntities, "TotalEntities mismatch for 1200")
						assert.Equal(t, 3, meta.TotalFiles, "TotalFiles mismatch for 1200")
						assert.Equal(t, DirStats{EntityAmount: 5, FileAmount: 3}, meta.L2Stats["abc12"], "L2Stats mismatch for 1200")
					case "1201":
						assert.Equal(t, 2, meta.TotalEntities, "TotalEntities mismatch for 1201")
						assert.Equal(t, 1, meta.TotalFiles, "TotalFiles mismatch for 1201")
						assert.Equal(t, DirStats{EntityAmount: 2, FileAmount: 1}, meta.L2Stats["def34"], "L2Stats mismatch for 1201")
					}
				}
			},
		},
		{
			name: "handle non-existent directory",
			l1Map: map[string]map[string]DirStats{
				"1200": {
					"abc12": {EntityAmount: 5, FileAmount: 3},
				},
			},
			setup: func() {
				// Remove the third directory
				err := os.RemoveAll(tempDirs[2])
				require.NoError(t, err)
				// Mock GetImageDir to return only the deleted directory
				GetImageDir = func(board string) []string { return []string{tempDirs[2]} }
			},
			cleanup: func() {
				// Restore GetImageDir to its original state
				GetImageDir = originalGetImageDir
				// Recreate the third directory if it doesn't exist
				if _, err := os.Stat(tempDirs[2]); os.IsNotExist(err) {
					err := os.MkdirAll(tempDirs[2], 0755)
					require.NoError(t, err)
				}
			},
			verify: func(t *testing.T, l1 string) {
				// No verification needed as we expect an error
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup if needed
			if tt.setup != nil {
				tt.setup()
			}

			// Execute
			err := store.saveStatsToFile(tt.l1Map)
			if err != nil {
				t.Logf("saveCurrentInFileWithData returned error: %v", err)
			}

			// Verify
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				for l1 := range tt.l1Map {
					tt.verify(t, l1)
				}
			}

			// Cleanup if needed
			if tt.cleanup != nil {
				tt.cleanup()
			}
		})
	}
}

// Add more test cases to improve coverage
func TestDirKeyStore_CalculateDirSize(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	// Create a temporary directory for testing
	tempDir, err := os.MkdirTemp("", "gofile_test_*")
	require.NoError(t, err)
	defer func() {
		if err := os.RemoveAll(tempDir); err != nil {
			t.Errorf("Failed to remove temporary directory: %v", err)
		}
	}()

	// Create test files with different sizes
	files := []struct {
		name string
		size int64
	}{
		{"file1.txt", 100},
		{"file2.txt", 200},
		{"file3.txt", 300},
	}

	for _, f := range files {
		filePath := filepath.Join(tempDir, f.name)
		err := os.WriteFile(filePath, make([]byte, f.size), 0644)
		require.NoError(t, err)
	}

	// Test calculateDirSize
	actualSize, diskSize, err := calculateDirSize(tempDir)
	require.NoError(t, err)
	assert.Equal(t, int64(600), actualSize)        // 100 + 200 + 300
	assert.GreaterOrEqual(t, diskSize, actualSize) // Disk size should be >= actual size
}

func TestDirKeyStore_GroupStatsByL1(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	// Add test data
	store.AddDirStats("1200", "abc12", 5, 3)
	store.AddDirStats("1200", "def34", 2, 1)
	store.AddDirStats("1201", "ghi56", 3, 2)

	// Test groupStatsByL1
	l1Map := groupStatsByL1(store.dirMap)

	// Verify results
	assert.Equal(t, 2, len(l1Map))
	assert.Equal(t, 2, len(l1Map["1200"]))
	assert.Equal(t, 1, len(l1Map["1201"]))

	// Check specific values
	assert.Equal(t, DirStats{EntityAmount: 5, FileAmount: 3}, l1Map["1200"]["abc12"])
	assert.Equal(t, DirStats{EntityAmount: 2, FileAmount: 1}, l1Map["1200"]["def34"])
	assert.Equal(t, DirStats{EntityAmount: 3, FileAmount: 2}, l1Map["1201"]["ghi56"])
}

func TestDirKeyStore_InvalidBoard(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	// Test invalid board
	assert.False(t, IsValidBoard(""))
	assert.False(t, IsValidBoard("INVALID"))
	assert.True(t, IsValidBoard("TRB"))
}

func TestDirKeyStore_Close(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	// Add some stats
	store.AddDirStats("1200", "abc12", 5, 3)

	// Close the store
	store.Close()

	// Verify that dirMap is NOT empty after Close()
	assert.Empty(t, store.dirMap, "Close() should also clear dirMap")
}

func TestDirKeyStore_AddDirStats_EdgeCases(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	// Test adding zero stats
	store.AddDirStats("1200", "abc12", 0, 0)
	key := "1200-abc12"
	_, exists := store.dirMap[key]
	assert.False(t, exists)

	// Test adding negative stats
	store.AddDirStats("1200", "abc12", -5, -3)
	stats, exists2 := store.dirMap[key]
	assert.True(t, exists2)
	assert.Equal(t, -5, stats.EntityAmount)
	assert.Equal(t, -3, stats.FileAmount)
}

func TestDirKeyStore_SaveChangedCurrent_NoChanges(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	// Add some stats
	store.AddDirStats("1200", "abc12", 5, 3)
	store.AddDirStats("1200", "def34", 2, 1)

	// Save changes first time
	store.SaveChangedCurrent()

	// Try to save again without changes
	store.SaveChangedCurrent()

	// Verify that dirMap is still empty
	store.mu.RLock()
	assert.Empty(t, store.dirMap, "dirMap should remain empty when no changes are made")
	store.mu.RUnlock()
}

func TestDirKeyStore_SaveChangedCurrent_WithMultipleL1s(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	// Add stats for multiple L1 directories
	store.AddDirStats("1200", "abc12", 5, 3)
	store.AddDirStats("1201", "def34", 2, 1)
	store.AddDirStats("1202", "ghi56", 3, 2)

	// Save changes
	store.SaveChangedCurrent()

	// Verify that dirMap is cleared
	store.mu.RLock()
	assert.Empty(t, store.dirMap, "dirMap should be cleared after saving changes")
	store.mu.RUnlock()
}

func TestDirKeyStore_SaveChangedCurrent_WithNegativeStats(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	// Add positive stats first
	store.AddDirStats("1200", "abc12", 5, 3)
	store.AddDirStats("1200", "def34", 2, 1)

	// Save changes
	store.SaveChangedCurrent()

	// Add negative stats
	store.AddDirStats("1200", "abc12", -2, -1)
	store.AddDirStats("1200", "def34", -1, -1)

	// Save changes again
	store.SaveChangedCurrent()

	// Verify that dirMap is cleared
	store.mu.RLock()
	assert.Empty(t, store.dirMap, "dirMap should be cleared after saving changes")
	store.mu.RUnlock()
}

func TestDirKeyStore_SaveChangedCurrent_WithZeroStats(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	// Add some stats
	store.AddDirStats("1200", "abc12", 5, 3)
	store.AddDirStats("1200", "def34", 2, 1)

	// Save changes
	store.SaveChangedCurrent()

	// Add zero stats
	store.AddDirStats("1200", "abc12", 0, 0)
	store.AddDirStats("1200", "def34", 0, 0)

	// Save changes again
	store.SaveChangedCurrent()

	// Verify that dirMap is cleared
	store.mu.RLock()
	assert.Empty(t, store.dirMap, "dirMap should be cleared after saving changes")
	store.mu.RUnlock()
}

func TestDirKeyStore_SaveChangedCurrent_WithNilStore(t *testing.T) {
	var store *DirKeyStore
	assert.NotPanics(t, func() {
		store.SaveChangedCurrent()
	}, "SaveChangedCurrent should not panic when store is nil")
}

func TestDirKeyStore_SaveChangedCurrent_WithNilCollection(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	store.collection = nil // Set collection to nil after creation
	assert.NotPanics(t, func() {
		store.SaveChangedCurrent()
	}, "SaveChangedCurrent should not panic when collection is nil")
}

func TestDirKeyStore_SaveChangedCurrent_WithFileError(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "gofile_test_*")
	require.NoError(t, err)
	defer func() {
		if err := os.RemoveAll(tempDir); err != nil {
			t.Errorf("Failed to remove temporary directory: %v", err)
		}
	}()

	// Make directory read-only
	err = os.Chmod(tempDir, 0444)
	require.NoError(t, err)
	defer func() {
		if err := os.Chmod(tempDir, 0755); err != nil {
			t.Errorf("Failed to restore directory permissions: %v", err)
		}
	}()

	// Create test store with proper initialization
	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	store.sourceRoot = tempDir
	store.lastUpdateTs = time.Now()
	store.lastSavedTs = time.Now().Add(-time.Hour) // Make sure there are changes

	// Mock GetImageDir to return our test directory
	originalGetImageDir := GetImageDir
	GetImageDir = func(board string) []string { return []string{tempDir} }
	defer func() { GetImageDir = originalGetImageDir }()

	// Add test data
	store.AddDirStats("1200", "abc12", 5, 3)

	// Save changes - should not panic
	store.SaveChangedCurrent()

	// Verify database was still updated despite file error
	ctx := context.Background()
	var result struct {
		L2Stats       map[string]DirStats `bson:"l2Stats"`
		TotalFiles    int                 `bson:"totalFiles"`
		TotalEntities int                 `bson:"totalEntities"`
	}

	err = coll.FindOne(ctx, bson.D{
		{Key: "_id", Value: bson.D{
			{Key: "board", Value: "TRB"},
			{Key: "l1", Value: "1200"},
		}},
	}).Decode(&result)
	require.NoError(t, err)
	assert.Equal(t, 3, result.TotalFiles)
	assert.Equal(t, 5, result.TotalEntities)
	assert.Equal(t, DirStats{EntityAmount: 5, FileAmount: 3}, result.L2Stats["abc12"])
}

func TestDirKeyStore_SaveChangedCurrent_WithDBError(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	store.collection = nil // Set collection to nil after creation
	store.lastUpdateTs = time.Now()
	store.lastSavedTs = time.Now().Add(-time.Hour) // Make sure there are changes

	// Add test data
	store.AddDirStats("1200", "abc12", 5, 3)

	// Save changes - should not panic
	assert.NotPanics(t, func() {
		store.SaveChangedCurrent()
	}, "SaveChangedCurrent should not panic when collection is nil")
}

func TestDirKeyStore_SetSourceRoot(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	tests := []struct {
		name     string
		root     string
		expected string
	}{
		{
			name:     "set valid root",
			root:     "/custom/path",
			expected: "/custom/path",
		},
		{
			name:     "set empty root",
			root:     "",
			expected: DEFAULT_SOURCE_ROOT, // Should keep default
		},
		{
			name:     "set root with spaces",
			root:     "  /path/with/spaces  ",
			expected: "/path/with/spaces",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store.SetSourceRoot(tt.root)
			assert.Equal(t, tt.expected, store.sourceRoot)
		})
	}
}

// Test the new extracted DirKeyStore methods
func TestDirKeyStore_FetchDirArrayByL1(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	ctx := context.Background()
	board := "TRB"
	l1 := "1200"

	tests := []struct {
		name      string
		board     string
		l1        string
		setupData bool
		wantErr   bool
	}{
		{
			name:      "valid fetch with existing data",
			board:     board,
			l1:        l1,
			setupData: true,
			wantErr:   false,
		},
		{
			name:      "fetch non-existent data creates new",
			board:     board,
			l1:        "9999",
			setupData: false,
			wantErr:   false, // Should create new array
		},
		{
			name:      "invalid board",
			board:     "INVALID",
			l1:        l1,
			setupData: false,
			wantErr:   true,
		},
		{
			name:      "empty l1",
			board:     board,
			l1:        "",
			setupData: false,
			wantErr:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test data if needed
			if tt.setupData {
				_, err := store.SaveDirArrayByL1(ctx, board, l1, true)
				require.NoError(t, err)
			}

			result, err := store.FetchDirArrayByL1(ctx, tt.board, tt.l1)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Greater(t, len(result), 0)
			}
		})
	}
}

func TestDirKeyStore_SaveDirArrayByL1(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	ctx := context.Background()
	board := "TRB"
	l1 := "1201"

	tests := []struct {
		name        string
		board       string
		l1          string
		forceUpdate bool
		wantErr     bool
	}{
		{
			name:        "create new array",
			board:       board,
			l1:          l1,
			forceUpdate: false,
			wantErr:     false,
		},
		{
			name:        "force update existing array",
			board:       board,
			l1:          l1,
			forceUpdate: true,
			wantErr:     false,
		},
		{
			name:        "create array for new l1",
			board:       board,
			l1:          "1202",
			forceUpdate: false,
			wantErr:     false,
		},
		{
			name:        "invalid board",
			board:       "INVALID",
			l1:          l1,
			forceUpdate: false,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := store.SaveDirArrayByL1(ctx, tt.board, tt.l1, tt.forceUpdate)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Greater(t, len(result), 0)
			}
		})
	}
}

func TestDirKeyStore_FetchSave_Integration(t *testing.T) {
	coll, cleanup := SetupTestProcess(t)
	defer cleanup(coll)

	store, err := NewDirKeyStore("TRB", coll)
	require.NoError(t, err)
	defer store.Close()

	ctx := context.Background()
	board := "TRB"
	l1 := "1203"

	// Test integration: save then fetch
	t.Run("save_then_fetch", func(t *testing.T) {
		// First save an array
		savedArray, err := store.SaveDirArrayByL1(ctx, board, l1, true)
		require.NoError(t, err)
		require.NotNil(t, savedArray)
		require.Greater(t, len(savedArray), 0)

		// Then fetch the same array
		fetchedArray, err := store.FetchDirArrayByL1(ctx, board, l1)
		require.NoError(t, err)
		require.NotNil(t, fetchedArray)

		// Should be the same
		assert.Equal(t, savedArray, fetchedArray)
	})

	// Test fetch non-existent (should auto-create)
	t.Run("fetch_nonexistent_autocreate", func(t *testing.T) {
		l1New := "1204"

		// Fetch non-existent should auto-create
		fetchedArray, err := store.FetchDirArrayByL1(ctx, board, l1New)
		require.NoError(t, err)
		require.NotNil(t, fetchedArray)
		require.Greater(t, len(fetchedArray), 0)

		// Fetch again should return the same array
		fetchedArray2, err := store.FetchDirArrayByL1(ctx, board, l1New)
		require.NoError(t, err)
		assert.Equal(t, fetchedArray, fetchedArray2)
	})

	// Test original methods still work
	t.Run("original_methods_compatibility", func(t *testing.T) {
		propTsD := 20240615 // 2024-06-15

		// Original FetchDirArray should work
		array1, err := store.FetchDirArray(ctx, board, propTsD)
		require.NoError(t, err)
		require.NotNil(t, array1)

		// Original SaveDirArrayInDB should work
		array2, err := store.SaveDirArrayInDB(ctx, board, propTsD, true)
		require.NoError(t, err)
		require.NotNil(t, array2)

		// Should be the same
		assert.Equal(t, array1, array2)
	})
}
