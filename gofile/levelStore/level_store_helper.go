package levelStore

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"hash/crc32"
	"io/fs"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/spaolacci/murmur3"

	goconfig "github.com/real-rm/goconfig"
	gohelper "github.com/real-rm/gohelper"
	golog "github.com/real-rm/golog"
)

const (
	// 使用一个随机无意义的字符串作为 seed
	FIXED_SEED        = "a9#T" // 4位随机字符串，包含字母、数字和特殊字符
	TOTAL_UUID5_COUNT = 2048   // Total number of UUID5s to generate
)

var (
	defaultL2FolderFile string
	cachedUUIDs         []string
	cachedUUIDsMu       sync.RWMutex
)

var BoardImageDirName = map[string]string{
	"CAR": "reso_car_image_dir",
	"DDF": "reso_crea_image_dir",
	"BRE": "reso_bcre_image_dir",
	"EDM": "reso_edm_image_dir",
	"TRB": "reso_treb_image_dir",
}

// SetL2FolderFile sets the L2 folder file path
func SetL2FolderFile(path string) {
	defaultL2FolderFile = path
}

func init() {
	// Default to output UUID5 list file to temp directory to avoid overwriting source code
	defaultL2FolderFile = filepath.Join(os.TempDir(), "file_server_uuid5.go")
}

// CalculateL1 calculates L1 value based on date number (YYYYMMDD)
// Returns a folder number where:
// - Each year has 50 folders (weeks)
// - Weeks 0 and 49+ are merged into one folder
// - 2015 starts at 750
// - 2024 maps to 1200
// - 2025 maps to 1250
func CalculateL1(dateNum int, board string) string {
	date := gohelper.DateToTime(dateNum)
	year := date.Year()
	_, week := date.ISOWeek()
	if year < 2015 {
		return fmt.Sprintf("%d", 750)
	}
	// Calculate base folder number for the year
	baseFolder := 750 + (year-2015)*50

	// Adjust week number
	// Merge weeks 0 and 50+ into one folder (0)
	if week == 1 || week > 50 {
		week = 1
	}

	folderNum := baseFolder + week - 1
	if board == "USER" {
		folderNum = baseFolder
	}
	return fmt.Sprintf("%d", folderNum)
}

// CalculateL2Index calculates an index between 0 and size-1 based on sid
func CalculateL2Index(sid string, size int) (int, error) {
	if size <= 0 {
		golog.Error("invalid l2Array size", "size", size)
		return 0, fmt.Errorf("invalid l2Array size: %d", size)
	}
	h := crc32.NewIEEE()

	if _, err := fmt.Fprintf(h, "%s:%s", FIXED_SEED, sid); err != nil {
		golog.Error("failed to calculate L2 index", "error", err)
		return 0, fmt.Errorf("failed to calculate L2 index: %v", err)
	}
	hash := h.Sum32()

	return int(hash % uint32(size)), nil
}

// CalculateL2 calculates L2 value (eg. abc12) using CRC32
func CalculateL2(sid string, l2Array []string) (string, error) {
	if len(l2Array) == 0 {
		golog.Error("l2Array cannot be empty or nil")
		return "", fmt.Errorf("l2Array cannot be empty or nil")
	}
	index, err := CalculateL2Index(sid, len(l2Array))
	if err != nil {
		golog.Error("fail to get L2 index", "error", err)
		return "", fmt.Errorf("failed to get L2 index: %w", err)
	}
	return l2Array[index], nil
}

// GenerateUUID5Array generates an array of 5-character UUIDs
func GenerateUUID5Array(size int) ([]string, error) {
	if size <= 0 {
		return nil, fmt.Errorf("size must be greater than 0, got %d", size)
	}
	result := make([]string, size)
	seen := make(map[string]struct{}, size)
	for i := 0; i < size; {
		b := make([]byte, 4)
		if _, err := rand.Read(b); err != nil {
			return nil, fmt.Errorf("failed to generate random bytes: %w", err)
		}
		id := hex.EncodeToString(b)[:5]
		if _, exists := seen[id]; exists {
			continue
		}
		seen[id] = struct{}{}
		result[i] = id
		i++
	}
	return result, nil
}

// GenerateAndSaveUUID5List generates size number of UUID5s and saves them to a file as a Go constant
// Returns the generated UUIDs and any error that occurred
// This step has already been done and file is saved in goconfig package, no need to run it again.
func GenerateAndSaveUUID5List(filePath string, size int) ([]string, error) {
	// Generate UUIDs
	uuids, err := GenerateUUID5Array(size)
	if err != nil {
		golog.Error("failed to generate UUID5 array", "error", err)
		return nil, fmt.Errorf("failed to generate UUID5 array: %w", err)
	}

	// Create the constant declaration with package
	var builder strings.Builder
	builder.WriteString("package goconfig\n\n")
	builder.WriteString("var L2_FOLDER_LIST = []string{")

	// Write UUIDs, 4 per line
	for i := 0; i < len(uuids); i++ {
		if i%4 == 0 {
			builder.WriteString("\n\t")
		}
		builder.WriteString(fmt.Sprintf("%q", uuids[i]))
		if i < len(uuids)-1 {
			builder.WriteString(", ")
		}
	}
	builder.WriteString(",\n}\n")

	// Ensure directory exists
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0o755); err != nil {
		return nil, fmt.Errorf("create dir: %w", err)
	}

	// Add timestamp to filename to avoid conflicts
	baseName := filepath.Base(filePath)
	ext := filepath.Ext(baseName)
	nameWithoutExt := strings.TrimSuffix(baseName, ext)
	timestamp := time.Now().Format("20060102_150405")
	newFileName := fmt.Sprintf("%s_%s%s", nameWithoutExt, timestamp, ext)
	filePath = filepath.Join(dir, newFileName)

	// Write to file
	if err := os.WriteFile(filePath, []byte(builder.String()), 0644); err != nil {
		golog.Error("failed to write UUID5 list to file", "file", filePath, "error", err)
		return nil, fmt.Errorf("write UUID5 list: %w", err)
	}

	golog.Info("Generated UUID5 list file", "path", filePath)
	return uuids, nil
}

// GetL2ListForL1 returns a slice of L2 folder names for the given L1 folder
// The returned slice starts at index L1 mod 1024 and has the specified size
func GetL2ListForL1(l1Folder string, size int) ([]string, error) {
	// Try to read from cache first with read lock
	cachedUUIDsMu.RLock()
	if len(cachedUUIDs) > 0 {
		uuids := make([]string, len(cachedUUIDs))
		copy(uuids, cachedUUIDs)
		cachedUUIDsMu.RUnlock()
		return getL2ListFromUUIDs(l1Folder, size, uuids)
	}
	cachedUUIDsMu.RUnlock()

	// Need to initialize cache, acquire write lock
	cachedUUIDsMu.Lock()
	defer cachedUUIDsMu.Unlock()

	// Double check after acquiring write lock
	if len(cachedUUIDs) > 0 {
		uuids := make([]string, len(cachedUUIDs))
		copy(uuids, cachedUUIDs)
		return getL2ListFromUUIDs(l1Folder, size, uuids)
	}

	// Check if L2_FOLDER_LIST exists in constants package
	if len(goconfig.L2_FOLDER_LIST) > 0 {
		cachedUUIDs = make([]string, len(goconfig.L2_FOLDER_LIST))
		copy(cachedUUIDs, goconfig.L2_FOLDER_LIST)
		golog.Info("Using L2_FOLDER_LIST from constants package")
	} else {
		// Generate new UUIDs and save to file
		golog.Info("Generating const L2 folder list")
		uuids, err := GenerateAndSaveUUID5List(defaultL2FolderFile, TOTAL_UUID5_COUNT)
		if err != nil {
			golog.Error("failed to generate and save UUID5 list", "error", err)
			return nil, fmt.Errorf("failed to generate and save UUID5 list: %v", err)
		}
		cachedUUIDs = make([]string, len(uuids))
		copy(cachedUUIDs, uuids)
	}

	return getL2ListFromUUIDs(l1Folder, size, cachedUUIDs)
}

// getL2ListFromUUIDs is a helper function to calculate L2 list from UUIDs
func getL2ListFromUUIDs(l1Folder string, size int, uuids []string) ([]string, error) {
	// Check for empty or nil uuids array
	if len(uuids) == 0 {
		return nil, fmt.Errorf("uuids array cannot be empty or nil")
	}

	// Convert L1 folder to integer
	l1Int, err := strconv.Atoi(l1Folder)
	if err != nil {
		golog.Error("failed to convert L1 folder to int", "error", err)
		return nil, fmt.Errorf("failed to convert L1 folder to int: %v", err)
	}

	// Calculate start index and create result slice
	startIndex := l1Int % len(uuids)
	result := make([]string, size)
	for i := 0; i < size; i++ {
		index := (startIndex + i) % len(uuids)
		result[i] = uuids[index]
	}
	return result, nil
}

// getFullFilePathComponents generates path components and returns them in a structured format
func getFullFilePathComponents(inputTs time.Time, board string, sid string) (*PathResult, error) {
	// Validate propTs
	if inputTs.IsZero() {
		golog.Error("invalid timestamp", "timestamp", inputTs)
		return nil, fmt.Errorf("invalid timestamp: timestamp cannot be zero")
	}

	// Convert to date integer (YYYYMMDD)
	inputTsD := gohelper.TimeToDateInt(inputTs)
	if inputTsD <= 19700101 {
		golog.Error("invalid date", "date", inputTsD, "timestamp", inputTs)
		return nil, fmt.Errorf("invalid date: date must be after 1970-01-01, got %d", inputTsD)
	}

	// Check sid
	if sid == "" {
		golog.Error("sid cannot be empty")
		return nil, fmt.Errorf("sid cannot be empty")
	}

	// 1. Calculate L1
	l1 := CalculateL1(inputTsD, board)
	if l1 == "" {
		golog.Error("failed to calculate L1", "date", inputTsD, "board", board)
		return nil, fmt.Errorf("failed to calculate L1 for date: %d, board: %s", inputTsD, board)
	}

	// 2. Get L2 folder list for the given L1
	l2Array, err := GetL2ListForL1(l1, goconfig.SOURCE_L2_SIZE[board])
	if err != nil {
		golog.Error("failed to get L2 folder list",
			"inputTsD", inputTsD,
			"board", board,
			"sid", sid,
			"error", err)
		return nil, fmt.Errorf("failed to get L2 folder list: %w", err)
	}

	// 3. Calculate L2
	l2, err := CalculateL2(sid, l2Array)
	if err != nil {
		golog.Error("failed to calculate L2", "error", err)
		return nil, fmt.Errorf("failed to calculate L2: %w", err)
	}

	// 4. Construct the result
	result := &PathResult{
		Combined: fmt.Sprintf("%s/%s", l1, l2),
		L1:       l1,
		L2:       l2,
	}

	golog.Debug("generated file path components",
		"combined", result.Combined,
		"l1", result.L1,
		"l2", result.L2,
		"timestamp", inputTs,
		"date", inputTsD,
		"board", board,
		"sid", sid)

	return result, nil
}

// PathResult contains the path components
type PathResult struct {
	Combined string // "L1/L2" format (without leading slash)
	L1       string // L1 directory
	L2       string // L2 directory
}

// GetFullFilePathForProp generates the complete file path based on propTs, source, and sid
// Returns the complete path in the format /l1/l2 (backward compatibility)
func GetFullFilePathForProp(propTs time.Time, board string, sid string) (string, error) {
	// Check board
	if board == "" {
		golog.Error("board cannot be empty")
		return "", fmt.Errorf("board cannot be empty")
	}
	if _, exists := goconfig.SOURCE_L2_SIZE[board]; !exists {
		golog.Error("invalid board", "board", board)
		return "", fmt.Errorf("invalid board: %s", board)
	}

	// Get path components
	result, err := getFullFilePathComponents(propTs, board, sid)
	if err != nil {
		return "", err
	}

	// Return with leading slash for backward compatibility
	return "/" + result.Combined, nil
}

// GetPathComponents returns both L1 and L2 components separately
func GetPathComponents(propTs time.Time, board string, sid string) (*PathResult, error) {
	// Check board
	if board == "" {
		golog.Error("board cannot be empty")
		return nil, fmt.Errorf("board cannot be empty")
	}
	if _, exists := goconfig.SOURCE_L2_SIZE[board]; !exists {
		golog.Error("invalid board", "board", board)
		return nil, fmt.Errorf("invalid board: %s", board)
	}

	return getFullFilePathComponents(propTs, board, sid)
}

// GetL1L2Separate returns L1 and L2 as separate strings
func GetL1L2Separate(propTs time.Time, board string, sid string) (l1, l2 string, err error) {
	result, err := GetPathComponents(propTs, board, sid)
	if err != nil {
		return "", "", err
	}
	return result.L1, result.L2, nil
}

// MurmurToInt32 returns the int32 value of the murmur3 hash of the given key
func MurmurToInt32(key string) int32 {
	h := murmur3.New32()
	h.Write([]byte(key))
	return int32(h.Sum32())
}

var base62Chars = []byte("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789")

// Int32ToBase62 converts an int32 to a base62 string
var Int32ToBase62 = func(n int32) (string, error) {
	num := uint32(n) // treats negative numbers as high unsigned values
	result := []byte{}
	if n == 0 {
		return string(base62Chars[0]), nil
	}
	for num > 0 {
		remainder := num % 62
		result = append([]byte{base62Chars[remainder]}, result...)
		num /= 62
	}
	if len(result) == 0 {
		return "", fmt.Errorf("invalid int32: %d", n)
	}
	return string(result), nil
}

// Base62ToInt32 converts a base62 string to an int32
func Base62ToInt32(s string) (int32, error) {
	num := uint32(0)

	for i := 0; i < len(s); i++ {
		ch := s[i]
		var val uint32
		switch {
		case 'A' <= ch && ch <= 'Z':
			val = uint32(ch - 'A')
		case 'a' <= ch && ch <= 'z':
			val = uint32(ch - 'a' + 26)
		case '0' <= ch && ch <= '9':
			val = uint32(ch - '0' + 52)
		default:
			return 0, fmt.Errorf("invalid base62 character: %c", ch)
		}
		num = num*62 + val
	}

	return int32(num), nil
}

// GetImageDir retrieves the configured image directories for a given board.
// It returns a slice of directory paths or nil if no directories are configured.
var GetImageDir = func(board string) []string {
	imgStore, ok := goconfig.Config("imageStore").(map[string]interface{})
	if !ok {
		golog.Error("Invalid or missing imageStore configuration")
		return nil
	}
	if imgDirs, ok := imgStore[BoardImageDirName[board]].([]interface{}); ok {
		dirs := make([]string, len(imgDirs))
		for i, dir := range imgDirs {
			if strDir, ok := dir.(string); ok {
				dirs[i] = strDir
			} else {
				golog.Error("Invalid directory type in configuration", "dir", dir)
				return nil
			}
		}
		golog.Info("GetImageDir", "board", board, "dirs", dirs)
		return dirs
	}
	return nil
}

// ListFiles finds files in baseDir/filePath that start with sid, return file names list
func ListFiles(baseDir string, filePath string, sid string) ([]string, error) {
	searchPath := filepath.Join(baseDir, filePath)
	var fileList []string

	// walk directory
	err := filepath.WalkDir(searchPath, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			// if access file failed, return error to terminate walk
			return err
		}
		if !d.IsDir() && strings.HasPrefix(d.Name(), sid) {
			fileList = append(fileList, d.Name()) // only add file name
		}
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("error walking directory %s: %w", searchPath, err)
	}
	return fileList, nil
}
