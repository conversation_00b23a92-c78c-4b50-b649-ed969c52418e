package levelStore

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	goconfig "github.com/real-rm/goconfig"
	gohelper "github.com/real-rm/gohelper"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	DEFAULT_UPDATE_INTERVAL = 10 * time.Minute   // default update interval
	DEFAULT_CLEAN_INTERVAL  = 7 * 24 * time.Hour // default clean interval
	DEFAULT_L1_DIFF_LIMIT   = 4                  // default L1 diff limit
	DEFAULT_SOURCE_ROOT     = "/data/files"      // default source root directory
)

// DirStats tracks directory statistics
type DirStats struct {
	EntityAmount int `bson:"l" json:"entity_amount"` // Number of entities in directory
	FileAmount   int `bson:"f" json:"file_amount"`   // Number of files in directory
}

// DirMetaInfo represents the metadata information for a directory
type DirMetaInfo struct {
	TotalEntities int                 `json:"total_entities"`
	TotalFiles    int                 `json:"total_files"`
	L2Stats       map[string]DirStats `json:"l2_stats"`
	ActualSize    int64               `json:"actual_size"` // Actual size in bytes
	DiskSize      int64               `json:"disk_size"`   // Disk size in bytes (including block size)
	LastUpdated   time.Time           `json:"last_updated"`
}

// DirKeyStore manages directory key allocation and storage
type DirKeyStore struct {
	mu           sync.RWMutex
	prefix       string                   // Board prefix (e.g., "TRB", "DDF")
	collection   *gomongo.MongoCollection // MongoDB collection
	tmpDirMap    sync.Map                 // Temporary directory mapping {L1: []string}
	dirMap       map[string]DirStats      // Directory statistics {L1-L2: stats}
	lastUpdateTs time.Time                // Last update timestamp
	lastSavedTs  time.Time                // Last save timestamp
	saveTicker   *gohelper.Interval       // Ticker for saving changes
	cleanTicker  *gohelper.Interval       // Ticker for cleaning tmp dir map
	sourceRoot   string                   // Root directory for file storage
}

// NewDirKeyStore creates a new DirKeyStore instance
func NewDirKeyStore(prefix string, coll interface{}, updateInterval ...time.Duration) (*DirKeyStore, error) {
	if coll == nil {
		return nil, fmt.Errorf("collection is nil")
	}

	mongoCollection, ok := coll.(*gomongo.MongoCollection)
	if !ok {
		return nil, fmt.Errorf("collection must be *gomongo.MongoCollection, got %T", coll)
	}
	dirs := GetImageDir(prefix)
	if len(dirs) == 0 {
		golog.Error("No image directories configured", "board", prefix)
		return nil, fmt.Errorf("no image directories configured for board: %s", prefix)
	}

	store := &DirKeyStore{
		prefix:       prefix,
		collection:   mongoCollection,
		dirMap:       make(map[string]DirStats),
		lastUpdateTs: time.Now(),
		lastSavedTs:  time.Now(),
		sourceRoot:   DEFAULT_SOURCE_ROOT,
	}

	// Set update interval
	interval := DEFAULT_UPDATE_INTERVAL
	if len(updateInterval) > 0 {
		if updateInterval[0] <= 0 {
			golog.Warn("Invalid update interval, using default", "interval", updateInterval[0])
		} else {
			interval = updateInterval[0]
		}
	}

	// Start background save ticker
	store.saveTicker = gohelper.StartInterval(float64(interval.Seconds()), store.SaveChangedCurrent)

	// Start background clean ticker
	store.cleanTicker = gohelper.StartInterval(float64(DEFAULT_CLEAN_INTERVAL.Seconds()), store.CleanTmpDirMap)

	return store, nil
}

// SetSourceRoot sets the source root directory
func (store *DirKeyStore) SetSourceRoot(root string) {
	root = strings.TrimSpace(root)
	if root == "" {
		store.sourceRoot = DEFAULT_SOURCE_ROOT
	} else {
		store.sourceRoot = root
	}
}

// Close stops all background goroutines and waits for them to finish
func (store *DirKeyStore) Close() {
	if store == nil {
		return
	}
	if store.collection == nil {
		return
	}
	// Save any remaining changes
	store.SaveChangedCurrent()

	// Stop tickers
	if store.saveTicker != nil {
		store.saveTicker.Stop()
		store.saveTicker = nil // Set to nil after stopping
	}
	if store.cleanTicker != nil {
		store.cleanTicker.Stop()
		store.cleanTicker = nil // Set to nil after stopping
	}

	// Clear dirMap
	store.mu.Lock()
	store.dirMap = make(map[string]DirStats)
	store.mu.Unlock()
}

// groupStatsByL1 groups directory statistics by L1 directory
func groupStatsByL1(dirMap map[string]DirStats) map[string]map[string]DirStats {
	l1Map := make(map[string]map[string]DirStats) // l1 -> {l2 -> stats}
	for key, stats := range dirMap {
		parts := strings.SplitN(key, "-", 2) // only split once
		if len(parts) != 2 {
			golog.Warn("invalid dirMap key", "key", key)
			continue
		}
		l1, l2 := parts[0], parts[1]
		if _, ok := l1Map[l1]; !ok {
			l1Map[l1] = make(map[string]DirStats)
		}
		l1Map[l1][l2] = stats
	}
	return l1Map
}

// SaveChangedCurrent saves current state if changes exist
func (store *DirKeyStore) SaveChangedCurrent() {
	if store == nil {
		golog.Error("SaveChangedCurrent called on nil store")
		return
	}
	if store.collection == nil {
		golog.Error("SaveChangedCurrent called with nil collection")
		return
	}

	store.mu.Lock()
	hasChanges := store.lastUpdateTs.After(store.lastSavedTs)
	if !hasChanges {
		// Clear dirMap even when no changes
		store.dirMap = make(map[string]DirStats)
		store.mu.Unlock()
		return
	}

	// Take snapshot and clear map in O(1) time
	snapshot := store.dirMap
	store.dirMap = make(map[string]DirStats)
	pendingTs := time.Now()
	store.mu.Unlock()

	// Process snapshot and perform IO operations outside the lock
	l1Map := groupStatsByL1(snapshot)

	// Try to save to file first
	fileErr := store.saveStatsToFile(l1Map)
	if fileErr != nil {
		golog.Error("Failed to save current state to file", "error", fileErr)
		// Continue to try saving to database even if file save fails
	}

	// Try to save to database
	dbErr := store.saveStatsToDb(l1Map)
	if dbErr != nil {
		golog.Error("Failed to save current state to database", "error", dbErr)
		store.mu.Lock()
		for k, v := range snapshot {
			merged := store.dirMap[k]
			merged.EntityAmount += v.EntityAmount
			merged.FileAmount += v.FileAmount
			store.dirMap[k] = merged
		}
		store.mu.Unlock()
		return
	}

	// Only update lastSavedTs if database save succeeded
	store.mu.Lock()
	store.lastSavedTs = pendingTs
	store.mu.Unlock()
}

// saveStatsToFile saves the provided data to file
func (store *DirKeyStore) saveStatsToFile(l1Map map[string]map[string]DirStats) error {
	dirs := GetImageDir(store.prefix)
	if len(dirs) == 0 {
		golog.Error("No image directories configured", "board", store.prefix)
		return fmt.Errorf("no image directories configured for board: %s", store.prefix)
	}

	// Calculate stats once for each L1
	type l1Stats struct {
		metaInfo  DirMetaInfo
		lastError error
	}
	l1StatsMap := make(map[string]l1Stats)

	// First pass: calculate stats for each L1
	for l1, l2Stats := range l1Map {
		// Read existing meta info if exists from first directory
		metaPath := filepath.Join(dirs[0], l1, "dir_stats.json")
		var existingMeta DirMetaInfo
		if data, err := os.ReadFile(metaPath); err == nil {
			if err := json.Unmarshal(data, &existingMeta); err == nil {
				// Initialize L2Stats if nil
				if existingMeta.L2Stats == nil {
					existingMeta.L2Stats = make(map[string]DirStats)
				}
				// Merge L2 stats
				for l2, stats := range l2Stats {
					existingStats := existingMeta.L2Stats[l2]
					existingStats.EntityAmount += stats.EntityAmount
					existingStats.FileAmount += stats.FileAmount
					existingMeta.L2Stats[l2] = existingStats
				}
			} else {
				// Log error and prevent further writes for this L1
				golog.Error("Failed to parse dir_stats.json",
					"path", metaPath,
					"error", err,
					"l1", l1)
				// Consider backing up the corrupted file
				backupPath := metaPath + ".corrupted." + time.Now().Format("20060102150405")
				if backupErr := os.Rename(metaPath, backupPath); backupErr != nil {
					golog.Error("Failed to backup corrupted file", "error", backupErr)
				} else {
					golog.Info("Backed up corrupted file", "backup", backupPath)
				}
				l1StatsMap[l1] = l1Stats{lastError: fmt.Errorf("failed to parse dir_stats.json: %w", err)}
				continue
			}
		} else {
			// If file doesn't exist, initialize with new stats
			existingMeta = DirMetaInfo{
				L2Stats:     make(map[string]DirStats),
				LastUpdated: time.Now(),
			}
			for l2, stats := range l2Stats {
				existingMeta.L2Stats[l2] = stats
			}
		}

		// Calculate totals from merged L2Stats
		var totalFiles, totalEntities int
		for _, stats := range existingMeta.L2Stats {
			totalFiles += stats.FileAmount
			totalEntities += stats.EntityAmount
		}

		// Calculate directory sizes from first directory
		actualSize, diskSize, err := calculateDirSize(filepath.Join(dirs[0], l1))
		if err != nil {
			golog.Error("Failed to calculate directory size",
				"l1", l1,
				"error", err)
			l1StatsMap[l1] = l1Stats{lastError: err}
			continue
		}

		// Create meta info
		metaInfo := DirMetaInfo{
			TotalEntities: totalEntities,
			TotalFiles:    totalFiles,
			L2Stats:       existingMeta.L2Stats,
			ActualSize:    actualSize,
			DiskSize:      diskSize,
			LastUpdated:   time.Now(),
		}

		l1StatsMap[l1] = l1Stats{metaInfo: metaInfo}
	}

	// Second pass: save to all directories
	var lastErr error
	for _, baseDir := range dirs {
		for l1, stats := range l1StatsMap {
			if stats.lastError != nil {
				lastErr = stats.lastError
				continue
			}

			// Create directory if not exists
			l1Path := filepath.Join(baseDir, l1)
			if err := os.MkdirAll(l1Path, 0755); err != nil {
				golog.Error("Failed to create directory",
					"l1", l1,
					"path", l1Path,
					"error", err)
				lastErr = fmt.Errorf("failed to create directory %s: %w", l1Path, err)
				continue
			}

			// Check if directory is writable
			if info, err := os.Stat(l1Path); err != nil {
				golog.Error("Failed to stat directory",
					"l1", l1,
					"path", l1Path,
					"error", err)
				lastErr = fmt.Errorf("failed to stat directory %s: %w", l1Path, err)
				continue
			} else if !info.IsDir() {
				lastErr = fmt.Errorf("path exists but is not a directory: %s", l1Path)
				continue
			}

			// Try to create a test file to verify write permissions
			testFile := filepath.Join(l1Path, ".test_write")
			if err := os.WriteFile(testFile, []byte("test"), 0644); err != nil {
				golog.Error("Directory is not writable",
					"l1", l1,
					"path", l1Path,
					"error", err)
				lastErr = fmt.Errorf("directory is not writable: %s", l1Path)
				continue
			}
			if err := os.Remove(testFile); err != nil {
				golog.Error("Failed to remove test file",
					"l1", l1,
					"path", l1Path,
					"error", err)
				lastErr = fmt.Errorf("failed to remove test file: %w", err)
				continue
			}

			// Convert to JSON
			jsonData, err := json.MarshalIndent(stats.metaInfo, "", "  ")
			if err != nil {
				golog.Error("Failed to marshal meta info",
					"l1", l1,
					"error", err)
				lastErr = err
				continue
			}

			// Save to file
			metaPath := filepath.Join(l1Path, "dir_stats.json")
			if err := os.WriteFile(metaPath, jsonData, 0644); err != nil {
				golog.Error("Failed to write meta file",
					"l1", l1,
					"path", metaPath,
					"error", err)
				lastErr = err
				continue
			}
		}
	}

	return lastErr
}

// saveStatsToDb saves the provided data to database
func (store *DirKeyStore) saveStatsToDb(l1Map map[string]map[string]DirStats) error {
	ctx := context.Background()

	// Update each l1 document
	for l1, l2Stats := range l1Map {
		// Calculate totals for this L1
		var totalFiles, totalEntities int
		for _, stats := range l2Stats {
			totalFiles += stats.FileAmount
			totalEntities += stats.EntityAmount
		}

		// Get existing document
		var existingDoc struct {
			L2Stats       map[string]DirStats `bson:"l2Stats"`
			TotalFiles    int                 `bson:"totalFiles"`
			TotalEntities int                 `bson:"totalEntities"`
		}

		filter := bson.D{
			{Key: "_id", Value: bson.D{
				{Key: "board", Value: store.prefix},
				{Key: "l1", Value: l1},
			}},
		}

		// Try to get existing document
		err := store.collection.FindOne(ctx, filter).Decode(&existingDoc)
		if err == nil {
			if existingDoc.L2Stats == nil {
				existingDoc.L2Stats = make(map[string]DirStats)
			}
			// Add new stats to existing ones
			totalFiles += existingDoc.TotalFiles
			totalEntities += existingDoc.TotalEntities
			// Merge L2 stats
			for l2, stats := range l2Stats {
				existingStats := existingDoc.L2Stats[l2]
				existingStats.EntityAmount += stats.EntityAmount
				existingStats.FileAmount += stats.FileAmount
				existingDoc.L2Stats[l2] = existingStats
			}
			l2Stats = existingDoc.L2Stats
		}

		// Update the database
		update := bson.M{
			"$set": bson.M{
				"l2Stats":       l2Stats,
				"totalFiles":    totalFiles,
				"totalEntities": totalEntities,
			},
		}

		_, err = store.collection.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
		if err != nil {
			golog.Error("Failed to update document",
				"l1", l1,
				"error", err)
			return err
		}
	}

	return nil
}

// calculateDirSize calculates the actual size and disk size of a directory
func calculateDirSize(dirPath string) (int64, int64, error) {
	var actualSize, diskSize int64

	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() {
			actualSize += info.Size()
			// Calculate disk size (including block size)
			stat, ok := info.Sys().(*syscall.Stat_t)
			if ok {
				diskSize += stat.Blocks * 512 // 512 is the standard block size
			} else {
				diskSize += info.Size() // Fallback to actual size if stat_t is not available
			}
		}
		return nil
	})

	return actualSize, diskSize, err
}

// AddDirStats adds to the directory statistics for a specific L1-L2 combination
func (store *DirKeyStore) AddDirStats(l1 string, l2 string, entityAmount int, fileAmount int) {
	// Ensure L2 array exists and is cached (important for data consistency)
	dirArray, err := store.FetchDirArrayByL1(context.Background(), store.prefix, l1)
	if err != nil {
		golog.Error("failed to fetch dir array", "l1", l1, "error", err)
		return
	} else {
		golog.Debug("fetched dir array", "l1", l1, "dirArray", len(dirArray))
	}

	store.mu.Lock()
	defer store.mu.Unlock()

	if fileAmount == 0 && entityAmount == 0 {
		golog.Warn("AddDirStats called with fileAmount 0", "l1", l1, "l2", l2)
		return
	}
	key := fmt.Sprintf("%s-%s", l1, l2)
	stats := store.dirMap[key]
	stats.EntityAmount += entityAmount
	stats.FileAmount += fileAmount
	store.dirMap[key] = stats
	store.lastUpdateTs = time.Now()

}

// getDirArray retrieves dirArray from database
// Returns nil if not found or empty
func (store *DirKeyStore) getDirArrayFromDB(ctx context.Context, board string, l1 string) ([]string, error) {
	var result struct {
		DirArray []string `bson:"dirArray"`
	}

	err := store.collection.FindOne(ctx, bson.D{
		{Key: "_id", Value: bson.D{
			{Key: "board", Value: board},
			{Key: "l1", Value: l1},
		}},
	}).Decode(&result)

	if err != nil {
		return nil, err
	}

	if len(result.DirArray) == 0 {
		return nil, fmt.Errorf("empty dirArray")
	}

	return result.DirArray, nil
}

// AddTmpDirMap adds a new L2 directory array to the temporary directory map
func (store *DirKeyStore) AddTmpDirMap(l1 string, dirArray []string) {
	store.tmpDirMap.Store(l1, dirArray)
	golog.Debug("Added new L2 directory array to tmpDirMap",
		"l1", l1,
		"array_size", len(dirArray))
}

// CleanTmpDirMap removes entries from tmpDirMap that are older than the specified duration
// or have L1 values that are too old compared to the current L1
func (store *DirKeyStore) CleanTmpDirMap() {
	cleaned := 0
	now := time.Now()
	currentOnD := gohelper.TimeToDateInt(now)
	currentL1 := CalculateL1(currentOnD, store.prefix)

	store.tmpDirMap.Range(func(key, value interface{}) bool {
		if l1Str, ok := key.(string); ok {
			// Calculate the date for this L1 value
			oldDate := now.AddDate(0, 0, -DEFAULT_L1_DIFF_LIMIT*7)
			oldOnD := gohelper.TimeToDateInt(oldDate)
			oldL1 := CalculateL1(oldOnD, store.prefix)

			// Clean if L1 is older than the limit
			l1Val, err1 := strconv.Atoi(l1Str)
			oldVal, err2 := strconv.Atoi(oldL1)
			if err1 != nil || err2 != nil {
				golog.Warn("invalid L1 value when cleaning tmpDirMap",
					"l1Str", l1Str, "oldL1", oldL1, "err1", err1, "err2", err2)
				return true // skip
			}
			if l1Val < oldVal {
				store.tmpDirMap.Delete(key)
				cleaned++
			}
		}
		return true
	})

	if cleaned > 0 {
		golog.Info("Cleaned old entries from tmpDirMap",
			"cleaned_count", cleaned,
			"current_l1", currentL1)
	}
}

// FetchDirArray retrieves l2 Array using board and propTsD
// If the array doesn't exist or is empty, creates a new one
func (store *DirKeyStore) FetchDirArray(ctx context.Context, board string, propTsD int) ([]string, error) {
	if board == "" || !IsValidBoard(board) {
		return nil, fmt.Errorf("invalid board: %s", board)
	}

	// Calculate L1 from propTsD
	l1 := CalculateL1(propTsD, board)

	// Use the new internal method
	return store.FetchDirArrayByL1(ctx, board, l1)
}

// FetchDirArrayByL1 retrieves l2 Array using board and l1 (extracted from FetchDirArray)
// If the array doesn't exist or is empty, creates a new one
func (store *DirKeyStore) FetchDirArrayByL1(ctx context.Context, board string, l1 string) ([]string, error) {
	if board == "" || !IsValidBoard(board) {
		return nil, fmt.Errorf("invalid board: %s", board)
	}
	if l1 == "" {
		return nil, fmt.Errorf("l1 cannot be empty")
	}
	if store.collection == nil {
		return nil, fmt.Errorf("store collection is nil")
	}

	// First check tmpDirMap
	if value, ok := store.tmpDirMap.Load(l1); ok {
		if dirArray, ok := value.([]string); ok {
			return dirArray, nil
		}
	}

	// Then check database
	dirArray, err := store.getDirArrayFromDB(ctx, board, l1)
	if err == nil {
		// Cache the result
		store.AddTmpDirMap(l1, dirArray)

		golog.Debug("Got L2 directory list",
			"board", board,
			"l1", l1,
			"array_size", len(dirArray))
		return dirArray, nil
	}

	// If not found or empty array, create a new one
	return store.SaveDirArrayByL1(ctx, board, l1, true)
}

// SaveDirArrayInDB creates or updates l2Array in database
// If force is false (default), returns existing array or creates new one if not exists
// If force is true, always creates new array
func (store *DirKeyStore) SaveDirArrayInDB(ctx context.Context, board string, propTsD int, force ...bool) ([]string, error) {
	if board == "" || !IsValidBoard(board) {
		return nil, fmt.Errorf("invalid board: %s", board)
	}

	// Check if force update is requested
	forceUpdate := false
	if len(force) > 0 {
		forceUpdate = force[0]
	}

	// Calculate L1 from propTsD
	l1 := CalculateL1(propTsD, board)

	// Use the new internal method
	return store.SaveDirArrayByL1(ctx, board, l1, forceUpdate)
}

// SaveDirArrayByL1 creates or updates l2Array in database using board and l1 (extracted from SaveDirArrayInDB)
// If force is false, returns existing array or creates new one if not exists
// If force is true, always creates new array
func (store *DirKeyStore) SaveDirArrayByL1(ctx context.Context, board string, l1 string, forceUpdate bool) ([]string, error) {
	if board == "" || !IsValidBoard(board) {
		return nil, fmt.Errorf("invalid board: %s", board)
	}
	if l1 == "" {
		return nil, fmt.Errorf("l1 cannot be empty")
	}
	if store.collection == nil {
		return nil, fmt.Errorf("store collection is nil")
	}

	golog.Info("saving L2 list", "board", board, "l1", l1, "force", forceUpdate)

	// Check existing array first
	dirArray, err := store.getDirArrayFromDB(ctx, board, l1)
	if err == nil && !forceUpdate {
		golog.Debug("Using existing L2 directory list",
			"board", board,
			"l1", l1,
			"array_size", len(dirArray))
		return dirArray, nil
	}

	// Create new array if forced or not exists
	dirArray, err = GetL2ListForL1(l1, goconfig.SOURCE_L2_SIZE[board])
	if err != nil {
		golog.Error("failed to get L2 list", "error", err)
		return nil, fmt.Errorf("failed to get L2 list: %v", err)
	}

	filter := bson.D{
		{Key: "_id", Value: bson.D{
			{Key: "board", Value: board},
			{Key: "l1", Value: l1},
		}},
	}
	update := bson.D{
		{Key: "$set", Value: bson.D{
			{Key: "dirArray", Value: dirArray},
		}},
	}

	_, err = store.collection.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
	if err != nil {
		golog.Error("Failed to update L2 list",
			"board", board,
			"l1", l1,
			"error", err)
		return nil, fmt.Errorf("failed to update L2 list: %w", err)
	}

	// Update cache
	store.AddTmpDirMap(l1, dirArray)

	golog.Info("Successfully updated L2 list",
		"board", board,
		"l1", l1,
		"array_size", len(dirArray),
		"force", forceUpdate)

	return dirArray, nil
}

// IsValidBoard checks if the board is valid
func IsValidBoard(board string) bool {
	if board == "" {
		return false
	}

	if _, ok := goconfig.SOURCE_L2_SIZE[board]; !ok {
		return false
	}

	return true
}
