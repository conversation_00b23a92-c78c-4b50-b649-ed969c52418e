<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# levelStore

```go
import "github.com/real-rm/gofile/levelStore"
```

## Index

- [Constants](<#constants>)
- [Variables](<#variables>)
- [func Base62ToInt32\(s string\) \(int32, error\)](<#Base62ToInt32>)
- [func CalculateL1\(dateNum int, board string\) string](<#CalculateL1>)
- [func CalculateL2\(sid string, l2Array \[\]string\) \(string, error\)](<#CalculateL2>)
- [func CalculateL2Index\(sid string, size int\) \(int, error\)](<#CalculateL2Index>)
- [func GenerateAndSaveUUID5List\(filePath string, size int\) \(\[\]string, error\)](<#GenerateAndSaveUUID5List>)
- [func GenerateUUID5Array\(size int\) \(\[\]string, error\)](<#GenerateUUID5Array>)
- [func GetFullFilePathForProp\(propTs time.Time, board string, sid string\) \(string, error\)](<#GetFullFilePathForProp>)
- [func GetL1L2Separate\(propTs time.Time, board string, sid string\) \(l1, l2 string, err error\)](<#GetL1L2Separate>)
- [func GetL2ListForL1\(l1Folder string, size int\) \(\[\]string, error\)](<#GetL2ListForL1>)
- [func IsValidBoard\(board string\) bool](<#IsValidBoard>)
- [func ListFiles\(baseDir string, filePath string, sid string\) \(\[\]string, error\)](<#ListFiles>)
- [func MurmurToInt32\(key string\) int32](<#MurmurToInt32>)
- [func SetL2FolderFile\(path string\)](<#SetL2FolderFile>)
- [type DirKeyStore](<#DirKeyStore>)
  - [func NewDirKeyStore\(prefix string, coll interface\{\}, updateInterval ...time.Duration\) \(\*DirKeyStore, error\)](<#NewDirKeyStore>)
  - [func \(store \*DirKeyStore\) AddDirStats\(l1 string, l2 string, entityAmount int, fileAmount int\)](<#DirKeyStore.AddDirStats>)
  - [func \(store \*DirKeyStore\) AddTmpDirMap\(l1 string, dirArray \[\]string\)](<#DirKeyStore.AddTmpDirMap>)
  - [func \(store \*DirKeyStore\) CleanTmpDirMap\(\)](<#DirKeyStore.CleanTmpDirMap>)
  - [func \(store \*DirKeyStore\) Close\(\)](<#DirKeyStore.Close>)
  - [func \(store \*DirKeyStore\) FetchDirArray\(ctx context.Context, board string, propTsD int\) \(\[\]string, error\)](<#DirKeyStore.FetchDirArray>)
  - [func \(store \*DirKeyStore\) FetchDirArrayByL1\(ctx context.Context, board string, l1 string\) \(\[\]string, error\)](<#DirKeyStore.FetchDirArrayByL1>)
  - [func \(store \*DirKeyStore\) SaveChangedCurrent\(\)](<#DirKeyStore.SaveChangedCurrent>)
  - [func \(store \*DirKeyStore\) SaveDirArrayByL1\(ctx context.Context, board string, l1 string, forceUpdate bool\) \(\[\]string, error\)](<#DirKeyStore.SaveDirArrayByL1>)
  - [func \(store \*DirKeyStore\) SaveDirArrayInDB\(ctx context.Context, board string, propTsD int, force ...bool\) \(\[\]string, error\)](<#DirKeyStore.SaveDirArrayInDB>)
  - [func \(store \*DirKeyStore\) SetSourceRoot\(root string\)](<#DirKeyStore.SetSourceRoot>)
- [type DirMetaInfo](<#DirMetaInfo>)
- [type DirStats](<#DirStats>)
- [type PathResult](<#PathResult>)
  - [func GetPathComponents\(propTs time.Time, board string, sid string\) \(\*PathResult, error\)](<#GetPathComponents>)


## Constants

<a name="DEFAULT_UPDATE_INTERVAL"></a>

```go
const (
    DEFAULT_UPDATE_INTERVAL = 10 * time.Minute   // default update interval
    DEFAULT_CLEAN_INTERVAL  = 7 * 24 * time.Hour // default clean interval
    DEFAULT_L1_DIFF_LIMIT   = 4                  // default L1 diff limit
    DEFAULT_SOURCE_ROOT     = "/data/files"      // default source root directory
)
```

<a name="FIXED_SEED"></a>

```go
const (
    // 使用一个随机无意义的字符串作为 seed
    FIXED_SEED        = "a9#T" // 4位随机字符串，包含字母、数字和特殊字符
    TOTAL_UUID5_COUNT = 2048   // Total number of UUID5s to generate
)
```

## Variables

<a name="BoardImageDirName"></a>

```go
var BoardImageDirName = map[string]string{
    "CAR": "reso_car_image_dir",
    "DDF": "reso_crea_image_dir",
    "BRE": "reso_bcre_image_dir",
    "EDM": "reso_edm_image_dir",
    "TRB": "reso_treb_image_dir",
}
```

<a name="GetImageDir"></a>GetImageDir retrieves the configured image directories for a given board. It returns a slice of directory paths or nil if no directories are configured.

```go
var GetImageDir = func(board string) []string {
    imgStore, ok := goconfig.Config("imageStore").(map[string]interface{})
    if !ok {
        golog.Error("Invalid or missing imageStore configuration")
        return nil
    }
    if imgDirs, ok := imgStore[BoardImageDirName[board]].([]interface{}); ok {
        dirs := make([]string, len(imgDirs))
        for i, dir := range imgDirs {
            if strDir, ok := dir.(string); ok {
                dirs[i] = strDir
            } else {
                golog.Error("Invalid directory type in configuration", "dir", dir)
                return nil
            }
        }
        golog.Info("GetImageDir", "board", board, "dirs", dirs)
        return dirs
    }
    return nil
}
```

<a name="Int32ToBase62"></a>Int32ToBase62 converts an int32 to a base62 string

```go
var Int32ToBase62 = func(n int32) (string, error) {
    num := uint32(n)
    result := []byte{}
    if n == 0 {
        return string(base62Chars[0]), nil
    }
    for num > 0 {
        remainder := num % 62
        result = append([]byte{base62Chars[remainder]}, result...)
        num /= 62
    }
    if len(result) == 0 {
        return "", fmt.Errorf("invalid int32: %d", n)
    }
    return string(result), nil
}
```

<a name="Base62ToInt32"></a>
## func [Base62ToInt32](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L404>)

```go
func Base62ToInt32(s string) (int32, error)
```

Base62ToInt32 converts a base62 string to an int32

<a name="CalculateL1"></a>
## func [CalculateL1](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L60>)

```go
func CalculateL1(dateNum int, board string) string
```

CalculateL1 calculates L1 value based on date number \(YYYYMMDD\) Returns a folder number where: \- Each year has 50 folders \(weeks\) \- Weeks 0 and 49\+ are merged into one folder \- 2015 starts at 750 \- 2024 maps to 1200 \- 2025 maps to 1250

<a name="CalculateL2"></a>
## func [CalculateL2](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L101>)

```go
func CalculateL2(sid string, l2Array []string) (string, error)
```

CalculateL2 calculates L2 value \(eg. abc12\) using CRC32

<a name="CalculateL2Index"></a>
## func [CalculateL2Index](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L84>)

```go
func CalculateL2Index(sid string, size int) (int, error)
```

CalculateL2Index calculates an index between 0 and size\-1 based on sid

<a name="GenerateAndSaveUUID5List"></a>
## func [GenerateAndSaveUUID5List](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L140>)

```go
func GenerateAndSaveUUID5List(filePath string, size int) ([]string, error)
```

GenerateAndSaveUUID5List generates size number of UUID5s and saves them to a file as a Go constant Returns the generated UUIDs and any error that occurred This step has already been done and file is saved in goconfig package, no need to run it again.

<a name="GenerateUUID5Array"></a>
## func [GenerateUUID5Array](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L115>)

```go
func GenerateUUID5Array(size int) ([]string, error)
```

GenerateUUID5Array generates an array of 5\-character UUIDs

<a name="GetFullFilePathForProp"></a>
## func [GetFullFilePathForProp](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L331>)

```go
func GetFullFilePathForProp(propTs time.Time, board string, sid string) (string, error)
```

GetFullFilePathForProp generates the complete file path based on propTs, source, and sid Returns the complete path in the format /l1/l2 \(backward compatibility\)

<a name="GetL1L2Separate"></a>
## func [GetL1L2Separate](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L368>)

```go
func GetL1L2Separate(propTs time.Time, board string, sid string) (l1, l2 string, err error)
```

GetL1L2Separate returns L1 and L2 as separate strings

<a name="GetL2ListForL1"></a>
## func [GetL2ListForL1](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L191>)

```go
func GetL2ListForL1(l1Folder string, size int) ([]string, error)
```

GetL2ListForL1 returns a slice of L2 folder names for the given L1 folder The returned slice starts at index L1 mod 1024 and has the specified size

<a name="IsValidBoard"></a>
## func [IsValidBoard](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L713>)

```go
func IsValidBoard(board string) bool
```

IsValidBoard checks if the board is valid

<a name="ListFiles"></a>
## func [ListFiles](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L451>)

```go
func ListFiles(baseDir string, filePath string, sid string) ([]string, error)
```

ListFiles finds files in baseDir/filePath that start with sid, return file names list

<a name="MurmurToInt32"></a>
## func [MurmurToInt32](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L377>)

```go
func MurmurToInt32(key string) int32
```

MurmurToInt32 returns the int32 value of the murmur3 hash of the given key

<a name="SetL2FolderFile"></a>
## func [SetL2FolderFile](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L44>)

```go
func SetL2FolderFile(path string)
```

SetL2FolderFile sets the L2 folder file path

<a name="DirKeyStore"></a>
## type [DirKeyStore](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L47-L58>)

DirKeyStore manages directory key allocation and storage

```go
type DirKeyStore struct {
    // contains filtered or unexported fields
}
```

<a name="NewDirKeyStore"></a>
### func [NewDirKeyStore](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L61>)

```go
func NewDirKeyStore(prefix string, coll interface{}, updateInterval ...time.Duration) (*DirKeyStore, error)
```

NewDirKeyStore creates a new DirKeyStore instance

<a name="DirKeyStore.AddDirStats"></a>
### func \(\*DirKeyStore\) [AddDirStats](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L477>)

```go
func (store *DirKeyStore) AddDirStats(l1 string, l2 string, entityAmount int, fileAmount int)
```

AddDirStats adds to the directory statistics for a specific L1\-L2 combination

<a name="DirKeyStore.AddTmpDirMap"></a>
### func \(\*DirKeyStore\) [AddTmpDirMap](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L529>)

```go
func (store *DirKeyStore) AddTmpDirMap(l1 string, dirArray []string)
```

AddTmpDirMap adds a new L2 directory array to the temporary directory map

<a name="DirKeyStore.CleanTmpDirMap"></a>
### func \(\*DirKeyStore\) [CleanTmpDirMap](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L538>)

```go
func (store *DirKeyStore) CleanTmpDirMap()
```

CleanTmpDirMap removes entries from tmpDirMap that are older than the specified duration or have L1 values that are too old compared to the current L1

<a name="DirKeyStore.Close"></a>
### func \(\*DirKeyStore\) [Close](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L115>)

```go
func (store *DirKeyStore) Close()
```

Close stops all background goroutines and waits for them to finish

<a name="DirKeyStore.FetchDirArray"></a>
### func \(\*DirKeyStore\) [FetchDirArray](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L576>)

```go
func (store *DirKeyStore) FetchDirArray(ctx context.Context, board string, propTsD int) ([]string, error)
```

FetchDirArray retrieves l2 Array using board and propTsD If the array doesn't exist or is empty, creates a new one

<a name="DirKeyStore.FetchDirArrayByL1"></a>
### func \(\*DirKeyStore\) [FetchDirArrayByL1](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L590>)

```go
func (store *DirKeyStore) FetchDirArrayByL1(ctx context.Context, board string, l1 string) ([]string, error)
```

FetchDirArrayByL1 retrieves l2 Array using board and l1 \(extracted from FetchDirArray\) If the array doesn't exist or is empty, creates a new one

<a name="DirKeyStore.SaveChangedCurrent"></a>
### func \(\*DirKeyStore\) [SaveChangedCurrent](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L160>)

```go
func (store *DirKeyStore) SaveChangedCurrent()
```

SaveChangedCurrent saves current state if changes exist

<a name="DirKeyStore.SaveDirArrayByL1"></a>
### func \(\*DirKeyStore\) [SaveDirArrayByL1](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L649>)

```go
func (store *DirKeyStore) SaveDirArrayByL1(ctx context.Context, board string, l1 string, forceUpdate bool) ([]string, error)
```

SaveDirArrayByL1 creates or updates l2Array in database using board and l1 \(extracted from SaveDirArrayInDB\) If force is false, returns existing array or creates new one if not exists If force is true, always creates new array

<a name="DirKeyStore.SaveDirArrayInDB"></a>
### func \(\*DirKeyStore\) [SaveDirArrayInDB](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L628>)

```go
func (store *DirKeyStore) SaveDirArrayInDB(ctx context.Context, board string, propTsD int, force ...bool) ([]string, error)
```

SaveDirArrayInDB creates or updates l2Array in database If force is false \(default\), returns existing array or creates new one if not exists If force is true, always creates new array

<a name="DirKeyStore.SetSourceRoot"></a>
### func \(\*DirKeyStore\) [SetSourceRoot](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L105>)

```go
func (store *DirKeyStore) SetSourceRoot(root string)
```

SetSourceRoot sets the source root directory

<a name="DirMetaInfo"></a>
## type [DirMetaInfo](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L37-L44>)

DirMetaInfo represents the metadata information for a directory

```go
type DirMetaInfo struct {
    TotalEntities int                 `json:"total_entities"`
    TotalFiles    int                 `json:"total_files"`
    L2Stats       map[string]DirStats `json:"l2_stats"`
    ActualSize    int64               `json:"actual_size"` // Actual size in bytes
    DiskSize      int64               `json:"disk_size"`   // Disk size in bytes (including block size)
    LastUpdated   time.Time           `json:"last_updated"`
}
```

<a name="DirStats"></a>
## type [DirStats](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L31-L34>)

DirStats tracks directory statistics

```go
type DirStats struct {
    EntityAmount int `bson:"l" json:"entity_amount"` // Number of entities in directory
    FileAmount   int `bson:"f" json:"file_amount"`   // Number of files in directory
}
```

<a name="PathResult"></a>
## type [PathResult](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L323-L327>)

PathResult contains the path components

```go
type PathResult struct {
    Combined string // "L1/L2" format (without leading slash)
    L1       string // L1 directory
    L2       string // L2 directory
}
```

<a name="GetPathComponents"></a>
### func [GetPathComponents](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L353>)

```go
func GetPathComponents(propTs time.Time, board string, sid string) (*PathResult, error)
```

GetPathComponents returns both L1 and L2 components separately

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# levelStore

```go
import "github.com/real-rm/gofile/levelStore"
```

## Index

- [Constants](<#constants>)
- [Variables](<#variables>)
- [func Base62ToInt32\(s string\) \(int32, error\)](<#Base62ToInt32>)
- [func CalculateL1\(dateNum int, board string\) string](<#CalculateL1>)
- [func CalculateL2\(sid string, l2Array \[\]string\) \(string, error\)](<#CalculateL2>)
- [func CalculateL2Index\(sid string, size int\) \(int, error\)](<#CalculateL2Index>)
- [func GenerateAndSaveUUID5List\(filePath string, size int\) \(\[\]string, error\)](<#GenerateAndSaveUUID5List>)
- [func GenerateUUID5Array\(size int\) \(\[\]string, error\)](<#GenerateUUID5Array>)
- [func GetFullFilePathForProp\(propTs time.Time, board string, sid string\) \(string, error\)](<#GetFullFilePathForProp>)
- [func GetL1L2Separate\(propTs time.Time, board string, sid string\) \(l1, l2 string, err error\)](<#GetL1L2Separate>)
- [func GetL2ListForL1\(l1Folder string, size int\) \(\[\]string, error\)](<#GetL2ListForL1>)
- [func IsValidBoard\(board string\) bool](<#IsValidBoard>)
- [func ListFiles\(baseDir string, filePath string, sid string\) \(\[\]string, error\)](<#ListFiles>)
- [func MurmurToInt32\(key string\) int32](<#MurmurToInt32>)
- [func SetL2FolderFile\(path string\)](<#SetL2FolderFile>)
- [type DirKeyStore](<#DirKeyStore>)
  - [func NewDirKeyStore\(prefix string, coll interface\{\}, updateInterval ...time.Duration\) \(\*DirKeyStore, error\)](<#NewDirKeyStore>)
  - [func \(store \*DirKeyStore\) AddDirStats\(l1 string, l2 string, entityAmount int, fileAmount int\)](<#DirKeyStore.AddDirStats>)
  - [func \(store \*DirKeyStore\) AddTmpDirMap\(l1 string, dirArray \[\]string\)](<#DirKeyStore.AddTmpDirMap>)
  - [func \(store \*DirKeyStore\) CleanTmpDirMap\(\)](<#DirKeyStore.CleanTmpDirMap>)
  - [func \(store \*DirKeyStore\) Close\(\)](<#DirKeyStore.Close>)
  - [func \(store \*DirKeyStore\) FetchDirArray\(ctx context.Context, board string, propTsD int\) \(\[\]string, error\)](<#DirKeyStore.FetchDirArray>)
  - [func \(store \*DirKeyStore\) FetchDirArrayByL1\(ctx context.Context, board string, l1 string\) \(\[\]string, error\)](<#DirKeyStore.FetchDirArrayByL1>)
  - [func \(store \*DirKeyStore\) SaveChangedCurrent\(\)](<#DirKeyStore.SaveChangedCurrent>)
  - [func \(store \*DirKeyStore\) SaveDirArrayByL1\(ctx context.Context, board string, l1 string, forceUpdate bool\) \(\[\]string, error\)](<#DirKeyStore.SaveDirArrayByL1>)
  - [func \(store \*DirKeyStore\) SaveDirArrayInDB\(ctx context.Context, board string, propTsD int, force ...bool\) \(\[\]string, error\)](<#DirKeyStore.SaveDirArrayInDB>)
  - [func \(store \*DirKeyStore\) SetSourceRoot\(root string\)](<#DirKeyStore.SetSourceRoot>)
- [type DirMetaInfo](<#DirMetaInfo>)
- [type DirStats](<#DirStats>)
- [type PathResult](<#PathResult>)
  - [func GetPathComponents\(propTs time.Time, board string, sid string\) \(\*PathResult, error\)](<#GetPathComponents>)


## Constants

<a name="DEFAULT_UPDATE_INTERVAL"></a>

```go
const (
    DEFAULT_UPDATE_INTERVAL = 10 * time.Minute   // default update interval
    DEFAULT_CLEAN_INTERVAL  = 7 * 24 * time.Hour // default clean interval
    DEFAULT_L1_DIFF_LIMIT   = 4                  // default L1 diff limit
    DEFAULT_SOURCE_ROOT     = "/data/files"      // default source root directory
)
```

<a name="FIXED_SEED"></a>

```go
const (
    // 使用一个随机无意义的字符串作为 seed
    FIXED_SEED        = "a9#T" // 4位随机字符串，包含字母、数字和特殊字符
    TOTAL_UUID5_COUNT = 2048   // Total number of UUID5s to generate
)
```

## Variables

<a name="BoardImageDirName"></a>

```go
var BoardImageDirName = map[string]string{
    "CAR": "reso_car_image_dir",
    "DDF": "reso_crea_image_dir",
    "BRE": "reso_bcre_image_dir",
    "EDM": "reso_edm_image_dir",
    "TRB": "reso_treb_image_dir",
}
```

<a name="GetImageDir"></a>GetImageDir retrieves the configured image directories for a given board. It returns a slice of directory paths or nil if no directories are configured.

```go
var GetImageDir = func(board string) []string {
    imgStore, ok := goconfig.Config("imageStore").(map[string]interface{})
    if !ok {
        golog.Error("Invalid or missing imageStore configuration")
        return nil
    }
    if imgDirs, ok := imgStore[BoardImageDirName[board]].([]interface{}); ok {
        dirs := make([]string, len(imgDirs))
        for i, dir := range imgDirs {
            if strDir, ok := dir.(string); ok {
                dirs[i] = strDir
            } else {
                golog.Error("Invalid directory type in configuration", "dir", dir)
                return nil
            }
        }
        golog.Info("GetImageDir", "board", board, "dirs", dirs)
        return dirs
    }
    return nil
}
```

<a name="Int32ToBase62"></a>Int32ToBase62 converts an int32 to a base62 string

```go
var Int32ToBase62 = func(n int32) (string, error) {
    num := uint32(n)
    result := []byte{}
    if n == 0 {
        return string(base62Chars[0]), nil
    }
    for num > 0 {
        remainder := num % 62
        result = append([]byte{base62Chars[remainder]}, result...)
        num /= 62
    }
    if len(result) == 0 {
        return "", fmt.Errorf("invalid int32: %d", n)
    }
    return string(result), nil
}
```

<a name="Base62ToInt32"></a>
## func [Base62ToInt32](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L404>)

```go
func Base62ToInt32(s string) (int32, error)
```

Base62ToInt32 converts a base62 string to an int32

<a name="CalculateL1"></a>
## func [CalculateL1](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L60>)

```go
func CalculateL1(dateNum int, board string) string
```

CalculateL1 calculates L1 value based on date number \(YYYYMMDD\) Returns a folder number where: \- Each year has 50 folders \(weeks\) \- Weeks 0 and 49\+ are merged into one folder \- 2015 starts at 750 \- 2024 maps to 1200 \- 2025 maps to 1250

<a name="CalculateL2"></a>
## func [CalculateL2](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L101>)

```go
func CalculateL2(sid string, l2Array []string) (string, error)
```

CalculateL2 calculates L2 value \(eg. abc12\) using CRC32

<a name="CalculateL2Index"></a>
## func [CalculateL2Index](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L84>)

```go
func CalculateL2Index(sid string, size int) (int, error)
```

CalculateL2Index calculates an index between 0 and size\-1 based on sid

<a name="GenerateAndSaveUUID5List"></a>
## func [GenerateAndSaveUUID5List](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L140>)

```go
func GenerateAndSaveUUID5List(filePath string, size int) ([]string, error)
```

GenerateAndSaveUUID5List generates size number of UUID5s and saves them to a file as a Go constant Returns the generated UUIDs and any error that occurred This step has already been done and file is saved in goconfig package, no need to run it again.

<a name="GenerateUUID5Array"></a>
## func [GenerateUUID5Array](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L115>)

```go
func GenerateUUID5Array(size int) ([]string, error)
```

GenerateUUID5Array generates an array of 5\-character UUIDs

<a name="GetFullFilePathForProp"></a>
## func [GetFullFilePathForProp](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L331>)

```go
func GetFullFilePathForProp(propTs time.Time, board string, sid string) (string, error)
```

GetFullFilePathForProp generates the complete file path based on propTs, source, and sid Returns the complete path in the format /l1/l2 \(backward compatibility\)

<a name="GetL1L2Separate"></a>
## func [GetL1L2Separate](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L368>)

```go
func GetL1L2Separate(propTs time.Time, board string, sid string) (l1, l2 string, err error)
```

GetL1L2Separate returns L1 and L2 as separate strings

<a name="GetL2ListForL1"></a>
## func [GetL2ListForL1](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L191>)

```go
func GetL2ListForL1(l1Folder string, size int) ([]string, error)
```

GetL2ListForL1 returns a slice of L2 folder names for the given L1 folder The returned slice starts at index L1 mod 1024 and has the specified size

<a name="IsValidBoard"></a>
## func [IsValidBoard](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L713>)

```go
func IsValidBoard(board string) bool
```

IsValidBoard checks if the board is valid

<a name="ListFiles"></a>
## func [ListFiles](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L451>)

```go
func ListFiles(baseDir string, filePath string, sid string) ([]string, error)
```

ListFiles finds files in baseDir/filePath that start with sid, return file names list

<a name="MurmurToInt32"></a>
## func [MurmurToInt32](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L377>)

```go
func MurmurToInt32(key string) int32
```

MurmurToInt32 returns the int32 value of the murmur3 hash of the given key

<a name="SetL2FolderFile"></a>
## func [SetL2FolderFile](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L44>)

```go
func SetL2FolderFile(path string)
```

SetL2FolderFile sets the L2 folder file path

<a name="DirKeyStore"></a>
## type [DirKeyStore](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L47-L58>)

DirKeyStore manages directory key allocation and storage

```go
type DirKeyStore struct {
    // contains filtered or unexported fields
}
```

<a name="NewDirKeyStore"></a>
### func [NewDirKeyStore](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L61>)

```go
func NewDirKeyStore(prefix string, coll interface{}, updateInterval ...time.Duration) (*DirKeyStore, error)
```

NewDirKeyStore creates a new DirKeyStore instance

<a name="DirKeyStore.AddDirStats"></a>
### func \(\*DirKeyStore\) [AddDirStats](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L477>)

```go
func (store *DirKeyStore) AddDirStats(l1 string, l2 string, entityAmount int, fileAmount int)
```

AddDirStats adds to the directory statistics for a specific L1\-L2 combination

<a name="DirKeyStore.AddTmpDirMap"></a>
### func \(\*DirKeyStore\) [AddTmpDirMap](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L529>)

```go
func (store *DirKeyStore) AddTmpDirMap(l1 string, dirArray []string)
```

AddTmpDirMap adds a new L2 directory array to the temporary directory map

<a name="DirKeyStore.CleanTmpDirMap"></a>
### func \(\*DirKeyStore\) [CleanTmpDirMap](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L538>)

```go
func (store *DirKeyStore) CleanTmpDirMap()
```

CleanTmpDirMap removes entries from tmpDirMap that are older than the specified duration or have L1 values that are too old compared to the current L1

<a name="DirKeyStore.Close"></a>
### func \(\*DirKeyStore\) [Close](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L115>)

```go
func (store *DirKeyStore) Close()
```

Close stops all background goroutines and waits for them to finish

<a name="DirKeyStore.FetchDirArray"></a>
### func \(\*DirKeyStore\) [FetchDirArray](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L576>)

```go
func (store *DirKeyStore) FetchDirArray(ctx context.Context, board string, propTsD int) ([]string, error)
```

FetchDirArray retrieves l2 Array using board and propTsD If the array doesn't exist or is empty, creates a new one

<a name="DirKeyStore.FetchDirArrayByL1"></a>
### func \(\*DirKeyStore\) [FetchDirArrayByL1](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L590>)

```go
func (store *DirKeyStore) FetchDirArrayByL1(ctx context.Context, board string, l1 string) ([]string, error)
```

FetchDirArrayByL1 retrieves l2 Array using board and l1 \(extracted from FetchDirArray\) If the array doesn't exist or is empty, creates a new one

<a name="DirKeyStore.SaveChangedCurrent"></a>
### func \(\*DirKeyStore\) [SaveChangedCurrent](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L160>)

```go
func (store *DirKeyStore) SaveChangedCurrent()
```

SaveChangedCurrent saves current state if changes exist

<a name="DirKeyStore.SaveDirArrayByL1"></a>
### func \(\*DirKeyStore\) [SaveDirArrayByL1](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L649>)

```go
func (store *DirKeyStore) SaveDirArrayByL1(ctx context.Context, board string, l1 string, forceUpdate bool) ([]string, error)
```

SaveDirArrayByL1 creates or updates l2Array in database using board and l1 \(extracted from SaveDirArrayInDB\) If force is false, returns existing array or creates new one if not exists If force is true, always creates new array

<a name="DirKeyStore.SaveDirArrayInDB"></a>
### func \(\*DirKeyStore\) [SaveDirArrayInDB](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L628>)

```go
func (store *DirKeyStore) SaveDirArrayInDB(ctx context.Context, board string, propTsD int, force ...bool) ([]string, error)
```

SaveDirArrayInDB creates or updates l2Array in database If force is false \(default\), returns existing array or creates new one if not exists If force is true, always creates new array

<a name="DirKeyStore.SetSourceRoot"></a>
### func \(\*DirKeyStore\) [SetSourceRoot](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L105>)

```go
func (store *DirKeyStore) SetSourceRoot(root string)
```

SetSourceRoot sets the source root directory

<a name="DirMetaInfo"></a>
## type [DirMetaInfo](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L37-L44>)

DirMetaInfo represents the metadata information for a directory

```go
type DirMetaInfo struct {
    TotalEntities int                 `json:"total_entities"`
    TotalFiles    int                 `json:"total_files"`
    L2Stats       map[string]DirStats `json:"l2_stats"`
    ActualSize    int64               `json:"actual_size"` // Actual size in bytes
    DiskSize      int64               `json:"disk_size"`   // Disk size in bytes (including block size)
    LastUpdated   time.Time           `json:"last_updated"`
}
```

<a name="DirStats"></a>
## type [DirStats](<https://github.com/real-rm/gofile/blob/main/levelStore/dirKeyStore.go#L31-L34>)

DirStats tracks directory statistics

```go
type DirStats struct {
    EntityAmount int `bson:"l" json:"entity_amount"` // Number of entities in directory
    FileAmount   int `bson:"f" json:"file_amount"`   // Number of files in directory
}
```

<a name="PathResult"></a>
## type [PathResult](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L323-L327>)

PathResult contains the path components

```go
type PathResult struct {
    Combined string // "L1/L2" format (without leading slash)
    L1       string // L1 directory
    L2       string // L2 directory
}
```

<a name="GetPathComponents"></a>
### func [GetPathComponents](<https://github.com/real-rm/gofile/blob/main/levelStore/level_store_helper.go#L353>)

```go
func GetPathComponents(propTs time.Time, board string, sid string) (*PathResult, error)
```

GetPathComponents returns both L1 and L2 components separately

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
