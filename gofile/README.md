# gofile

A Go language file handling package that provides efficient file storage management with hierarchical directory structure and MongoDB integration, along with advanced image download, processing, and conversion capabilities.

## Features

### Core File Management
- **Hierarchical directory structure** (L1/L2) based on dates and board types
- **Automatic UUID5 list generation** for L2 directories with configurable sizes
- **MongoDB integration** for directory mapping storage and statistics
- **Directory statistics tracking** with real-time updates
- **Temporary directory mapping** with automatic cleanup

### Image Processing & Download
- **Advanced download system** with configurable retry mechanism
- **Multi-format support**: JPEG and WebP with automatic conversion
- **Image resizing** capabilities with aspect ratio preservation
- **WebP compression** with JPEG fallback for compatibility
- **Multiple directory saving** support for redundancy
- **Concurrent download** support with timeout handling

### Board Types & Configuration
- **Support for 8 board types**: TRB, DDF, BRE, CLG, OTW, EDM, CAR, USER
- **Configurable L2 directory sizes** per board type
- **Base62 encoding** for efficient file naming
- **Murmur hash integration** for consistent file distribution

## Installation

```bash
go get github.com/real-rm/gofile
```

## Quick Start

```go
package main

import (
    "log"
    "time"

    "github.com/real-rm/gofile"
    "github.com/real-rm/gofile/levelStore"
)

func main() {
    // Download and save an image with WebP compression
    opts := &gofile.DownloadAndSaveFileOptions{
        URL:          "https://example.com/image.jpg",
        SavePaths:    []string{"/path/to/save/image.webp"},
        IsPhoto:      true,
        CompressWebP: true,
        MaxRetries:   3,
    }

    results, err := gofile.DownloadAndSaveFile(opts)
    if err != nil {
        log.Fatal(err)
    }
    log.Printf("Image saved to: %v", results)


    // Get full file path
    path, err := levelStore.GetFullFilePathForProp(time.Now(), "TRB", "W4054894")
    if err != nil {
        log.Fatal(err)
    }
    log.Printf("File path: %s", path)


    // record dirKeyStore, automatically save satistics to db every 10 minutes
    dirKeyStore, err := levelStore.NewDirKeyStore("TRB", mongoCollection, 10*time.Minute)
    if err != nil {
        log.Fatal(err)
    }
    defer dirKeyStore.Close()

    // Add directory statistics
    dirKeyStore.AddDirStats("1200", "abc12", 5, 100) // 5 properties, 100 files

}
```

## Configuration

The package uses a configuration system that supports TOML format. Configure your settings in your config file:

```toml
[golog]
dir = "/path/to/logs"
level = "debug"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"

[imageStore]
# Configure image storage directories for each board
TRB = ["/path/to/trb/images"]
DDF = ["/path/to/ddf/images"]
CAR = ["/path/to/car/images"]
```

## API Reference

### 1. Core Download Functions

#### DownloadAndSaveFile (Recommended)

The main function for downloading and saving files with advanced options:

```go
opts := &gofile.DownloadAndSaveFileOptions{
    URL:          "https://example.com/image.jpg",
    SavePaths:    []string{"/path1/image.webp", "/path2/image.jpg"},
    IsPhoto:      true,        // Enable image processing
    CompressWebP: true,        // Use WebP compression with JPEG fallback
    MaxRetries:   3,           // Number of retry attempts
}

results, err := gofile.DownloadAndSaveFile(opts)
if err != nil {
    log.Printf("Download failed: %v", err)
    return
}
log.Printf("Files saved to: %v", results)
```

#### DownloadAndSaveImageInDirs

Save images to multiple directories:

```go
results, err := gofile.DownloadAndSaveImageInDirs(
    "https://example.com/image.jpg",
    []string{"/path1/pic.jpg", "/path2/pic.webp"},
    true, // compressWebP
)
```

#### DownloadAndResizeImage

Download and resize images:

```go
img, err := gofile.DownloadAndResizeImage(
    "https://example.com/image.jpg",
    800,  // maxWidth
    600,  // maxHeight
)
```

### Image Processing Functions

#### SaveImage

Save images with format conversion:

```go
// Save as WebP with JPEG fallback
path, err := gofile.SaveImage(img, "/path/to/image.webp", true)

// Save as JPEG
path, err := gofile.SaveImage(img, "/path/to/image.jpg", false)
```

## 2. LevelStore Package

The `levelStore` package provides advanced directory management with hierarchical structure:

### Directory Structure

The system uses a two-level directory structure:
- **L1**: Year-week based folders (e.g., 1200, 1201, etc.)
- **L2**: UUID5-based subfolders (e.g., "abc12", "def34", etc.), using crc32 hashing calculated from sid to get index, then get the corresponding uuid-5 from the pre-generated list[goconfig.L2_FOLDER_LIST].

### L1, L2 Calculation
```go
// Calculate L1 and L2 directories for a given timestamp, board, and sid
l1, l2, err := levelStore.GetL1L2Separate(propTs, "TRB", sid)

// Calculate L1 directory for a given date and board
l1 := levelStore.CalculateL1(dateInt, "TRB")
// Example: 2024 week 25 for TRB board → "1225"

// Calculate L2 directory for a given sid and L2 array
l2Array, err := levelStore.GetL2ListForL1(l1, goconfig.SOURCE_L2_SIZE["TRB"])
l2, err := levelStore.CalculateL2("W4054894", l2Array)
// Example: "W4054894" → "abc12"

```

- Each year has 50 folders (weeks 1-50)
- Week 0 and weeks 51+ are merged into adjacent folders
- **2015** starts at **750**
- **2024** maps to **1200-1249**
- **2025** maps to **1250-1299**


### Board Types and L2 Sizes

| Board | L2 Directories | Use Case |
|-------|----------------|----------|
| TRB   | 512           | Main board |
| DDF   | 1024          | High volume |
| BRE   | 64            | Small board |
| CLG   | 64            | Small board |
| OTW   | 64            | Small board |
| EDM   | 64            | Small board |
| CAR   | 256           | Medium board |
| USER  | 512           | User content |


### L2 Directory Generation
```go
// Generate L2 directories, this is a one-time operation, already done and saved in goconfig package, no need to run it again.
GenerateAndSaveUUID5List("/path/to/uuids.go", 512)
```


## 3. Directory Store Management

The `levelStore` package provides a robust directory store management system with MongoDB integration:

### DirKeyStore - Statistics and Mapping

```go
import "github.com/real-rm/gofile/levelStore"

// Create a new directory store with MongoDB collection
dirKeyStore, err := levelStore.NewDirKeyStore("TRB", mongoCollection, 10*time.Minute)
if err != nil {
    log.Fatal(err)
}
defer dirKeyStore.Close()

// Add directory statistics: L1, L2, property count, file count
// This step will automatically save satistics to db every 10 minutes
dirKeyStore.AddDirStats("1200", "abc12", 5, 100) // 5 properties, 100 files

// Save accumulated statistics to database and files
err = dirKeyStore.SaveChangedCurrent()
if err != nil {
    log.Printf("Failed to save stats: %v", err)
}
```

### MongoDB Document Structure
```json
{
    "_id": {
        "board": "TRB",
        "l1": "1200"
    },
    "l2Stats": {
        "abc12": {
            "propNum": 5,
            "picNum": 100,
            "lastUpdated": "2024-06-27T10:30:00Z"
        }
    },
    "lastUpdated": "2024-06-27T10:30:00Z"
}
```

### File Path Generation and Hashing

```go
// Get full file path from timestamp, board, and sid
path, err := levelStore.GetFullFilePathForProp(propTs, "TRB", "W4054894")
if err != nil {
    log.Printf("Path generation failed: %v", err)
}
fmt.Println(path) // Output: /1200/abc12

// Generate consistent hash for file naming
hash := levelStore.MurmurToInt32("mediaKey123") // Returns: int32

// Convert to Base62 for compact file names (a-zA-Z0-9)
base62, err := levelStore.Int32ToBase62(hash)
if err != nil {
    log.Fatalf("Base62 conversion failed: %v", err)
}
// Result: "abc123"

// File saved as: /1200/abc12/W4054894_abc123.jpg
```

### Image Directory Configuration

```go
// Get configured image directories for a board
dirs := levelStore.GetImageDir("TRB")
// Returns: []string{"/path/to/trb/images", "/backup/trb/images"}
```

## Advanced Features

### Retry Mechanism

The package includes a sophisticated retry mechanism for downloads:

```go
// Built-in retry with exponential backoff
resp, err := gofile.DownloadWithRetry("https://example.com/image.jpg", 5)

// Automatic retry in DownloadAndSaveFile
opts := &gofile.DownloadAndSaveFileOptions{
    URL:        "https://example.com/image.jpg",
    SavePaths:  []string{"/path/to/image.jpg"},
    MaxRetries: 5, // Custom retry count
}
```

**Retry Features:**
- **Default attempts**: 3 retries
- **Exponential backoff**: 1s, 2s, 3s delays
- **Timeout handling**: 120s default timeout
- **Error categorization**: Network vs HTTP errors
- **Configurable retry count**

### Image Processing Pipeline

```go
// Download → Resize → Convert → Save
img, err := gofile.DownloadAndResizeImage(url, 800, 600)
if err != nil {
    return err
}

// Save with WebP compression and JPEG fallback
path, err := gofile.SaveImage(img, "/path/to/image.webp", true)
```



### Configuration Requirements

1. **MongoDB Connection**: For directory statistics storage
2. **Board Configuration**: L2 sizes in `goconfig.SOURCE_L2_SIZE`
3. **Image Directories**: Paths in `goconfig.Config("imageStore")`
4. **Logging**: golog configuration for debugging

**Default Settings:**
- Update interval: 10 minutes
- Clean interval: 7 days
- L1 diff limit: 4 weeks
- Download timeout: 120 seconds

## Testing

The package includes comprehensive test coverage (85%+):

```bash
# Run all tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run specific test
go test -run TestDownloadAndSaveFile -v
```

## Folder Sync Integration

The package supports optional integration with `folder_sync` for automatic file synchronization across multiple servers.

### Basic Usage (Default OS Operations)

```go
import "github.com/real-rm/gofile"

// Direct usage - no initialization needed
// All operations use standard os package by default
data := []byte("Hello, World!")
err := gofile.WriteFile("/tmp/test.txt", data, 0644)  // Uses os.WriteFile

// Download images using OS operations
opts := &gofile.DownloadAndSaveFileOptions{
    URL:       "https://example.com/image.jpg",
    SavePaths: []string{"/tmp/image.jpg"},
    IsPhoto:   true,
}
results, err := gofile.DownloadAndSaveFile(opts)  // All file ops use os package
```

### Enable Folder Sync (Optional)
Need to run `folder_sync` service first.

```go
import (
    "github.com/real-rm/gofile"
    "github.com/real-rm/golog"
)

func main() {
    // Initialize logging
    golog.InitLog()

    // Enable folder_sync with custom config
    config := gofile.FolderSyncConfig{
        Enabled:    true,
        SocketPath: "/tmp/folder-sync.sock",
        SyncPath:   "/app/sync",
    }
    err := gofile.InitializeWithFolderSync(config)
    if err != nil {
        log.Printf("folder_sync unavailable, using OS: %v", err)
    } else {
        defer gofile.ShutdownFolderSync()
        log.Println("folder_sync enabled - files will auto-sync")
    }

    // Same code - now automatically syncs files across servers
    data := []byte("This will be synced!")
    err = gofile.WriteFile("/app/sync/important.txt", data, 0644)

    // Downloads also auto-sync
    opts := &gofile.DownloadAndSaveFileOptions{
        URL:       "https://example.com/image.jpg",
        SavePaths: []string{"/app/sync/image.jpg"},
        IsPhoto:   true,
    }
    results, err := gofile.DownloadAndSaveFile(opts)  // Auto-synced
}
```

### Configuration

```go
// Simple configuration - customize as needed
config := gofile.FolderSyncConfig{
    Enabled:    true,
    SocketPath: "/tmp/folder-sync.sock",  // Must match folder_sync config
    SyncPath:   "/app/sync",              // Must match folder_sync config
}
```

### Status Monitoring

```go
// Check if folder_sync is active
if gofile.IsFolderSyncEnabled() {
    fmt.Println("✓ Using folder_sync (auto-sync)")
} else {
    fmt.Println("✓ Using OS filesystem (local only)")
}

// Get detailed status
status := gofile.GetFolderSyncStatus()
fmt.Printf("Status: %+v\n", status)
```

### Migration from OS Package

```go
// Before: using os package
import "os"
err := os.WriteFile("/tmp/file.txt", data, 0644)

// After: using gofile (same behavior by default)
import "github.com/real-rm/gofile"
err := gofile.WriteFile("/tmp/file.txt", data, 0644)  // Identical behavior

// Optional: enable sync when needed
config := gofile.FolderSyncConfig{
    Enabled:    true,
    SocketPath: "/tmp/folder-sync.sock",
    SyncPath:   "/app/sync",
}
gofile.InitializeWithFolderSync(config)
// Now automatically syncs - no other code changes needed
```

### Prerequisites for Folder Sync

1. **folder_sync service** must be running with configured socket
2. **Socket path** must match between gofile and folder_sync configs
3. **Sync path** must match between gofile and folder_sync configs

Example folder_sync configuration:
```yaml
# /etc/folder-sync.ini
monitor:
  socket_path: "/tmp/folder-sync.sock"  # Must match gofile config
folder:
  sync_path: "/app/sync"                # Must match gofile config
```

**Key Benefits:**
- **Zero code changes** - existing code works unchanged
- **Graceful fallback** - automatically uses OS if folder_sync unavailable
- **Optional upgrade** - enable sync with one line of code
- **Transparent operation** - file operations automatically routed

## Dependencies

### Core Dependencies
- **github.com/HugoSmits86/nativewebp** - WebP image encoding
- **github.com/real-rm/goconfig** - Configuration management
- **github.com/real-rm/gohelper** - Utility functions
- **github.com/real-rm/golog** - Structured logging
- **github.com/real-rm/gomongo** - MongoDB operations
- **github.com/real-rm/folder_sync** - Optional file synchronization

### Standard Library
- **go.mongodb.org/mongo-driver** - MongoDB driver
- **image/jpeg** - JPEG processing
- **net/http** - HTTP client
- **time** - Time operations


