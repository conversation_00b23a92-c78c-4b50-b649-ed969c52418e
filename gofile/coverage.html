
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>gofile: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">/home/<USER>/github/go/gofile/folder_sync.go (58.1%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package gofile

import (
        "os"
        "path/filepath"
        "sync"

        "github.com/real-rm/folder_sync/pkg/client"
)

// FolderSyncConfig holds configuration for folder_sync integration
type FolderSyncConfig struct {
        Enabled    bool   `json:"enabled"`
        SocketPath string `json:"socket_path"`
        SyncPath   string `json:"sync_path"`
}

var (
        folderSyncClient *client.Client
        folderSyncMutex  sync.RWMutex
        folderSyncConfig FolderSyncConfig // Default zero value has Enabled = false
)

// InitializeWithFolderSync initializes folder_sync integration
// After calling this, all file operations will use folder_sync client
func InitializeWithFolderSync(config FolderSyncConfig) error <span class="cov8" title="1">{
        folderSyncMutex.Lock()
        defer folderSyncMutex.Unlock()

        // Close existing client if any
        if folderSyncClient != nil </span><span class="cov0" title="0">{
                folderSyncClient.Close()
                folderSyncClient = nil
        }</span>

        <span class="cov8" title="1">folderSyncConfig = config

        if !config.Enabled </span><span class="cov8" title="1">{
                updateOSFunctions() // Ensure OS functions are set
                return nil
        }</span>

        // Check if socket exists
        <span class="cov8" title="1">if _, err := os.Stat(config.SocketPath); os.IsNotExist(err) </span><span class="cov8" title="1">{
                updateOSFunctions() // Ensure OS functions are set
                return nil
        }</span>

        // Create client
        <span class="cov8" title="1">client, err := client.NewClient(config.SocketPath, config.SyncPath)
        if err != nil </span><span class="cov8" title="1">{
                updateOSFunctions() // Ensure OS functions are set
                return nil
        }</span>

        // Test connection
        <span class="cov0" title="0">connected, err := client.TestConnection()
        if err != nil </span><span class="cov0" title="0">{
                client.Close()
                updateOSFunctions() // Ensure OS functions are set
                return nil
        }</span>

        <span class="cov0" title="0">if !connected </span><span class="cov0" title="0">{
                client.Close()
                updateOSFunctions() // Ensure OS functions are set
                return nil
        }</span>

        <span class="cov0" title="0">folderSyncClient = client
        updateOSFunctions() // Update function pointers
        return nil</span>
}

// ShutdownFolderSync closes the folder_sync client
func ShutdownFolderSync() error <span class="cov8" title="1">{
        folderSyncMutex.Lock()
        defer folderSyncMutex.Unlock()

        var err error
        if folderSyncClient != nil </span><span class="cov0" title="0">{
                err = folderSyncClient.Close()
                folderSyncClient = nil
        }</span>

        // Always reset config and functions
        <span class="cov8" title="1">folderSyncConfig = FolderSyncConfig{} // Reset config
        updateOSFunctions()                   // Reset to OS functions

        return err</span>
}

// IsFolderSyncEnabled returns whether folder_sync is enabled and connected
func IsFolderSyncEnabled() bool <span class="cov8" title="1">{
        folderSyncMutex.RLock()
        defer folderSyncMutex.RUnlock()
        return folderSyncClient != nil
}</span>

// GetFolderSyncStatus returns the current status of folder_sync
func GetFolderSyncStatus() map[string]interface{} <span class="cov8" title="1">{
        folderSyncMutex.RLock()
        defer folderSyncMutex.RUnlock()

        status := map[string]interface{}{
                "enabled":   folderSyncConfig.Enabled,
                "connected": folderSyncClient != nil,
        }

        if folderSyncConfig.Enabled </span><span class="cov8" title="1">{
                status["socket_path"] = folderSyncConfig.SocketPath
                status["sync_path"] = folderSyncConfig.SyncPath
        }</span>

        <span class="cov8" title="1">return status</span>
}

// File operation functions that can be overridden by folder_sync
// These default to os operations and can be used directly without any initialization
var (
        WriteFile = os.WriteFile
        MkdirAll  = os.MkdirAll
        Remove    = os.RemoveAll
        Create    = os.Create
)

// updateOSFunctions updates the function pointers based on folder_sync status
// Note: This function assumes the caller already holds the appropriate lock
func updateOSFunctions() <span class="cov8" title="1">{
        if folderSyncClient != nil </span><span class="cov0" title="0">{
                // Use folder_sync functions
                WriteFile = func(path string, data []byte, perm os.FileMode) error </span><span class="cov0" title="0">{
                        return folderSyncClient.WriteFile(path, data, perm)
                }</span>

                <span class="cov0" title="0">MkdirAll = func(path string, perm os.FileMode) error </span><span class="cov0" title="0">{
                        return folderSyncClient.CreateDir(path, perm)
                }</span>

                <span class="cov0" title="0">Remove = func(path string) error </span><span class="cov0" title="0">{
                        return folderSyncClient.Remove(path)
                }</span>

                <span class="cov0" title="0">Create = func(path string) (*os.File, error) </span><span class="cov0" title="0">{
                        // Ensure directory exists first
                        dir := filepath.Dir(path)
                        if err := folderSyncClient.CreateDir(dir, 0755); err != nil </span>{<span class="cov0" title="0">
                                // Ignore error if directory already exists
                        }</span>
                        // Create the file using OS (folder_sync will detect the change)
                        <span class="cov0" title="0">return os.Create(path)</span>
                }
        } else<span class="cov8" title="1"> {
                // Use OS functions
                WriteFile = os.WriteFile
                MkdirAll = os.MkdirAll
                Remove = os.RemoveAll
                Create = os.Create
        }</span>
}
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
