package gofile

import (
	"os"
	"path/filepath"
	"sync"

	"github.com/real-rm/folder_sync/pkg/client"
)

// FolderSyncConfig holds configuration for folder_sync integration
type FolderSyncConfig struct {
	Enabled    bool   `json:"enabled"`
	SocketPath string `json:"socket_path"`
	SyncPath   string `json:"sync_path"`
}

var (
	folderSyncClient *client.Client
	folderSyncMutex  sync.RWMutex
	folderSyncConfig FolderSyncConfig // Default zero value has Enabled = false
)

// InitializeWithFolderSync initializes folder_sync integration
// After calling this, all file operations will use folder_sync client
func InitializeWithFolderSync(config FolderSyncConfig) error {
	folderSyncMutex.Lock()
	defer folderSyncMutex.Unlock()

	// Close existing client if any
	if folderSyncClient != nil {
		_ = folderSyncClient.Close()
		folderSyncClient = nil
	}

	folderSyncConfig = config

	if !config.Enabled {
		updateOSFunctions() // Ensure OS functions are set
		return nil
	}

	// Check if socket exists
	if _, err := os.Stat(config.SocketPath); os.IsNotExist(err) {
		updateOSFunctions() // Ensure OS functions are set
		return nil
	}

	// Create client
	client, err := client.NewClient(config.SocketPath, config.SyncPath)
	if err != nil {
		updateOSFunctions() // Ensure OS functions are set
		return nil
	}

	// Test connection
	connected, err := client.TestConnection()
	if err != nil {
		_ = client.Close()
		updateOSFunctions() // Ensure OS functions are set
		return nil
	}

	if !connected {
		_ = client.Close()
		updateOSFunctions() // Ensure OS functions are set
		return nil
	}

	folderSyncClient = client
	updateOSFunctions() // Update function pointers
	return nil
}

// ShutdownFolderSync closes the folder_sync client
func ShutdownFolderSync() error {
	folderSyncMutex.Lock()
	defer folderSyncMutex.Unlock()

	var err error
	if folderSyncClient != nil {
		err = folderSyncClient.Close()
		folderSyncClient = nil
	}

	// Always reset config and functions
	folderSyncConfig = FolderSyncConfig{} // Reset config
	updateOSFunctions()                   // Reset to OS functions

	return err
}

// IsFolderSyncEnabled returns whether folder_sync is enabled and connected
func IsFolderSyncEnabled() bool {
	folderSyncMutex.RLock()
	defer folderSyncMutex.RUnlock()
	return folderSyncClient != nil
}

// GetFolderSyncStatus returns the current status of folder_sync
func GetFolderSyncStatus() map[string]interface{} {
	folderSyncMutex.RLock()
	defer folderSyncMutex.RUnlock()

	status := map[string]interface{}{
		"enabled":   folderSyncConfig.Enabled,
		"connected": folderSyncClient != nil,
	}

	if folderSyncConfig.Enabled {
		status["socket_path"] = folderSyncConfig.SocketPath
		status["sync_path"] = folderSyncConfig.SyncPath
	}

	return status
}

// File operation functions that can be overridden by folder_sync
// These default to os operations and can be used directly without any initialization
var (
	WriteFile = os.WriteFile
	MkdirAll  = os.MkdirAll
	Remove    = os.RemoveAll
	Create    = os.Create
)

// updateOSFunctions updates the function pointers based on folder_sync status
// Note: This function assumes the caller already holds the appropriate lock
func updateOSFunctions() {
	if folderSyncClient != nil {
		// Use folder_sync functions
		WriteFile = func(path string, data []byte, perm os.FileMode) error {
			return folderSyncClient.WriteFile(path, data, perm)
		}

		MkdirAll = func(path string, perm os.FileMode) error {
			return folderSyncClient.CreateDir(path, perm)
		}

		Remove = func(path string) error {
			return folderSyncClient.Remove(path)
		}

		Create = func(path string) (*os.File, error) {
			// Ensure directory exists first
			dir := filepath.Dir(path)
			_ = folderSyncClient.CreateDir(dir, 0755) // Ignore error if directory already exists
			// Create the file using OS (folder_sync will detect the change)
			return os.Create(path)
		}
	} else {
		// Use OS functions
		WriteFile = os.WriteFile
		MkdirAll = os.MkdirAll
		Remove = os.RemoveAll
		Create = os.Create
	}
}
