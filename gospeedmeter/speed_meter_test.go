package gospeedmeter

import (
	"testing"
	"time"
)

func TestNewSpeedMeter(t *testing.T) {
	options := SpeedMeterOptions{
		Values: map[string]float64{"meter1": 10},
	}
	sm := NewSpeedMeter(options)
	if sm == nil {
		t.<PERSON>rror("Expected non-nil SpeedMeter")
	}
	if len(sm.GetCounters()) != 1 {
		t.<PERSON><PERSON>("Expected 1 counter")
	}
}

func TestSpeedMeterCheck(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Test initial check
	sm.Check("meter1", 13)
	sm.Check("meter2", 200)
	counters := sm.GetCounters()
	if counters["meter1"] != 13 || counters["meter2"] != 200 {
		t.Error("Check values not properly updated")
	}

	// Test second check
	sm.Check("meter1", 10)
	sm.Check("meter3", 10)
	counters = sm.GetCounters()
	if counters["meter1"] != 23 || counters["meter2"] != 200 || counters["meter3"] != 10 {
		t.Error("Check values not properly accumulated")
	}
}

func TestSpeedMeterGetSpeed(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Add some values
	sm.Check("meter1", 100)

	// Wait a bit to ensure time difference
	time.Sleep(100 * time.Millisecond)

	// Get speed in different units
	speed := sm.GetSpeed(UnitS)
	if speed["meter1"] < 900 || speed["meter1"] > 1100 {
		t.Errorf("Speed should be approximately 1000/s, got %f", speed["meter1"])
	}
}

func TestSpeedMeterReset(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Add initial values
	sm.Check("meter1", 100)

	// Reset with values
	sm.Reset(map[string]float64{"meter1": 50})
	counters := sm.GetCounters()
	if counters["meter1"] != 50 {
		t.Error("Reset not working properly")
	}

	// Reset without values
	sm.Reset()
	counters = sm.GetCounters()
	if len(counters) != 0 {
		t.Error("Reset should clear counters")
	}
}

func TestSpeedMeterEstimate(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Add some values
	sm.Check("meter1", 100)

	// Wait a bit
	time.Sleep(100 * time.Millisecond)

	// Test estimation
	estimate := sm.Estimate("meter1", 1000, UnitS)
	expectedEstimate := 0.9 // estimate should be 9.0
	if estimate < expectedEstimate*0.8 || estimate > expectedEstimate*1.2 {
		t.Errorf("Estimate should be approximately %f, got %f", expectedEstimate, estimate)
	}
}

func TestSpeedMeterToString(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Add some values
	sm.Check("meter1", 100)

	// Test string representation
	str := sm.ToString(UnitS, nil)
	if str == "" {
		t.Error("String representation should not be empty")
	}

	// Test string with estimates
	str = sm.ToString(UnitS, map[string]float64{"meter1": 1000})
	if str == "" {
		t.Error("String representation with estimates should not be empty")
	}
}

func TestSpeedMeterIntervalCallback(t *testing.T) {
	callbackCalled := false
	callback := func(sm *SpeedMeter) {
		callbackCalled = true
	}

	options := SpeedMeterOptions{
		IntervalTriggerCount: 2,
		IntervalCallback:     callback,
	}
	sm := NewSpeedMeter(options)

	// Add values to trigger callback
	sm.Check("meter1", 1)
	sm.Check("meter1", 1)

	if !callbackCalled {
		t.Error("Interval callback should have been called")
	}
}

func TestSpeedMeterConcurrency(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Test concurrent access
	done := make(chan bool)
	for i := 0; i < 10; i++ {
		go func() {
			sm.Check("meter1", 1)
			done <- true
		}()
	}

	// Wait for all goroutines to complete
	for i := 0; i < 10; i++ {
		<-done
	}

	counters := sm.GetCounters()
	if counters["meter1"] != 10 {
		t.Error("Concurrent access not handled properly")
	}
}
